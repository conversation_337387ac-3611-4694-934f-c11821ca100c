import { z } from 'zod'

export enum ErrorCodes {
  OK = 0,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  USER_NOT_FOUND = 40104,
  UNKNOWN = 500,
}

export const RESPONSE_ERROR_SCHEMA = z.object({
  success: z.literal(false),
  errCode: z.nativeEnum(ErrorCodes),
  toast: z.string(),
})

export const COMMON_ERROR_RESPONSE_CODES = {
  [ErrorCodes.BAD_REQUEST]: RESPONSE_ERROR_SCHEMA,
  [ErrorCodes.UNAUTHORIZED]: RESPONSE_ERROR_SCHEMA,
  [ErrorCodes.UNKNOWN]: RESPONSE_ERROR_SCHEMA,
}
