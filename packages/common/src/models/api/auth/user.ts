import { z } from 'zod'

export const ANONYMOUS_CREATE_RES_SCHEMA = z.object({
  success: z.literal(true),
  token: z.string(),
})

export const CREATE_USER_REQ_SCHEMA = z.object({
  username: z.string(),
  password: z.string(),
  token: z.string().optional(),
})

export const CREATE_USER_RES_SCHEMA = z.object({
  success: z.literal(true),
  token: z.string(),
})

export const CHECK_USER_REQ_SCHEMA = z.object({
  username: z.string(),
})

export const CHECK_USER_RES_SCHEMA = z.object({
  success: z.literal(true),
  exists: z.boolean(),
})

export const LOGIN_USER_REQ_SCHEMA = z.object({
  username: z.string(),
  password: z.string(),
})

export const LOGIN_USER_RES_SCHEMA = z.object({
  success: z.literal(true),
  token: z.string(),
})

export const USER_PROFILE_SCHEMA = z.object({
  id: z.string(),
  username: z.string().nullable(),
})

export const GET_USER_PROFILE_RES_SCHEMA = z.object({
  success: z.literal(true),
  user: USER_PROFILE_SCHEMA,
})
