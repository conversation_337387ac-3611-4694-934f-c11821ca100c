{"name": "@repo/common", "version": "1.0.0", "description": "", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "tsc", "watch": "tsc -w", "dev": "npm run watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@repo/database": "workspace:^", "openai": "^4.92.1", "zod": "^3.22.4"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^17.0.12", "typescript": "^5.3.2"}}