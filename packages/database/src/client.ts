import { PrismaClient } from '../generated/client'
import { similarChannelTaskExtension } from './extensions/similar-channel-task-extension'
import { UserMembershipExtension } from './extensions/user-membership-extension'

export * from '../generated/client'

export const prisma = new PrismaClient({
  omit: {
    project: {
      candidates: true,
    },
    tikTokUserInfo: {
      videos: true,
    },
    instagramUserInfo: {
      posts: true,
    },
    youTubeChannel: {
      videos: true,
    },
    emailRecord: {
      content: true,
    },
  },
}).$extends({
  // https://www.prisma.io/docs/orm/prisma-client/queries/computed-fields
  result: {
    ...similarChannelTaskExtension,
    ...UserMembershipExtension,
  },
})

export type TransactionClient = Omit<
  PrismaClient,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use'
>
