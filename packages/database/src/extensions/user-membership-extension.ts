import { MemberType, UserMembership } from '../client'

export const UserMembershipExtension = {
  UserMembership: {
    isEnterprise: {
      compute(membership: UserMembership) {
        return membership.type == MemberType.ENTERPRISE
      },
    },
    isPaid: {
      compute(membership: UserMembership) {
        return membership.type == MemberType.PAID
      },
    },
    isFree: {
      compute(membership: UserMembership) {
        return membership.type == MemberType.FREE
      },
    },
  },
}
