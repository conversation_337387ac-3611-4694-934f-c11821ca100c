import { KolPlatform, SimilarChannelTask, SimilarChannelTaskStatus, TaskType } from '../client'

export const similarChannelTaskExtension = {
  similarChannelTask: {
    isCompleted: {
      compute(task: SimilarChannelTask) {
        return (
          [
            SimilarChannelTaskStatus.COMPLETED,
            SimilarChannelTaskStatus.RESULT_READY,
          ] as SimilarChannelTaskStatus[]
        ).includes(task.status)
      },
    },
    isFailed: {
      compute(task: SimilarChannelTask) {
        return task.status === SimilarChannelTaskStatus.FAILED
      },
    },
    isYoutube: {
      compute(task: SimilarChannelTask) {
        return (task.params as any)?.platform === KolPlatform.YOUTUBE
      },
    },
    isTiktok: {
      compute(task: SimilarChannelTask) {
        return (task.params as any)?.platform === KolPlatform.TIKTOK
      },
    },
    isTwitter: {
      compute(task: SimilarChannelTask) {
        return (task.params as any)?.platform === KolPlatform.TWITTER
      },
    },
    isInstagram: {
      compute(task: SimilarChannelTask) {
        return (task.params as any)?.platform === KolPlatform.INSTAGRAM
      },
    },
    isPaginationTask: {
      compute(task: SimilarChannelTask) {
        return (
          task.type in
          [
            TaskType.FOLLOWING_LIST,
            TaskType.HASH_TAG_BREAK,
            TaskType.SEARCH_INPUT_BREAK,
            TaskType.BGM_BREAK,
          ]
        )
      },
    },
    isSingleTask: {
      compute(task: SimilarChannelTask) {
        return (
          task.type in
          [
            TaskType.SIMILAR,
            TaskType.KEYWORD,
            TaskType.WEB_LIST,
            TaskType.EASYKOL_TRACK,
            TaskType.AUDIENCE_ANALYSIS,
            TaskType.FOLLOWERS_SIMILAR,
          ]
        )
      },
    },
  },
}
