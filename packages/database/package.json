{"name": "@repo/database", "version": "1.0.0", "description": "", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**", "generated/**"], "scripts": {"dev": "pnpm run build", "build": "npx prisma generate && tsc", "db:generate": "npx prisma generate", "db:format": "npx prisma format", "db:migrate": "npx prisma migrate dev --create-only", "db:studio": "npx prisma studio", "db:deploy": "npx prisma migrate deploy"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.20.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "^17.0.12", "prisma": "^5.20.0", "typescript": "^5.3.2"}}