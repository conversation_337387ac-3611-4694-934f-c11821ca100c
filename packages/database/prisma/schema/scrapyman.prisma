model ScrapymanTask {
  id        String              @id @default(cuid())
  type      ScrapymanTaskType
  status    ScrapymanTaskStatus
  params    Json
  result    Json?
  errors    Json?
  meta      Json?
  lastRanAt DateTime?
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt

  @@index([status])
  @@index([type, status, createdAt])
}

model ScrapymanTaskGroup {
  id        String            @id @default(cuid())
  taskType  ScrapymanTaskType
  taskIds   String[]
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
}

enum ScrapymanTaskType {
  REDBOOK_DETAIL
  TIKTOK_DETAIL
}

enum ScrapymanTaskStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
