-- CreateTable
CREATE TABLE "UserSignatureLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL DEFAULT '',
    "browserId" TEXT NOT NULL DEFAULT '',
    "extensionId" TEXT NOT NULL DEFAULT '',
    "version" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserSignatureLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserSignatureLog_extensionId_idx" ON "UserSignatureLog"("extensionId");

-- CreateIndex
CREATE INDEX "UserSignatureLog_userId_idx" ON "UserSignatureLog"("userId");
