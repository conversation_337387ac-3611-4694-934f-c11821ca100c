/*
  Warnings:

  - Added the required column `recordId` to the `KolNote` table without a default value. This is not possible if the table is not empty.
  - Added the required column `recordId` to the `KolTag` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "KolNote" ADD COLUMN     "recordId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "KolTag" ADD COLUMN     "recordId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "KolRecord" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "KolRecord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "KolRecord_kolId_idx" ON "KolRecord"("kolId");

-- CreateIndex
CREATE INDEX "KolRecord_userId_idx" ON "KolRecord"("userId");

-- CreateIndex
CREATE INDEX "KolRecord_enterpriseId_idx" ON "KolRecord"("enterpriseId");
