-- CreateEnum
CREATE TYPE "FeishuTrackStatus" AS ENUM ('success', 'failed', 'error', 'timeout', 'rate_limit');

-- CreateTable
CREATE TABLE "FeishuTrackLog" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "requestUrl" TEXT NOT NULL,
    "platform" TEXT,
    "linkType" TEXT,
    "accountName" TEXT,
    "displayName" TEXT,
    "status" "FeishuTrackStatus" NOT NULL,
    "responseCode" INTEGER,
    "responseData" JSONB,
    "errorMessage" TEXT,
    "processingTime" INTEGER,
    "quotaUsed" INTEGER NOT NULL DEFAULT 1,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FeishuTrackLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FeishuTrackLog_requestId_key" ON "FeishuTrackLog"("requestId");

-- CreateIndex
CREATE INDEX "FeishuTrackLog_apiKey_idx" ON "FeishuTrackLog"("apiKey");

-- CreateIndex
CREATE INDEX "FeishuTrackLog_platform_idx" ON "FeishuTrackLog"("platform");

-- CreateIndex
CREATE INDEX "FeishuTrackLog_status_idx" ON "FeishuTrackLog"("status");

-- CreateIndex
CREATE INDEX "FeishuTrackLog_createdAt_idx" ON "FeishuTrackLog"("createdAt");

-- CreateIndex
CREATE INDEX "FeishuTrackLog_requestId_idx" ON "FeishuTrackLog"("requestId");
