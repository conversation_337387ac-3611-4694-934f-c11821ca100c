-- AlterEnum
ALTER TYPE "TaskReason" ADD VALUE 'NEXT_PAGE';

-- AlterEnum
ALTER TYPE "TaskType" ADD VALUE 'HASH_TAG_BREAK';

-- AlterTable
ALTER TABLE "SimilarChannelTask" ADD COLUMN     "candidate" JSONB DEFAULT '{}';

-- CreateTable
CREATE TABLE "ProjectCandidate" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "type" "TaskType" NOT NULL,
    "candidates" JSONB DEFAULT '{}',
    "meta" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProjectCandidate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProjectCandidate_projectId_idx" ON "ProjectCandidate"("projectId");

-- CreateIndex
CREATE INDEX "ProjectCandidate_type_idx" ON "ProjectCandidate"("type");

-- CreateIndex
CREATE UNIQUE INDEX "ProjectCandidate_projectId_type_key" ON "ProjectCandidate"("projectId", "type");
