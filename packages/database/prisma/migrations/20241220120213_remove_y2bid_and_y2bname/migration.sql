/*
  Warnings:

  - You are about to drop the column `y2bId` on the `KolInfo` table. All the data in the column will be lost.
  - You are about to drop the column `y2bName` on the `KolInfo` table. All the data in the column will be lost.
  - You are about to drop the `YouTubeChannels` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[source,platformAccount]` on the table `KolInfo` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "KolInfo_platformAccount_source_key";

-- DropIndex
DROP INDEX "KolInfo_y2bId_key";

-- AlterTable
ALTER TABLE "KolInfo" DROP COLUMN "y2bId",
DROP COLUMN "y2bName";

-- DropTable
DROP TABLE "YouTubeChannels";

-- CreateTable
CREATE TABLE "YouTubeChannel" (
    "channelId" TEXT NOT NULL,
    "channelName" TEXT NOT NULL,
    "channelHandle" TEXT NOT NULL DEFAULT '',
    "channelDescription" TEXT NOT NULL DEFAULT '',
    "numericSubscriberCount" BIGINT NOT NULL DEFAULT 0,
    "country" TEXT,
    "haveCrawlered" BOOLEAN NOT NULL DEFAULT false,
    "videosAverageViewCount" BIGINT NOT NULL,
    "officialEmail" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastPublishedTime" BIGINT NOT NULL,

    CONSTRAINT "YouTubeChannel_pkey" PRIMARY KEY ("channelId")
);

-- CreateIndex
CREATE INDEX "YouTubeChannel_lastPublishedTime_idx" ON "YouTubeChannel"("lastPublishedTime");

-- CreateIndex
CREATE INDEX "YouTubeChannel_videosAverageViewCount_idx" ON "YouTubeChannel"("videosAverageViewCount");

-- CreateIndex
CREATE INDEX "YouTubeChannel_numericSubscriberCount_idx" ON "YouTubeChannel"("numericSubscriberCount");

-- CreateIndex
CREATE INDEX "YouTubeChannel_country_idx" ON "YouTubeChannel"("country");

-- CreateIndex
CREATE UNIQUE INDEX "KolInfo_source_platformAccount_key" ON "KolInfo"("source", "platformAccount");
