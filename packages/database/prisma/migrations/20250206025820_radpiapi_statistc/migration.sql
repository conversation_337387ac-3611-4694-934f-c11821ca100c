-- CreateEnum
CREATE TYPE "RapidApiStatus" AS ENUM ('SUCCESS', 'ERROR', 'WARNING');

-- CreateTable
CREATE TABLE "RapidApiDailyStat" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "platform" TEXT NOT NULL,
    "status" "RapidApiStatus" NOT NULL,
    "endpoint" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RapidApiDailyStat_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "RapidApiDailyStat_date_idx" ON "RapidApiDailyStat"("date");

-- CreateIndex
CREATE INDEX "RapidApiDailyStat_endpoint_idx" ON "RapidApiDailyStat"("endpoint");

-- CreateIndex
CREATE INDEX "RapidApiDailyStat_status_idx" ON "RapidApiDailyStat"("status");

-- CreateIndex
CREATE INDEX "RapidApiDailyStat_platform_idx" ON "RapidApiDailyStat"("platform");

-- CreateIndex
CREATE UNIQUE INDEX "RapidApiDailyStat_date_endpoint_status_key" ON "RapidApiDailyStat"("date", "endpoint", "status");
