/*
  Warnings:

  - Changed the type of `type` on the `KolTagLog` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "TagAndNoteOpType" AS ENUM ('ADD_TAG', 'DELETE_TAG', 'UPDATE_TAG_META', 'MODIFY_NOTE');

-- AlterTable
ALTER TABLE "KolTagLog" DROP COLUMN "type",
ADD COLUMN     "type" "TagAndNoteOpType" NOT NULL;

-- DropEnum
DROP TYPE "TagOpType";
