-- CreateEnum
CREATE TYPE "EnterpriseStatus" AS ENUM ('active', 'suspended', 'expired');

-- AlterEnum
ALTER TYPE "MemberType" ADD VALUE 'enterprise';

-- AlterTable
ALTER TABLE "QuotaLogs" ADD COLUMN     "enterpriseId" TEXT DEFAULT '';

-- AlterTable
ALTER TABLE "UserMemberships" ADD COLUMN     "enterpriseId" TEXT DEFAULT '',
ADD COLUMN     "isEnterpriseAdmin" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "Enterprise" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "contactPerson" TEXT DEFAULT '',
    "contactPhone" TEXT DEFAULT '',
    "contactEmail" TEXT DEFAULT '',
    "address" TEXT DEFAULT '',
    "industry" TEXT DEFAULT '',
    "scale" TEXT DEFAULT '',
    "accountQuota" INTEGER NOT NULL DEFAULT 0,
    "usedQuota" INTEGER NOT NULL DEFAULT 0,
    "description" TEXT DEFAULT '',
    "status" "EnterpriseStatus" NOT NULL DEFAULT 'active',
    "effectiveAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expireAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Enterprise_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserMemberships_enterpriseId_idx" ON "UserMemberships"("enterpriseId");
