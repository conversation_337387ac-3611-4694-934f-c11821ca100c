-- CreateTable
CREATE TABLE "PublicationStatisticsSheetData" (
    "id" TEXT NOT NULL,
    "spreadsheetId" TEXT NOT NULL,
    "publishDate" TIMESTAMP(3) NOT NULL,
    "influencer" TEXT NOT NULL,
    "followers" INTEGER NOT NULL,
    "postLink" TEXT NOT NULL,
    "views" INTEGER NOT NULL,
    "likes" INTEGER NOT NULL,
    "comments" INTEGER NOT NULL,
    "favorites" INTEGER NOT NULL,
    "shares" INTEGER NOT NULL,
    "totalEngagement" INTEGER NOT NULL,
    "engagementRate" DOUBLE PRECISION NOT NULL,
    "cashCost" DOUBLE PRECISION NOT NULL,
    "itemCost" DOUBLE PRECISION NOT NULL,
    "cpsCost" DOUBLE PRECISION NOT NULL,
    "bonusCost" DOUBLE PRECISION NOT NULL,
    "totalCost" DOUBLE PRECISION NOT NULL,
    "cpm" DOUBLE PRECISION NOT NULL,
    "weightedCpm" DOUBLE PRECISION NOT NULL,
    "countryData" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PublicationStatisticsSheetData_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PublicationStatisticsSheetData_spreadsheetId_idx" ON "PublicationStatisticsSheetData"("spreadsheetId");

-- CreateIndex
CREATE INDEX "PublicationStatisticsSheetData_publishDate_idx" ON "PublicationStatisticsSheetData"("publishDate");
