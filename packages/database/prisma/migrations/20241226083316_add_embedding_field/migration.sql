/*
  Warnings:

  - A unique constraint covering the columns `[username]` on the table `InstagramUserInfo` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `embedding` to the `KolInfo` table without a default value. This is not possible if the table is not empty.

*/
create extension if not exists vector;

-- AlterTable
ALTER TABLE "InstagramUserInfo" ALTER COLUMN "username" DROP DEFAULT;

-- AlterTable
ALTER TABLE "KolInfo" ADD COLUMN     "embedding" vector(3072);

-- CreateIndex
CREATE UNIQUE INDEX "InstagramUserInfo_username_key" ON "InstagramUserInfo"("username");
