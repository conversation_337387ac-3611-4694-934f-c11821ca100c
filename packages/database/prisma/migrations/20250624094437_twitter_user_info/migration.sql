-- AlterEnum
ALTER TYPE "KolPlatform" ADD VALUE 'TWITTER';

-- CreateTable
CREATE TABLE "TwitterUser" (
    "id" TEXT NOT NULL,
    "restId" TEXT NOT NULL,
    "screenName" TEXT NOT NULL,
    "rawData" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TwitterUser_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TwitterUser_restId_key" ON "TwitterUser"("restId");

-- CreateIndex
CREATE UNIQUE INDEX "TwitterUser_screenName_key" ON "TwitterUser"("screenName");

-- CreateIndex
CREATE INDEX "TwitterUser_restId_idx" ON "TwitterUser"("restId");

-- CreateIndex
CREATE INDEX "TwitterUser_screenName_idx" ON "TwitterUser"("screenName");
