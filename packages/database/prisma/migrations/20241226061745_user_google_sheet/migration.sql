-- CreateEnum
CREATE TYPE "SheetStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'ERROR');

-- CreateTable
CREATE TABLE "UserGoogleSheet" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "spreadsheetId" TEXT NOT NULL,
    "sheetUrl" TEXT NOT NULL DEFAULT '',
    "title" TEXT NOT NULL DEFAULT 'easykol-sheet',
    "status" "SheetStatus" NOT NULL DEFAULT 'ACTIVE',
    "lastSyncAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserGoogleSheet_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserGoogleSheet_userId_key" ON "UserGoogleSheet"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserGoogleSheet_spreadsheetId_key" ON "UserGoogleSheet"("spreadsheetId");

-- CreateIndex
CREATE INDEX "UserGoogleSheet_userId_idx" ON "UserGoogleSheet"("userId");

-- CreateIndex
CREATE INDEX "UserGoogleSheet_spreadsheetId_idx" ON "UserGoogleSheet"("spreadsheetId");
