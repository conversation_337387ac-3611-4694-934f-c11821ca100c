-- CreateEnum
CREATE TYPE "KolPlatform" AS ENUM ('YOUTUBE', 'TIKTOK', 'INSTAGRAM');

-- CreateEnum
CREATE TYPE "ProjectMembershipRole" AS ENUM ('OWNER', 'MEMBER');

-- C<PERSON><PERSON>num
CREATE TYPE "EmailTemplateType" AS ENUM ('PLAIN', 'MJML');

-- CreateEnum
CREATE TYPE "ProjectKolAttitude" AS ENUM ('LIKE', 'DISLIKE', 'NORATE', 'SUPERLIKE');

-- CreateEnum
CREATE TYPE "SentEmailType" AS ENUM ('SAY_HI');

-- CreateEnum
CREATE TYPE "SendStatus" AS ENUM ('DRAFT', 'PENDING', 'SENT', 'FAILED');

-- CreateEnum
CREATE TYPE "TaskReason" AS ENUM ('SEARCH', 'LIKE', 'SUPERLIKE');

-- CreateEnum
CREATE TYPE "ProviderType" AS ENUM ('GOOGLE');

-- CreateEnum
CREATE TYPE "ScopeType" AS ENUM ('GMAIL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "EmailCredentialType" AS ENUM ('GMAIL');

-- CreateEnum
CREATE TYPE "SimilarChannelTaskStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- CreateEnum
CREATE TYPE "ScrapymanTaskType" AS ENUM ('REDBOOK_DETAIL', 'TIKTOK_DETAIL');

-- CreateEnum
CREATE TYPE "ScrapymanTaskStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "UserInfo" (
    "userId" TEXT NOT NULL,
    "email" TEXT,
    "phoneNumber" TEXT,
    "avatar" TEXT,
    "supabase" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserInfo_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "KolInfo" (
    "id" TEXT NOT NULL,
    "title" TEXT,
    "description" TEXT,
    "email" TEXT,
    "historyEmails" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "platformAccount" TEXT,
    "y2bId" TEXT,
    "y2bName" TEXT,
    "source" "KolPlatform" NOT NULL,
    "y2bObject" JSONB,
    "infoUpdatedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "nanoObject" JSONB,
    "avatar" TEXT DEFAULT '',

    CONSTRAINT "KolInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Project" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Project_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectMembership" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "role" "ProjectMembershipRole" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProjectMembership_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailTemplate" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "renderType" "EmailTemplateType" NOT NULL DEFAULT 'PLAIN',
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "createdBy" TEXT NOT NULL,
    "belongsToProductId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProjectKol" (
    "id" TEXT NOT NULL,
    "attitude" "ProjectKolAttitude" NOT NULL,
    "rateBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastSimilarAt" TIMESTAMP(3),
    "kolId" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "similarTaskId" TEXT NOT NULL,

    CONSTRAINT "ProjectKol_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailRecord" (
    "id" TEXT NOT NULL,
    "templateId" TEXT NOT NULL,
    "type" "SentEmailType" NOT NULL,
    "status" "SendStatus" NOT NULL,
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "tags" TEXT[],
    "resultObj" JSONB,
    "ProductChannelId" TEXT NOT NULL,
    "sentAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Provider" (
    "id" TEXT NOT NULL,
    "type" "ProviderType" NOT NULL,
    "key" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "clientSecret" TEXT NOT NULL,
    "scopes" "ScopeType"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Provider_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProviderCredential" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "expiresAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProviderCredential_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailSender" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "type" "EmailCredentialType" NOT NULL,
    "providerCredentialId" TEXT,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailSender_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SimilarChannelTask" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL DEFAULT '0',
    "status" "SimilarChannelTaskStatus" NOT NULL,
    "params" JSONB NOT NULL,
    "result" JSONB,
    "errors" JSONB,
    "meta" JSONB,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isTerminated" BOOLEAN NOT NULL DEFAULT false,
    "reason" "TaskReason" NOT NULL DEFAULT 'LIKE',

    CONSTRAINT "SimilarChannelTask_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TaskKol" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "taskId" TEXT NOT NULL,
    "channelId" TEXT NOT NULL,
    "score" DECIMAL(65,30) NOT NULL DEFAULT 0,

    CONSTRAINT "TaskKol_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "YouTubeChannels" (
    "channelId" TEXT NOT NULL,
    "channelName" TEXT NOT NULL,
    "channelDescription" TEXT NOT NULL DEFAULT '',
    "numericSubscriberCount" BIGINT NOT NULL DEFAULT 0,
    "country" TEXT,
    "haveCrawlered" BOOLEAN NOT NULL DEFAULT false,
    "videosAverageViewCount" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastPublishedTime" BIGINT NOT NULL,

    CONSTRAINT "YouTubeChannels_pkey" PRIMARY KEY ("channelId")
);

-- CreateTable
CREATE TABLE "TikTokUserInfo" (
    "userId" TEXT NOT NULL,
    "uniqueId" TEXT NOT NULL,
    "nickname" TEXT NOT NULL,
    "lastPublishedTime" BIGINT NOT NULL,
    "haveCrawlered" BOOLEAN NOT NULL DEFAULT false,
    "averagePlayCount" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "avatar" TEXT NOT NULL,

    CONSTRAINT "TikTokUserInfo_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "InstagramUserInfo" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL DEFAULT '',
    "profilePicUrl" TEXT NOT NULL DEFAULT '',
    "fullName" TEXT NOT NULL DEFAULT '',
    "followerCount" INTEGER NOT NULL DEFAULT 0,
    "averageLikeCount" INTEGER NOT NULL DEFAULT 0,
    "averageCommentCount" INTEGER NOT NULL DEFAULT 0,
    "lastPublishedTime" BIGINT NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InstagramUserInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ScrapymanTask" (
    "id" TEXT NOT NULL,
    "type" "ScrapymanTaskType" NOT NULL,
    "status" "ScrapymanTaskStatus" NOT NULL,
    "params" JSONB NOT NULL,
    "result" JSONB,
    "errors" JSONB,
    "meta" JSONB,
    "lastRanAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ScrapymanTask_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ScrapymanTaskGroup" (
    "id" TEXT NOT NULL,
    "taskType" "ScrapymanTaskType" NOT NULL,
    "taskIds" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ScrapymanTaskGroup_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "KolInfo_platformAccount_key" ON "KolInfo"("platformAccount");

-- CreateIndex
CREATE UNIQUE INDEX "KolInfo_y2bId_key" ON "KolInfo"("y2bId");

-- CreateIndex
CREATE UNIQUE INDEX "KolInfo_y2bName_key" ON "KolInfo"("y2bName");

-- CreateIndex
CREATE INDEX "ProjectKol_projectId_similarTaskId_kolId_attitude_lastSimil_idx" ON "ProjectKol"("projectId", "similarTaskId", "kolId", "attitude", "lastSimilarAt");

-- CreateIndex
CREATE UNIQUE INDEX "Provider_key_key" ON "Provider"("key");

-- CreateIndex
CREATE UNIQUE INDEX "ProviderCredential_providerId_createdBy_key" ON "ProviderCredential"("providerId", "createdBy");

-- CreateIndex
CREATE UNIQUE INDEX "EmailSender_email_createdBy_key" ON "EmailSender"("email", "createdBy");

-- CreateIndex
CREATE UNIQUE INDEX "TikTokUserInfo_uniqueId_key" ON "TikTokUserInfo"("uniqueId");

-- CreateIndex
CREATE INDEX "ScrapymanTask_status_idx" ON "ScrapymanTask"("status");

-- CreateIndex
CREATE INDEX "ScrapymanTask_type_status_createdAt_idx" ON "ScrapymanTask"("type", "status", "createdAt");
