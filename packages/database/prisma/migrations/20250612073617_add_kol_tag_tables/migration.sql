-- CreateEnum
CREATE TYPE "TagOpType" AS ENUM ('ADD', 'DELETE');

-- CreateTable
CREATE TABLE "CreatorTag" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT '#EAD094',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "CreatorTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KolTag" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "KolTag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KolNote" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "note" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "KolNote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KolTagLog" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,
    "tagName" TEXT NOT NULL,
    "type" "TagOpType" NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "KolTagLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CreatorTag_userId_idx" ON "CreatorTag"("userId");

-- CreateIndex
CREATE INDEX "CreatorTag_enterpriseId_idx" ON "CreatorTag"("enterpriseId");

-- CreateIndex
CREATE INDEX "CreatorTag_name_idx" ON "CreatorTag"("name");

-- CreateIndex
CREATE INDEX "KolTag_kolId_idx" ON "KolTag"("kolId");

-- CreateIndex
CREATE INDEX "KolTag_userId_idx" ON "KolTag"("userId");

-- CreateIndex
CREATE INDEX "KolTag_enterpriseId_idx" ON "KolTag"("enterpriseId");

-- CreateIndex
CREATE INDEX "KolTag_tagId_idx" ON "KolTag"("tagId");

-- CreateIndex
CREATE INDEX "KolNote_kolId_idx" ON "KolNote"("kolId");

-- CreateIndex
CREATE INDEX "KolNote_userId_idx" ON "KolNote"("userId");

-- CreateIndex
CREATE INDEX "KolNote_enterpriseId_idx" ON "KolNote"("enterpriseId");

-- CreateIndex
CREATE INDEX "KolTagLog_kolId_idx" ON "KolTagLog"("kolId");

-- CreateIndex
CREATE INDEX "KolTagLog_userId_idx" ON "KolTagLog"("userId");

-- CreateIndex
CREATE INDEX "KolTagLog_enterpriseId_idx" ON "KolTagLog"("enterpriseId");

-- CreateIndex
CREATE INDEX "KolTagLog_tagName_idx" ON "KolTagLog"("tagName");
