-- CreateTable
CREATE TABLE "BatchTaskRecord" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "taskType" "TaskType" NOT NULL,
    "rawInput" JSONB,
    "result" JSONB,
    "summary" JSONB,
    "tasks" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BatchTaskRecord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BatchTaskRecord_userId_idx" ON "BatchTaskRecord"("userId");

-- CreateIndex
CREATE INDEX "BatchTaskRecord_taskType_idx" ON "BatchTaskRecord"("taskType");

-- CreateIndex
CREATE INDEX "BatchTaskRecord_createdAt_idx" ON "BatchTaskRecord"("createdAt");
