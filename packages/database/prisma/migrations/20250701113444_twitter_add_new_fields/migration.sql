/*
  Warnings:

  - Added the required column `name` to the `TwitterUser` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "TwitterUser" ADD COLUMN     "accountCreatedAt" TIMESTAMP(3),
ADD COLUMN     "description" TEXT,
ADD COLUMN     "followersCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "friendsCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "isBlueVerified" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isProtected" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "listedCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "location" TEXT,
ADD COLUMN     "mediaCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "profileBannerUrl" TEXT,
ADD COLUMN     "profileImageUrl" TEXT,
ADD COLUMN     "statusesCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "url" TEXT;
