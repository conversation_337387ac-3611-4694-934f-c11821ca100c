-- CreateEnum
CREATE TYPE "ExcludeListRecordStatus" AS ENUM ('PROCESSING', 'SUCCESS', 'FAILED');

-- CreateTable
CREATE TABLE "ExcludeListRecord" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL DEFAULT '',
    "enterpriseId" TEXT NOT NULL DEFAULT '',
    "rawContent" TEXT NOT NULL DEFAULT '',
    "result" JSONB,
    "status" "ExcludeListRecordStatus" NOT NULL DEFAULT 'PROCESSING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ExcludeListRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExcludeList" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL DEFAULT '',
    "platform" "KolPlatform" NOT NULL,
    "platformAccount" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ExcludeList_pkey" PRIMARY KEY ("id")
);
