/*
  Warnings:

  - You are about to drop the `KolTagLog` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "KolTagLog";

-- CreateTable
CREATE TABLE "KolRecordLog" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "tagId" TEXT,
    "recordId" TEXT,
    "tagName" TEXT,
    "note" TEXT,
    "type" "TagAndNoteOpType" NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "KolRecordLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "KolRecordLog_kolId_idx" ON "KolRecordLog"("kolId");

-- CreateIndex
CREATE INDEX "KolRecordLog_userId_idx" ON "KolRecordLog"("userId");

-- CreateIndex
CREATE INDEX "KolRecordLog_enterpriseId_idx" ON "KolRecordLog"("enterpriseId");

-- CreateIndex
CREATE INDEX "KolRecordLog_tagName_idx" ON "KolRecordLog"("tagName");
