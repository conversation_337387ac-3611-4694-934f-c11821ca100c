/*
  Warnings:

  - A unique constraint covering the columns `[date,endpoint,status,responseCode]` on the table `RapidApiDailyStat` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "RapidApiDailyStat_date_endpoint_status_key";

-- AlterTable
ALTER TABLE "RapidApiDailyStat" ADD COLUMN     "responseCode" INTEGER NOT NULL DEFAULT 1000;

-- CreateIndex
CREATE INDEX "RapidApiDailyStat_responseCode_idx" ON "RapidApiDailyStat"("responseCode");

-- CreateIndex
CREATE UNIQUE INDEX "RapidApiDailyStat_date_endpoint_status_responseCode_key" ON "RapidApiDailyStat"("date", "endpoint", "status", "responseCode");
