-- CreateTable
CREATE TABLE "UserTrackStrategyTemplate" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT,
    "totalRuns" INTEGER NOT NULL,
    "intervalHours" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserTrackStrategyTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserTrackStrategyTemplate_userId_key" ON "UserTrackStrategyTemplate"("userId");

-- CreateIndex
CREATE INDEX "UserTrackStrategyTemplate_userId_idx" ON "UserTrackStrategyTemplate"("userId");
