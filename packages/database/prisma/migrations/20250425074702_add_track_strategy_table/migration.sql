-- AlterTable
ALTER TABLE "PublicationStatisticsSheetData" ADD COLUMN     "easykolTrackStrategyId" TEXT;

-- AlterTable
ALTER TABLE "SimilarChannelTask" ADD COLUMN     "strategyId" TEXT;

-- CreateTable
CREATE TABLE "EasykolTrackStrategy" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT,
    "totalRuns" INTEGER NOT NULL,
    "remainingRuns" INTEGER NOT NULL,
    "intervalHours" INTEGER NOT NULL,
    "nextRunAt" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EasykolTrackStrategy_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "EasykolTrackStrategy_userId_isActive_idx" ON "EasykolTrackStrategy"("userId", "isActive");

-- CreateIndex
CREATE INDEX "EasykolTrackStrategy_isActive_nextRunAt_idx" ON "EasykolTrackStrategy"("isActive", "nextRunAt");

-- CreateIndex
CREATE INDEX "SimilarChannelTask_strategyId_idx" ON "SimilarChannelTask"("strategyId");
