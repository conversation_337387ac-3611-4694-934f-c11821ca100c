-- CreateTable
CREATE TABLE "AudienceAnalysis" (
    "id" TEXT NOT NULL,
    "usernameOrId" TEXT NOT NULL,
    "platform" "KolPlatform" NOT NULL,
    "postId" TEXT NOT NULL DEFAULT '',
    "city" TEXT NOT NULL DEFAULT '',
    "region" TEXT NOT NULL DEFAULT '',
    "location" JSONB,
    "taskId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AudienceAnalysis_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "AudienceAnalysis_usernameOrId_idx" ON "AudienceAnalysis"("usernameOrId");

-- CreateIndex
CREATE INDEX "AudienceAnalysis_platform_idx" ON "AudienceAnalysis"("platform");

-- CreateIndex
CREATE INDEX "AudienceAnalysis_taskId_idx" ON "AudienceAnalysis"("taskId");

-- CreateIndex
CREATE INDEX "SimilarChannelTask_projectId_idx" ON "SimilarChannelTask"("projectId");

-- CreateIndex
CREATE INDEX "SimilarChannelTask_createdBy_idx" ON "SimilarChannelTask"("createdBy");

-- CreateIndex
CREATE INDEX "SimilarChannelTask_status_idx" ON "SimilarChannelTask"("status");

-- CreateIndex
CREATE INDEX "SimilarChannelTask_type_idx" ON "SimilarChannelTask"("type");
