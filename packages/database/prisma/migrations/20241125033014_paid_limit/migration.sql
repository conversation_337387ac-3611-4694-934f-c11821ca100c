-- CreateEnum
CREATE TYPE "QuotaType" AS ENUM ('similar_search', 'ai_search', 'card_query', 'reset', 'admin', 'expire');

-- CreateEnum
CREATE TYPE "MemberType" AS ENUM ('free', 'paid');

-- CreateEnum
CREATE TYPE "MemberStatus" AS ENUM ('active', 'suspended');



-- CreateTable
CREATE TABLE "user_memberships" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "MemberType" NOT NULL DEFAULT 'free',
    "timezone" TEXT NOT NULL DEFAULT 'UTC+8',
    "effectiveAt" TIMESTAMP(3) NOT NULL,
    "expireAt" TIMESTAMP(3) NOT NULL,
    "accountQuota" INTEGER NOT NULL DEFAULT 30,
    "usedQuota" INTEGER NOT NULL DEFAULT 0,
    "status" "MemberStatus" NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_memberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "quota_logs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "membershipId" TEXT NOT NULL,
    "projectId" TEXT,
    "taskId" TEXT,
    "usage" INTEGER NOT NULL,
    "type" "QuotaType" NOT NULL,
    "description" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT NOT NULL,

    CONSTRAINT "quota_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_memberships_userId_key" ON "user_memberships"("userId");

-- CreateIndex
CREATE INDEX "user_memberships_type_idx" ON "user_memberships"("type");

-- CreateIndex
CREATE INDEX "user_memberships_expireAt_idx" ON "user_memberships"("expireAt");

-- CreateIndex
CREATE INDEX "user_memberships_userId_status_idx" ON "user_memberships"("userId", "status");

-- CreateIndex
CREATE INDEX "quota_logs_userId_idx" ON "quota_logs"("userId");

-- CreateIndex
CREATE INDEX "quota_logs_type_idx" ON "quota_logs"("type");

-- CreateIndex
CREATE INDEX "quota_logs_createdAt_idx" ON "quota_logs"("createdAt");

-- CreateIndex
CREATE INDEX "quota_logs_membershipId_createdAt_idx" ON "quota_logs"("membershipId", "createdAt");

-- CreateIndex
CREATE INDEX "quota_logs_userId_createdAt_idx" ON "quota_logs"("userId", "createdAt");
