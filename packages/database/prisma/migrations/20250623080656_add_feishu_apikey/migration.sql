-- CreateEnum
CREATE TYPE "FeishuShortcutApiKeyStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateTable
CREATE TABLE "FeishuShortcutApiKey" (
    "id" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "quota" INTEGER NOT NULL DEFAULT 0,
    "remainingQuota" INTEGER NOT NULL DEFAULT 0,
    "status" "FeishuShortcutApiKeyStatus" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeishuShortcutApiKey_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FeishuShortcutApiKey_apiKey_key" ON "FeishuShortcutApiKey"("apiKey");

-- CreateIndex
CREATE INDEX "FeishuShortcutApiKey_apiKey_idx" ON "FeishuShortcutApiKey"("apiKey");
