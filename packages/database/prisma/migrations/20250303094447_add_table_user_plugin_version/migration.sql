-- CreateTable
CREATE TABLE "UserPluginVersion" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserPluginVersion_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserPluginVersion_userId_key" ON "UserPluginVersion"("userId");

-- CreateIndex
CREATE INDEX "UserPluginVersion_userId_idx" ON "UserPluginVersion"("userId");
