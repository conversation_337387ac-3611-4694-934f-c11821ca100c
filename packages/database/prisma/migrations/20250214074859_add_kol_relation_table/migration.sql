-- CreateEnum
CREATE TYPE "KolRelationType" AS ENUM ('LIKED', 'CONTACTED');

-- CreateTable
CREATE TABLE "KolRelationRecord" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL DEFAULT '',
    "platform" "KolPlatform" NOT NULL,
    "platformAccount" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "enterpriseId" TEXT NOT NULL,
    "type" "KolRelationType" NOT NULL,
    "extra" JSONB,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "KolRelationRecord_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ExcludeList_userId_idx" ON "ExcludeList"("userId");

-- CreateIndex
CREATE INDEX "ExcludeList_enterpriseId_idx" ON "ExcludeList"("enterpriseId");
