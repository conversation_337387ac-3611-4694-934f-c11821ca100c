-- CreateEnum
CREATE TYPE "EmailFollowupThreadStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PAUSED');

-- CreateEnum
CREATE TYPE "EmailPlanStatus" AS ENUM ('PENDING', 'SENDING', 'SENT', 'FAILED', 'CANCELED');

-- AlterTable
ALTER TABLE "EmailTemplate" ADD COLUMN     "bcc" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "cc" TEXT[] DEFAULT ARRAY[]::TEXT[];

-- CreateTable
CREATE TABLE "EmailFollowup" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "emailTemplateId" TEXT NOT NULL,
    "followupsEmails" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailFollowup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailFollowupThread" (
    "id" TEXT NOT NULL,
    "kolId" TEXT NOT NULL,
    "kolEmail" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "userEmail" TEXT NOT NULL,
    "followupId" TEXT NOT NULL,
    "followupEmails" JSONB,
    "threadId" TEXT NOT NULL,
    "status" "EmailFollowupThreadStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailFollowupThread_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailPlan" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "cc" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "bcc" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "threadId" TEXT NOT NULL,
    "status" "EmailPlanStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailPlan_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "EmailFollowup_emailTemplateId_key" ON "EmailFollowup"("emailTemplateId");

-- CreateIndex
CREATE INDEX "EmailFollowup_userId_idx" ON "EmailFollowup"("userId");

-- CreateIndex
CREATE INDEX "EmailFollowup_emailTemplateId_idx" ON "EmailFollowup"("emailTemplateId");

-- CreateIndex
CREATE INDEX "EmailFollowupThread_followupId_idx" ON "EmailFollowupThread"("followupId");

-- CreateIndex
CREATE INDEX "EmailFollowupThread_kolId_idx" ON "EmailFollowupThread"("kolId");

-- CreateIndex
CREATE INDEX "EmailFollowupThread_kolEmail_idx" ON "EmailFollowupThread"("kolEmail");

-- CreateIndex
CREATE INDEX "EmailFollowupThread_threadId_idx" ON "EmailFollowupThread"("threadId");

-- CreateIndex
CREATE INDEX "EmailPlan_userId_idx" ON "EmailPlan"("userId");

-- CreateIndex
CREATE INDEX "EmailPlan_threadId_idx" ON "EmailPlan"("threadId");
