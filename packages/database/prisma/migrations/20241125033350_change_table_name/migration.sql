/*
  Warnings:

  - You are about to drop the `quota_logs` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `user_memberships` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "quota_logs";

-- DropTable
DROP TABLE "user_memberships";

-- CreateTable
CREATE TABLE "UserMemberships" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "MemberType" NOT NULL DEFAULT 'free',
    "timezone" TEXT NOT NULL DEFAULT 'UTC+8',
    "effectiveAt" TIMESTAMP(3) NOT NULL,
    "expireAt" TIMESTAMP(3) NOT NULL,
    "accountQuota" INTEGER NOT NULL DEFAULT 30,
    "usedQuota" INTEGER NOT NULL DEFAULT 0,
    "status" "MemberStatus" NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserMemberships_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QuotaLogs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "membershipId" TEXT NOT NULL,
    "projectId" TEXT,
    "taskId" TEXT,
    "usage" INTEGER NOT NULL,
    "type" "QuotaType" NOT NULL,
    "description" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT NOT NULL,

    CONSTRAINT "QuotaLogs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserMemberships_userId_key" ON "UserMemberships"("userId");

-- CreateIndex
CREATE INDEX "UserMemberships_type_idx" ON "UserMemberships"("type");

-- CreateIndex
CREATE INDEX "UserMemberships_expireAt_idx" ON "UserMemberships"("expireAt");

-- CreateIndex
CREATE INDEX "UserMemberships_userId_status_idx" ON "UserMemberships"("userId", "status");

-- CreateIndex
CREATE INDEX "QuotaLogs_userId_idx" ON "QuotaLogs"("userId");

-- CreateIndex
CREATE INDEX "QuotaLogs_type_idx" ON "QuotaLogs"("type");

-- CreateIndex
CREATE INDEX "QuotaLogs_createdAt_idx" ON "QuotaLogs"("createdAt");

-- CreateIndex
CREATE INDEX "QuotaLogs_membershipId_createdAt_idx" ON "QuotaLogs"("membershipId", "createdAt");

-- CreateIndex
CREATE INDEX "QuotaLogs_userId_createdAt_idx" ON "QuotaLogs"("userId", "createdAt");
