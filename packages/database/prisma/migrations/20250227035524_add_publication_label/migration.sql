-- AlterTable
ALTER TABLE "PublicationStatisticsSheetData" ADD COLUMN     "postThumbnailUrl" TEXT;

-- CreateTable
CREATE TABLE "Tag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT '#EAD094',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Tag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PublicationTag" (
    "id" TEXT NOT NULL,
    "publicationId" TEXT NOT NULL,
    "tagId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PublicationTag_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Tag_name_key" ON "Tag"("name");

-- CreateIndex
CREATE INDEX "PublicationTag_publicationId_idx" ON "PublicationTag"("publicationId");

-- CreateIndex
CREATE INDEX "PublicationTag_tagId_idx" ON "PublicationTag"("tagId");

-- CreateIndex
CREATE UNIQUE INDEX "PublicationTag_publicationId_tagId_key" ON "PublicationTag"("publicationId", "tagId");
