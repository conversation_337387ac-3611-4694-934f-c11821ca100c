-- CreateTable
CREATE TABLE "ApiDailyUsage" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "apiCode" TEXT NOT NULL,
    "usageQuota" INTEGER NOT NULL DEFAULT 0,
    "remainingQuota" INTEGER NOT NULL DEFAULT 0,
    "cost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ApiDailyUsage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ApiDailyUsage_date_idx" ON "ApiDailyUsage"("date");

-- CreateIndex
CREATE INDEX "ApiDailyUsage_apiCode_idx" ON "ApiDailyUsage"("apiCode");

-- CreateIndex
CREATE UNIQUE INDEX "ApiDailyUsage_apiCode_date_key" ON "ApiDailyUsage"("apiCode", "date");
