-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SubscriptionStatus" ADD VALUE 'UNPAID';
ALTER TYPE "SubscriptionStatus" ADD VALUE 'PAUSED';

-- DropIndex
DROP INDEX "StripeSubscription_userId_key";

-- CreateIndex
CREATE INDEX "StripeSubscription_priceId_idx" ON "StripeSubscription"("priceId");

-- CreateIndex
CREATE INDEX "StripeSubscription_planType_idx" ON "StripeSubscription"("planType");

-- CreateIndex
CREATE INDEX "StripeSubscription_userId_status_idx" ON "StripeSubscription"("userId", "status");
