# EasyKol 项目数据库 schema 管理

## 背景

本项目使用了 [prisma](https://www.prisma.io/) 作为数据库访问层库，在代码中定义 schema 文件并应用到数据库中，从而使数据库 schema 的版本管理成为可能。

然而在实际情况中，我们开发人员对于 prisma 的协作方式并不清楚，在过去的经历中发生了数次清空数据库的惨案。为了避免再次发生这样的事故，特撰写一份当前达成一致的 prisma 操作流程手册，供后续开发参考。

## 初始化 DB

一般项目会有本地测试库（dev）、测试环境库（beta）、线上环境库（prod）等多个环境的数据库，这些数据库的连接串配置在 `.env` 文件里，执行 prisma 命令前请务必确认当前生效中的 `DATABASSE_URL` 是哪个环境的数据库。

初始化 DB 有两种方式：从 schema 文件到数据表，从数据表到 schema 文件。

### 从 schema 创建数据表：

```shell
npx prisma db push
```

### 从数据表生成 schema：

```shell
npx prisma db pull
```

自己一个人玩的时候可以这么操作，但是在涉及到团队协作，需要将每一次 schema 的变更梳理清晰，知道在哪个数据库当前是哪个版本的 schema，就需要使用 migrate 系列命令。

## migrate 命令管理 schema 版本

在实际开发中，我们一般先根据需求设计表结构，手动修改 schema 文件，再去将变更应用到本地数据库，经过多轮测试再逐步上线 beta 和 prod。因此，操作分为如下几个步骤：

### 1. 修改 schema

```shell
vi prisma/schema.prisma
# 增加一些字段或者数据表
# ...
```

### 2. 生成变更对应的 migration 文件

> 这一步只是为了先生成 sql 确认一下变更范围，让开发者心里有点底，可以跳过。

```shell
npx prisma migrate dev --script-only --name some_migrate
```

### 3. 变更应用到本地

```shell
# 修改 env 文件，确认影响的是本地数据库
vi .env

npx prisma migrate dev
```

### 4. 变更应用到其他环境

```shell
# 修改 env 文件，确认影响的是目标数据库
vi .env

# 确认当前 migrate 的状态，有哪些 migration 会被应用
npx prisma migrate status

# 应用全部
npx prisma migrate deploy
```

# 解决基准线问题

npx prisma migrate resolve --applied 0_init

npx prisma migrate dev --create-only

## 变更流程

1. 修改 schema.prisma
2. 运行 prisma migrate dev
3. Prisma 在影子数据库上测试变更。prisma-shadow
4. 如果测试成功，才会应用到实际数据库

## 去除旧字段，枚举

1.先增加要替换的字段，或者新增字段（最好附上默认值）2.数据库中使用sql更新旧字段的数据，更新枚举类型3.再从prisma.shema中移除旧字段，或者旧枚举类型
