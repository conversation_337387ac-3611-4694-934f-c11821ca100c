{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx", "./code": "./src/code.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/eslint": "^8.56.5", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "react": "^18.2.0", "typescript": "^5.3.3"}}