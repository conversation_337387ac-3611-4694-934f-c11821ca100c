name: scrawler

on:
  workflow_dispatch:
  # push:
  #   paths:
  #     - 'apps/scrawler/**'
  #     # - 'packages/database/**'
  #     - '.github/workflows/scrawler.yaml'

env:
  PROJECT_NAME: talent-marketing-scrawler-worker

jobs:

  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          always-auth: true
          registry-url: https://npm.midway.run
          scope: '@ruguoapp'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        id: pnpm-install
        with:
          version: 9.7.1
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --filter=worker...
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_AUTH_TOKEN}}

      - name: Compile
        run: pnpm run build --filter=worker...
        env:
          NODE_ENV: production

      - name: Compile Worker
        run: cd apps/scrawler && pnpm run build:worker
        env:
          NODE_ENV: production

      - name: PNPM Deploy
        run: pnpm --filter=worker --prod deploy worker

      - name: Copy dist
        run: cp -r apps/scrawler/dist worker/

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          # list of Docker images to use as base name for tags
          images: |
            registry.jellow.site/iftech/talent-marketing-scrawler-worker
          # generate Docker tags based on the following events/attributes
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Aliyun
        uses: docker/login-action@v2
        with:
          registry: registry.jellow.site
          username: ${{ secrets.ALIYUN_CR_USERNAME }}
          password: ${{ secrets.ALIYUN_CR_PASSWORD }}
      - name: Output
        run: echo ${{ fromJSON(steps.meta.outputs.json).tags[1] }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          push: ${{ github.event_name != 'pull_request' }}
          context: worker
          file: worker/Dockerfile-worker
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            NPM_TOKEN=${{ secrets.NPM_AUTH_TOKEN }}
          # labels: |
          #   org.opencontainers.image.source=${{ github.event.repository.clone_url }}
          #   org.opencontainers.image.created=${{ steps.prep.outputs.created }}
          #   org.opencontainers.image.revision=${{ github.sha }}
      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
      - name: Notify JKDPY
        run: |
          curl ${{ secrets.JKDPY_WEBHOOK_URL }} \
            -u ${{ secrets.JKDPY_WEBHOOK_USERNAME }}:${{ secrets.JKDPY_WEBHOOK_PASSWORD }} \
            -d 'image_name=${{ fromJSON(steps.meta.outputs.json).tags[1] }}&branch_name=${{ github.ref_name }}&service_name=${{ env.PROJECT_NAME }}&commit_message="${{ github.event.head_commit.message }}"'
