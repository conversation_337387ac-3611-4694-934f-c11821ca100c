{"name": "talent-marking", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\" --ignore-unknown", "prepare": "husky"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/typescript-estree": "^8.0.1", "eslint": "^8.57.0", "husky": "^9.1.4", "lint-staged": "^15.2.0", "prettier": "^3.2.5", "turbo": "latest", "typescript": "5.3.3", "vitest": "^2.0.5"}, "packageManager": "pnpm@9.7.1", "engines": {"node": ">=18"}, "dependencies": {"@bull-board/api": "^5.21.4", "@bull-board/fastify": "^5.21.4", "@zilliz/milvus2-sdk-node": "^2.5.6"}}