FROM registry.jellow.site/iftech/node:20-alpine3.18 AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

ENV NEW_RELIC_NO_CONFIG_FILE=true
ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
ENV NEW_RELIC_LOG=stdout

RUN apk update && apk add --no-cache \
    libc6-compat \
    nss \
    chromium \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_EXECUTABLE_PATH="/usr/bin/chromium-browser"

WORKDIR /app
COPY package.json pnpm-lock.yaml /app/

# ----------------------------------
FROM base as build

RUN pnpm install --no-frozen-lockfile --ignore-scripts --force
COPY . /app/
RUN pnpm run build

# ----------------------------------
FROM base as prod
RUN pnpm install --prod --no-frozen-lockfile --ignore-scripts --force
COPY --from=build /app/dist /app/dist

CMD ["node", "dist/main.js"]

# Ensure the container will not run as root user
USER node
