FROM registry.jellow.site/iftech/node:20-alpine3.18 AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# ENV NEW_RELIC_NO_CONFIG_FILE=true
# ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
# ENV NEW_RELIC_LOG=stdout

RUN apk update && apk add --no-cache \
    libc6-compat \
    nss \
    chromium \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# ENV PUPPETEER_EXECUTABLE_PATH="/usr/bin/chromium-browser"

WORKDIR /app

COPY ./node_modules/ ./node_modules/
COPY ./package.json ./package.json
COPY ./dist/ ./dist/

EXPOSE 3000

ENV PORT=3000

ENTRYPOINT ["npm", "run"]
CMD ["start:worker", "--options"]
USER node
