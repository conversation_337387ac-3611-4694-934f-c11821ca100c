下面是包含**如何使用**和**如何实现**的 Markdown 说明：

---

## 日志工具 Logger 使用与实现

### 一、如何使用

#### 1. 引入 Logger

```typescript
import Logger from '@/utils/logger'
```

#### 2. 常用日志方法

- **记录 info 日志**

  ```typescript
  Logger.info('操作成功', { userId: '123' })
  ```

- **记录 debug 日志**

  ```typescript
  Logger.debug('调试信息', { detail: 'xxx' })
  ```

- **记录 warn 日志**

  ```typescript
  Logger.warn('警告信息', { warning: 'xxx' })
  ```

- **记录 error 日志**

  ```typescript
  Logger.error('错误信息', new Error('出错了'), { extra: 'xxx' })
  ```

> 日志会自动带上当前请求的 requestId，无需手动传递。

---

### 二、如何实现

#### 1. 日志上下文与 requestId

- 使用 `AsyncLocalStorage` 维护每个请求的上下文信息。
- 在每个请求进入时，自动生成并存储唯一的 `requestId`，用于日志链路追踪。

#### 2. Fastify 日志配置

- 在 `server.ts` 中配置了多环境日志（开发/生产/测试），并通过 `mixin` 方法将 `requestId` 注入到每条日志中。
- 日志格式为结构化 JSON，便于云日志平台检索。

#### 3. Logger 工具类实现（`apps/scrawler/src/utils/logger.ts`）

```typescript
import { als, fastify } from '@/server'

class Logger {
  static getContext() {
    const store = als.getStore()
    if (!store) {
      return { requestId: 'no-context' }
    }
    return { requestId: store.get('requestId') || 'unknown' }
  }

  static info(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.info({ ...context, msg: message, ...data })
  }

  static debug(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.debug({ ...context, msg: message, ...data })
  }

  static warn(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.warn({ ...context, msg: message, ...data })
  }

  static error(message: string, error?: Error, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.error({
      ...context,
      msg: message,
      error: error?.message,
      stack: error?.stack,
      ...data,
    })
  }
}

export default Logger
```

#### 4. 请求生命周期中的 requestId 注入

- 在 `server.ts` 的 `onRequest` 钩子中，将 requestId 存入 ALS 上下文：

  ```typescript
  fastify.addHook('onRequest', (request, reply, done) => {
    als.run(new Map(), () => {
      const store = als.getStore()
      if (store) {
        store.set('requestId', request.id)
      }
      done()
    })
  })
  ```

---

### 总结

- 直接调用 `Logger` 的静态方法即可记录带有 requestId 的结构化日志。
- 日志上下文自动注入，无需手动传递。
- 便于后续在云日志平台通过 requestId 检索和聚合分析。

---
