# 任务指标收集系统设计方案

## 概述

本方案设计了一个通用的任务指标收集系统，用于在任务执行全流程中收集和存储业务指标数据。系统具有完全通用、灵活扩展、最小侵入的特点，适用于所有任务类型和平台。

## 系统架构

```
Worker Consumer → Queue Service → Specific Service → Database Storage
      ↓               ↓               ↓                ↓
  初始化收集器    传递收集器    记录业务指标    存储到meta字段
```

## 核心组件

### 1. TaskMetricsCollector 通用指标收集器

```typescript
class TaskMetricsCollector {
  private metrics: Record<string, number> = {}
  private startTime: number = Date.now()

  // 基础操作
  set(key: string, value: number): void
  increment(key: string, value: number = 1): void

  // 计时器操作
  startTimer(key: string): void
  endTimer(key: string): void

  // 获取结果
  getMetrics(): Record<string, number>
}
```

**功能特点：**

- 支持任意键值对存储
- 内置计时器功能
- 自动记录总执行时间
- 轻量级设计，性能开销极小

### 2. 任务数据结构扩展

```typescript
interface IEmbeddingTask {
  id: string
  params: any
  type: TaskType
  result?: any
  platform: string
  reason: TaskReason
  createdAt: Date
  createdBy: string
  metricsCollector?: TaskMetricsCollector // 新增字段
}
```

## 实施流程

### 阶段1：Worker Consumer 初始化

**位置：** `src/consumers/worker.consumer.ts`

**职责：** 在每个队列处理器中初始化指标收集器

```typescript
// 示例：YouTube队列处理器
youtubeTaskQueue.process(+YOUTUBE_CONCURRENCY, async (job) => {
  try {
    // 初始化指标收集器
    job.data.metricsCollector = new TaskMetricsCollector()
    job.data.metricsCollector.set('worker_pid', process.pid)
    job.data.metricsCollector.set('queue_type', 'youtube')

    logger.info(`Worker ${process.pid} processing YouTube job ${job.id}`)
    await queueService.processJob(job)
  } catch (err) {
    // 错误处理逻辑
  }
})
```

**记录的公共指标：**

- `worker_pid`: 处理任务的进程ID
- `queue_type`: 队列类型标识
- `processing_start`: 开始处理时间戳

### 阶段2：Queue Service 传递和协调

**位置：** `src/services/worker/queue.service.ts`

**职责：** 在任务分发过程中传递指标收集器，记录任务级别的指标

```typescript
export class QueueService {
  async processJob(job: Bull.Job<IEmbeddingTask>) {
    const { id, type, params, metricsCollector } = job.data

    // 记录任务基础信息
    metricsCollector?.set('task_id_hash', this.hashTaskId(id))
    metricsCollector?.set('task_type_code', this.getTaskTypeCode(type))
    metricsCollector?.startTimer('total_processing')

    try {
      switch (type) {
        case TaskType.SIMILAR:
          return await this.processSimilarJob(job)
        case TaskType.HASH_TAG_BREAK:
          return await this.processHashTagBreakJob(job)
        // ... 其他任务类型
      }
    } finally {
      metricsCollector?.endTimer('total_processing')
    }
  }
}
```

**记录的协调层指标：**

- `task_id_hash`: 任务ID哈希值（隐私保护）
- `task_type_code`: 任务类型编码
- `total_processing_duration_ms`: 总处理耗时

### 阶段3：Specific Service 业务指标记录

**位置：** 各个具体的服务文件（如 `YoutubeService`, `TiktokService` 等）

**职责：** 在业务逻辑执行过程中记录详细的业务指标

#### YouTube服务示例

```typescript
async youtubeProcessSimilarJob(
  channelId: string,
  params: SimilarTaskParams,
  taskId: string,
  metricsCollector?: TaskMetricsCollector
): Promise<UserSimilarityOutput[]> {

  // 1. 源频道获取阶段
  metricsCollector?.startTimer('fetch_source_channel')
  const sourceChannel = await YoutubeApi.getInstance().getChannelWithVideos(channelId)
  metricsCollector?.endTimer('fetch_source_channel')
  metricsCollector?.set('source_videos_count', sourceChannel?.videos?.length || 0)
  metricsCollector?.set('source_subscribers', sourceChannel.subscriberCount || 0)

  // 2. 相关频道获取阶段
  metricsCollector?.startTimer('fetch_related_channels')
  const relatedChannels = await this.getRelatedChannelsByRapidApi(sourceChannel, params.regions)
  metricsCollector?.endTimer('fetch_related_channels')
  metricsCollector?.set('related_channels_found', relatedChannels.length)
  metricsCollector?.increment('rapid_api_calls', relatedChannels.length)

  // 3. 向量处理阶段
  metricsCollector?.startTimer('vector_processing')
  const validDenseVector = await this.getValidDenseVector(sourceChannel, params.ytbVideoIds)
  metricsCollector?.endTimer('vector_processing')
  metricsCollector?.set('vectors_generated', validDenseVector ? 1 : 0)

  // 4. AI相似度搜索阶段
  metricsCollector?.startTimer('ai_similarity_search')
  const searchResults = await advancedFilterSearch([validDenseVector], filter)
  metricsCollector?.endTimer('ai_similarity_search')
  metricsCollector?.set('ai_candidates_found', searchResults.length)

  // 5. 过滤阶段
  metricsCollector?.set('min_subscribers_filter', params.minSubscribers || 0)
  metricsCollector?.set('max_subscribers_filter', params.maxSubscribers || 0)
  metricsCollector?.set('regions_filter_count', params.regions?.length || 0)

  const filteredResults = searchResults.filter(/* 过滤逻辑 */)
  metricsCollector?.set('final_candidates_count', filteredResults.length)
  metricsCollector?.set('filter_reduction_rate',
    Math.round((1 - filteredResults.length / searchResults.length) * 100))

  return filteredResults
}
```

#### TikTok服务示例

```typescript
async processSimilarJobByEmbedding(
  params: SimilarTaskParams,
  taskId: string,
  metricsCollector?: TaskMetricsCollector
): Promise<TikTokProcessResult> {

  // 爬取用户视频阶段
  metricsCollector?.startTimer('crawl_user_videos')
  const userVideos = await this.crawlUserVideos(params.source)
  metricsCollector?.endTimer('crawl_user_videos')
  metricsCollector?.set('crawled_videos_count', userVideos.length)

  // 标签提取阶段
  metricsCollector?.startTimer('extract_hashtags')
  const hashtags = await this.extractHashtags(userVideos)
  metricsCollector?.endTimer('extract_hashtags')
  metricsCollector?.set('unique_hashtags_count', hashtags.length)

  // 联合搜索阶段
  metricsCollector?.startTimer('union_search')
  const searchResults = await this.unionSearchByHashtags(hashtags)
  metricsCollector?.endTimer('union_search')
  metricsCollector?.set('union_search_results', searchResults.length)

  // 去重和过滤
  const uniqueUsers = this.removeDuplicates(searchResults)
  metricsCollector?.set('duplicate_users_removed', searchResults.length - uniqueUsers.length)
  metricsCollector?.set('final_unique_users', uniqueUsers.length)

  return {
    uniqueIds: uniqueUsers,
    hashtags: hashtags,
    authorCount: uniqueUsers.length
  }
}
```

### 阶段4：Database Storage 指标存储

**位置：** `src/services/worker/queue.service.ts` 的 `handleComplete` 方法

**职责：** 将收集的指标存储到数据库的 `meta` 字段

```typescript
async handleComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, type, result, metricsCollector } = job.data

  try {
    // 获取最终指标数据
    const finalMetrics = metricsCollector?.getMetrics() || {}

    // 添加完成时的统计信息
    finalMetrics.completion_timestamp = Date.now()
    finalMetrics.result_size = JSON.stringify(result).length

    // 更新任务记录
    await prisma.similarChannelTask.update({
      where: { id },
      data: {
        result: result,
        status: SimilarChannelTaskStatus.RESULT_READY,
        meta: finalMetrics  // 存储所有收集的指标
      },
    })

    logger.info(`Task ${id} completed with ${Object.keys(finalMetrics).length} metrics`)

  } catch (error) {
    logger.error(`Failed to store metrics for task ${id}:`, error)
  }
}
```

## 指标分类和命名规范

### 公共指标（所有任务都有）

- `worker_pid`: 处理进程ID
- `total_processing_duration_ms`: 总处理时间
- `completion_timestamp`: 完成时间戳
- `result_size`: 结果数据大小

### 业务流程指标

- `{阶段名}_duration_ms`: 各阶段耗时
- `{数据类型}_count`: 各类数据计数
- `{操作名}_success_rate`: 成功率统计

### 平台特定指标

#### YouTube

- `source_videos_count`: 源频道视频数
- `related_channels_found`: 找到的相关频道数
- `rapid_api_calls`: RapidAPI调用次数
- `ai_candidates_found`: AI找到的候选数

#### TikTok

- `crawled_videos_count`: 爬取的视频数
- `unique_hashtags_count`: 唯一标签数
- `duplicate_users_removed`: 去重用户数
- `union_search_results`: 联合搜索结果数

#### Instagram

- `posts_analyzed`: 分析的帖子数
- `usernames_extracted`: 提取的用户名数
- `engagement_rate_avg`: 平均互动率

### 任务类型特定指标

#### SIMILAR任务

- `ai_similarity_search_duration_ms`: AI相似度搜索耗时
- `filter_reduction_rate`: 过滤减少率
- `final_candidates_count`: 最终候选人数

#### HASH_TAG_BREAK任务

- `hashtags_processed`: 处理的标签数
- `posts_per_hashtag_avg`: 每个标签平均帖子数
- `users_per_hashtag_avg`: 每个标签平均用户数

## 使用指南

### 1. 在新服务中添加指标收集

```typescript
// 在任何服务方法中添加指标收集
async someBusinessMethod(params: any, metricsCollector?: TaskMetricsCollector) {
  // 开始计时
  metricsCollector?.startTimer('business_operation')

  try {
    // 业务逻辑
    const data = await this.fetchData()
    metricsCollector?.set('fetched_records', data.length)

    const processed = await this.processData(data)
    metricsCollector?.increment('processing_errors', processed.errors)

    return processed.results
  } finally {
    // 结束计时
    metricsCollector?.endTimer('business_operation')
  }
}
```

### 2. 查询和分析指标数据

```sql
-- 查看某个任务的详细指标
SELECT meta FROM similarChannelTask WHERE id = 'task_id';

-- 分析YouTube任务的平均处理时间
SELECT
  AVG(CAST(meta->>'total_processing_duration_ms' AS INTEGER)) as avg_duration,
  AVG(CAST(meta->>'ai_candidates_found' AS INTEGER)) as avg_candidates
FROM similarChannelTask
WHERE type = 'SIMILAR' AND params->>'platform' = 'YOUTUBE';

-- 查看TikTok任务的去重效率
SELECT
  taskId,
  CAST(meta->>'union_search_results' AS INTEGER) as before_dedup,
  CAST(meta->>'final_unique_users' AS INTEGER) as after_dedup,
  CAST(meta->>'duplicate_users_removed' AS INTEGER) as removed
FROM similarChannelTask
WHERE type = 'SIMILAR' AND params->>'platform' = 'TIKTOK';
```

## 系统优势

### 1. 完全通用

- 不预定义任何具体指标结构
- 支持任意键值对存储
- 适用于所有任务类型和平台

### 2. 灵活扩展

- 任何服务都可以自定义业务指标
- 支持动态添加新的指标类型
- 向后兼容现有代码

### 3. 最小侵入

- 只需要传递一个可选参数
- 现有代码不传递也能正常工作
- 渐进式改造，风险可控

### 4. 高性能

- 轻量级设计，内存占用极小
- 异步存储，不影响任务执行性能
- 自动清理临时数据

## 实施计划

### Phase 1: 基础设施搭建

- [ ] 创建 `TaskMetricsCollector` 类
- [ ] 修改 `IEmbeddingTask` 接口
- [ ] 更新 Worker Consumer 初始化逻辑

### Phase 2: 核心服务集成

- [ ] 集成 QueueService 指标传递
- [ ] 更新 handleComplete 存储逻辑
- [ ] 测试基础指标收集功能

### Phase 3: 业务服务改造

- [ ] YouTube服务指标集成
- [ ] TikTok服务指标集成
- [ ] Instagram服务指标集成
- [ ] 其他服务逐步改造

### Phase 4: 监控和优化

- [ ] 添加指标查询工具
- [ ] 建立监控面板
- [ ] 性能优化和问题修复

## 注意事项

1. **隐私保护**: 避免在指标中存储敏感信息，使用哈希或编码
2. **性能考虑**: 指标收集不应影响主业务逻辑性能
3. **存储限制**: 注意数据库字段大小限制，避免指标过多
4. **错误处理**: 指标收集失败不应影响任务正常执行
5. **命名规范**: 统一指标命名规范，便于后续分析和查询

## 总结

本方案提供了一个完整的、通用的任务指标收集系统，能够满足不同任务类型的业务指标收集需求。通过最小化的代码侵入和最大化的灵活性，为业务分析和系统优化提供了强有力的数据支撑。
