import { redis } from '@/infras/redis'
import { scheduler } from 'timers/promises'

export async function acquire(key: string, seconds: number = 10): Promise<boolean> {
  const result = await redis.set(
    key,
    '1',
    'NX', // Only set if key doesn't exist
    'EX',
    seconds,
  )
  return !!result
}

export async function release(key: string): Promise<void> {
  await redis.del(key)
}

export async function waitFor(key: string): Promise<void> {
  while (true) {
    const exists = await redis.get(key)
    if (!exists) {
      return
    }
    console.log(`waiting for ${key}`)
    await scheduler.wait(1000)
  }
}
