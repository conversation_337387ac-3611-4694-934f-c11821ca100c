export class TaskMetricsCollector {
  private metrics: Record<string, any> = {}
  private startTime: number = Date.now()

  constructor() {
    this.metrics.start_timestamp = this.startTime
  }

  set(key: string, value: any): void {
    this.metrics[key] = value
  }

  increment(key: string, value: number = 1): void {
    this.metrics[key] = (this.metrics[key] || 0) + value
  }

  // set batch metrics
  setBatch(metrics: Record<string, any>): void {
    Object.entries(metrics).forEach(([key, value]) => {
      this.metrics[key] = value
    })
  }

  // start timer
  startTimer(key: string): void {
    this.metrics[`${key}_start`] = Date.now()
  }

  endTimer(key: string): void {
    const startKey = `${key}_start`
    if (this.metrics[startKey]) {
      this.metrics[`${key}_duration`] = (Date.now() - this.metrics[startKey]) / 1000 // 转换为秒
      delete this.metrics[startKey] // 清理临时数据
    }
  }

  // phase wrapper - auto statistics phase duration and result
  async withPhase<T>(phaseName: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(phaseName)
    try {
      return await fn()
    } finally {
      this.endTimer(phaseName)
    }
  }

  // 获取结果
  getMetrics(): Record<string, number> {
    return {
      ...this.metrics,
      total_duration: (Date.now() - this.startTime) / 1000, // 转换为秒
      completion_timestamp: Date.now(),
    }
  }
}
