/**
 * 尝试修复并解析可能格式不正确的JSON字符串
 * @param jsonString 原始JSON字符串
 * @returns 解析后的JSON对象
 */
function parseJsonRobustly<T>(jsonString: string): T {
  try {
    // 首先尝试直接解析
    return JSON.parse(jsonString) as T
  } catch (error) {
    console.warn('JSON直接解析失败，尝试修复JSON格式问题...')

    try {
      // 尝试修复常见的JSON问题
      // 1. 处理属性名没有引号的情况
      let fixedJson = jsonString.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3')

      // 2. 处理单引号替换为双引号
      fixedJson = fixedJson.replace(/:\s*'([^']*)'/g, ': "$1"')

      // 3. 移除可能的控制字符
      fixedJson = fixedJson.replace(/[\u0000-\u001F\u007F-\u009F]/g, '')

      // 4. 处理尾部的逗号问题(如 {a: 1, b: 2,})
      fixedJson = fixedJson.replace(/,\s*}/g, '}').replace(/,\s*]/g, ']')

      // 5. 处理JSON字符串中未转义的反斜杠（但不影响已转义的）
      fixedJson = fixedJson.replace(/([^\\])\\([^\\"])/g, '$1\\\\$2')

      // 6. 处理字符串中开头的未转义反斜杠
      fixedJson = fixedJson.replace(/"\\([^\\"])/g, '"\\\\$1')

      // 7. 处理字符串结尾的反斜杠
      fixedJson = fixedJson.replace(/\\"/g, '\\\\"')

      console.log('修复后的JSON字符串:', fixedJson.substring(0, 100) + '...')

      return JSON.parse(fixedJson) as T
    } catch (repairError) {
      console.error('JSON修复和解析都失败了:', repairError)

      try {
        // 最后尝试：使用更极端的方法
        console.log('尝试最后的修复方案...')

        // 1. 尝试转义所有反斜杠

        let lastChanceJson = jsonString.replace(/\\/g, '\\\\')

        // 2. 修复双重转义问题
        lastChanceJson = lastChanceJson.replace(/\\\\\\\\/g, '\\\\')

        // 3. 移除所有不可打印字符
        lastChanceJson = lastChanceJson.replace(/[\u0000-\u001F\u007F-\u009F\u2028\u2029]/g, '')

        console.log('最终修复后的JSON字符串:', lastChanceJson.substring(0, 100) + '...')
        return JSON.parse(lastChanceJson) as T
      } catch (lastError) {
        throw new Error(
          `无法解析或修复JSON: ${error instanceof Error ? error.message : String(error)}`,
        )
      }
    }
  }
}

export const JsonParseUtil = {
  parseJsonRobustly,
}
