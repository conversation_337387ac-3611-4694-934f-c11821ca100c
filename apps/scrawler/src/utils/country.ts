import { IgUser } from '@/api/@types/rapidapi/Instagram'
import { TiktokCommentUser } from '@/api/@types/rapidapi/Tiktok'
import { Channel } from '@/api/@types/rapidapi/Youtube'
import {
  RegionAnalysisResult,
  RegionDevelopmentLevel,
  RegionDevelopmentStatistic,
  RegionStatistic,
} from '@/types/audience'

/**
 * 受众总数: Audience Profile:Gender(Male57%/Female43%), Age(under18:4%, 18-25:23%, 25-45:44%, above45:29%);
 * Region: Market Level(T2:68.9%, T1:28.1%, T3:3.0%), Top 5 Countries(BR:12.0%, ID:9.0%, IN:7.2%, RU:6.0%, US:5.4%)
 * @param countryData
 * @returns
 */

// 格式化国家数据的辅助函数
export function formatCountryData(countryData: any): string {
  if (!countryData) {
    return ''
  }
  // 如果是受众分析结果的新格式
  if (countryData.userPortraitResult && countryData.regionAnalysisResult) {
    try {
      const { userPortraitResult, regionAnalysisResult } = countryData

      // 检查对象是否为空或缺少必要属性
      if (
        Object.keys(userPortraitResult).length === 0 &&
        Object.keys(regionAnalysisResult).length === 0
      ) {
        return ''
      }

      // 处理性别数据
      const gender = userPortraitResult.gender
      const genderStr = `Gender(Male${gender.male}/Female${gender.female})`

      // 处理年龄数据
      const age = userPortraitResult.age
      const ageStr = `Age(under18:${age.under18}, 18-25:${age.age18to25}, 25-45:${age.age25to45}, above45:${age.above45})`

      // 处理市场等级数据
      const devStats = regionAnalysisResult.developmentStatistics || []
      const marketLevelStr =
        devStats.length > 0
          ? `Market Level(${devStats
              .map(
                (item: { developmentLevel: string; percentage: string }) =>
                  `${item.developmentLevel}:${item.percentage}`,
              )
              .join(', ')})`
          : ''
      // 处理国家数据
      const stats = regionAnalysisResult.statistics || []
      const top5Countries = stats
        .sort((a: { count: number }, b: { count: number }) => b.count - a.count)
        .slice(0, 5)
        .map((item: { region: string; percentage: string }) => `${item.region}:${item.percentage}`)
        .join(', ')

      const countryStr = stats.length > 0 ? `Top 5 Countries(${top5Countries})` : ''

      // 组合所有数据
      return `${countryStr}|${marketLevelStr}|${genderStr}|${ageStr}`
    } catch (error) {
      console.error('格式化受众分析数据失败:', error)
      return ''
    }
  }

  // 原有的格式化逻辑，处理简单的国家数据
  // 如果是对象格式，转换为数组格式
  if (typeof countryData === 'object' && !Array.isArray(countryData)) {
    const dataArray = Object.entries(countryData).map(([country, count]) => ({
      country,
      count: Number(count),
    }))
    countryData = dataArray
  }
  if (!Array.isArray(countryData) || countryData.length === 0) {
    return ''
  }

  const totalComments = countryData.reduce((sum, item) => sum + item.count, 0)

  const sortedData = [...countryData]
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
    .map((item) => {
      const percentage = ((item.count / totalComments) * 100).toFixed(1)
      return `${item.country}(${percentage}%)`
    })
    .join(' | ')

  return sortedData
}

/**
 * 国家/地区发展程度映射
 * T1: 发达国家
 * T2: 发展中国家
 * T3: 欠发达国家
 */
const countryDevelopmentMap: Record<string, RegionDevelopmentLevel> = {
  // 发达国家 (T1)
  US: 'T1', // 美国
  CA: 'T1', // 加拿大
  GB: 'T1', // 英国
  AU: 'T1', // 澳大利亚
  NZ: 'T1', // 新西兰
  JP: 'T1', // 日本
  KR: 'T1', // 韩国
  SG: 'T1', // 新加坡
  DE: 'T1', // 德国
  FR: 'T1', // 法国
  IT: 'T1', // 意大利
  ES: 'T1', // 西班牙
  NL: 'T1', // 荷兰
  BE: 'T1', // 比利时
  CH: 'T1', // 瑞士
  SE: 'T1', // 瑞典
  NO: 'T1', // 挪威
  DK: 'T1', // 丹麦
  FI: 'T1', // 芬兰
  IE: 'T1', // 爱尔兰
  AT: 'T1', // 奥地利
  LU: 'T1', // 卢森堡
  IS: 'T1', // 冰岛
  IL: 'T1', // 以色列
  PT: 'T1', // 葡萄牙
  GR: 'T1', // 希腊
  EE: 'T1', // 爱沙尼亚
  LV: 'T1', // 拉脱维亚
  LT: 'T1', // 立陶宛
  SI: 'T1', // 斯洛文尼亚
  SK: 'T1', // 斯洛伐克
  CY: 'T1', // 塞浦路斯
  MT: 'T1', // 马耳他
  AD: 'T1', // 安道尔
  MC: 'T1', // 摩纳哥
  SM: 'T1', // 圣马力诺
  LI: 'T1', // 列支敦士登
  HK: 'T1', // 香港（地区）
  MO: 'T1', // 澳门（地区）
  TW: 'T1', // 台湾（地区）

  // 发展中国家 (T2)
  CN: 'T2', // 中国
  BR: 'T2', // 巴西
  RU: 'T2', // 俄罗斯
  IN: 'T2', // 印度
  MX: 'T2', // 墨西哥
  ZA: 'T2', // 南非
  TR: 'T2', // 土耳其
  TH: 'T2', // 泰国
  MY: 'T2', // 马来西亚
  AR: 'T2', // 阿根廷
  CL: 'T2', // 智利
  CO: 'T2', // 哥伦比亚
  PE: 'T2', // 秘鲁
  PH: 'T2', // 菲律宾
  VN: 'T2', // 越南
  ID: 'T2', // 印度尼西亚
  SA: 'T2', // 沙特阿拉伯
  AE: 'T2', // 阿联酋
  PL: 'T2', // 波兰
  CZ: 'T2', // 捷克
  HU: 'T2', // 匈牙利
  RO: 'T2', // 罗马尼亚
  UA: 'T2', // 乌克兰
  KZ: 'T2', // 哈萨克斯坦
  BG: 'T2', // 保加利亚
  HR: 'T2', // 克罗地亚
  RS: 'T2', // 塞尔维亚
  BA: 'T2', // 波黑
  ME: 'T2', // 黑山
  AL: 'T2', // 阿尔巴尼亚
  MK: 'T2', // 北马其顿
  BY: 'T2', // 白俄罗斯
  MD: 'T2', // 摩尔多瓦
  AM: 'T2', // 亚美尼亚
  AZ: 'T2', // 阿塞拜疆
  GE: 'T2', // 格鲁吉亚
  UZ: 'T2', // 乌兹别克斯坦
  TM: 'T2', // 土库曼斯坦
  KG: 'T2', // 吉尔吉斯斯坦
  TJ: 'T2', // 塔吉克斯坦
  MN: 'T2', // 蒙古
  LK: 'T2', // 斯里兰卡
  JO: 'T2', // 约旦
  LB: 'T2', // 黎巴嫩
  IR: 'T2', // 伊朗
  IQ: 'T2', // 伊拉克
  EG: 'T2', // 埃及
  MA: 'T2', // 摩洛哥
  TN: 'T2', // 突尼斯
  DZ: 'T2', // 阿尔及利亚
  LY: 'T2', // 利比亚
  KW: 'T2', // 科威特
  BH: 'T2', // 巴林
  QA: 'T2', // 卡塔尔
  OM: 'T2', // 阿曼
  MU: 'T2', // 毛里求斯
  NA: 'T2', // 纳米比亚
  BW: 'T2', // 博茨瓦纳
  GA: 'T2', // 加蓬
  DO: 'T2', // 多米尼加共和国
  JM: 'T2', // 牙买加
  TT: 'T2', // 特立尼达和多巴哥
  PA: 'T2', // 巴拿马
  CR: 'T2', // 哥斯达黎加
  EC: 'T2', // 厄瓜多尔
  PY: 'T2', // 巴拉圭
  UY: 'T2', // 乌拉圭
  CU: 'T2', // 古巴
  FJ: 'T2', // 斐济

  // 欠发达国家 (T3)
  NG: 'T3', // 尼日利亚
  KE: 'T3', // 肯尼亚
  GH: 'T3', // 加纳
  ET: 'T3', // 埃塞俄比亚
  TZ: 'T3', // 坦桑尼亚
  BD: 'T3', // 孟加拉国
  PK: 'T3', // 巴基斯坦
  MM: 'T3', // 缅甸
  NP: 'T3', // 尼泊尔
  KH: 'T3', // 柬埔寨
  LA: 'T3', // 老挝
  AF: 'T3', // 阿富汗
  YE: 'T3', // 也门
  HT: 'T3', // 海地
  CD: 'T3', // 刚果民主共和国
  SD: 'T3', // 苏丹
  SS: 'T3', // 南苏丹
  SL: 'T3', // 塞拉利昂
  SN: 'T3', // 塞内加尔
  ML: 'T3', // 马里
  BF: 'T3', // 布基纳法索
  MG: 'T3', // 马达加斯加
  MZ: 'T3', // 莫桑比克
  ZW: 'T3', // 津巴布韦
  AO: 'T3', // 安哥拉
  BI: 'T3', // 布隆迪
  BJ: 'T3', // 贝宁
  CF: 'T3', // 中非共和国
  CI: 'T3', // 科特迪瓦
  CM: 'T3', // 喀麦隆
  CG: 'T3', // 刚果共和国
  ER: 'T3', // 厄立特里亚
  GM: 'T3', // 冈比亚
  GN: 'T3', // 几内亚
  GW: 'T3', // 几内亚比绍
  LR: 'T3', // 利比里亚
  LS: 'T3', // 莱索托
  MW: 'T3', // 马拉维
  NE: 'T3', // 尼日尔
  RW: 'T3', // 卢旺达
  SO: 'T3', // 索马里
  TD: 'T3', // 乍得
  TG: 'T3', // 多哥
  UG: 'T3', // 乌干达
  ZM: 'T3', // 赞比亚
  DJ: 'T3', // 吉布提
  KI: 'T3', // 基里巴斯
  SB: 'T3', // 所罗门群岛
  TV: 'T3', // 图瓦卢
  VU: 'T3', // 瓦努阿图
  WS: 'T3', // 萨摩亚
  TL: 'T3', // 东帝汶
  ST: 'T3', // 圣多美和普林西比
  CV: 'T3', // 佛得角
  KM: 'T3', // 科摩罗
  MR: 'T3', // 毛里塔尼亚
}

/**
 * 获取国家/地区发展程度
 */
export function getRegionDevelopmentLevel(region: string): RegionDevelopmentLevel {
  return countryDevelopmentMap[region.toUpperCase()] || 'T3' // 默认为T3
}

/**
 * 统计评论用户的地区分布
 */
export async function statisticsUserRegion(
  commentsUsers:
    | TiktokCommentUser[]
    | IgUser[]
    | Channel[]
    | { username: string; country: string }[],
): Promise<RegionAnalysisResult> {
  const regionStats: Record<string, number> = {}

  let hasRegionUser = 0
  commentsUsers.forEach((user) => {
    let region = ''
    if ('region' in user && typeof user.region === 'string') {
      region = user.region
    } else if ('country' in user && typeof user.country === 'string') {
      region = user.country
    }

    if (region) {
      regionStats[region] = (regionStats[region] || 0) + 1
      hasRegionUser++
    }
  })

  const total = hasRegionUser

  const statistics: RegionStatistic[] = Object.entries(regionStats)
    .map(([region, count]) => {
      return {
        region,
        count,
        percentage: `${((count / total) * 100).toFixed(2)}%`,
        developmentLevel: getRegionDevelopmentLevel(region),
      }
    })
    .sort((a, b) => b.count - a.count)

  const developmentLevelStats: Record<RegionDevelopmentLevel, number> = {
    T1: 0,
    T2: 0,
    T3: 0,
  }

  statistics.forEach((stat) => {
    developmentLevelStats[stat.developmentLevel] += stat.count
  })

  const developmentStatistics: RegionDevelopmentStatistic[] = Object.entries(developmentLevelStats)
    .map(([level, count]) => ({
      developmentLevel: level as RegionDevelopmentLevel,
      count,
      percentage: `${((count / total) * 100).toFixed(2)}%`,
    }))
    .sort((a, b) => b.count - a.count)

  return {
    total,
    statistics,
    developmentStatistics,
  }
}
