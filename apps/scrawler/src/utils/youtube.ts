export const parseSubscriberCount = (text: string): number => {
  if (!text || !text.endsWith(' subscribers')) {
    console.log(`${text} is not a valid subscriber count`)
    return 0
  }
  const numberText = text.split(' ')[0]
  const lastChar = numberText.slice(-1).toUpperCase()
  const numberPart = parseFloat(numberText)
  if (isNaN(numberPart)) {
    return 0
  }
  if (lastChar === 'K' || lastChar === 'M') {
    const baseNumber = parseFloat(numberText.slice(0, -1))
    if (isNaN(baseNumber)) {
      return 0
    }
    switch (lastChar) {
      case 'K':
        return baseNumber * 1_000
      case 'M':
        return baseNumber * 1_000_000
    }
  }
  return numberPart
}

export const parseShortedNumber = (text: string): number => {
  const numberText = text.split(' ')[0]
  const lastChar = numberText.slice(-1).toUpperCase()
  const numberPart = parseFloat(numberText)
  if (lastChar === 'K' || lastChar === 'M') {
    const baseNumber = parseFloat(numberText.slice(0, -1))
    if (isNaN(baseNumber)) {
      return 0
    }
    switch (lastChar) {
      case 'K':
        return baseNumber * 1_000
      case 'M':
        return baseNumber * 1_000_000
    }
  }
  return numberPart
}
