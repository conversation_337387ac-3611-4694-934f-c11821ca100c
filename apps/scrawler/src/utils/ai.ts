import { ChatCompletionContentPart } from 'openai/resources/index.mjs'

async function text(content: string): Promise<ChatCompletionContentPart> {
  return {
    type: 'text',
    text: content,
  }
}

async function fetchImageAsBase64(
  url: string,
  retries = 3,
): Promise<{ data: string; mimeType: string }> {
  let lastError: Error | null = null

  for (let i = 0; i < retries; i++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 20_000) // 20秒超时

      const response = await fetch(url, {
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`获取图片失败: ${response.status} ${response.statusText}`)
      }

      const buffer = await response.arrayBuffer()
      const base64 = Buffer.from(buffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'

      return {
        data: base64,
        mimeType,
      }
    } catch (error) {
      lastError = error as Error

      if (i === retries - 1) {
        console.error(`处理图片 ${url} 最终失败，已尝试 ${retries} 次`)
        throw lastError
      }

      // 指数退避策略
      const delay = Math.min(1000 * Math.pow(2, i), 5000)
      console.warn(`处理图片 ${url} 失败，将在 ${delay}ms 后重试 (${i + 1}/${retries})`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }

  throw lastError || new Error('未知错误')
}

async function image(
  url: string,
  detail: 'low' | 'high' | 'auto' = 'auto',
  useBase64: boolean = false,
): Promise<ChatCompletionContentPart> {
  if (!url || url.trim() === '') {
    console.warn('Empty image URL detected')
    return {
      type: 'text',
      text: '[图片无法加载]',
    }
  }

  if (useBase64) {
    return new Promise(async (resolve, reject) => {
      try {
        const { data, mimeType } = await fetchImageAsBase64(url)
        resolve({
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${data}`,
            detail: detail,
          },
        })
      } catch (error) {
        console.warn('Failed to convert image to base64:', error)
        // 转换失败时尝试直接使用 URL
        resolve({
          type: 'image_url',
          image_url: {
            url: url,
            detail: detail,
          },
        })
      }
    })
  }

  // 默认直接使用 URL
  try {
    return {
      type: 'image_url',
      image_url: {
        url: url,
        detail: detail,
      },
    }
  } catch (error) {
    console.warn('Invalid URL format:', url, error)
    return {
      type: 'text',
      text: '[图片URL格式无效]',
    }
  }
}

export const aiUtils = {
  text,
  image,
}
