import { dynamicGet } from '@/infras/remembrall'
import { KolPlatform } from '@repo/database'

/**************
 * YOUTUBE
 *************/
export const YoutubeAllowList = ['youtube.com/@', 'youtube.com/c/', 'youtube.com/channel/']

export const isYoutube = (url: string): boolean => {
  return YoutubeAllowList.some((youtube) => url.toLowerCase().includes(youtube))
}

export const isYoutubeChannelIdUrl = (url: string): boolean => {
  return url.includes('youtube.com/channel/')
}

export const getYoutubeChannelId = (url: string): string | null => {
  if (!isYoutubeChannelIdUrl(url)) {
    return null
  }
  return getPiece(url, 2)
}

export const isYoutubeChannelHandle = (url: string): boolean => {
  return url.toLowerCase().includes('youtube.com/@')
}

export const isYoutubeChandle = (url: string): boolean => {
  return url.toLowerCase().includes('youtube.com/c/')
}

export const getYoutubeChannelHandle = (url: string): string | null => {
  if (isYoutubeChannelHandle(url)) {
    return getPiece(url, 1)
  } else if (isYoutubeChandle(url)) {
    return getPiece(url, 2)
  } else {
    return null
  }
}

export const isYoutubeVideo = (url: string): boolean => {
  return (
    url.toLowerCase().includes('youtube.com/watch?v=') || url.toLowerCase().includes('youtu.be/')
  )
}

export const getYoutubeVideoId = (url: string): string | null => {
  if (!isYoutubeVideo(url)) {
    return null
  }
  if (url.toLowerCase().includes('youtu.be/')) {
    return getPiece(url, 1) ?? null
  }
  return getParams(url).get('v') ?? null
}

export const isYoutubeShorts = (url: string): boolean => {
  return url.toLowerCase().includes('youtube.com/shorts/')
}

export const getYoutubeShortsId = (url: string) => {
  if (!isYoutubeShorts(url)) {
    return null
  }
  return getPiece(url, 2)
}

/**************
 * INSTAGRAM
 *************/

export const isInstagram = (url: string): boolean => {
  return url.toLowerCase().includes('instagram.com')
}

export const isInstagramUsername = (url: string): boolean => {
  return url.includes('instagram.com/')
}

export const getInstagramUsername = (url: string): string | null => {
  if (!isInstagramUsername(url)) {
    return null
  }
  return getPiece(url, 1)
}

export const isInstagramPost = (url: string): boolean => {
  return url.toLowerCase().includes('instagram.com/p/')
}

export const getInstagramPostId = (url: string): string | null => {
  if (!isInstagramPost(url)) {
    return null
  }
  return getPiece(url, 2)
}

export const isInstagramReel = (url: string): boolean => {
  return url.toLowerCase().includes('instagram.com/reel/')
}

export const getInstagramReelId = (url: string): string | null => {
  if (!isInstagramReel(url)) {
    return null
  }
  return getPiece(url, 2)
}

/**************
 * TIKTOK
 *************/
export const isTiktok = (url: string): boolean => {
  return url.toLowerCase().includes('tiktok.com')
}

export const isTiktokUniqueId = (url: string): boolean => {
  return (
    url.toLowerCase().includes('tiktok.com/') &&
    !url.toLowerCase().includes('/t/') &&
    !url.toLowerCase().includes('/post/')
  )
}

export const getTiktokUniqueId = (url: string): string | null => {
  if (!isTiktokUniqueId(url)) {
    return null
  }
  let id = getPiece(url, 1)
  while (id?.startsWith('@')) {
    id = id.substring(1)
  }
  return id
}

export const isTiktokVideo = (url: string): boolean => {
  return (
    url.toLowerCase().includes('tiktok.com') &&
    (url.toLowerCase().includes('/video/') ||
      url.toLowerCase().includes('/t/') ||
      url.toLowerCase().includes('/post/'))
  )
}

export const getTiktokVideoid = (url: string): string | null => {
  if (!isTiktokVideo(url)) {
    return null
  }
  if (url.toLowerCase().includes('/video/')) {
    return getPiece(url, 3)
  }
  return getPiece(url, 2)
}

/**************
 * EXTERNAL
 *************/
export const isSnapchat = (url: string): boolean => {
  return url.toLowerCase().includes('snapchat.com')
}

export const isLinktree = (url: string): boolean => {
  return url.toLowerCase().includes('linktr.ee')
}

export const isWhatsApp = (url: string): boolean => {
  const domainResult =
    url.toLowerCase().includes('whatsapp.com') ||
    url.toLowerCase().includes('wa.me') ||
    url.toLowerCase().includes('wa.link')
  // const pathBanList = [
  //   'whatsapp.com/stayconnected',
  //   'whatsapp.com/privacy',
  //   'whatsapp.com/download',
  //   'whatsapp.com/legal',
  //   'whatsapp.com/join',
  //   'whatsapp.com/security',
  //   'whatsapp.com/about',
  //   'whatsapp.com/contact',
  //   'whatsapp.com/community',
  //   'whatsapp.com/expressyourself',
  //   'faq.whatsapp.com/',
  //   'whatsapp.com/android',
  //   'whatsapp.com/sitemap',
  //   'business.whatsapp.com/'
  // ]
  if (url.toLowerCase().includes('whatsapp.com')) {
    return !/\d/.test(url)
  }
  return domainResult
}

export const isHomepage = async (url: string): Promise<boolean> => {
  const homepageList = await dynamicGet('external_link_allow_list', 60)
  if (homepageList instanceof Array && homepageList.length)
    return homepageList.some((i) => url.toLowerCase().includes(i))
  return false
}

export const getPiece = (url: string, idx: number): string | null => {
  const pieces = splitUrl(url)
  if (pieces.length < idx + 1) {
    return null
  }
  return pieces[idx]
}

const splitUrl = (url: string): string[] => {
  const urlWithoutParams = url.trim().split('?')[0]
  let urlWithoutProtocol = urlWithoutParams
  if (urlWithoutProtocol.startsWith('http://')) {
    urlWithoutProtocol = urlWithoutProtocol.replace('http://', '')
  } else if (urlWithoutProtocol.startsWith('https://')) {
    urlWithoutProtocol = urlWithoutProtocol.replace('https://', '')
  }
  return urlWithoutProtocol.split('/')
}

const getParams = (url: string): Map<string, string> => {
  const result = new Map()
  if (!url.includes('?')) {
    return result
  }
  const params = url.split('?')[1].split('#')[0]
  const kvs = params.split('&')
  kvs.forEach((kv) => {
    if (!kv.includes('=')) {
      return
    }
    const pair = kv.split('=')
    result.set(pair[0], decodeURIComponent(pair[1]))
  })
  return result
}

export const getKolLink = (
  platform: KolPlatform | undefined,
  uniqueId: string | undefined | null,
): string => {
  if (!platform || !uniqueId) {
    return ''
  }
  switch (platform) {
    case 'YOUTUBE':
      return `https://www.youtube.com/channel/${uniqueId}`
    case 'INSTAGRAM':
      return `https://www.instagram.com/${uniqueId}`
    case 'TIKTOK':
      return `https://www.tiktok.com/@${uniqueId}`
    default:
      return ''
  }
}
