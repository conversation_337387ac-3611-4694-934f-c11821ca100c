import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime.js' // 用于处理 'ago' 格式
import utc from 'dayjs/plugin/utc.js' // 如果需要处理 UTC 时间

dayjs.extend(utc)
dayjs.extend(relativeTime)

export function parsePublishedTime(publishedTime: string): string {
  const currentTime = dayjs()

  if (publishedTime.includes('ago')) {
    const parts = publishedTime.split(' ')
    const number = parseInt(parts[0], 10)
    const unit = parts[1].toLowerCase()

    let delta
    switch (true) {
      case unit.startsWith('second'):
        delta = currentTime.subtract(number, 'second')
        break
      case unit.startsWith('minute'):
        delta = currentTime.subtract(number, 'minute')
        break
      case unit.startsWith('hour'):
        delta = currentTime.subtract(number, 'hour')
        break
      case unit.startsWith('day'):
        delta = currentTime.subtract(number, 'day')
        break
      case unit.startsWith('week'):
        delta = currentTime.subtract(number, 'week')
        break
      case unit.startsWith('month'):
        delta = currentTime.subtract(number, 'month')
        break
      case unit.startsWith('year'):
        delta = currentTime.subtract(number, 'year')
        break
      default:
        return dayjs('1970-01-01').toISOString()
    }

    return delta.toISOString() // dayjs 自动处理 ISO 格式
  } else {
    try {
      const publishDate = dayjs(publishedTime)
      return publishDate.toISOString()
    } catch (error) {
      return publishedTime
    }
  }
}

export function getUnixTimestamp(isoDate: string): number {
  try {
    const date = dayjs(isoDate)
    return date.unix() // 转换为 Unix 时间戳
  } catch (error) {
    return 0
  }
}
