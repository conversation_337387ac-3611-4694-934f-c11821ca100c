/**
 * 通用重试工具函数
 * @param fn 需要重试的异步函数
 * @param retries 最大重试次数
 * @param delay 初始延迟时间（毫秒）
 * @param backoff 退避系数（每次失败后延迟时间的倍数）
 * @param onRetry 重试前的回调函数，可用于日志记录
 * @returns 异步函数执行的结果
 */
async function retry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 1000,
  backoff = 2,
  onRetry?: (error: any, retryCount: number, nextDelay: number) => void,
): Promise<T> {
  let lastError: any

  for (let i = 0; i < retries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error

      if (i === retries - 1) {
        console.error(`所有 ${retries} 次重试尝试都失败了`)
        throw lastError
      }

      const nextDelay = delay * Math.pow(backoff, i)

      if (onRetry) {
        onRetry(error, i + 1, nextDelay)
      } else {
        console.warn(`操作失败，将在 ${nextDelay}ms 后进行第 ${i + 1}/${retries - 1} 次重试`, error)
      }

      await new Promise((resolve) => setTimeout(resolve, nextDelay))
    }
  }

  throw lastError
}

/**
 * 增强的重试工具函数，可以处理API返回空值的情况
 * @param fn 需要重试的异步函数
 * @param validator 结果验证函数，返回true表示结果有效，不需要重试；返回false表示结果无效，需要重试
 * @param retries 最大重试次数
 * @param delay 初始延迟时间（毫秒）
 * @param backoff 退避系数（每次失败后延迟时间的倍数）
 * @param onRetry 重试前的回调函数，可用于日志记录
 * @returns 异步函数执行的结果，如果所有重试都失败则返回最后一次的结果
 */
async function retryWithValidator<T>(
  fn: () => Promise<T>,
  validator: (result: T) => boolean,
  retries = 3,
  delay = 1000,
  backoff = 2,
  onRetry?: (result: T | null, error: any | null, retryCount: number, nextDelay: number) => void,
): Promise<T> {
  let lastResult: T | null = null
  let lastError: any = null

  for (let i = 0; i < retries; i++) {
    try {
      const result = await fn()
      lastResult = result

      // 如果结果通过验证，直接返回
      if (validator(result)) {
        return result
      }

      // 结果未通过验证，需要重试
      if (i === retries - 1) {
        console.error(`所有 ${retries} 次重试尝试都返回了无效结果`)
        return result // 返回最后一次的结果，即使是无效的
      }

      const nextDelay = delay * Math.pow(backoff, i)

      if (onRetry) {
        onRetry(result, null, i + 1, nextDelay)
      } else {
        console.warn(`API返回无效结果，将在 ${nextDelay}ms 后进行第 ${i + 1}/${retries - 1} 次重试`)
      }

      await new Promise((resolve) => setTimeout(resolve, nextDelay))
    } catch (error) {
      lastError = error

      if (i === retries - 1) {
        console.error(`所有 ${retries} 次重试尝试都失败了`)
        throw lastError
      }

      const nextDelay = delay * Math.pow(backoff, i)

      if (onRetry) {
        onRetry(null, error, i + 1, nextDelay)
      } else {
        console.warn(`操作失败，将在 ${nextDelay}ms 后进行第 ${i + 1}/${retries - 1} 次重试`, error)
      }

      await new Promise((resolve) => setTimeout(resolve, nextDelay))
    }
  }

  // 这个应该永远不会到达，但为了TypeScript类型检查添加
  if (lastResult !== null) {
    return lastResult
  }
  throw lastError
}

export const retryUtil = {
  retry,
  retryWithValidator,
}
