import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'
import dayjs from 'dayjs'

interface ApiCallRecord {
  platform: string
  endpoint: string
  date: string
  count: number
  status: RapidApiStatus
  responseCode: number
}

export class RapidApiStats {
  private static instance: RapidApiStats
  private redis = redis
  private RAPID_API_REDIS_KEY_PREFIX = 'rapid_api_stats:'

  public static getInstance(): RapidApiStats {
    if (!RapidApiStats.instance) {
      RapidApiStats.instance = new RapidApiStats()
    }
    return RapidApiStats.instance
  }

  private getRedisKey(
    platform: string,
    status: RapidApiStatus,
    endpoint: string,
    responseCode: number,
    date: string,
  ): string {
    return `${REDIS_KEY_PREFIX}${this.RAPID_API_REDIS_KEY_PREFIX}${platform}:${status}:${endpoint}:${responseCode}:${date}`
  }

  public async recordApiCall(
    platform: string,
    status: RapidApiStatus,
    endpoint: string,
    responseCode: number,
  ): Promise<void> {
    const today = dayjs().format('YYYY-MM-DD')
    const redisKey = this.getRedisKey(platform, status, endpoint, responseCode, today)
    await this.redis.incr(redisKey)
    await this.redis.expire(redisKey, 60 * 60 * 24 * 30)
  }

  public async getDailyStats(
    date: string = dayjs().format('YYYY-MM-DD'),
  ): Promise<ApiCallRecord[]> {
    const pattern = `${REDIS_KEY_PREFIX}${this.RAPID_API_REDIS_KEY_PREFIX}*:*:*:*:${date}`
    const keys = await this.redis.keys(pattern)
    const results: ApiCallRecord[] = []

    for (const key of keys) {
      const count = parseInt((await this.redis.get(key)) || '0')
      const parts = key.split(':')
      // 新key：包含responseCode
      if (parts.length >= 9) {
        results.push({
          platform: parts[3],
          endpoint: parts.slice(5, parts.length - 2).join(':'),
          responseCode: parseInt(parts[parts.length - 2]),
          date: parts[parts.length - 1],
          count,
          status: parts[4] as RapidApiStatus,
        })
      }
    }

    return results
  }

  // 获取指定rapidapi uri的历史统计数据
  public async getEndpointStats(
    platform: string,
    status: RapidApiStatus,
    endpoint: string,
    responseCode: number,
    days: number = 7,
  ): Promise<ApiCallRecord[]> {
    const results: ApiCallRecord[] = []

    for (let i = 0; i < days; i++) {
      const date = dayjs().subtract(i, 'day').format('YYYY-MM-DD')
      const redisKey = this.getRedisKey(platform, status, endpoint, responseCode, date)
      const count = parseInt((await this.redis.get(redisKey)) || '0')

      results.push({
        platform,
        endpoint,
        date,
        count,
        status,
        responseCode,
      })
    }

    return results
  }
}
