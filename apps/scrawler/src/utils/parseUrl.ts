import YoutubeApi from '@/api/youtube.ts'
import { KolPlatform } from '@repo/database'

export const extractTiktokUsername = (input: string): string | null => {
  const patterns = {
    standard: /^https?:\/\/(www\.)?tiktok\.com\/@([^\/\s?]+)/,
    shortLink: /^https?:\/\/(www\.)?vm\.tiktok\.com\/([^\/\s?]+)/,
    username: /^@?([^\/\s?]+)$/,
  }

  const trimmedInput = input.trim()

  // 处理标准 TikTok URL
  const standardMatch = trimmedInput.match(patterns.standard)
  if (standardMatch) {
    return standardMatch[2]
  }

  // 处理短链接
  const shortLinkMatch = trimmedInput.match(patterns.shortLink)
  if (shortLinkMatch) {
    return shortLinkMatch[2]
  }

  // 处理纯用户名
  const usernameMatch = trimmedInput.match(patterns.username)
  if (usernameMatch && !trimmedInput.includes('tiktok.com')) {
    return usernameMatch[1]
  }

  return null
}

export const extractTiktokUsernames = async (inputs: string[]): Promise<string[]> => {
  const promises = inputs.map((input) => Promise.resolve(extractTiktokUsername(input)))
  const results = await Promise.all(promises)
  return results.filter((username): username is string => username !== null)
}

export const extractInstagramUsername = (input: string): string | null => {
  const patterns = {
    standard: /^https?:\/\/(www\.)?instagram\.com\/([^\/\s?]+)\/?/,
    username: /^@?([^\/\s?]+)$/,
  }

  const trimmedInput = input.trim()
  // 处理标准 Instagram URL
  const standardMatch = trimmedInput.match(patterns.standard)
  if (standardMatch) {
    return standardMatch[2]
  }
  // 处理纯用户名
  const usernameMatch = trimmedInput.match(patterns.username)
  if (usernameMatch && !trimmedInput.includes('instagram.com')) {
    return usernameMatch[1]
  }

  return null
}

export const extractInstagramUsernames = async (inputs: string[]): Promise<string[]> => {
  const promises = inputs.map((input) => Promise.resolve(extractInstagramUsername(input)))
  const results = await Promise.all(promises)
  return results.filter((username): username is string => username !== null)
}

// handle,ucId
export const extractYoutubeIds = (
  input: string,
): { ucId: string | null; handle: string | null } => {
  const trimmedInput = input.trim()
  const result: { ucId: string | null; handle: string | null } = { ucId: null, handle: null }

  // 提取UC ID (channel ID)
  const ucIdMatch = trimmedInput.match(/youtube\.com\/channel\/(UC[a-zA-Z0-9_-]{22})/i)
  if (ucIdMatch) {
    result.ucId = ucIdMatch[1]
  }

  // 提取handle (@用户名)
  const handleMatch = trimmedInput.match(/youtube\.com\/@([a-zA-Z0-9_.-]+)/i)
  if (handleMatch) {
    result.handle = handleMatch[1]
  }

  return result
}

export const extractYoutubeIdsFromArray = async (
  inputs: string[],
): Promise<{ ucIds: string[]; handles: string[] }> => {
  const results = await Promise.all(
    inputs.map((input) => Promise.resolve(extractYoutubeIds(input))),
  )

  // 分离UC IDs和handles到各自的数组中
  const ucIds: string[] = []
  const handles: string[] = []

  results.forEach((result) => {
    if (result.ucId) ucIds.push(result.ucId)
    if (result.handle) handles.push(result.handle)
  })

  return { ucIds, handles }
}

// get platform from url
function getPlatformFromUrl(url: string): KolPlatform | null {
  if (!url) return null

  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return KolPlatform.YOUTUBE
  } else if (
    url.includes('tiktok.com') ||
    url.includes('vt.tiktok.com') ||
    url.includes('vm.tiktok.com')
  ) {
    return KolPlatform.TIKTOK
  } else if (url.includes('instagram.com')) {
    return KolPlatform.INSTAGRAM
  }
  return null
}

function extractVideoId(url: string): string | null {
  if (!url) return null

  // YouTube
  if (url.includes('youtube.com/watch?v=')) {
    const videoId = url.split('v=')[1]?.split('&')[0]
    return videoId || null
  } else if (url.includes('youtube.com/shorts/')) {
    const shortId = url.split('shorts/')[1]?.split('?')[0]
    return shortId || null
  }
  // TikTok
  else if (url.includes('tiktok.com')) {
    let videoId = null
    if (url.includes('/video/')) {
      videoId = url.split('video/')[1]?.split('?')[0]
    } else if (url.includes('/t/')) {
      videoId = url.split('/t/')[1]?.split('/')[0]
    } else if (url.includes('vt.tiktok.com')) {
      videoId = url.split('vt.tiktok.com/')[1]?.split('/')[0]
    } else if (url.includes('vm.tiktok.com')) {
      videoId = url.split('vm.tiktok.com/')[1]?.split('/')[0]
    } else if (url.includes('/photo/')) {
      videoId = url.split('photo/')[1]?.split('?')[0]
    }
    return videoId
  }
  // Instagram
  else if (url.includes('instagram.com/p/') || url.match(/instagram\.com\/[^\/]+\/p\//)) {
    let postId
    if (url.match(/instagram\.com\/[^\/]+\/p\//)) {
      const m = url.match(/instagram\.com\/[^\/]+\/p\/([^\/\?]+)/)
      postId = m ? m[1] : null
    } else {
      postId = url.split('/p/')[1]?.split('/')[0]
    }
    return postId
  } else if (url.includes('instagram.com/reel/') || url.match(/instagram\.com\/[^\/]+\/reel\//)) {
    let reelId
    if (url.match(/instagram\.com\/[^\/]+\/reel\//)) {
      const m = url.match(/instagram\.com\/[^\/]+\/reel\/([^\/\?]+)/)
      reelId = m ? m[1] : null
    } else {
      reelId = url.split('/reel/')[1]?.split('/')[0]
    }
    return reelId
  }

  return null
}

async function extractUsernameFromLink(
  link: string,
  platform: KolPlatform,
): Promise<string | null> {
  switch (platform) {
    case KolPlatform.TIKTOK:
      return extractTiktokUsername(link)
    case KolPlatform.INSTAGRAM:
      return extractInstagramUsername(link)
    case KolPlatform.YOUTUBE: {
      const { handle, ucId } = extractYoutubeIds(link)
      if (ucId) return ucId
      if (handle) {
        try {
          const channelId = await YoutubeApi.getInstance().getYoutubeChannelId(handle)
          return channelId || null
        } catch (error) {
          console.error(`Failed to convert YouTube handle @${handle} to channelId:`, error)
          return null
        }
      }
      return null
    }
    default:
      return null
  }
}

export const parseUrlUtils = {
  extractTiktokUsername,
  extractTiktokUsernames,
  extractInstagramUsername,
  extractInstagramUsernames,
  extractYoutubeIds,
  extractYoutubeIdsFromArray,
  extractVideoId,
  getPlatformFromUrl,
  extractUsernameFromLink,
}
