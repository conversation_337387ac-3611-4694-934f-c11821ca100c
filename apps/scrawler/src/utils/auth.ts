import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { FEATURE_DEVICE_BAN } from '@/config/env'
import { MemberStatus, MemberType, prisma, UserAuditLogType } from '@repo/database'
import { User } from '@supabase/supabase-js'
import { FastifyRequest } from 'fastify'
import { cache } from './cache'
import Logger from './logger'

export async function verifyDeviceBan(userId: string, browserId: string, fromWeb: boolean = true) {
  if (FEATURE_DEVICE_BAN !== 'true') {
    return
  }
  const userMembership = await prisma.userMembership.findUnique({
    where: {
      userId: userId,
    },
    select: {
      type: true,
      enterpriseId: true,
    },
  })

  if (userMembership?.type !== MemberType.FREE) {
    return
  }
  const signatureCount = await prisma.userSignatureLog.findMany({
    where: {
      browserId: browserId,
      createdAt: {
        gte: new Date(new Date().setMonth(new Date().getMonth() - 1)),
      },
    },
    select: {
      userId: true,
    },
    distinct: ['userId'],
  })

  if (signatureCount.length > 2) {
    Logger.info(`${userId} has been banned on ${browserId}.`)
    if (!fromWeb) {
      await prisma.userMembership.update({
        where: {
          userId: userId,
        },
        data: {
          status: MemberStatus.DEVICE_BAN,
        },
      })
    }
    await prisma.userAuditLog.create({
      data: {
        userId: userId,
        enterpriseId: userMembership.enterpriseId ?? '',
        type: fromWeb ? UserAuditLogType.DEVICE_BAN_WEB : UserAuditLogType.DEVICE_BAN,
      },
    })
  }
}

export async function getUserEnterpriseId(userId: string): Promise<string | undefined> {
  return cache.wrap(`user:${userId}:enterpriseId`, async () => {
    const membership = await prisma.userMembership.findFirst({
      where: {
        userId: userId,
      },
      select: {
        enterpriseId: true,
        type: true,
      },
    })
    if (membership?.type === MemberType.ENTERPRISE && !membership?.enterpriseId) {
      throw new Error(
        `User not in enterprise, userId: ${userId}, enterpriseId: ${membership?.enterpriseId}`,
      )
    }
    return membership?.type === MemberType.ENTERPRISE ? membership.enterpriseId! : ''
  })
}

export async function isEnterpriseAdmin(userId: string): Promise<boolean> {
  const cacheKey = `user:${userId}:isEnterpriseAdmin`
  const isEnterpriseAdmin = await cache.wrap(cacheKey, async () => {
    const user = await prisma.userMembership.findUnique({
      where: {
        userId: userId,
        type: MemberType.ENTERPRISE,
      },
      select: {
        isEnterpriseAdmin: true,
      },
    })
    return user?.isEnterpriseAdmin ?? false
  })
  return isEnterpriseAdmin
}
export async function getUserAndEntepriseId(request: any): Promise<{
  userId: string
  enterpriseId: string | undefined
}> {
  const user = request.user
  if (!user.id) {
    throw new Error('User not found')
  }
  const enterpriseId = await getUserEnterpriseId(user.id)
  return {
    userId: user.id,
    enterpriseId,
  }
}

export async function isWeb(request: FastifyRequest) {
  return request.headers['x-client-type'] === 'web'
}

export function getUser(request: FastifyRequest) {
  const user = (request as any).user as User
  if (!user) {
    throwError(StatusCodes.UNAUTHORIZED, 'User not found')
  }
  return user
}
