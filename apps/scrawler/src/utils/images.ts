import { CLOUDFLARE_WORKER_URL } from '@/config/env'
import { ImagePart } from '@/types/images'
import { retryUtil } from './retry'

async function fetchImageAsBase64(url: string, retries = 2): Promise<ImagePart> {
  return retryUtil.retry(
    async () => {
      let response: Response

      try {
        const proxyUrl = `${CLOUDFLARE_WORKER_URL}?url=${encodeURIComponent(url)}`

        console.log(`[ImageUtil] 获取图片: ${url.slice(0, 80)}...`)

        response = await fetch(proxyUrl)

        if (!response.ok) {
          let errorMessage = `获取图片失败: ${response.status}`
          try {
            const errorData = await response.json()
            if (errorData.error) {
              errorMessage = errorData.error
            }
          } catch {
            errorMessage += ` ${response.statusText}`
          }

          if (response.status >= 500 || response.status === 403) {
            console.warn(`[ImageUtil] Cloudflare Worker 请求失败，尝试直接获取图片`)
            response = await fetch(url)

            if (!response.ok) {
              throw new Error(`直接获取图片失败: ${response.status} ${response.statusText}`)
            }
          } else {
            throw new Error(errorMessage)
          }
        }
      } catch (error) {
        console.warn(`[ImageUtil] Cloudflare Worker 不可用，尝试直接获取图片: ${error}`)
        response = await fetch(url)

        if (!response.ok) {
          throw new Error(`直接获取图片失败: ${response.status} ${response.statusText}`)
        }
      }
      const buffer = await response.arrayBuffer()
      const base64 = Buffer.from(buffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'

      const cacheStatus = response.headers.get('x-cache-status') || 'MISS'
      const responseTime = response.headers.get('x-response-time') || '0ms'
      console.log(
        `[ImageUtil] ✓ ${(buffer.byteLength / 1024).toFixed(0)}KB [${cacheStatus}] ${responseTime}`,
      )
      return {
        inlineData: {
          data: base64,
          mimeType,
        },
      }
    },
    retries,
    1000,
    2,
    (error, retryCount, nextDelay) => {
      console.warn(`[ImageUtil] ✗ 重试 ${retryCount}/${retries} (${nextDelay}ms): ${error.message}`)
    },
  )
}

const ImageUtil = {
  fetchImageAsBase64,
}

export default ImageUtil
