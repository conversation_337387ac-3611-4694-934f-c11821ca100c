import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'

class Cache {
  /**
   *
   * @param key 不带环境前缀的 key
   * @param fn 需要执行并缓存返回值的函数
   * @returns 返回缓存值或执行函数返回的值
   */
  async wrap<T>(key: string, fn: () => Promise<T>, ttl: number = 60 * 60 * 24): Promise<T> {
    const realKey = `${REDIS_KEY_PREFIX}${key}`
    const cache = await redis.get(realKey)
    if (cache) {
      return JSON.parse(cache)
    }
    const result = await fn()
    await redis.set(realKey, JSON.stringify(result), 'EX', ttl)
    return result
  }

  async del(key: string) {
    const realKey = `${REDIS_KEY_PREFIX}${key}`
    await redis.del(realKey)
  }
}

export const cache = new Cache()
