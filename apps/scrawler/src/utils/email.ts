export function extractEmail(text: string | undefined | null): string | null {
  if (!text) {
    return null
  }
  // 首先匹配可能的邮箱模式
  const matches = text.match(
    /[a-zA-Z0-9][a-zA-Z0-9._%+-]*@([a-zA-Z0-9][a-zA-Z0-9-]*\.){1,}[a-zA-Z]+/,
  )
  if (!matches) return null

  // 获取匹配到的邮箱
  let email = matches[0].trim()
  while (email.startsWith('.') || email.startsWith('-')) {
    email = email.substring(1)
  }
  while (email.endsWith('.') || email.endsWith('-')) {
    email = email.substring(0, email.length - 1)
  }

  return email.trim()
}

export const extractFullEmail = (signature: string | null): string | null => {
  if (!signature) {
    return null
  }

  const emailRegex =
    /^[a-zA-Z0-9\p{L}!#$%&'*+\-/=?^_`{|}~.]+@(?=.{1,254}$)(?:(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,}$/u
  const matches = signature.match(emailRegex)
  return matches ? matches[0] : null
}

export const extractUsernameFromEmail = (email: string) => {
  return email.split('@')[0]
}
