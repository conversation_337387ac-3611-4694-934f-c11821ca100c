import { allCombination } from '@/utils/common.ts'
import { prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'

describe('test common utils', () => {
  it('should work fine with string arrs', async () => {
    const arr = ['a', 'b', 'c']
    const result = allCombination(arr)
    console.log(JSON.stringify(result))
    expect(result.length).eq(6)
  })

  it('should find how many users login on the same browser', async () => {
    const browserId = '02414872d17b091b8f63998d3c312c62'
    console.log(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
    const signatureCount = await prisma.userSignatureLog.findMany({
      where: {
        browserId: browserId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        },
      },
      select: {
        userId: true,
      },
      distinct: ['userId'],
    })
    console.log(JSON.stringify(signatureCount))
    console.log(signatureCount.length)
    expect(signatureCount.length).eq(1)
  })
})
