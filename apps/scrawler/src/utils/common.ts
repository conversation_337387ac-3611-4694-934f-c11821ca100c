import { hostname } from 'os'

export const allCombination = function <T>(arr: T[]): T[][] {
  /**
   * 全排列的函数，优先把长度长的组合放前面，方便搜索场景
   */
  let length = arr.length
  const result: T[][] = []
  while (length) {
    let left = 0
    while (left + length <= arr.length) {
      result.push(arr.slice(left, left + length))
      left++
    }
    length--
  }
  return result
}

export const isDev = () => {
  return !hostname().startsWith('talent-marketing-backend')
}
