import { PaginatedResponse, PaginationParams } from '@/types/pagination'

// 处理分页参数
const handlePagination = (pagination: PaginationParams = {}) => {
  const page = Number(pagination.page) || 1
  const pageSize = Number(pagination.pageSize) || 10
  const skip = (page - 1) * pageSize
  return { page, pageSize, skip }
}

// 处理分页响应
const handlePaginatedResponse = <T>(
  data: T[],
  total: number,
  page: number,
  pageSize: number,
): PaginatedResponse<T> => {
  return {
    data,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize),
  }
}

export const PaginationService = {
  handlePagination,
  handlePaginatedResponse,
}
