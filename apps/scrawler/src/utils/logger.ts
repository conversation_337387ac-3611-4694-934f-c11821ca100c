import { als, fastify } from '@/server'

class Logger {
  static getContext() {
    const store = als.getStore()
    if (!store) {
      return {
        requestId: 'no-context',
      }
    }

    return {
      requestId: store.get('requestId') || 'unknown',
    }
  }
  static setContext(key: string, value: any) {
    const store = als.getStore()
    if (store) {
      store.set(key, value)
    }
  }

  /**
   * info
   */
  static info(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.info({
      ...context,
      msg: message,
      ...data,
    })
  }

  /**
   * debug
   */
  static debug(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.debug({
      ...context,
      msg: message,
      ...data,
    })
  }

  /**
   * warn
   */
  static warn(message: string, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.warn({
      ...context,
      msg: message,
      ...data,
    })
  }

  /**
   * error
   */
  static error(message: string, error?: Error, data?: Record<string, any>) {
    const context = this.getContext()
    fastify.log.error({
      ...context,
      msg: message,
      error: error?.message,
      stack: error?.stack,
      ...data,
    })
  }
}

export default Logger
