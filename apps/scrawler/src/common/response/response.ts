// response.ts

// 定义 ApiResponse 接口
export interface ApiResponse<T = any> {
  statusCode: number // 自定义状态码
  error?: string | null
  message: string
  data?: T | null
}

// 定义自定义状态码
export const StatusCodes = {
  // 成功类状态码
  SUCCESS: 1000, // 成功
  CREATED: 1001, // 资源已创建
  ACCEPTED: 1002, // 请求已接受但未处理完成
  NO_CONTENT: 1003, // 成功但无返回内容
  UNIMPLEMENTED: 1004, // 方法未实现

  // 客户端错误类状态码
  BAD_REQUEST: 1400, // 错误请求
  UNAUTHORIZED: 1401, // 未授权
  FORBIDDEN: 1403, // 无权限访问
  NOT_FOUND: 1404, // 资源未找到
  CONFLICT: 1409, // 请求冲突（如重复提交）
  TOKEN_OUTDATED: 1411, // 该账号已在其他设备登录
  DEVICE_BAN: 1412, // 该设备已被限制登录
  UNPROCESSABLE_ENTITY: 1422, // 请求格式正确，但无法处理（如参数验证失败）
  VERSION_TOO_LOW: 1426, // 版本过低
  TOO_MANY_REQUESTS: 1429, // 请求频率过高

  // 服务器错误类状态码
  SERVER_ERROR: 1500, // 服务器内部错误
  SERVICE_UNAVAILABLE: 1503, // 服务不可用
  GATEWAY_TIMEOUT: 1504, // 网关超时

  // 自定义网络状态码
  NETWORK_ERROR: 1700, // 网络连接错误
  TIMEOUT: 1701, // 请求超时
  DNS_RESOLUTION_FAILED: 1702, // DNS 解析失败

  // 文件处理相关状态码
  FILE_TOO_LARGE: 1800, // 上传文件过大
  UNSUPPORTED_FILE_TYPE: 1801, // 不支持的文件类型
  FILE_UPLOAD_FAILED: 1802, // 文件上传失败

  // 会员相关状态码
  INVALID_MEMBERSHIP_TYPE: 1900, // 无效的会员类型
  MEMBERSHIP_EXPIRED: 1901, // 会员已过期
  MEMBERSHIP_NOT_FOUND: 1902, // 会员信息未找到
  FREE_USER_RESTRICTED: 1903, // 免费用户受限
  FREE_QUOTA_EXCEEDED: 1910, // 免费用户配额耗尽
  FREE_INSUFFICIENT_QUOTA: 1911, // 免费用户配额不足
  PAID_QUOTA_EXCEEDED: 1912, // 付费会员配额耗尽
  PAID_INSUFFICIENT_QUOTA: 1913, // 付费会员配额不足
  ENTERPRISE_NOT_FOUND: 1919, // 企业账户不存在
  ENTERPRISE_EXPIRED: 1920, // 企业账户过期
  ENTERPRISE_QUOTA_EXCEEDED: 1921, // 企业账户余额耗尽
  ENTERPRISE_INSUFFICIENT_QUOTA: 1922, // 企业账户余额不足
  ENTERPRISE_NOT_EFFECTIVE: 1923, // 企业账户未生效
  ENTERPRISE_SUSPENDED: 1924, // 企业账户被暂停
  MEMBERSHIP_NOT_EFFECTIVE: 1925, // 会员未生效
  MEMBERSHIP_SUSPENDED: 1926, // 会员被暂停
  ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED: 1927, // 企业成员每日使用额度超限
  ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED: 1928, // 企业成员个人每日使用额度超限
  ENTERPRISE_MEMBER_NOT_FOUND: 1929, // 企业成员不存在

  TOO_MANY_SUPERLIKES: 1949, // superlike请求频率过高

  // kol
  EMAIL_REQUIRED: 2000, // 邮箱必填

  // 支付相关状态码
  PAYMENT_REQUIRED: 2002, // 需要支付或更新支付方式
  PAYMENT_FAILED: 2003, // 支付失败
  SUBSCRIPTION_INVALID: 2004, // 订阅状态无效
  NO_ACTIVE_SUBSCRIPTION: 2005, // 没有活跃的订阅
}

// 定义状态码到消息的映射
export const StatusMessages = new Map<number, string>([
  // 成功类状态码
  [StatusCodes.SUCCESS, 'Request was successful'], // 1000
  [StatusCodes.CREATED, 'Resource created successfully'], // 1001
  [StatusCodes.ACCEPTED, 'Request accepted and is being processed'], // 1002
  [StatusCodes.NO_CONTENT, 'No content to return'], // 1003
  [StatusCodes.UNIMPLEMENTED, 'Method not implemented'], // 1004
  // 客户端错误类状态码
  [StatusCodes.BAD_REQUEST, 'Bad request'], // 1400
  [StatusCodes.UNAUTHORIZED, 'Unauthorized access'], // 1401
  [StatusCodes.FORBIDDEN, 'Access forbidden'], // 1403
  [StatusCodes.NOT_FOUND, 'Resource not found'], // 1404
  [StatusCodes.CONFLICT, 'Request conflict detected'], // 1409
  [StatusCodes.TOKEN_OUTDATED, 'The account is logged in on other devices'], // 1411
  [StatusCodes.DEVICE_BAN, 'Too many accounts linked. Contact <EMAIL>.'], // 1412
  [StatusCodes.UNPROCESSABLE_ENTITY, 'Unprocessable entity'], // 1422
  [StatusCodes.VERSION_TOO_LOW, 'The version is too low, please update to the latest version'], // 1426
  [StatusCodes.TOO_MANY_REQUESTS, 'Request frequency too high. Please wait.'], // 1429

  // 服务器错误类状态码
  [StatusCodes.SERVER_ERROR, 'Internal server error'], // 1500
  [StatusCodes.SERVICE_UNAVAILABLE, 'Service unavailable'], // 1503
  [StatusCodes.GATEWAY_TIMEOUT, 'Gateway timeout'], // 1504
  // 自定义网络状态码
  [StatusCodes.NETWORK_ERROR, 'Network error occurred'], // 1700
  [StatusCodes.TIMEOUT, 'Request timed out'], // 1701
  [StatusCodes.DNS_RESOLUTION_FAILED, 'DNS resolution failed'], // 1702
  // 文件处理相关状态码
  [StatusCodes.FILE_TOO_LARGE, 'File too large'], // 1800
  [StatusCodes.UNSUPPORTED_FILE_TYPE, 'Unsupported file type'], // 1801
  [StatusCodes.FILE_UPLOAD_FAILED, 'File upload failed'], // 1802
  // 会员相关状态码
  [StatusCodes.INVALID_MEMBERSHIP_TYPE, 'Invalid membership type'], // 1900
  [StatusCodes.MEMBERSHIP_EXPIRED, 'Membership expired'], // 1901
  [StatusCodes.MEMBERSHIP_NOT_FOUND, 'Membership not found'], // 1902
  [StatusCodes.FREE_USER_RESTRICTED, 'the feature is only available for paid users.'], // 1903
  [
    StatusCodes.FREE_QUOTA_EXCEEDED,
    'Daily free quota exhausted. Upgrade to premium or try again tomorrow.',
  ], // 1910
  [
    StatusCodes.FREE_INSUFFICIENT_QUOTA,
    'Insufficient free quota for this operation. Please upgrade to premium.',
  ], // 1911
  [StatusCodes.PAID_QUOTA_EXCEEDED, 'Member quota exhausted. Please purchase additional quota.'], // 1912
  [
    StatusCodes.PAID_INSUFFICIENT_QUOTA,
    'Insufficient member quota for this operation. Please purchase additional quota.',
  ], // 1913
  [StatusCodes.ENTERPRISE_NOT_FOUND, 'Enterprise account not found'], // 1919
  [StatusCodes.ENTERPRISE_EXPIRED, 'Enterprise account has expired. Please contact administrator.'], // 1920
  [StatusCodes.ENTERPRISE_QUOTA_EXCEEDED, 'Enterprise quota insufficient for this operation.'], // 1921
  [StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA, 'Insufficient enterprise quota for this operation'], // 1922
  [StatusCodes.ENTERPRISE_NOT_EFFECTIVE, 'Enterprise membership not effective'], // 1923
  [StatusCodes.ENTERPRISE_SUSPENDED, 'Enterprise suspended'], // 1924
  [StatusCodes.MEMBERSHIP_NOT_EFFECTIVE, 'Membership not effective'], // 1925
  [StatusCodes.MEMBERSHIP_SUSPENDED, 'Membership suspended'], // 1926
  [StatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED, 'Enterprise daily usage limit exceeded'], // 1927
  [
    StatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED,
    'Enterprise personal daily usage limit exceeded',
  ], // 1928
  [StatusCodes.ENTERPRISE_MEMBER_NOT_FOUND, 'Enterprise member not found'], // 1929
  [StatusCodes.TOO_MANY_SUPERLIKES, 'Superlike limit reached. Please wait.'], // 1949

  // email
  [StatusCodes.EMAIL_REQUIRED, 'Email is required'], // 2000

  // 支付相关状态码
  [StatusCodes.PAYMENT_REQUIRED, 'Payment required or payment method needs to be updated'], // 2002
  [StatusCodes.PAYMENT_FAILED, 'Payment failed'], // 2003
  [StatusCodes.SUBSCRIPTION_INVALID, 'Subscription status is invalid'], // 2004
  [StatusCodes.NO_ACTIVE_SUBSCRIPTION, 'No active subscription'], // 2005
])

// 获取状态码对应的消息
function getMessageForStatusCode(statusCode: number, customMessage?: string): string {
  return customMessage || StatusMessages.get(statusCode) || 'Unknown status code'
}

// 定义通用的响应函数
export function customResponse<T>(
  statusCode: number,
  data: T | null = null,
  error: string | null = null,
  customMessage?: string,
): ApiResponse<T> {
  const message = getMessageForStatusCode(statusCode, customMessage)
  return {
    statusCode,
    error,
    message,
    data,
  }
}

// 定义 successResponse 函数
export function successResponse<T>(data: T, customMessage?: string): ApiResponse<T> {
  return customResponse(StatusCodes.SUCCESS, data, null, customMessage)
}

export function successResponseWithoutData(customMessage?: string): ApiResponse<null> {
  return customResponse(StatusCodes.SUCCESS, null, null, customMessage)
}

// 定义 errorResponse 函数
export function errorResponse(
  statusCode: number = StatusCodes.BAD_REQUEST,
  error: string | null = null,
  customMessage?: string,
): ApiResponse<null> {
  return customResponse(statusCode, null, error, customMessage)
}

// 定义 notFoundResponse 函数
export function notFoundResponse(customMessage?: string): ApiResponse<null> {
  return customResponse(StatusCodes.NOT_FOUND, null, null, customMessage)
}

// 定义 serverErrorResponse 函数
export function serverErrorResponse(customMessage?: string): ApiResponse<null> {
  return customResponse(StatusCodes.SERVER_ERROR, null, null, customMessage)
}
