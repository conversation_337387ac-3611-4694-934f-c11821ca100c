import { StatusCodes } from '../response/response'

export type StatusCodeMetadata = {
  code: number
  message: string
}

// 增强类型定义
export type ErrorCode = (typeof StatusCodes)[keyof typeof StatusCodes]

export const metadataMap = new Map<ErrorCode, StatusCodeMetadata>()

const DEFAULT = {
  code: 500,
  message: 'Internal Server Error',
}

export function code(httpCode: number) {
  return function (target: any, propertyKey: string | symbol) {
    const enumValue = target[propertyKey]

    const metadata = {
      code: httpCode,
      message: DEFAULT.message,
    }
    metadataMap.set(enumValue, metadata)
  }
}

// message 装饰器工厂
export function message(msg: string) {
  return function (target: any, propertyKey: string | symbol) {
    // 获取枚举值
    const enumValue = target[propertyKey]

    // 确保元数据对象存在
    if (!metadataMap.has(enumValue)) {
      metadataMap.set(enumValue, DEFAULT)
    }

    // 设置 message
    const metadata = metadataMap.get(enumValue)!
    metadata.message = msg
  }
}
