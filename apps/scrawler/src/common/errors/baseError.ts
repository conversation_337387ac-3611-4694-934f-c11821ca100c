import { StatusCodes } from './statusCodes'

export class BaseError extends Error {
  public readonly code: number
  public readonly httpCode: number
  public readonly details?: any

  constructor(code: number, message?: string, details?: any) {
    const metadata = StatusCodes.get(code)

    super(message || metadata.message)

    this.code = code
    this.httpCode = metadata.code
    this.details = details

    Object.setPrototypeOf(this, BaseError.prototype)
  }

  toJSON() {
    return {
      success: false,
      code: this.code,
      message: this.message,
      details: this.details,
    }
  }
}

export class UnimplementedError extends BaseError {
  constructor(message?: string, details?: any) {
    super(StatusCodes.INTERNAL_ERROR, message, details)
  }
}
