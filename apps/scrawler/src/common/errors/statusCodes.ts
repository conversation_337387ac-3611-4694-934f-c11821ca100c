import {
  StatusCodes as ResponseStatusCodes,
  StatusMessages as ResponseStatusMessages,
} from '../response/response'
import { BaseError } from './baseError.ts'
import { ErrorCode, StatusCodeMetadata, code, message, metadataMap } from './meta.js'

export class StatusCodes {
  //--------------- 成功类状态码 -------------------
  @code(200) // 业务码: 1000
  @message('success')
  static readonly SUCCESS = ResponseStatusCodes.SUCCESS

  @code(201) // 业务码: 1001
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.CREATED)}`)
  static readonly CREATED = ResponseStatusCodes.CREATED

  @code(202) // 业务码: 1002
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ACCEPTED)}`)
  static readonly ACCEPTED = ResponseStatusCodes.ACCEPTED

  @code(204) // 业务码: 1003
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.NO_CONTENT)}`)
  static readonly NO_CONTENT = ResponseStatusCodes.NO_CONTENT

  @code(500) // 业务码: 1004
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.UNIMPLEMENTED)}`)
  static readonly UNIMPLEMENTED = ResponseStatusCodes.UNIMPLEMENTED

  //--------------- 客户端错误类状态码 -------------------
  @code(400) // 业务码: 1400
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.BAD_REQUEST)}`)
  static readonly BAD_REQUEST = ResponseStatusCodes.BAD_REQUEST

  @code(401) // 业务码: 1401
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.UNAUTHORIZED)}`)
  static readonly UNAUTHORIZED = ResponseStatusCodes.UNAUTHORIZED

  @code(400) // 业务码: 1400
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.BAD_REQUEST)}`)
  static readonly BAD_REQUEST_14 = ResponseStatusCodes.BAD_REQUEST

  @code(401) // 业务码: 1401
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.UNAUTHORIZED)}`)
  static readonly UNAUTHORIZED_14 = ResponseStatusCodes.UNAUTHORIZED

  @code(403) // 业务码: 1403
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FORBIDDEN)}`)
  static readonly FORBIDDEN = ResponseStatusCodes.FORBIDDEN

  @code(404) // 业务码: 1404
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.NOT_FOUND)}`)
  static readonly NOT_FOUND = ResponseStatusCodes.NOT_FOUND

  @code(409) // 业务码: 1409
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.CONFLICT)}`)
  static readonly CONFLICT = ResponseStatusCodes.CONFLICT

  @code(411) // 业务码: 1411
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.TOKEN_OUTDATED)}`)
  static readonly TOKEN_OUTDATED = ResponseStatusCodes.TOKEN_OUTDATED

  @code(412) // 业务码: 1412
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.DEVICE_BAN)}`)
  static readonly DEVICE_BAN = ResponseStatusCodes.DEVICE_BAN

  @code(422) // 业务码: 1422
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.UNPROCESSABLE_ENTITY)}`)
  static readonly UNPROCESSABLE_ENTITY = ResponseStatusCodes.UNPROCESSABLE_ENTITY

  @code(426) // 业务码: 1426
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.VERSION_TOO_LOW)}`)
  static readonly VERSION_TOO_LOW = ResponseStatusCodes.VERSION_TOO_LOW

  @code(429) // 业务码: 1429
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.TOO_MANY_REQUESTS)}`)
  static readonly TOO_MANY_REQUESTS = ResponseStatusCodes.TOO_MANY_REQUESTS

  //--------------- 服务器错误类状态码 -------------------
  @code(500) // 业务码: 1500
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.SERVER_ERROR)}`)
  static readonly SERVER_ERROR = ResponseStatusCodes.SERVER_ERROR

  @code(503) // 业务码: 1503
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.SERVICE_UNAVAILABLE)}`)
  static readonly SERVICE_UNAVAILABLE = ResponseStatusCodes.SERVICE_UNAVAILABLE

  @code(504) // 业务码: 1504
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.GATEWAY_TIMEOUT)}`)
  static readonly GATEWAY_TIMEOUT = ResponseStatusCodes.GATEWAY_TIMEOUT

  //--------------- 自定义网络状态码 -------------------
  @code(500) // 业务码: 1700
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.NETWORK_ERROR)}`)
  static readonly NETWORK_ERROR = ResponseStatusCodes.NETWORK_ERROR

  @code(408) // 业务码: 1701
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.TIMEOUT)}`)
  static readonly TIMEOUT = ResponseStatusCodes.TIMEOUT

  @code(500) // 业务码: 1702
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.DNS_RESOLUTION_FAILED)}`)
  static readonly DNS_RESOLUTION_FAILED = ResponseStatusCodes.DNS_RESOLUTION_FAILED

  //--------------- 文件处理相关状态码 -------------------
  @code(413) // 业务码: 1800
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FILE_TOO_LARGE)}`)
  static readonly FILE_TOO_LARGE = ResponseStatusCodes.FILE_TOO_LARGE

  @code(415) // 业务码: 1801
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.UNSUPPORTED_FILE_TYPE)}`)
  static readonly UNSUPPORTED_FILE_TYPE = ResponseStatusCodes.UNSUPPORTED_FILE_TYPE

  @code(500) // 业务码: 1802
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FILE_UPLOAD_FAILED)}`)
  static readonly FILE_UPLOAD_FAILED = ResponseStatusCodes.FILE_UPLOAD_FAILED

  //--------------- 会员相关状态码 -------------------
  @code(400) // 业务码: 1900
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.INVALID_MEMBERSHIP_TYPE)}`)
  static readonly INVALID_MEMBERSHIP_TYPE = ResponseStatusCodes.INVALID_MEMBERSHIP_TYPE

  @code(403) // 业务码: 1901
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.MEMBERSHIP_EXPIRED)}`)
  static readonly MEMBERSHIP_EXPIRED = ResponseStatusCodes.MEMBERSHIP_EXPIRED

  @code(404) // 业务码: 1902
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.MEMBERSHIP_NOT_FOUND)}`)
  static readonly MEMBERSHIP_NOT_FOUND = ResponseStatusCodes.MEMBERSHIP_NOT_FOUND

  @code(403) // 业务码: 1903
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FREE_USER_RESTRICTED)}`)
  static readonly FREE_USER_RESTRICTED = ResponseStatusCodes.FREE_USER_RESTRICTED

  @code(403) // 业务码: 1910
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FREE_QUOTA_EXCEEDED)}`)
  static readonly FREE_QUOTA_EXCEEDED = ResponseStatusCodes.FREE_QUOTA_EXCEEDED

  @code(403) // 业务码: 1911
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.FREE_INSUFFICIENT_QUOTA)}`)
  static readonly FREE_INSUFFICIENT_QUOTA = ResponseStatusCodes.FREE_INSUFFICIENT_QUOTA

  @code(403) // 业务码: 1912
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.PAID_QUOTA_EXCEEDED)}`)
  static readonly PAID_QUOTA_EXCEEDED = ResponseStatusCodes.PAID_QUOTA_EXCEEDED

  @code(403) // 业务码: 1913
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.PAID_INSUFFICIENT_QUOTA)}`)
  static readonly PAID_INSUFFICIENT_QUOTA = ResponseStatusCodes.PAID_INSUFFICIENT_QUOTA

  @code(404) // 业务码: 1919
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_NOT_FOUND)}`)
  static readonly ENTERPRISE_NOT_FOUND = ResponseStatusCodes.ENTERPRISE_NOT_FOUND

  @code(403) // 业务码: 1920
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_EXPIRED)}`)
  static readonly ENTERPRISE_EXPIRED = ResponseStatusCodes.ENTERPRISE_EXPIRED

  @code(403) // 业务码: 1921
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_QUOTA_EXCEEDED)}`)
  static readonly ENTERPRISE_QUOTA_EXCEEDED = ResponseStatusCodes.ENTERPRISE_QUOTA_EXCEEDED

  @code(403) // 业务码: 1922
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA)}`)
  static readonly ENTERPRISE_INSUFFICIENT_QUOTA = ResponseStatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA

  @code(403) // 业务码: 1923
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_NOT_EFFECTIVE)}`)
  static readonly ENTERPRISE_NOT_EFFECTIVE = ResponseStatusCodes.ENTERPRISE_NOT_EFFECTIVE

  @code(403) // 业务码: 1924
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_SUSPENDED)}`)
  static readonly ENTERPRISE_SUSPENDED = ResponseStatusCodes.ENTERPRISE_SUSPENDED

  @code(403) // 业务码: 1925
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.MEMBERSHIP_NOT_EFFECTIVE)}`)
  static readonly MEMBERSHIP_NOT_EFFECTIVE = ResponseStatusCodes.MEMBERSHIP_NOT_EFFECTIVE

  @code(403) // 业务码: 1926
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.MEMBERSHIP_SUSPENDED)}`)
  static readonly MEMBERSHIP_SUSPENDED = ResponseStatusCodes.MEMBERSHIP_SUSPENDED

  @code(403) // 业务码: 1927
  @message(
    `${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED)}`,
  )
  static readonly ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED =
    ResponseStatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED

  @code(403) // 业务码: 1928
  @message(
    `${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED)}`,
  )
  static readonly ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED =
    ResponseStatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED

  @code(404) // 业务码: 1929
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.ENTERPRISE_MEMBER_NOT_FOUND)}`)
  static readonly ENTERPRISE_MEMBER_NOT_FOUND = ResponseStatusCodes.ENTERPRISE_MEMBER_NOT_FOUND

  @code(429) // 业务码: 1949
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.TOO_MANY_SUPERLIKES)}`)
  static readonly TOO_MANY_SUPERLIKES = ResponseStatusCodes.TOO_MANY_SUPERLIKES

  //--------------- 邮箱相关状态码 -------------------
  // KOL 相关
  @code(400) // 业务码: 2000
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.EMAIL_REQUIRED)}`)
  static readonly EMAIL_REQUIRED = ResponseStatusCodes.EMAIL_REQUIRED

  //--------------- 支付相关状态码 -------------------
  @code(402) // 业务码: 2002
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.PAYMENT_REQUIRED)}`)
  static readonly PAYMENT_REQUIRED = ResponseStatusCodes.PAYMENT_REQUIRED

  @code(400) // 业务码: 2003
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.PAYMENT_FAILED)}`)
  static readonly PAYMENT_FAILED = ResponseStatusCodes.PAYMENT_FAILED

  @code(400) // 业务码: 2004
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.SUBSCRIPTION_INVALID)}`)
  static readonly SUBSCRIPTION_INVALID = ResponseStatusCodes.SUBSCRIPTION_INVALID

  @code(400) // 业务码: 2005
  @message(`${ResponseStatusMessages.get(ResponseStatusCodes.NO_ACTIVE_SUBSCRIPTION)}`)
  static readonly NO_ACTIVE_SUBSCRIPTION = ResponseStatusCodes.NO_ACTIVE_SUBSCRIPTION

  static get(statusCode: ErrorCode): StatusCodeMetadata {
    return metadataMap.get(statusCode) || { code: 500, message: 'Unknown Error' }
  }

  static list(): Record<ErrorCode, StatusCodeMetadata> {
    return Object.fromEntries(metadataMap.entries())
  }
}

export function newError(code: ErrorCode, message?: string, details?: any): never {
  throw new BaseError(code, message, details)
}

export function throwError(code: ErrorCode, message?: string, details?: any): never {
  throw new BaseError(code, message, details)
}
