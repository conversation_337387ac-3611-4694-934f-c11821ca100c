import { RapidApiStats } from '@/utils/rapidApiStats'
import { prisma } from '@repo/database'

// 同步到数据库
async function syncRapidApiStats() {
  try {
    const stats = await RapidApiStats.getInstance().getDailyStats()

    await Promise.all(
      stats.map(async (stat) => {
        await prisma.rapidApiDailyStat.upsert({
          where: {
            date_endpoint_status_responseCode: {
              date: new Date(stat.date),
              endpoint: stat.endpoint,
              status: stat.status.toUpperCase() as 'SUCCESS' | 'ERROR' | 'WARNING',
              responseCode: stat.responseCode,
            },
          },
          create: {
            date: new Date(stat.date),
            platform: stat.platform,
            status: stat.status.toUpperCase() as 'SUCCESS' | 'ERROR' | 'WARNING',
            endpoint: stat.endpoint,
            count: stat.count,
            responseCode: stat.responseCode,
          },
          update: {
            count: stat.count,
            updatedAt: new Date(),
          },
        })
      }),
    )

    console.log('Successfully synced RapidAPI stats to database')
  } catch (error) {
    console.error('Failed to sync RapidAPI stats:', error)
    throw error
  }
}

syncRapidApiStats()
  .then(() => {
    console.log('all done')
    process.exit(0)
  })
  .catch((err) => {
    console.error(err)
    process.exit(1)
  })
