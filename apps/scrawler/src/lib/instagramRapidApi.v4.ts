import {
  IgCommentResponse,
  IgFollowersResponse,
  IgFollowingsResponse,
  IgHashTagVideosResponse,
  IgLikesResponse,
  IgPost,
  IgPostsResponse,
  IgReelsResponse,
  IgTaggedUsersResponse,
  IgUser,
} from '@/api/@types/rapidapi/Instagram'
import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { parsePathParams } from '@/api/util'
import { Y2B_RAPID_API_KEY } from '@/config/env'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { KolPlatform } from '@repo/database'
import dayjs from 'dayjs'
import countries from 'i18n-iso-countries'
import { ofetch } from 'ofetch'
import { InstagramApi, userRequestStruct } from './instagram'

/**
 * 并发更高的 API，价格也便宜不少。考虑作为主力使用
 * https://rapidapi.com/thetechguy32744/api/instagram-scraper-stable-api/pricing
 * */
class InstagramRapidApiV4 implements InstagramApi {
  private static instance: InstagramRapidApiV4

  HOST = 'instagram-scraper-stable-api.p.rapidapi.com'
  API_KEY = Y2B_RAPID_API_KEY
  VERSION = 'v4'
  public static getInstance(): InstagramRapidApiV4 {
    if (!InstagramRapidApiV4.instance) {
      InstagramRapidApiV4.instance = new InstagramRapidApiV4()
    }

    return InstagramRapidApiV4.instance
  }

  private async fulfillUser(user: userRequestStruct): Promise<userRequestStruct> {
    await this.getUserInternal(user)
    return user
  }

  public async getUserWithPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const idOrName = user.id ?? user.username
    const fullUser = await this.getUser(user, collector)
    if (!fullUser?.id) {
      throw new Error('user not found' + idOrName)
    }
    try {
      fullUser.posts = await this.getPosts(user, collector)
      fullUser.lastPublishedTime =
        fullUser.posts && fullUser.posts.length ? fullUser.posts[0].created_at : dayjs().unix()
    } catch (error) {
      console.log(`fail get user posts`)
      Sentry.captureException(error)
      fullUser.posts = []
    }
    return fullUser
  }

  public async getUserRelatedUsers(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser[]> {
    if (!user.username) {
      user = await this.fulfillUser(user)
    }
    const users = await this.get<RelatedUserFull[]>(
      `get_ig_similar_accounts.php?username_or_url=${user.username}`,
      {},
      collector,
    )
    console.log(`get ${users.length} related users for ${user.username} by V4`)
    return users.length
      ? users.map((i) => {
          return {
            id: i.id,
            username: i.username,
            profilePicUrl: i?.profile_pic_url ?? '',
            fullName: i?.full_name ?? '',
          } as IgUser
        })
      : []
  }

  public async getUser(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const fullUser = await this.getUserInternal(user, collector)
    const about = await this.get<IgAboutFull>(
      `get_ig_user_about.php?${parsePathParams({ username_or_url: fullUser.username })}`,
      {},
      collector,
    )
    if (about?.creation_country?.length) {
      const country = countries.getAlpha2Code(about.creation_country, 'en')
      if (country) {
        fullUser.region = country
      }
    }
    return fullUser
  }

  private async getUserInternal(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser> {
    const fullUser = await this.get<UserFull>(
      `ig_get_fb_profile.php`,
      { method: 'POST', params: { username_or_url: user.username } },
      collector,
    )
    user.id = fullUser.id
    user.username = fullUser.username
    const bio_email = fullUser?.email_from_biography?.length
      ? fullUser.email_from_biography[0].email
      : null
    let email = undefined
    let source = undefined
    if (bio_email) {
      email = bio_email
      source = EmailSourceType.BIO_EMAIL
    } else if (extractEmail(fullUser.biography)) {
      email = extractEmail(fullUser.biography)
      if (email) {
        source = EmailSourceType.BIO_EMAIL
      }
    }
    return {
      id: fullUser.id,
      username: fullUser.username,
      profilePicUrl: fullUser.profile_pic_url,
      fullName: fullUser.full_name,
      followerCount: fullUser.follower_count,
      biography: fullUser.biography,
      biographyEmail: bio_email,
      posts: [],
      publicEmail: undefined,
      region: undefined,
      category: fullUser.category,
      accountType: fullUser.account_type,
      email,
      source,
    } as IgUser
  }

  public async getPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost[]> {
    if (!user.username) {
      user = await this.fulfillUser(user)
    }
    try {
      const res = await this.get<{ posts: { node: PostFull }[]; pagination_token: string }>(
        `get_ig_user_posts.php`,
        {
          method: 'POST',
          params: { username_or_url: user.username },
        },
        collector,
      )
      return res.posts.map((i) => {
        return this.parsePost(i.node)
      })
    } catch (err) {
      console.log(err)
      Sentry.captureException(err)
      return []
    }
  }

  public async getPost(
    psotId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost | undefined> {
    throw new Error('getPost not implemented')
  }

  getPostComments(
    postId: string,
    options?: { pagination_token?: string; sort_by?: 'recent' | 'popular' },
    collector?: RapidApiStatsCollector,
  ): Promise<IgCommentResponse> {
    throw new Error('Method not implemented.')
  }
  getReels(
    user: userRequestStruct,
    options?: { pagination_token?: string; url_embed_safe?: boolean },
    collector?: RapidApiStatsCollector,
  ): Promise<IgReelsResponse> {
    throw new Error('Method not implemented.')
  }
  getUserPosts(
    user: userRequestStruct,
    options?: { pagination_token?: string; url_embed_safe?: boolean },
    collector?: RapidApiStatsCollector,
  ): Promise<IgPostsResponse> {
    throw new Error('Method not implemented.')
  }
  getFollowers(
    user: userRequestStruct,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowersResponse> {
    throw new Error('Method not implemented.')
  }

  getLikes(postId: string, collector?: RapidApiStatsCollector): Promise<IgLikesResponse> {
    throw new Error('Method not implemented.')
  }

  getHashTagVideos(
    hashtag: string,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgHashTagVideosResponse> {
    throw new Error('Method not implemented.')
  }

  getFollowings(
    user: userRequestStruct,
    options?: { pagination_token?: string; amount?: number },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowingsResponse> {
    throw new Error('Method not implemented.')
  }
  getTaggedUsers(
    username: string,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgTaggedUsersResponse> {
    throw new Error('Method not implemented.')
  }

  getQuota(): Promise<{ used: number; total: number }> {
    throw new Error('Method not implemented.')
  }

  private parsePost(post: PostFull): IgPost {
    return {
      id: post.id,
      type: post.video_versions ? 'reel' : 'post',
      thumbnail_url: post.thumbnails?.items[0]?.url,
      comment_count: Number(post.comment_count),
      like_count: Number(post.like_count),
      play_count: Number(post.view_count || 0),
      caption: {
        text: post.caption?.text,
      },
      created_at: Number(post.taken_at),
      like_and_view_counts_disabled: post.like_and_view_counts_disabled,
      tags: [], // TODO tag 需要手动从 caption 里提取
      username: post.owner.username,
      userId: post.owner.id,
    } as IgPost
  }
  private async get<T>(uri: string, options: object, collector?: RapidApiStatsCollector) {
    const url = `https://${this.HOST}/${uri}`
    const endpoint = url.split('?')[0]
    const ofetchOptions = {
      ...options,
      headers: {
        'x-rapidapi-key': this.API_KEY,
        'x-rapidapi-host': this.HOST,
      },
      retry: 3,
      retryDelay: 500,
    }

    let response: any
    let status: number = 0
    try {
      response = await ofetch.raw(url, ofetchOptions)
      const data = response._data
      status = response.status

      if (collector) {
        await collector.collect(Promise.resolve(data), uri.split('?')[0])
      }

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.SUCCESS, endpoint, status)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })
      return data as T
    } catch (err: any) {
      // 尝试从错误对象中获取状态码
      const errorStatus = err.response?.status || err.status || 0

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.ERROR, endpoint, errorStatus)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }
}
export default InstagramRapidApiV4

interface RelatedUserFull {
  friendship_status: null
  full_name: string
  is_verified: boolean
  pk: string
  profile_pic_url: string
  username: string
  is_private: boolean
  supervision_info: null
  social_context: string
  live_broadcast_visibility: null
  live_broadcast_id: null
  hd_profile_pic_url_info: null
  is_unpublished: null
  id: string
}

interface UserFull {
  friendship_status: {
    following: boolean
    blocking: boolean
    is_feed_favorite: boolean
    outgoing_request: boolean
    followed_by: boolean
    incoming_request: boolean
    is_restricted: boolean
    is_bestie: boolean
    muting: boolean
    is_muting_reel: boolean
  }
  gating: null
  is_memorialized: boolean
  is_private: boolean
  has_story_archive: null
  supervision_info: null
  is_regulated_c18: boolean
  regulated_news_in_locations: any[]
  bio_links: {
    image_url: string
    is_pinned: boolean
    link_type: string
    lynx_url: string
    media_type: string
    title: string
    url: string
  }[]
  text_post_app_badge_label: null
  show_text_post_app_badge: null
  username: string
  text_post_new_post_count: null
  pk: string
  live_broadcast_visibility: null
  live_broadcast_id: null
  profile_pic_url: string
  hd_profile_pic_url_info: {
    url: string
  }
  is_unpublished: boolean
  id: string
  latest_reel_media: number
  has_profile_pic: null
  profile_pic_genai_tool_info: any[]
  biography: string
  full_name: string
  is_verified: boolean
  show_account_transparency_details: boolean
  account_type: number
  follower_count: number
  mutual_followers_count: number
  profile_context_links_with_user_ids: any[]
  address_street: string
  city_name: string
  is_business: boolean
  zip: string
  biography_with_entities: {
    entities: {
      hashtag: null
      user: {
        username: string
        id: string
      }
    }[]
  }
  category: string
  should_show_category: boolean
  account_badges: any[]
  ai_agent_type: null
  fb_profile_bio_link_web: null
  external_lynx_url: string
  external_url: string
  pronouns: any[]
  transparency_label: null
  transparency_product: null
  has_chaining: boolean
  remove_message_entrypoint: boolean
  fbid_v2: string
  is_embeds_disabled: boolean
  is_professional_account: null
  following_count: number
  media_count: number
  total_clips_count: number
  latest_besties_reel_media: number
  reel_media_seen_timestamp: null
  email_from_biography: any[]
  phone_from_biography: any[]
}
interface IgAboutFull {
  date_joined: string
  verified_on: string
  creation_country: string
  active_ads: string
}

interface PostFull {
  code: string
  pk: string
  id: string
  ad_id: null
  boosted_status: null
  boost_unavailable_identifier: null
  boost_unavailable_reason: null
  caption: {
    has_translation: null
    created_at: number
    pk: string
    text: string
  }
  caption_is_edited: boolean
  feed_demotion_control: null
  feed_recs_demotion_control: null
  taken_at: number
  inventory_source: null
  video_versions:
    | {
        width: number
        height: number
        url: string
        type: number
      }[]
    | null
  is_dash_eligible: number
  number_of_qualities: number
  video_dash_manifest: string
  image_versions2: {
    candidates: {
      url: string
      height: number
      width: number
    }[]
  }
  sharing_friction_info: {
    bloks_app_url: null
    should_have_sharing_friction: boolean
  }
  is_paid_partnership: boolean
  sponsor_tags: null
  affiliate_info: null
  original_height: number
  original_width: number
  organic_tracking_token: string
  link: null
  story_cta: null
  user: {
    pk: string
    username: string
    profile_pic_url: string
    is_private: boolean
    is_embeds_disabled: boolean
    is_unpublished: boolean
    is_verified: boolean
    friendship_status: {
      following: boolean
      is_bestie: boolean
      is_feed_favorite: boolean
      is_restricted: boolean
    }
    latest_besties_reel_media: null
    latest_reel_media: number
    live_broadcast_visibility: null
    live_broadcast_id: null
    seen: null
    supervision_info: null
    id: string
    hd_profile_pic_url_info: {
      url: string
    }
    full_name: string
    __typename: string
  }
  group: null
  owner: {
    pk: string
    profile_pic_url: string
    username: string
    friendship_status: {
      is_feed_favorite: boolean
      following: boolean
      is_restricted: boolean
      is_bestie: boolean
    }
    is_embeds_disabled: boolean
    is_unpublished: boolean
    is_verified: boolean
    show_account_transparency_details: boolean
    supervision_info: null
    transparency_product: null
    transparency_product_enabled: boolean
    transparency_label: null
    id: string
    __typename: string
    is_private: boolean
  }
  coauthor_producers: any[]
  invited_coauthor_producers: any[]
  follow_hashtag_info: null
  title: null
  comment_count: number
  comments_disabled: null
  commenting_disabled_for_viewer: null
  like_and_view_counts_disabled: boolean
  has_liked: boolean
  top_likers: any[]
  facepile_top_likers: any[]
  like_count: number
  preview: null
  can_see_insights_as_brand: boolean
  social_context: any[]
  view_count: null
  can_reshare: null
  can_viewer_reshare: boolean
  ig_media_sharing_disabled: boolean
  photo_of_you: boolean
  product_type: string
  media_type: number
  usertags: {
    in: {
      user: {
        pk: string
        full_name: string
        username: string
        profile_pic_url: string
        is_verified: boolean
        id: string
      }
      position: number[]
    }[]
  }
  media_overlay_info: null
  carousel_parent_id: null
  carousel_media_count: null
  carousel_media: null
  location: {
    pk: string
    lat: number
    lng: number
    name: string
    profile_pic_url: null
  }
  has_audio: boolean
  clips_metadata: {
    audio_type: string
    achievements_info: {
      show_achievements: boolean
    }
    music_info: {
      music_consumption_info: {
        should_mute_audio: boolean
        should_mute_audio_reason: string
        is_trending_in_clips: boolean
      }
      music_asset_info: {
        audio_cluster_id: string
        title: string
        display_artist: string
        is_explicit: boolean
      }
    }
    original_sound_info: null
  }
  clips_attribution_info: null
  accessibility_caption: null
  audience: null
  display_uri: null
  media_cropping_info: {
    square_crop: null
    four_by_three_crop: {
      crop_bottom: number
      crop_left: number
      crop_right: number
      crop_top: number
    }
  }
  profile_grid_thumbnail_fitting_style: string
  thumbnails: {
    items: {
      url: string
      width: number
      height: number
    }[]
  } | null
  timeline_pinned_user_ids: any[]
  upcoming_event: null
  logging_info_token: null
  explore: null
  main_feed_carousel_starting_media_id: null
  is_seen: null
  open_carousel_submission_state: null
  previous_submitter: null
  all_previous_submitters: null
  headline: null
  comments: null
  fb_like_count: number
  saved_collection_ids: null
  has_viewer_saved: null
  media_level_comment_controls: null
  __typename: string
}
