import { describe, it } from 'vitest'
import { douyinV1Api } from './douyin.v1.js'

describe('douyin', () => {
  it('should get video detail', async () => {
    // 67f613c60000000009038e5b
    const videoId = '7327268882455072027'
    try {
      const videoDetail = await douyinV1Api.getVideoDetail(videoId)
      console.log(JSON.stringify(videoDetail, null, 2))
    } catch (err: unknown) {
      console.error(String(err))
    }
  }, 1000_000)
})
