import assert from 'assert'
import { ofetch } from 'ofetch'
import { DOUYIN_V1_HOST, DOUYIN_V1_TOKEN } from '../config/env'
import { DouyinCommonResponse, DouyinVideoDetailResponse } from '../types/douyin/rapid/douyin.v1'

async function get<T>(uri: string): Promise<T> {
  const url = `http://${DOUYIN_V1_HOST}/${uri}`
  const endpoint = url.split('?')[0]
  const options = {
    retry: 3,
    retryDelay: 500,
  }
  try {
    const response = await ofetch(url, options)
    return response as T
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    throw new Error(`抖音 API请求失败: ${errorMessage}`)
  }
}

async function getVideoDetail(
  videoId: string,
): Promise<DouyinCommonResponse<DouyinVideoDetailResponse>> {
  assert(videoId, new Error('videoId is required'))
  try {
    const uri = `api/douyin/get-video-detail/v2?token=${DOUYIN_V1_TOKEN}&videoId=${videoId}`
    const response = await get<DouyinCommonResponse<DouyinVideoDetailResponse>>(uri)
    return response
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    throw new Error(`抖音视频详情获取失败: ${errorMessage}`)
  }
}

export const douyinV1Api = {
  getVideoDetail,
}
