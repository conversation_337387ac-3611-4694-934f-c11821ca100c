import { Client } from 'youtubei'

class YoutubeiApi {
  private static instance: YoutubeiApi
  public client: Client
  public static getInstance(): YoutubeiApi {
    if (!YoutubeiApi.instance) {
      YoutubeiApi.instance = new YoutubeiApi(new Client())
    }
    return YoutubeiApi.instance
  }
  constructor(client: Client) {
    this.client = client
  }

  public async getChannelId(channelHandle: string): Promise<string | undefined> {
    const searchResult = await this.client.search(channelHandle, { type: 'channel' })
    let result = undefined
    if (searchResult.items.length > 0) {
      result = searchResult.items[0].id
    }
    return result
  }
}

export default YoutubeiApi
