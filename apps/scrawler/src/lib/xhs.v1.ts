import assert from 'assert'
import { ofetch } from 'ofetch'
import { XHS_V1_HOST, XHS_V1_TOKEN } from '../config/env'
import { XhsCommonResponse, XhsNoteDetailResponse } from '../types/xhs/rapid/xhs.v1'

async function get<T>(uri: string): Promise<T> {
  const url = `http://${XHS_V1_HOST}/${uri}`
  const endpoint = url.split('?')[0]
  const options = {
    retry: 3,
    retryDelay: 500,
  }
  try {
    const response = await ofetch(url, options)
    return response as T
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    throw new Error(`小红书 API请求失败: ${errorMessage}`)
  }
}

async function getNoteDetail(noteId: string): Promise<XhsCommonResponse<XhsNoteDetailResponse>> {
  assert(noteId, new Error('noteId is required'))
  try {
    const uri = `api/xiaohongshu/get-note-detail/v1?token=${XHS_V1_TOKEN}&noteId=${noteId}`
    const response = await get<XhsCommonResponse<XhsNoteDetailResponse>>(uri)
    return response
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    throw new Error(`小红书笔记详情获取失败: ${errorMessage}`)
  }
}

export const xhsV1Api = {
  getNoteDetail,
}
