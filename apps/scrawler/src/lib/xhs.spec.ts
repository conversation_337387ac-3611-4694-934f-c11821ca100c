import { describe, it } from 'vitest'
import { xhsV1Api } from './xhs.v1'

describe('xhs', () => {
  it('should get note detail', async () => {
    // 67f613c60000000009038e5b
    const noteId = '6805e69e000000001d025007'
    try {
      const noteDetail = await xhsV1Api.getNoteDetail(noteId)
      console.log(JSON.stringify(noteDetail, null, 2))
    } catch (err: unknown) {
      console.error(String(err))
    }
  }, 1000_000)
})
