import { IgPost, IgUser } from '@/api/@types/rapidapi/Instagram'
import InstagramApi from '@/api/instagram'
import Bluebird from 'bluebird'
import ExcelJS from 'exceljs'
import fs from 'fs'
import { describe, expect, it } from 'vitest'
import { userRequestStruct } from './instagram'
import InstagramRapidApiV3 from './instagramRapidApi.v3'

/****************************
 *                          *
 *  INSTAGRAM V3 RAPIDAPI   *
 *                          *
 ****************************/

describe('should test all instagram apis', () => {
  describe('Instagram V3 API Demo', () => {
    it(
      'should get related users and their posts',
      async () => {
        // 初始化 API 实例
        const api = InstagramRapidApiV3.getInstance()

        // 源用户
        const sourceUser: userRequestStruct = {
          username: 'toyotagram_iran', // 替换为实际的用户名
        }

        try {
          // 1. 获取相关用户
          console.log('获取相关用户...')
          const relatedUsers = await api.getUserRelatedUsers(sourceUser)
          console.log(`找到 ${relatedUsers.length} 个相关用户`)

          // 2. 获取每个用户的帖子
          const usersWithPosts: { user: IgUser; posts?: IgPost[] }[] = await Bluebird.map(
            relatedUsers,
            async (user) => {
              console.log(`获取用户 ${user.username} 的帖子...`)
              const posts = await api.getPosts({ username: user.username })
              console.log(`用户 ${user.username} 有 ${posts.length} 个帖子`)
              return {
                user,
                posts,
              }
            },
            { concurrency: 20 },
          )

          // 3. 打印结果
          console.log('\n结果统计:')
          console.log(`总共处理了 ${usersWithPosts.length} 个用户`)
        } catch (error) {
          console.error('测试失败:', error)
          throw error
        }
      },
      60 * 1000,
    ) // 设置超时时间为 60 秒

    it(
      'should get user posts',
      async () => {
        const api = InstagramRapidApiV3.getInstance()
        const posts = await api.getPosts({ username: 'toyotagram_iran' })
      },
      60 * 1000,
    )

    it(
      'should get user',
      async () => {
        const api = InstagramRapidApiV3.getInstance()
        const posts = await api.getUser({ username: 'toyotagram_iran' })
      },
      60 * 1000,
    )

    it(
      'should get user related users posts',
      async () => {
        const api = InstagramRapidApiV3.getInstance()
        const relatedUsers = await api.getUserRelatedUsers({ username: 'toyotagram_iran' })
        const firstUser = relatedUsers[0]
        const posts = await api.getPosts({ username: firstUser.username })
        firstUser.posts = posts
        console.log(firstUser)
      },
      60 * 1000,
    )

    it(
      'should get hash tag videos',
      async () => {
        const hashTagResponse = await InstagramApi.getInstance().getHashTagVideos('appletv')
        console.log(hashTagResponse)
        expect(hashTagResponse.total).toBeGreaterThan(0)
      },
      60 * 1000,
    )

    it(
      'should get quota',
      async () => {
        const api = InstagramRapidApiV3.getInstance()
        const quota = await api.getQuota()
        console.log(quota)
      },
      60 * 1000,
    )
  })
})

describe('should get multi page search results', () => {
  it(
    'should get 10 pages recent posts by time',
    async () => {
      const tag = 'insta360motorcycle'
      const options = {
        pagination_token: '',
        feed_type: 'recent' as const,
        url_embed_safe: false,
      }
      const posts: any[] = [
        // {
        //   id: '123',
        //   comment_count: 10,
        //   like_count: 10,
        //   play_count: 10,
        //   created_at: 1717536000,
        //   username: 'test',
        //   caption: { text: 'test' },
        //   like_and_view_counts_disabled: false,
        // },
      ]
      for (let i = 0; i < 10; i++) {
        console.log(`fetching page ${i}...`)
        const page = await InstagramRapidApiV3.getInstance().getHashTagVideos(tag, options)
        posts.push(...page.items)
        if (page.pagination_token) {
          options.pagination_token = page.pagination_token
        } else {
          console.log('no more pages')
          break
        }
      }
      console.log(posts.length)

      // 将原始数据写入 JSON 文件
      fs.writeFileSync(`./${tag}_posts.json`, JSON.stringify(posts, null, 2))

      // 准备 Excel 数据（不包含link字段，因为超链接需要单独设置）
      const excelData = posts.map((post) => ({
        code: post.code,
        created_at: new Date(post.caption.created_at * 1000), // 将时间戳转换为 Date 对象
        username: post.user?.username,
        caption: post.caption.text,
      }))

      // 使用 exceljs 创建新的工作簿
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Posts')

      // 设置列
      worksheet.columns = [
        { header: 'Code', key: 'code', width: 20 },
        {
          header: 'Created At',
          key: 'created_at',
          width: 20,
          style: {
            numFmt: 'yyyy-mm-dd hh:mm:ss', // 设置日期时间格式
          },
        },
        { header: 'Username', key: 'username', width: 20 },
        { header: 'Caption', key: 'caption', width: 50 },
        { header: 'Link', key: 'link', width: 50 },
      ]

      // 添加数据行
      worksheet.addRows(excelData)

      // 为Link列添加超链接
      posts.forEach((post, index) => {
        const row = index + 2 // 从第2行开始（第1行是标题）
        const linkUrl = `https://www.instagram.com/p/${post.code}/`

        // 设置超链接
        worksheet.getCell(`E${row}`).value = {
          text: linkUrl,
          hyperlink: linkUrl,
        }

        // 设置超链接样式（蓝色下划线）
        worksheet.getCell(`E${row}`).font = {
          color: { argb: 'FF0000FF' }, // 蓝色
          underline: true,
        }
      })

      // 保存文件
      await workbook.xlsx.writeFile(`./${tag}_posts.xlsx`)
    },
    1000 * 60 * 2,
  )
})
