import { TWITTER241_API_KEY } from '@/config/env'
import {
  Twitter241FollowingsResponse,
  Twitter241User,
  Twitter241UserResponse,
} from '@/types/twitter/rapid'
import axios, { AxiosInstance } from 'axios'

class Twitter241ApiClient {
  private static instance: Twitter241ApiClient
  private readonly apiKey: string
  private readonly baseUrl: string
  private readonly http: AxiosInstance

  private constructor() {
    this.apiKey = TWITTER241_API_KEY || ''
    this.baseUrl = 'https://twitter241.p.rapidapi.com'

    // 创建一个预配置好的axios实例
    this.http = axios.create({
      headers: {
        'x-rapidapi-key': this.apiKey,
        'x-rapidapi-host': 'twitter241.p.rapidapi.com',
      },
    })
  }

  static getInstance(): Twitter241ApiClient {
    if (!Twitter241ApiClient.instance) {
      Twitter241ApiClient.instance = new Twitter241ApiClient()
    }
    return Twitter241ApiClient.instance
  }

  /**
   * 获取用户的关注列表
   * @param userId 用户的 rest_id (数字ID)
   * @param count 每页数量，最大70
   * @param cursor 游标，用于分页
   * @returns 关注列表响应
   */
  async getFollowings(
    userId: string,
    count: number = 70,
    cursor?: string,
  ): Promise<Twitter241User[] | null> {
    try {
      const url = `${this.baseUrl}/followings`
      const params: Record<string, string | number> = {
        user: userId, // This should be the numeric rest_id
        count: Math.min(count, 70),
      }
      if (cursor) {
        params.cursor = cursor
      }

      const response = await this.http.get<Twitter241FollowingsResponse>(url, {
        params,
      })

      // Extract users from the nested response structure
      const users: Twitter241User[] = []
      const instructions = response.data?.result?.timeline?.instructions || []

      for (const instruction of instructions) {
        if (instruction.type === 'TimelineAddEntries' && instruction.entries) {
          for (const entry of instruction.entries) {
            const userResult = entry.content?.itemContent?.user_results?.result
            if (userResult && userResult.__typename === 'User') {
              // Use core data if available, otherwise fall back to legacy
              const user: Twitter241User = {
                id: userResult.id,
                rest_id: userResult.rest_id,
                name: userResult.core?.name || userResult.legacy.name,
                screen_name: userResult.core?.screen_name || userResult.legacy.screen_name,
                description: userResult.legacy.description,
                profile_image_url_https:
                  userResult.avatar?.image_url || userResult.legacy.profile_image_url_https,
                location: userResult.location?.location || userResult.legacy.location || '',
                created_at: userResult.core?.created_at || userResult.legacy.created_at,
                followers_count: userResult.legacy.followers_count,
                friends_count: userResult.legacy.friends_count,
                statuses_count: userResult.legacy.statuses_count,
                media_count: userResult.legacy.media_count,
                listed_count: userResult.legacy.listed_count,
                verified: userResult.verification?.verified ?? userResult.legacy.verified,
                is_blue_verified: userResult.is_blue_verified,
                profile_banner_url: userResult.legacy.profile_banner_url,
                pinned_tweet_ids_str: userResult.legacy.pinned_tweet_ids_str || [],
                is_protected: userResult.privacy?.protected || false,
              }
              users.push(user)
            }
          }
        }
      }

      console.log(`[Twitter241] 成功获取用户 ${userId} 的关注列表, 数量: ${users.length}`)

      // Store the cursor for pagination
      if (response.data?.cursor?.bottom) {
        (users as Twitter241User[] & { nextCursor?: string }).nextCursor =
          response.data.cursor.bottom
      }

      return users
    } catch (error) {
      console.error(
        `[Twitter241] 获取用户 ${userId} 的关注列表失败:`,
        (error as any).response?.data || (error as any).message,
      )
      return null
    }
  }

  /**
   * 获取用户的多页关注列表
   * @param userIdOrScreenName 用户ID(rest_id)或screen_name
   * @param targetCount 目标获取的用户总数
   * @param perPageCount 每页数量，最大100
   * @returns 所有关注的用户列表
   */
  async getFollowingsWithPagination(
    userIdOrScreenName: string,
    targetCount: number = 700,
    perPageCount: number = 100,
  ): Promise<Twitter241User[]> {
    // 如果输入不是纯数字，先获取用户信息
    let userId = userIdOrScreenName
    if (!/^\d+$/.test(userIdOrScreenName)) {
      const userInfo = await this.getUserByScreenName(userIdOrScreenName)
      if (!userInfo) {
        console.error(`[Twitter241] 无法获取用户 ${userIdOrScreenName} 的信息`)
        return []
      }
      userId = userInfo.rest_id
    }

    const allUsers: Twitter241User[] = []
    let cursor: string | undefined = undefined
    let currentPage = 0

    try {
      while (allUsers.length < targetCount) {
        const users = await this.getFollowings(userId, perPageCount, cursor)

        if (!users || users.length === 0) {
          console.log(`[Twitter241] 用户 ${userId} 没有更多关注列表，当前页: ${currentPage + 1}`)
          break
        }

        // 如果加上当前页会超过目标数量，只取需要的部分
        const remainingCount = targetCount - allUsers.length
        const usersToAdd = users.slice(0, remainingCount)
        allUsers.push(...usersToAdd)

        // Get the next cursor from the response
        cursor = (users as Twitter241User[] & { nextCursor?: string }).nextCursor
        currentPage++

        console.log(
          `[Twitter241] 用户 ${userId} 第 ${currentPage} 页获取到 ${users.length} 个用户，添加了 ${usersToAdd.length} 个，总计: ${allUsers.length}`,
        )

        // 如果已经达到目标数量，停止获取
        if (allUsers.length >= targetCount) {
          console.log(`[Twitter241] 已达到目标数量 ${targetCount}，停止获取`)
          break
        }

        // 如果没有下一页游标，停止获取
        if (!cursor) {
          console.log(`[Twitter241] 用户 ${userId} 关注列表已全部获取完毕`)
          break
        }

        // 添加延迟避免请求过快
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }

      console.log(`[Twitter241] 用户 ${userId} 最终获取到 ${allUsers.length} 个关注用户`)
      return allUsers
    } catch (error) {
      console.error(`[Twitter241] 获取用户 ${userId} 的分页关注列表失败:`, error)
      return allUsers
    }
  }

  /**
   * 通过screen_name获取用户信息
   * @param screenName 用户的screen_name
   * @returns 用户信息
   */
  async getUserByScreenName(screenName: string): Promise<Twitter241User | null> {
    try {
      const url = `${this.baseUrl}/user`
      const response = await this.http.get<Twitter241UserResponse>(url, {
        params: {
          username: screenName,
        },
      })

      console.log(`[Twitter241] 成功获取用户 ${screenName} 的信息`)

      // 从嵌套的响应结构中提取用户信息
      const userResult = response.data?.result?.data?.user?.result
      if (!userResult) {
        console.error(`[Twitter241] 用户 ${screenName} 的响应格式不正确`)
        return null
      }

      // 转换为简化的 Twitter241User 格式
      const user: Twitter241User = {
        id: userResult.id,
        rest_id: userResult.rest_id,
        name: userResult.core?.name || userResult.legacy.name,
        screen_name: userResult.core?.screen_name || userResult.legacy.screen_name,
        description: userResult.legacy.description,
        profile_image_url_https:
          userResult.avatar?.image_url || userResult.legacy.profile_image_url_https,
        location: userResult.location?.location || userResult.legacy.location || '',
        created_at: userResult.core?.created_at || userResult.legacy.created_at,
        followers_count: userResult.legacy.followers_count,
        friends_count: userResult.legacy.friends_count,
        statuses_count: userResult.legacy.statuses_count,
        media_count: userResult.legacy.media_count,
        listed_count: userResult.legacy.listed_count,
        verified: userResult.verification?.verified ?? userResult.legacy.verified,
        is_blue_verified: userResult.is_blue_verified,
        profile_banner_url: userResult.legacy.profile_banner_url,
        pinned_tweet_ids_str: userResult.legacy.pinned_tweet_ids_str || [],
        is_protected: userResult.privacy?.protected || false,
      }

      return user
    } catch (error) {
      console.error(
        `[Twitter241] 获取用户 ${screenName} 的信息失败:`,
        (error as any).response?.data || (error as any).message,
      )
      return null
    }
  }
}

export const twitter241Api = Twitter241ApiClient.getInstance()
