import {
  IgComment,
  IgCommentResponse,
  IgFollower,
  IgFollowersResponse,
  IgFollowing,
  IgFollowingsResponse,
  IgHashTagVideosResponse,
  IgLikesResponse,
  IgPost,
  IgPostDetail,
  IgPostsResponse,
  IgReel,
  IgReelsResponse,
  IgTaggedUser,
  IgTaggedUsersResponse,
  IgUser,
  PostType,
  igComment,
  igFollower,
  igFollowing,
  igLike,
  igPost,
  igPostDetail,
  igReel,
  igTaggedUser,
  igUser,
} from '@/api/@types/rapidapi/Instagram'
import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { parsePathParams } from '@/api/util'
import {
  IG_AUTH_MODE,
  IG_RAPID_API_HOST,
  IG_RAPID_API_KEY,
  INS_RAPID_API_HOST,
  INS_RAPID_AUTH_TOKEN,
} from '@/config/env'
import Sentry from '@/infras/sentry'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { KolPlatform } from '@repo/database'
import dayjs from 'dayjs'
import { ofetch } from 'ofetch'
import { InstagramApi, userRequestStruct } from './instagram'

// 认证模式类型
export enum IgAuthMode {
  BEARER_TOKEN = 'rapid_bearer',
  RAPID_API_KEY = 'rapid_api_key',
}

/**
 * 更新一点，但是并发和价格和 V1 一样
 * https://rapidapi.com/social-api1-instagram/api/instagram-scraper-api2/playground/apiendpoint_b1301387-dc09-4b1f-ba39-b7b51d186b40
 * */
class InstagramRapidApiV3 implements InstagramApi {
  private static instance: InstagramRapidApiV3

  // RapidAPI密钥配置
  RAPIDAPI_HOST = IG_RAPID_API_HOST || 'instagram-scraper-20252.p.rapidapi.com'
  RAPIDAPI_KEY = IG_RAPID_API_KEY || ''

  // Bearer令牌配置
  BEARER_HOST = INS_RAPID_API_HOST || 'api.socialapi.io/ig'
  BEARER_KEY = INS_RAPID_AUTH_TOKEN || ''

  get HOST(): string {
    return this.getCurrentHost()
  }

  get API_KEY(): string {
    return this.getCurrentKey()
  }

  VERSION = 'v3'

  AUTH_MODE = IG_AUTH_MODE as IgAuthMode

  public static getInstance(): InstagramRapidApiV3 {
    if (!InstagramRapidApiV3.instance) {
      InstagramRapidApiV3.instance = new InstagramRapidApiV3()
    }

    return InstagramRapidApiV3.instance
  }

  public setAuthMode(mode: IgAuthMode): void {
    this.AUTH_MODE = mode
    console.log(`[Instagram API] 认证模式已切换为: ${mode}`)
  }

  public getAuthMode(): IgAuthMode {
    return this.AUTH_MODE
  }

  private getCurrentHost(): string {
    return this.AUTH_MODE === IgAuthMode.BEARER_TOKEN ? this.BEARER_HOST : this.RAPIDAPI_HOST
  }

  private getCurrentKey(): string {
    return this.AUTH_MODE === IgAuthMode.BEARER_TOKEN ? this.BEARER_KEY : this.RAPIDAPI_KEY
  }

  private async fulfillUser(user: userRequestStruct): Promise<userRequestStruct> {
    // 要耗费一次请求获取完整的 user 信息，尽量避免使用
    await this.getUser(user)
    return user
  }
  public async getUserWithPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const fullUser = await this.getUser(user, collector)
    if (!fullUser?.id) {
      throw new Error('user not found' + user)
    }
    try {
      fullUser.posts = await this.getPosts(user, collector)
      fullUser.lastPublishedTime =
        fullUser.posts && fullUser.posts.length ? fullUser.posts[0].created_at : dayjs().unix()
    } catch (error) {
      console.log(`fail get user posts`)
      Sentry.captureException(error)
      fullUser.posts = []
    }
    return fullUser
  }

  public async getUserRelatedUsers(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser[]> {
    const idOrName = user.id ?? user.username
    const users = (
      await this.get<{ data: { count: number; items: any[] } }>(
        `v1/similar_accounts?username_or_id_or_url=${idOrName}`,
        collector,
      )
    ).data
    console.log(`get ${users?.items?.length ?? 0} related users for ${user.username} by v3`)
    return users?.items?.map((i) => igUser(i)).filter((user): user is IgUser => user !== null) ?? []
  }

  public async getUser(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const idOrName = user.id ?? user.username
    const fullUser = await this.get<{ data: any }>(
      `v1/info?url_embed_safe=true&include_about=true&username_or_id_or_url=${idOrName}`,
      collector,
    )
    if (!user.id) {
      user.id = fullUser.data.id
    }
    if (!user.username) {
      user.username = fullUser.data.username
    }
    return igUser(fullUser.data)
  }

  public async getUserWithoutAbout(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const idOrName = user.id ?? user.username
    const fullUser = await this.get<{ data: any }>(
      `v1/info?username_or_id_or_url=${idOrName}`,
      collector,
    )
    if (!user.id) {
      user.id = fullUser.data.id
    }
    if (!user.username) {
      user.username = fullUser.data.username
    }
    return igUser(fullUser.data)
  }

  public async getPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost[]> {
    try {
      const idOrName = user.id ?? user.username
      const res = await this.get<{ data: { items: any[] } }>(
        `v1.2/posts?url_embed_safe=true&username_or_id_or_url=${idOrName}`,
        collector,
      )
      return res.data.items.map((i) => igPost(i))
    } catch (err) {
      console.log(err)
      Sentry.captureException(err)
      return []
    }
  }

  public async getPost(
    postId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost | undefined> {
    try {
      const resp = (
        await this.get<any>(
          `v1/post_info?include_insights=true&url_embed_safe=true&code_or_id_or_url=${postId}`,
          collector,
        )
      ).data
      return {
        id: resp.code,
        type: resp.media_name as PostType,
        thumbnail_url: resp?.thumbnail_url ?? '',
        comment_count: Number(resp?.metrics?.comment_count ?? 0),
        like_count: Number(resp?.metrics?.like_count ?? 0),
        play_count: Number(resp?.metrics?.play_count ?? 0),
        caption: {
          text: resp?.caption?.text ?? '',
        },
        created_at: Number(resp?.taken_at ?? resp?.caption?.created_at ?? 0),
        like_and_view_counts_disabled: resp?.like_and_view_counts_disabled ?? false,
        tags: resp?.caption?.hashtags ?? [],
        username: resp.user?.username ?? '',
        userId: resp?.user?.id ?? '',
      } as IgPost
    } catch (err) {
      console.log(`[instagram rapidapi v1]failed get ig post ${postId} detail: ${err}`)
      Sentry.captureException(err)
      return undefined
    }
  }

  /**
   * 获取Instagram帖子的评论
   * @param postId 帖子ID、代码或URL
   * @param options 可选参数，包括分页令牌和排序方式
   * @param collector API调用统计收集器
   * @returns 评论响应数据
   */
  public async getPostComments(
    psotId: string,
    options?: {
      pagination_token?: string
      sort_by?: 'recent' | 'popular'
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgCommentResponse> {
    try {
      console.log(`[instagram rapidapi v3] 尝试获取帖子评论，原始ID: ${psotId}`)

      const queryParams = [`code_or_id_or_url=${psotId}`]

      if (options) {
        if (options.pagination_token) {
          queryParams.push(`pagination_token=${options.pagination_token}`)
        }
        if (options.sort_by) {
          queryParams.push(`sort_by=${options.sort_by}`)
        }
      }
      const queryString = queryParams.join('&')
      // 发起API请求
      const response = await this.get<{
        data?: { items?: any[]; total?: number; pagination_token?: string }
      }>(`v1/comments?${queryString}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取帖子 ${psotId} 的评论失败: 无响应数据`)
        return { count: 0, total: 0, comments: [] }
      }

      // 处理响应数据
      const { data, pagination_token } = response as any
      const comments: IgComment[] = (data.items || []).map((item: any) => igComment(item))

      console.log(`[instagram rapidapi v3] 成功获取帖子 ${psotId} 的评论: ${comments.length} 条`)
      return {
        count: data.length,
        total: data.total || 0,
        pagination_token: pagination_token || undefined,
        comments,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取帖子 ${psotId} 的评论失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        total: 0,
        comments: [],
      }
    }
  }

  /**
   * 获取Instagram用户的Reels视频
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌和URL嵌入安全选项
   * @param collector API调用统计收集器
   * @returns Reels响应数据
   */
  public async getReels(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgReelsResponse> {
    const idOrName = user.id ?? user.username
    try {
      // 构建查询参数
      const params = { username_or_id_or_url: idOrName, ...(options ? options : {}) }

      // 发起API请求
      const response = await this.get<{
        data?: { count?: number; items?: any[]; pagination_token?: string }
      }>(`v1/reels?${parsePathParams(params)}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的Reels失败: 无响应数据`)
        return { count: 0, total: 0, reels: [] }
      }

      // 处理响应数据
      const { data } = response
      const reels: IgReel[] = (data.items || []).map((item: any) => igReel(item))

      return {
        count: reels.length,
        total: data.count || 0,
        pagination_token: data.pagination_token || undefined,
        reels,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的Reels失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        total: 0,
        reels: [],
      }
    }
  }

  /**
   * 获取Instagram用户的帖子（详细信息）
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌和URL嵌入安全选项
   * @param collector API调用统计收集器
   * @returns 帖子响应数据
   */
  public async getUserPosts(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgPostsResponse> {
    const idOrName = user.id ?? user.username
    try {
      const params = { username_or_id_or_url: idOrName, ...(options ? options : {}) }

      const response = await this.get<{
        data?: { count?: number; items?: any[]; pagination_token?: string }
      }>(`v1/posts?${parsePathParams(params)}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的帖子失败: 无响应数据`)
        return { count: 0, total: 0, posts: [] }
      }

      // 处理响应数据
      const { data } = response
      const posts: IgPostDetail[] = (data.items || []).map((item: any) => igPostDetail(item))

      return {
        count: posts.length,
        total: data.count || 0,
        pagination_token: data.pagination_token || undefined,
        posts,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的帖子失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        total: 0,
        posts: [],
      }
    }
  }

  /**
   * 获取Instagram用户的粉丝列表
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌
   * @param collector API调用统计收集器
   * @returns 粉丝响应数据
   */
  public async getFollowers(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowersResponse> {
    const idOrName = user.id ?? user.username
    try {
      // 构建查询参数
      const params = { username_or_id_or_url: idOrName, ...(options ? options : {}) }
      // 发起API请求
      const response = await this.get<{
        data?: { count?: number; items?: any[]; pagination_token?: string }
      }>(`v1/followers?${parsePathParams(params)}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的粉丝列表失败: 无响应数据`)
        return { count: 0, total: 0, followers: [] }
      }

      // 处理响应数据
      const { data, pagination_token } = response as any
      const followers: IgFollower[] = (data.items || []).map((item: any) => igFollower(item))

      console.log(
        `[instagram rapidapi v3] 成功获取用户 ${idOrName} 的粉丝列表: ${followers.length} 个粉丝`,
      )

      return {
        count: followers.length,
        total: data.count || 0,
        pagination_token: pagination_token || undefined,
        followers,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的粉丝列表失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        total: 0,
        followers: [],
      }
    }
  }

  /**
   * 获取Instagram帖子的点赞列表
   * @param postId 帖子ID、代码或URL
   * @param collector API调用统计收集器
   * @returns 点赞响应数据
   */
  public async getLikes(
    postId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgLikesResponse> {
    try {
      console.log(`[instagram rapidapi v3] 尝试获取帖子点赞列表，原始ID: ${postId}`)

      const response = await this.get<{
        data?: {
          count?: number
          items?: any[]
          total?: number
        }
      }>(`v1/likes?code_or_id_or_url=${postId}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取帖子 ${postId} 的点赞列表失败: 无响应数据`)
        return { count: 0, total: 0, likes: [] }
      }

      const { data } = response
      const likes = (data.items || []).map((item: any) => igLike(item))

      console.log(
        `[instagram rapidapi v3] 成功获取帖子 ${postId} 的点赞列表: ${likes.length} 个点赞`,
      )
      return {
        count: likes.length,
        total: data.total || 0,
        likes,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取帖子 ${postId} 的点赞列表失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        total: 0,
        likes: [],
      }
    }
  }

  /**
   * 获取Instagram标签信息和相关帖子
   * @param hashtag 标签名（不包含#号）
   * @param options 可选参数，包括分页令牌、动态类型和URL嵌入安全选项
   * @param collector API调用统计收集器
   * @returns 标签信息和相关帖子
   */
  public async getHashTagVideos(
    hashtag: string,
    options?: {
      pagination_token?: string
      feed_type?: 'top' | 'recent' | 'clips'
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgHashTagVideosResponse> {
    try {
      // 构建请求参数
      const params = { hashtag: hashtag, ...(options ? options : {}) }

      const response = await this.get<{
        data: {
          additional_data: any
          count: number
          items: any[]
          total: number
        }
        pagination_token?: string
      }>(`v1/hashtag?${parsePathParams(params)}`, collector)

      if (response && response.data) {
        return {
          additional_data: response.data.additional_data || {},
          count: response.data.count || 0,
          items: response.data.items || [],
          total: response.data.total || 0,
          pagination_token: response.pagination_token,
        }
      }

      return {
        additional_data: {},
        count: 0,
        items: [],
        total: 0,
        pagination_token: undefined,
      }
    } catch (error) {
      console.error(`获取Instagram标签 #${hashtag} 信息失败:`, error)
      Sentry.captureException(error)

      return {
        additional_data: {},
        count: 0,
        items: [],
        total: 0,
        pagination_token: undefined,
      }
    }
  }

  /**
   * 获取Instagram用户的关注列表
   * @param user 用户信息（用户名、ID或URL）
   * @param options 可选参数，包括分页令牌和获取数量
   * @param collector API调用统计收集器
   * @returns 关注列表响应数据
   */
  public async getFollowings(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      amount?: number
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowingsResponse> {
    const idOrName = user.id ?? user.username
    try {
      const params = {
        username_or_id_or_url: idOrName,
        ...(options ? options : {}),
      }

      const response = await this.get<{
        data?: { count?: number; items?: any[]; pagination_token?: string }
      }>(`v1/following?${parsePathParams(params)}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的关注列表失败: 无响应数据`)
        return { count: 0, followings: [] }
      }

      const { data, pagination_token } = response as any
      const followings: IgFollowing[] = (data.items || []).map((item: any) => igFollowing(item))

      console.log(
        `[instagram rapidapi v3] 成功获取用户 ${idOrName} 的关注列表: ${followings.length} 个关注`,
      )

      return {
        count: data.count || followings.length || 0,
        pagination_token: pagination_token || undefined,
        followings,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取用户 ${idOrName} 的关注列表失败: ${err}`)
      Sentry.captureException(err)
      return {
        count: 0,
        followings: [],
      }
    }
  }

  /**
   * 获取Instagram被tagged的用户帖子
   * @param username 要查询的用户名
   * @param options 可选参数，包括分页令牌
   * @param collector API调用统计收集器
   * @returns 被tagged的用户列表
   */
  public async getTaggedUsers(
    username: string,
    options?: {
      pagination_token?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgTaggedUsersResponse> {
    try {
      const params = { username_or_id_or_url: username, ...(options ? options : {}) }

      const response = await this.get<{
        data: { count?: number; items?: any[]; total?: number }
        pagination_token?: string
      }>(`v1/tagged?${parsePathParams(params)}`, collector)

      if (!response || !response.data) {
        console.log(`[instagram rapidapi v3] 获取用户 ${username} 的被标记用户失败: 无响应数据`)
        return {
          total: 0,
          count: 0,
          items: [],
          pagination_token: undefined,
        }
      }

      const { data, pagination_token } = response as any

      const allUsers: IgTaggedUser[] = []

      if (data.items && data.items.length > 0) {
        data.items.forEach((item: any) => {
          // if (item.tagged_users && item.tagged_users.length > 0) {
          //   item.tagged_users.forEach((taggedUser: any) => {
          //     if (taggedUser.user) {
          //       allUsers.push(igTaggedUser(taggedUser.user))
          //     }
          //   })
          // }

          if (item.user) {
            allUsers.push(igTaggedUser(item.user))
          }
        })
      }

      const uniqueUsers = Array.from(new Map(allUsers.map((user) => [user.id, user])).values())

      console.log(
        `[instagram rapidapi v3] 成功获取用户 ${username} 的相关用户: ${uniqueUsers.length} 个用户（包括标记者和被标记者）`,
      )

      return {
        count: data.count || 0,
        total: data.total || 0,
        items: uniqueUsers,
        pagination_token: pagination_token,
      }
    } catch (err) {
      console.log(`[instagram rapidapi v3] 获取用户 ${username} 的被标记用户失败: ${err}`)
      Sentry.captureException(err)
      return {
        total: 0,
        count: 0,
        items: [],
        pagination_token: undefined,
      }
    }
  }

  public async getQuota(): Promise<{
    used: number
    total: number
  }> {
    const response = await this.get<{
      used: number
      total: number
    }>(`v1/quota`)
    return {
      used: response.used || 0,
      total: response.total || 0,
    }
  }

  private async get<T>(uri: string, collector?: RapidApiStatsCollector) {
    const host = this.getCurrentHost()
    const key = this.getCurrentKey()
    const url = `https://${host}/${uri}`
    const endpoint = url.split('?')[0]

    const options: any = {
      headers: {},
      retry: 3,
      retryDelay: 500,
    }

    if (this.AUTH_MODE === IgAuthMode.BEARER_TOKEN) {
      // Bearer令牌认证方式
      options.headers = {
        authorization: `Bearer ${key}`,
      }
    } else {
      // RapidAPI密钥认证方式
      options.headers = {
        'x-rapidapi-host': host,
        'x-rapidapi-key': key,
      }
    }

    let response: any
    let status: number = 0
    try {
      // 使用 ofetch.raw 获取完整的 response 对象
      response = await ofetch.raw(url, options)

      const data = response._data
      status = response.status

      if (collector) {
        await collector.collect(Promise.resolve(data), uri.split('?')[0])
      }

      // 记录成功的 API 调用
      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.SUCCESS, endpoint, status)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })

      return data as T
    } catch (err: any) {
      // 尝试从错误对象中获取状态码
      const errorStatus = err.response?.status || err.status || 0

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.ERROR, endpoint, errorStatus)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }
}
export default InstagramRapidApiV3
