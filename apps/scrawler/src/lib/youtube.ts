import {
  Channel,
  ChannelVideo,
  CommentResponse,
  RelatedVideo,
  SearchVideoResult,
  VideoDetail,
  YoutubeHashTagBreakRes,
} from '@/api/@types/rapidapi/Youtube'
import RapidApiStatsCollector from '@/api/ApiCallStat'

export interface YoutubeInterface {
  getChannel(channelId: string, collector?: RapidApiStatsCollector): Promise<Channel>

  getChannelVideos(
    channelIdOrName: string,
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]>

  getVideoRelatedVideos(
    videoId: string,
    count: number,
    collector?: RapidApiStatsCollector,
  ): Promise<RelatedVideo[]>

  getChannelId(channelIdOrName: string, collector?: RapidApiStatsCollector): Promise<string>

  searchVideo(
    keyword: string,
    options: VideoSearchOptions,
    collector?: RapidApiStatsCollector,
  ): Promise<SearchVideoResult>

  getVideo(videoId: string, collector?: RapidApiStatsCollector): Promise<VideoDetail | undefined>

  getChannelShorts(
    idOrName: {
      channelId?: string
      forUsername?: string
    },
    options?: {
      sort_by?: string
      token?: string
      geo?: string
      lang?: string
      local?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]>

  getVideoComments(
    videoId: string,
    options?: {
      token?: string
      sort_by?: 'newest' | 'top'
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<CommentResponse>

  getHashtag(
    hashtag: string,
    options: {
      token?: string
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakRes>
}

export interface VideoSearchOptions {
  token?: string
  geo?: string
  lang?: string
  duration?: string
  upload_date?: string
  sort_by?: string
}
