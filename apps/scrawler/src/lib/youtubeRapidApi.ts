import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import {
  Channel,
  ChannelVideo,
  CommentResponse,
  RelatedVideo,
  ResolveResponse,
  SearchVideoResult,
  Thumbnail,
  VideoDetail,
  YoutubeHashTagBreakRes,
} from '@/api/@types/rapidapi/Youtube'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { logFilter, parsePathParams } from '@/api/util'
import { Y2B_RAPID_API_KEY, YOUTUBE_USER_VIDEO_COUNT } from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { parseShortedNumber } from '@/utils/youtube'
import { KolPlatform } from '@repo/database'
import axios from 'axios'
import axiosRetry from 'axios-retry'
import countries from 'i18n-iso-countries'
import { VideoSearchOptions, YoutubeInterface } from './youtube'

// 配置 axios-retry
const axiosInstance = axios.create()
axiosRetry(axiosInstance, {
  retries: 3,
  retryDelay: (_retryCount) => {
    return 1_000 // 重试延迟1s
  },
})

class YoutubeRapidApi implements YoutubeInterface {
  private static instance: YoutubeRapidApi

  private HOST = 'yt-api.p.rapidapi.com'
  private API_KEY = Y2B_RAPID_API_KEY

  public static getInstance(): YoutubeRapidApi {
    if (!YoutubeRapidApi.instance) {
      YoutubeRapidApi.instance = new YoutubeRapidApi()
    }

    return YoutubeRapidApi.instance
  }

  public async getChannel(channelId: string, collector?: RapidApiStatsCollector) {
    const channel = await this.get<Channel>('channel/about?id=' + channelId, {}, collector)
    if (channel?.country && channel.country.length > 2) {
      channel.country = countries.getAlpha2Code(channel.country, 'en') ?? ''
    }
    const email = extractEmail(channel.description)
    if (email) {
      channel.email = email
      channel.emailSource = EmailSourceType.BIO_EMAIL
    }
    return channel
  }

  public async getChannelVideos(
    channelIdOrName: string,
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]> {
    let videos: any = []
    if (channelIdOrName.startsWith('@')) {
      videos = await this.get<any>('channel/videos?forUsername=' + channelIdOrName, {}, collector)
    } else {
      videos = await this.get<any>('channel/videos?id=' + channelIdOrName, {}, collector)
    }
    if (videos.msg) {
      console.log('[rapidapi]get channel failed: ' + videos.msg)
    }
    return (videos.data || [])
      .filter((v: any) => v.type === 'video' && v.publishedTimeText !== null)
      .slice(0, YOUTUBE_USER_VIDEO_COUNT)
  }

  public async getVideoRelatedVideos(
    videoId: string,
    count: number,
    collector?: RapidApiStatsCollector,
  ): Promise<RelatedVideo[]> {
    let token = undefined
    const videos: RelatedVideo[] = []
    let times = 1
    let error: any = undefined
    while (videos.length < count && !error) {
      try {
        const resp: { continuation: string; data: RelatedVideo[] } = await this.get<{
          continuation: string
          data: RelatedVideo[]
        }>(`related?${parsePathParams({ id: videoId })}`, { 'X-TOKEN': token }, collector)
        videos.push(...resp.data.filter((v: RelatedVideo) => v.type === 'video'))
        times += 1
        token = resp.continuation
      } catch (err) {
        error = err
        break
      }
    }
    console.log(`get ${videos.length} videos for ${times} requests from video ${videoId}`)
    if (error) {
      console.error(`end with error: `)
      console.error(error?.data ?? JSON.stringify(error))
    }
    return videos
  }

  public async getChannelId(channelIdOrName: string, collector?: RapidApiStatsCollector) {
    if (channelIdOrName.startsWith('UC')) {
      return channelIdOrName
    }
    channelIdOrName = channelIdOrName.split('?')[0].split('#')[0] // 去除参数和锚点
    const url = `https://www.youtube.com/${channelIdOrName.startsWith('@') ? channelIdOrName : `@${channelIdOrName}`}`
    const resp = await this.get<ResolveResponse>(
      `resolve?url=${encodeURIComponent(url)}`,
      {},
      collector,
    )
    return resp.browseId
  }

  public async searchVideo(
    query: string,
    options: VideoSearchOptions,
    collector?: RapidApiStatsCollector,
  ): Promise<SearchVideoResult> {
    const headers = {
      'X-TOKEN': options.token ?? undefined,
    }
    const params = { ...options, type: 'video', token: undefined, query: query }
    console.log('headers', headers)
    console.log('params', parsePathParams(params))
    const resp = await this.get<any>(`search?${parsePathParams(params)}`, headers, collector)
    const token = resp.continuation
    console.log('token', token)
    const list = resp.data
    let videos = logFilter(list, (v: any) => v.type == 'video' && v.videoId)
    videos = videos.map((video: any) => {
      return {
        type: video.type,
        videoId: video.videoId,
        title: video.title,
        lengthText: video.lengthText,
        viewCount: video.viewCount,
        publishedTimeText: video.publishedTimeText,
        thumbnail: video.thumbnail,
        channelTitle: video.channelTitle,
        channelId: video.channelId,
        authorThumbnail: video.channelThumbnail,
        channelHandle: video.channelHandle,
      }
    })
    return {
      token,
      count: Number(resp.estimatedResults),
      data: videos,
      msg: resp.msg,
    }
  }

  public async getVideo(
    videoId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<VideoDetail | undefined> {
    try {
      const resp = await this.get<any>(`shorts/info?id=${videoId}`, {}, collector)
      return {
        type: 'video',
        videoId: resp.videoId,
        title: resp.title,
        lengthText: '',
        description: resp.description,
        viewCount: resp.viewCount ?? 0,
        publishedTimeText: resp.publishedTimeText ?? '',
        publishedDate: resp.publishDate ?? '',
        thumbnail: (resp.thumbnail as Thumbnail[]) ?? [],
        channelTitle: resp.channelTitle ?? '',
        channelId: resp.channelId,
        channelHandle: resp.channelHandle ?? '',
        commentCount: resp.commentCount ?? '0',
        likeCount: resp.likeCount ?? '0',
      } as VideoDetail
    } catch (err) {
      console.log(`failed geting video ${videoId}: ${err}`)
      Sentry.captureException(err)
      return undefined
    }
  }

  public async getSpecificChannelVideos(
    idOrName: {
      channelId?: string
      forUsername?: string
    },
    options?: {
      sort_by?: 'newest' | 'popular' | 'oldest'
      token?: string
      geo?: string
      lang?: string
      local?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]> {
    try {
      const queryParams: string[] = []
      if (idOrName.channelId) {
        queryParams.push(`id=${idOrName.channelId}`)
      } else if (idOrName.forUsername) {
        queryParams.push(`forUsername=${idOrName.forUsername}`)
      } else {
        throw new Error('Invalid channel ID or username')
      }

      if (options) {
        if (options.sort_by) queryParams.push(`sort_by=${options.sort_by}`)
        if (options.token) queryParams.push(`token=${options.token}`)
        if (options.geo) queryParams.push(`geo=${options.geo.toUpperCase()}`)
        if (options.lang) queryParams.push(`lang=${options.lang}`)
        if (options.local) queryParams.push(`local=${options.local}`)
      }

      const queryString = queryParams.join('&')
      const response = await this.get<{
        continuation: string
        data: any[]
        msg: string
      }>(`channel/videos?${queryString}`, {}, collector)

      if (response.msg) {
        console.log(`[rapidapi]get channel videos failed: ${response.msg}`)
      }

      // 转换视频数据为ChannelVideo类型
      return (response.data || [])
        .filter((v: any) => v.type === 'video' && v.publishedTimeText !== null)
        .map((video: any): ChannelVideo => {
          return {
            type: 'video',
            videoId: video.videoId,
            title: video.title,
            lengthText: video.lengthText || '',
            viewCount: video.viewCount || '0',
            publishedTimeText: video.publishedTimeText || '',
            publishedDate: video.publishDate || '',
            thumbnail: video.thumbnail || [],
            description: video.description || '',
            richThumbnail: video.richThumbnail || [],
            channelId: video.channelId || '',
          }
        })
    } catch (err) {
      console.error(`Failed to get channel videos from meta: ${err}`)
      Sentry.captureException(err)
      return []
    }
  }

  public async getHashtag(
    hashtag: string,
    options?: {
      token?: string
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakRes> {
    const query = {
      tag: hashtag,
      geo: options?.geo ?? undefined,
      lang: options?.lang ?? undefined,
    }
    const result = (await this.get(
      `hashtag?${parsePathParams(query)}`,
      { 'X-TOKEN': options?.token ?? undefined },
      collector,
    )) as any
    let videoCount = 0
    let channelCount = 0
    const countText = result?.meta?.hashtagInfoText
    if (countText) {
      const countTextParts = countText.split(' ')
      if (countTextParts[0] == '<') {
        // 不到 100 个特殊处理
        videoCount = 100
        channelCount = 0
      } else {
        if (countTextParts.length > 0) {
          videoCount = parseShortedNumber(countTextParts[0])
        }
        if (countTextParts.length > 4) {
          channelCount = parseShortedNumber(countTextParts[3])
        }
      }
    }
    return {
      meta: {
        hashtag: hashtag,
        videoCount,
        channelCount,
      },
      data: result.data.map((i: any) => {
        return {
          videoId: i.videoId,
          title: i.title,
          channelTitle: i.channelTitle,
          channelId: i.channelId,
          description: i.description,
          viewCount: Number(i.viewCount),
          publishedText: i.publishedText,
          lengthText: i.lengthText,
          thumbnail: i.thumbnail,
          richThumbnail: i.richThumbnail,
          channelThumbnail: i.channelThumbnail,
        }
      }),
      continuation: result.continuation,
    } as YoutubeHashTagBreakRes
  }

  public async getChannelShorts(
    idOrName: {
      channelId?: string
      forUsername?: string
    },
    options?: {
      sort_by?: 'newest' | 'popular' | 'oldest'
      token?: string
      geo?: string
      lang?: string
      local?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]> {
    try {
      const queryParams: string[] = []
      if (idOrName.channelId) {
        queryParams.push(`id=${idOrName.channelId}`)
      } else if (idOrName.forUsername) {
        queryParams.push(`forUsername=${idOrName.forUsername}`)
      } else {
        throw new Error('无效的频道ID或用户名')
      }

      if (options) {
        if (options.sort_by) queryParams.push(`sort_by=${options.sort_by}`)
        if (options.token) queryParams.push(`token=${options.token}`)
        if (options.geo) queryParams.push(`geo=${options.geo.toUpperCase()}`)
        if (options.lang) queryParams.push(`lang=${options.lang}`)
        if (options.local) queryParams.push(`local=${options.local}`)
      }

      const queryString = queryParams.join('&')
      const response = await this.get<{
        continuation: string
        data: any[]
        msg: string
      }>(`channel/shorts?${queryString}`, {}, collector)

      if (response.msg) {
        console.log(`[rapidapi]获取频道shorts失败: ${response.msg}`)
      }

      // 转换视频数据为ChannelVideo类型
      return (response.data || [])
        .filter((v: any) => v.type === 'shorts' && v.publishedTimeText !== null)
        .map((video: any): ChannelVideo => {
          return {
            type: 'video',
            videoId: video.videoId,
            title: video.title,
            lengthText: video.lengthText || '',
            viewCount: video.viewCountText || '0',
            publishedTimeText: video.publishedTimeText || '',
            publishedDate: video.publishDate || '',
            thumbnail: video.thumbnail || [],
            description: video.description || '',
            richThumbnail: video.richThumbnail || [],
            channelId: video.channelId || '',
          }
        })
    } catch (err) {
      console.error(`获取频道shorts失败: ${err}`)
      Sentry.captureException(err)
      return []
    }
  }

  /**
   * 获取视频评论
   * @param videoId 视频ID
   * @param options 可选参数，包括分页令牌、排序方式、地区和语言
   * @param collector API调用统计收集器
   * @returns 评论响应数据
   */
  public async getVideoComments(
    videoId: string,
    options?: {
      token?: string
      sort_by?: 'newest' | 'top'
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<CommentResponse> {
    try {
      const queryParams: string[] = [`id=${videoId}`]

      if (options) {
        if (options.sort_by) queryParams.push(`sort_by=${options.sort_by}`)
        if (options.geo) queryParams.push(`geo=${options.geo.toUpperCase()}`)
        if (options.lang) queryParams.push(`lang=${options.lang}`)
      }

      const queryString = queryParams.join('&')
      const headers: Record<string, string> = {}

      // 如果提供了token，优先使用X-TOKEN头部
      if (options?.token) {
        headers['X-TOKEN'] = options.token
      }

      const response = await this.get<CommentResponse>(
        `comments?${queryString}`,
        headers,
        collector,
      )

      if (response.msg) {
        console.log(`[rapidapi]获取视频评论失败: ${response.msg}`)
      }

      return response
    } catch (err) {
      console.error(`获取视频评论失败: ${err}`)
      Sentry.captureException(err)
      return {
        commentsCount: '0',
        continuation: '',
        data: [],
        msg: `获取评论出错: ${err}`,
      }
    }
  }

  private async get<T>(uri: string, headers: object, collector?: RapidApiStatsCollector) {
    const url = `https://${this.HOST}/${uri}`
    const endpoint = url.split('?')[0]
    const options = {
      method: 'GET',
      headers: {
        'x-rapidapi-key': this.API_KEY,
        'x-rapidapi-host': this.HOST,
        ...headers,
      },
    }
    let response
    let statusCode = 0

    try {
      if (collector) {
        response = await collector.collect(axiosInstance.get(url, options), uri.split('?')[0])
      } else {
        response = await axiosInstance.get(url, options)
      }

      statusCode = response.status

      // 处理HTTP错误
      if (response.status != 200) {
        throw new Error(response.statusText)
      }

      // 处理API错误
      if (response.data && response.data.error) {
        // 如果有错误码，使用API错误码替换HTTP状态码
        if (response.data.code) {
          statusCode = parseInt(response.data.code) || statusCode
        }
        throw new Error(response.data.error)
      }

      // 检查 API 配额
      if (response.headers) {
        SlackClient.getInstance().checkApiQuota(response.headers, 'YouTube RapidAPI V1', undefined)
      }

      // 记录成功请求
      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.YOUTUBE, RapidApiStatus.SUCCESS, endpoint, statusCode)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })

      return response.data as T
    } catch (err: any) {
      // 记录错误请求 - 统一错误处理
      console.error(`YouTube API错误 (${statusCode}): ${err.message}`)

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.YOUTUBE, RapidApiStatus.ERROR, endpoint, statusCode)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })

      throw err
    }
  }
}

export default YoutubeRapidApi
