import {
  IgCommentResponse,
  IgFollowersResponse,
  IgFollowingsResponse,
  IgHashTagVideosResponse,
  IgLikesResponse,
  IgPost,
  IgPostsResponse,
  IgReelsResponse,
  IgTaggedUsersResponse,
  IgUser,
  PostType,
  igPost,
  igUser,
} from '@/api/@types/rapidapi/Instagram'
import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { Y2B_RAPID_API_KEY } from '@/config/env'
import Sentry from '@/infras/sentry'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { KolPlatform } from '@repo/database'
import dayjs from 'dayjs'
import { ofetch } from 'ofetch'
import { InstagramApi, userRequestStruct } from './instagram'

/**
 * 采购的 INS 第一套方案，拥有较低的并发，当前作为兜底使用
 * https://rapidapi.com/social-api1-instagram/api/instagram-scraper-api2/playground/apiendpoint_b1301387-dc09-4b1f-ba39-b7b51d186b40
 * */
class InstagramRapidApi implements InstagramApi {
  private static instance: InstagramRapidApi

  HOST = 'instagram-scraper-api2.p.rapidapi.com'
  API_KEY = Y2B_RAPID_API_KEY
  VERSION = 'v1'
  public static getInstance(): InstagramRapidApi {
    if (!InstagramRapidApi.instance) {
      InstagramRapidApi.instance = new InstagramRapidApi()
    }

    return InstagramRapidApi.instance
  }

  public async getUserWithPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const fullUser = await this.getUser(user, collector)
    if (!fullUser) {
      throw new Error('user not found' + user.username)
    }
    try {
      fullUser.posts = await this.getPosts(user, collector)
      fullUser.lastPublishedTime =
        fullUser.posts && fullUser.posts.length ? fullUser.posts[0].created_at : dayjs().unix()
    } catch (error) {
      console.log(`fail get user posts`)
      Sentry.captureException(error)
      fullUser.posts = []
    }
    return fullUser
  }

  public async getUserRelatedUsers(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser[]> {
    const urlOrIdOrName = user.url ?? user.id ?? user.username
    const users = (
      await this.get<{ data: { count: number; items: IgUser[] } }>(
        `v1/similar_accounts?username_or_id_or_url=${urlOrIdOrName}`,
        collector,
      )
    ).data
    return users?.items?.map((i) => igUser(i)).filter((u): u is IgUser => u !== null) ?? []
  }

  public async getUser(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    const urlOrIdOrName = user.url ?? user.id ?? user.username
    const fullUser = await this.get(
      `v1/info?url_embed_safe=true&include_about=true&username_or_id_or_url=${urlOrIdOrName}`,
      collector,
    )
    return igUser(fullUser)
  }

  public async getPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost[]> {
    const urlOrIdOrName = user.url ?? user.id ?? user.username
    const res = await this.get<{ items: IgPost[]; num_results: number }>(
      `v1.2/posts?username_or_id_or_url=${urlOrIdOrName}`,
      collector,
    )
    return res.items.map((i) => igPost(i))
  }

  public async getPost(
    postId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost | undefined> {
    try {
      const resp = (
        await this.get<any>(
          `v1/post_info?include_insights=true&url_embed_safe=true&code_or_id_or_url=${postId}`,
          collector,
        )
      ).data
      return {
        id: resp.code,
        type: resp.media_name as PostType,
        thumbnail: resp?.thumbnail_url ?? '',
        comment_count: resp?.metrics?.comment_count ?? 0,
        like_count: resp?.metrics?.like_count ?? 0,
        play_count: resp?.metrics?.play_count ?? 0,
        caption: {
          text: resp?.caption?.text ?? '',
        },
        created_at: resp?.taken_at ?? resp?.caption?.created_at ?? 0,
        like_and_view_counts_disabled: resp?.like_and_view_counts_disabled ?? false,
        tags: resp?.caption?.hashtags ?? [],
        username: resp.user.username!,
        userId: resp?.user?.id ?? '',
      } as IgPost
    } catch (err) {
      console.log(`[instagram rapidapi v1]failed get ig post ${postId} detail: ${err}`)
      Sentry.captureException(err)
      return undefined
    }
  }

  private async get<T>(uri: string, collector?: RapidApiStatsCollector) {
    const url = `https://${this.HOST}/${uri}`
    const endpoint = url.split('?')[0]

    const options = {
      headers: {
        'x-rapidapi-key': this.API_KEY,
        'x-rapidapi-host': this.HOST,
      },
      retry: 3,
      retryDelay: 500,
    }

    let response: any
    let status: number = 0
    try {
      response = await ofetch.raw(url, options)
      const data = response._data
      status = response.status

      if (collector) {
        await collector.collect(Promise.resolve(data), uri.split('?')[0])
      }

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.SUCCESS, endpoint, status)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })

      return data as T
    } catch (err: any) {
      // 尝试从错误对象中获取状态码
      const errorStatus = err.response?.status || err.status || 0

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.INSTAGRAM, RapidApiStatus.ERROR, endpoint, errorStatus)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }
  getPostComments(
    postId: string,
    options?: { pagination_token?: string; sort_by?: 'recent' | 'popular' },
    collector?: RapidApiStatsCollector,
  ): Promise<IgCommentResponse> {
    throw new Error('Method not implemented.')
  }
  getReels(
    user: userRequestStruct,
    options?: { pagination_token?: string; url_embed_safe?: boolean },
    collector?: RapidApiStatsCollector,
  ): Promise<IgReelsResponse> {
    throw new Error('Method not implemented.')
  }
  getUserPosts(
    user: userRequestStruct,
    options?: { pagination_token?: string; url_embed_safe?: boolean },
    collector?: RapidApiStatsCollector,
  ): Promise<IgPostsResponse> {
    throw new Error('Method not implemented.')
  }
  getFollowers(
    user: userRequestStruct,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowersResponse> {
    throw new Error('Method not implemented.')
  }

  getLikes(postId: string, collector?: RapidApiStatsCollector): Promise<IgLikesResponse> {
    throw new Error('Method not implemented.')
  }
  getHashTagVideos(
    hashtag: string,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgHashTagVideosResponse> {
    throw new Error('Method not implemented.')
  }
  getFollowings(
    user: userRequestStruct,
    options?: { pagination_token?: string; amount?: number },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowingsResponse> {
    throw new Error('Method not implemented.')
  }

  getTaggedUsers(
    username: string,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgTaggedUsersResponse> {
    throw new Error('Method not implemented.')
  }

  getQuota(): Promise<{
    used: number
    total: number
  }> {
    throw new Error('Method not implemented.')
  }
}

export default InstagramRapidApi
