// import { IgPost, IgUser } from '@/api/@types/rapidapi/Instagram'
// import RapidApiStatsCollector from '@/api/ApiCallStat'
// import { Y2B_RAPID_API_KEY } from '@/config/env'
// import Sentry from '@/infras/sentry'
// import { EmailSourceType } from '@/types/email'
// import { extractEmail } from '@/utils/email'
// import { RapidApiStats } from '@/utils/rapidApiStats'
// import { KolPlatform } from '@repo/database'
// import countries from 'i18n-iso-countries'
// import { ofetch } from 'ofetch'
// import { InstagramApi } from './instagram'

// /**
//  * @deprecated
//  * 采购的 INS 第二套方案，并发更高（80 rps)
//  * https://rapidapi.com/NikitusLLP/api/instagram-premium-api-2023/playground/apiendpoint_9bc9e2b2-89f2-47ed-b54d-ec181fadebb7
//  * */
// class InstagramRapidApiV2 implements InstagramApi {
//   private static instance: InstagramRapidApiV2

//   HOST = 'instagram-premium-api-2023.p.rapidapi.com'
//   API_KEY = Y2B_RAPID_API_KEY

//   public static getInstance(): InstagramRapidApiV2 {
//     if (!InstagramRapidApiV2.instance) {
//       InstagramRapidApiV2.instance = new InstagramRapidApiV2()
//     }

//     return InstagramRapidApiV2.instance
//   }
//   public async getUserWithPosts(
//     username: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<IgUser | null> {
//     try {
//       const resp = await this.get<any>(`a2/user?username=${username}`, collector)
//       if (resp.exc_type && resp.detail) {
//         throw new Error(resp?.detail)
//       }
//       const info = resp?.graphql?.user
//       if (!info?.id) {
//         console.log(`user ${username} cannot get any info by rapid api V2`)
//         return null
//       }
//       let posts: (IgPost | null)[] = []
//       if (info?.edge_owner_to_timeline_media?.edges) {
//         posts = info.edge_owner_to_timeline_media.edges.map((p: any) => {
//           try {
//             const post = p.node as unknown as any
//             return {
//               id: post.id,
//               comment_count: post?.edge_media_to_comment?.count ?? 0,
//               like_count: post?.edge_liked_by?.count ?? 0,
//               play_count: post?.video_view_count ?? 0,
//               caption: {
//                 text: post?.edge_media_to_caption?.edges[0]?.node?.text ?? '',
//               },
//               created_at: 0,
//               like_and_view_counts_disabled: false,
//               tags: [],
//             } as IgPost
//           } catch (err) {
//             console.log(`get post failed`)
//             return null
//           }
//         })
//         posts = posts.filter((p) => p)
//       }
//       const bioEmail = extractEmail(info?.biography)
//       const emailSource = bioEmail ? EmailSourceType.BIO_EMAIL : undefined
//       const user = {
//         id: info.id!,
//         username: username,
//         profilePicUrl: info.profile_pic_url ?? '',
//         fullName: info.full_name ?? '',
//         followerCount: info?.edge_followed_by?.count ?? 0,
//         biography: info?.biography ?? '',
//         biographyEmail: bioEmail,
//         posts: posts,
//         publicEmail: '',
//         region: '',
//         lastPublishedTime: 0, // TODO
//         email: bioEmail,
//         emailSource: emailSource,
//       } as IgUser
//       const region = await this.getUserCountry(user.id, collector)
//       if (region) {
//         user.region = region
//       }
//       return user
//     } catch (err) {
//       console.log(`get user failed: ${username}`)
//       Sentry.captureException(err)
//       return null
//     }
//   }

//   // special method
//   public async getUserCountry(
//     id: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<string | undefined> {
//     const user = await this.get<any>(`v1/user/about?id=${id}`, collector)
//     // const struct = {
//     // country: user?.country as string,
//     // date: user?.date as string,
//     // former_usernames: user?.former_usernames as string,
//     // is_verified: user?.is_verified as boolean,
//     // username: user.username as string
//     // }
//     if (user?.country) {
//       return countries.getAlpha2Code(user.country, 'en') ?? ''
//     }
//     return ''
//   }

//   public async getUserRelatedUsers(
//     userId: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<IgUser[]> {
//     const resp = await this.get<any>(`gql/user/related/profiles?id=${userId}`, collector)
//     if (Array.isArray(resp)) {
//       return resp
//         .filter((u) => typeof u.is_private == 'boolean' && !u.is_private)
//         .map((user) => {
//           try {
//             if (!user.id) {
//               console.log(`user info error: ${JSON.stringify(user)}`)
//               return null
//             }
//             return {
//               id: user.id,
//               username: user.username,
//               profilePicUrl: user.profile_pic_url,
//               fullName: user.full_name,
//             } as IgUser
//           } catch (err) {
//             Sentry.captureException(err)
//             return null
//           }
//         })
//         .filter((u) => u) as IgUser[]
//     }
//     console.log(`getUserRelatedUsers by v2 failed: ${userId}`)
//     return []
//   }

//   // special method
//   public async getUserFollowing(
//     userId: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<IgUser[]> {
//     const resp = await this.get(`v2/user/following?user_id=${userId}`, collector)
//     if (!resp?.response?.users) {
//       console.log(`there's error fetching following for user ${userId}`)
//       return []
//     }
//     let users = resp.response.users as any[]
//     users = users.filter((u) => u.id)
//     const rawSize = users.length
//     users = users.filter((u) => !u?.is_private)
//     console.log(
//       `there are ${rawSize - users.length} private users. User list from ${rawSize} to ${users.length}`,
//     )
//     return users.map((u) => {
//       return {
//         id: u.id!,
//         username: u?.username ?? '',
//         profilePicUrl: u?.profile_pic_url ?? '',
//         fullName: u?.fill_name ?? '',
//       } as IgUser
//     })
//   }

//   public async getUser(
//     username: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<IgUser | null> {
//     const resp = await this.get(`v2/user/by/username?username=${username}`, collector)
//     if (!resp?.user?.id) {
//       console.log(`error getting ins user ${username}`)
//       return null
//     }
//     const user = resp.user as any
//     let email = undefined
//     let emailSource = undefined
//     if (user.public_email) {
//       email = user.public_email
//       emailSource = EmailSourceType.RAPID_EMAIL_BUTTON
//     } else if (user.biography && extractEmail(user.biography)) {
//       email = extractEmail(user.biography)
//       emailSource = EmailSourceType.BIO_EMAIL
//     }
//     return {
//       id: user.id,
//       username: user.username ?? '',
//       profilePicUrl: user.profile_pic_url ?? '',
//       fullName: user.full_name ?? '',
//       followerCount: user.follower_count ?? 0,
//       biography: user.biography ?? '',
//       biographyEmail: extractEmail(user.biography ?? ''),
//       posts: [],
//       publicEmail: user.public_email ?? '',
//       region: await this.getUserCountry(user.id, collector),
//       lastPublishedTime: 0,
//       email: email ?? '',
//       emailSource: emailSource ?? '',
//       category: user.category ?? '',
//       accountType: user.account_type ?? 0,
//     } as IgUser
//   }

//   public async getPosts(id: string, collector?: RapidApiStatsCollector): Promise<IgPost[]> {
//     const resp = await this.get(`v1/user/medias/chunk?user_id=${id}`, collector)
//     if (!Array.isArray(resp) && !Array.isArray(resp[0])) {
//       return []
//     }
//     const arr = resp[0]
//     return arr.map((post) => {
//       if (!post.id) {
//         return undefined
//       }
//       return {
//         id: post.id,
//         comment_count: post?.comment_count ?? 0,
//         like_count: post?.like_count ?? 0,
//         play_count: post?.play_count ?? 0,
//         caption: {
//           text: post?.caption_text ?? '',
//         },
//         created_at: post?.taken_at_ts ?? 0,
//         like_and_view_counts_disabled: post.like_and_view_counts_disabled ?? false,
//         tags: (post?.caption_text ?? '').match(/#\w+/g) ?? [],
//       }
//     }) as IgPost[]
//   }

//   public async getPost(
//     postId: string,
//     collector?: RapidApiStatsCollector,
//   ): Promise<IgPost | undefined> {
//     try {
//       const resp = (await this.get<any>(`v2/media/info/by/code?code=${postId}`, collector))
//         .media_or_ad
//       return {
//         id: resp.code!,
//         type: resp?.clips_metadata ? 'reel' : 'post',
//         comment_count: resp?.comment_count ?? 0,
//         like_count: resp?.like_count ?? 0,
//         play_count: resp?.play_count ?? 0,
//         caption: {
//           text: resp?.caption?.text ?? '',
//         },
//         created_at: resp?.taken_at ?? resp?.caption?.created_at ?? 0,
//         like_and_view_counts_disabled: resp.like_and_view_counts_disabled,
//         tags: [],
//         username: resp.user.username!,
//         userId: resp?.user?.pk_id ?? '0',
//       } as IgPost
//     } catch (err) {
//       console.log(`[instatgram rapidapi v2]failed get post ${postId} detail: ${err}`)
//       Sentry.captureException(err)
//       return undefined
//     }
//   }
//   private async get<T>(uri: string, collector?: RapidApiStatsCollector) {
//     const url = `https://${this.HOST}/${uri}`
//     const options = {
//       headers: {
//         'x-rapidapi-key': this.API_KEY,
//         'x-rapidapi-host': this.HOST,
//       },
//       retry: 3,
//       retryDelay: 500,
//     }
//     let response
//     if (collector) {
//       response = await collector.collect(
//         ofetch(url, options).catch((error) => {
//           if (error?.response?.status === 403) {
//             console.log(`request ${url} response a 403: ${JSON.stringify(error?.response?._data)}`)
//             return error.response?._data
//           } else {
//             Sentry.captureException(error)
//           }
//         }),
//         uri.split('?')[0],
//       )
//     } else {
//       response = await ofetch(url, options).catch((error) => {
//         switch (error?.response?.status) {
//           case 403:
//           case 404: {
//             console.log(
//               `request ${url} response a ${error?.response?.status}: ${JSON.stringify(error?.response?._data)}`,
//             )
//             return error.response?._data
//           }
//           default:
//             Sentry.captureException(error)
//         }
//       })
//     }
//     const endpoint = uri.split('?')[0]
//     RapidApiStats.getInstance()
//       .recordApiCall(KolPlatform.INSTAGRAM, endpoint)
//       .catch((err: any) => {
//         console.error('Failed to record API stats:', err)
//         Sentry.captureException(err)
//       })
//     return response as T
//   }
// }

// export default InstagramRapidApiV2
