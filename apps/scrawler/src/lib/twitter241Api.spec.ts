import { describe, it } from 'vitest'
import { twitter241Api } from './twitter241Api'

// 测试用例参数，可根据实际情况替换
const userId = '18220175'
const screenName = 'RickRoss'

describe('Twitter241Api 测试', () => {
  it('getFollowings 应该返回用户关注列表', async () => {
    const users = await twitter241Api.getFollowings(userId, 10)
    console.log(users)
  })

  it('getFollowingsWithPagination 应该返回多页关注用户', async () => {
    const users = await twitter241Api.getFollowingsWithPagination(userId, 2, 5)
    console.log(users)
  })

  it('getUserByScreenName 应该返回用户信息', async () => {
    const user = await twitter241Api.getUserByScreenName(screenName)
    console.log(user)
  })
})
