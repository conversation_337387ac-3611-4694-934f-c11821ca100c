import { describe, expect, it } from 'vitest'
import YoutubeRapidApi from './youtubeRapidApi'
import YoutubeRapidApiV2 from './youtubeRapidApi.v2'
import YoutubeiApi from './youtubeiApi'

describe('should test all rapid apis v1', () => {
  const channelHandle = 'EDISON_SCIENCE_CORNER'
  const channelId = 'UCVcOsUBmH4hzVSnmJA_i5SA'
  const videoId = 'OY2x0TyKzIQ'
  // const keywords = 'apple vision pro'
  const keywords = 'otterlife app'

  it('should get channel id', async () => {
    const newChannelId = await YoutubeRapidApi.getInstance().getChannelId(channelHandle)
    console.log(newChannelId)
    expect(newChannelId).eq(channelId)
  })

  it('should get channel info', async () => {
    const channel = await YoutubeRapidApi.getInstance().getChannel(channelId)
    expect(channel.description.length).gt(0)
  })

  it('should get channel links', async () => {
    const channel = await YoutubeRapidApi.getInstance().getChannel('UC9jT4M6twJqZXwtBcrW3YIw')
    expect(channel.links.length).gt(0)
    console.log(channel.links)
  })

  it('should get channel videos', async () => {
    const videos = await YoutubeRapidApi.getInstance().getChannelVideos(channelHandle)
    expect(videos.length).gt(0)
  })

  it(
    'should get related videos',
    async () => {
      const relatedVideos = await YoutubeRapidApi.getInstance().getVideoRelatedVideos(
        'Z_wSBtIGgPs',
        200,
      )
      console.log(`get ${relatedVideos.length} related videos`)
      expect(relatedVideos.length).gt(0)
    },
    30 * 1000,
  )

  it('should search video for keywords', async () => {
    const firstPageVideos = await YoutubeRapidApi.getInstance().searchVideo(keywords, {})
    expect(firstPageVideos.data.length).gt(0)
    console.log(`there are ${firstPageVideos.count} videos for keywords ${keywords}`)
    console.log(firstPageVideos.data.map((video) => video.title).join('\n'))
    const secondPageVideos = await YoutubeRapidApi.getInstance().searchVideo(keywords, {
      token: firstPageVideos.token,
    })
    expect(secondPageVideos.data.length).gt(0)
    console.log(secondPageVideos.data.map((video) => video.title).join('\n'))
  })

  it('should get hashtag results for multi pages', async () => {
    const hashtag = 'applevisionpro'
    const hashtagResults = await YoutubeRapidApi.getInstance().getHashtag(hashtag, {})
    expect(hashtagResults.data.length).gt(0)
    const token = hashtagResults.continuation
    console.log(`get ${hashtagResults.data.length} hashtag results for page 1`)
    console.log(hashtagResults.data.map((result) => result.title).join('\n'))
    const hashtagResults2 = await YoutubeRapidApi.getInstance().getHashtag(hashtag, {
      token,
    })
    expect(hashtagResults2.data.length).gt(0)
    console.log(`get ${hashtagResults2.data.length} hashtag results for page 2`)
    console.log(hashtagResults2.data.map((result) => result.title).join('\n'))
  })
})

// 添加新的测试组，专门测试新增的方法
describe('should test new youtube api methods', () => {
  const channelHandle = 'EDISON_SCIENCE_CORNER'
  const channelId = 'UCMrvLMUITAImCHMOhX88PYQ'
  const videoId = 'OY2x0TyKzIQ'

  it('should get channel shorts', async () => {
    const shorts = await YoutubeRapidApi.getInstance().getSpecificChannelVideos(
      {
        channelId: channelId,
      },
      {
        sort_by: 'newest',
      },
    )
    console.log(`获取到 ${shorts.length} 个videos视频`)
    expect(shorts.length).toBeGreaterThanOrEqual(0) // 有可能会没有视频
    if (shorts.length > 0) {
      expect(shorts[0].videoId).toBeDefined()
      expect(shorts[0].title).toBeDefined()
    }
  })

  it('should get channel shorts', async () => {
    const shorts = await YoutubeRapidApi.getInstance().getChannelShorts(
      {
        channelId: channelId,
      },
      {
        sort_by: 'newest',
      },
    )

    console.log(`获取到 ${shorts.length} 个短视频`)
    expect(shorts.length).toBeGreaterThanOrEqual(0) // 有些频道可能没有shorts
    if (shorts.length > 0) {
      expect(shorts[0].videoId).toBeDefined()
      expect(shorts[0].title).toBeDefined()
    }
  })

  it('should get video comments', async () => {
    const comments = await YoutubeRapidApi.getInstance().getVideoComments(videoId)
    console.log(`获取到 ${comments.data.length} 条评论，总评论数：${comments.commentsCount}`)
    expect(comments.data.length).toBeGreaterThan(0)
    expect(comments.commentsCount).toBeDefined()
    expect(comments.continuation).toBeDefined()

    // 检查评论数据结构
    const firstComment = comments.data[0]
    expect(firstComment.commentId).toBeDefined()
    expect(firstComment.authorText).toBeDefined()
    expect(firstComment.textDisplay).toBeDefined()
    expect(firstComment.publishedTimeText).toBeDefined()
  })

  it('should get video comments with sorting', async () => {
    const newestComments = await YoutubeRapidApi.getInstance().getVideoComments(videoId, {
      sort_by: 'newest',
    })
    console.log(`按最新排序获取到 ${newestComments.data.length} 条评论`)
    expect(newestComments.data.length).toBeGreaterThan(0)
  })

  it('should get video comments with pagination', async () => {
    // 先获取第一页评论
    const firstPage = await YoutubeRapidApi.getInstance().getVideoComments(videoId)
    expect(firstPage.continuation).toBeDefined()

    // 使用continuation token获取下一页
    if (firstPage.continuation) {
      const secondPage = await YoutubeRapidApi.getInstance().getVideoComments(videoId, {
        token: firstPage.continuation,
      })
      console.log(`使用分页获取到第二页 ${secondPage.data.length} 条评论`)
      expect(secondPage.data.length).toBeGreaterThan(0)

      // 确保两页的评论不同
      if (secondPage.data.length > 0 && firstPage.data.length > 0) {
        expect(secondPage.data[0].commentId).not.toEqual(firstPage.data[0].commentId)
      }
    }
  })
})

describe('should test all rapid apis v2', () => {
  const channelHandle = '@mkbhd'
  const channelId = 'UCMrvLMUITAImCHMOhX88PYQ'
  const videoId = 'OY2x0TyKzIQ'
  const keywords = 'apple vision pro'
  it('should get channel id', async () => {
    const channelId = await YoutubeRapidApiV2.getInstance().getChannelId(channelHandle)
    expect(channelId).eq(channelId)
  })

  it('should get channel info', async () => {
    const channel = await YoutubeRapidApiV2.getInstance().getChannel(channelId)
    expect(channel.description.length).gt(0)
    expect(channel.avatar.length).gt(0)
  })

  it('should get channel links', async () => {
    const channel = await YoutubeRapidApiV2.getInstance().getChannel('UC9jT4M6twJqZXwtBcrW3YIw')
    expect(channel.links.length).gt(0)
    console.log(channel.links)
  })

  it('should get channel videos', async () => {
    const videos = await YoutubeRapidApiV2.getInstance().getChannelVideos(channelId)
    expect(videos.length).gt(0)
    videos.forEach((v) => console.log(v.title))
  })

  it('should search for some videos', async () => {
    const relatedVideos = await YoutubeRapidApiV2.getInstance().getVideoRelatedVideos(videoId, 50)
    expect(relatedVideos.length).gt(0)
    relatedVideos.forEach((v) => console.log(v.title))
  })

  it('should search video for keywords', async () => {
    // const videos = await YoutubeRapidApiV2.getInstance().searchVideo(keywords, {
    //   pages: 1
    // })
    // expect(videos.length).gt(0)
    // console.log(`get ${videos.length} videos`)
    const moreVideos = await YoutubeRapidApi.getInstance().searchVideo(keywords, {
      pages: 5,
    })
    expect(moreVideos.length).gt(20)
    console.log(`get ${moreVideos.length} videos`)
  })
})

describe('should test using youtubei lib', () => {
  it('should find channel id by handle', async () => {
    const channelHandle = '@大耳朵TV'
    const channelId = await YoutubeiApi.getInstance().getChannelId(channelHandle)
    console.log(channelId)
    expect(channelId).toBeDefined()
    expect(channelId).eq('UCD_cg9Tak9SvlPHRsWxUIpA')
  })
})

describe('should get all country with code', () => {
  const channelId = 'UCD-lncAGk7LrW9P7Ohepi7Q'
  it('should get channel by rapidApi 1', async () => {
    const channel = await YoutubeRapidApi.getInstance().getChannel(channelId)
    expect(channel.country).eq('US')
  })
  it('should get channel by rapidApi 2', async () => {
    const channel = await YoutubeRapidApiV2.getInstance().getChannel(channelId)
    expect(channel.country).eq('US')
  })
})

describe('should get all video details', () => {
  const videoId = '-kuG6RgL32c'

  it('should get video by rapidApi 1', async () => {
    const video = await YoutubeRapidApi.getInstance().getVideo(videoId)
    expect(video).toBeDefined()
    expect(video!.channelId).eq('UCBJycsmduvYEL83R_U4JriQ')
    expect(video?.viewCount).gt(0)
    expect(video?.publishedDate?.length).gt(0)
    expect(video?.description?.length).gt(0)
    expect(video?.commentCount).gt(0)
    expect(video?.likeCount).gt(0)
  })
  it('should get video by rapidApi 2', async () => {
    const video = await YoutubeRapidApiV2.getInstance().getVideo(videoId)
    expect(video).toBeDefined()
    expect(video!.channelId).eq('UCBJycsmduvYEL83R_U4JriQ')
    expect(video?.viewCount).gt(0)
    expect(video?.publishedDate?.length).gt(0)
    expect(video?.description?.length).gt(0)
    expect(video?.commentCount).eq(0)
    expect(video?.likeCount).eq(0)
  })
})
