import {
  IgCommentResponse,
  IgFollowersResponse,
  IgFollowingsResponse,
  IgHashTagVideosResponse,
  IgLikesResponse,
  IgPost,
  IgPostsResponse,
  IgReelsResponse,
  IgTaggedUsersResponse,
  IgUser,
} from '@/api/@types/rapidapi/Instagram'
import RapidApiStatsCollector from '@/api/ApiCallStat'

export interface userRequestStruct {
  id?: string
  username?: string
  url?: string
}

export interface postRequestStruct {
  id?: string
  code?: string
  url?: string
}

export interface InstagramApi {
  HOST: string
  API_KEY: string
  VERSION: string

  getUserWithPosts(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null>

  getUserRelatedUsers(
    user: userRequestStruct,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser[]>

  getUser(user: userRequestStruct, collector?: RapidApiStatsCollector): Promise<IgUser | null>

  getPosts(user: userRequestStruct, collector?: RapidApiStatsCollector): Promise<IgPost[]>

  getPost(postId: string, collector?: RapidApiStatsCollector): Promise<IgPost | undefined>

  /**
   * 获取Instagram帖子的评论
   * @param postId 帖子ID、代码或URL
   * @param options 可选参数，包括分页令牌和排序方式
   * @param collector API调用统计收集器
   * @returns 评论响应数据
   */
  getPostComments(
    postId: string,
    options?: {
      pagination_token?: string
      sort_by?: 'recent' | 'popular'
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgCommentResponse>

  /**
   * 获取Instagram用户的Reels视频
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌和URL嵌入安全选项
   * @param collector API调用统计收集器
   * @returns Reels响应数据
   */
  getReels(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgReelsResponse>

  /**
   * 获取Instagram用户的帖子（详细信息）
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌和URL嵌入安全选项
   * @param collector API调用统计收集器
   * @returns 帖子响应数据
   */
  getUserPosts(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgPostsResponse>

  /**
   * 获取Instagram用户的粉丝列表
   * @param usernameOrIdOrUrl 用户名、ID或URL
   * @param options 可选参数，包括分页令牌
   * @param collector API调用统计收集器
   * @returns 粉丝列表响应数据
   */
  getFollowers(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowersResponse>

  /**
   * 获取Instagram帖子的点赞列表
   * @param postId 帖子ID、代码或URL
   * @param collector API调用统计收集器
   * @returns 点赞响应数据
   */
  getLikes(postId: string, collector?: RapidApiStatsCollector): Promise<IgLikesResponse>

  /**
   * 获取Instagram标签下的视频
   * @param hashtag
   * @param options
   * @param collector
   */
  getHashTagVideos(
    hashtag: string,
    options?: {
      feed_type: 'top' | 'recent' | 'clips'
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgHashTagVideosResponse>

  /**
   * 获取Instagram用户的关注列表
   * @param user 用户信息（用户名、ID或URL）
   * @param options 可选参数，包括分页令牌和获取数量
   * @param collector API调用统计收集器
   * @returns 关注列表响应数据
   */
  getFollowings(
    user: userRequestStruct,
    options?: {
      pagination_token?: string
      amount?: number
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgFollowingsResponse>

  /**
   * 获取Instagram 被tagged的用户
   * @param username
   * @param options
   * @param collector
   */
  getTaggedUsers(
    username: string,
    options?: {
      pagination_token?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgTaggedUsersResponse>

  /**
   * 获取Instagram API配额
   * @returns 配额信息
   */
  getQuota(): Promise<{
    used: number
    total: number
  }>
}
