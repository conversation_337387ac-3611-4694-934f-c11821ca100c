import { describe, expect, it } from 'vitest'
import { Innertube } from 'youtubei.js/web'
describe('test about youtubei', () => {
  const videoUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
  const channelUrl = 'https://www.youtube.com/channel/UC_9-kyTW8ZkZNDHQJ6FgpwQ'

  it(
    'should get page type',
    async () => {
      const service = await Innertube.create({
        generate_session_locally: true,
        fetch: fetch,
      })
      let info = await service.resolveURL(channelUrl)
      console.log(info)
      expect(info.metadata.page_type).eq('WEB_PAGE_TYPE_CHANNEL')
      info = await service.resolveURL(videoUrl)
      console.log(info)
      expect(info.metadata.page_type).eq('WEB_PAGE_TYPE_WATCH')
    },
    20 * 60 * 1000,
  )
})
