import {
  EASYKOL_TRACK_CONCURRENCY,
  INSTAGRAM_CONCURRENCY,
  LONG_CRAWLER_TASK_CONCURRENCY,
  TIKTOK_CONCURRENCY,
  TWITTER_CONCURRENCY,
  YOUTUBE_CONCURRENCY,
} from '@/config/env'
import Sentry from '@/infras/sentry'
import {
  IEmbeddingTask,
  easykolTrackQueue,
  emailPlanQueue,
  instagramTaskQueue,
  longCrawlerTaskQueue,
  tiktokTaskQueue,
  twitterTaskQueue,
  youtubeTaskQueue,
} from '@/infras/worker/bullmq'
import { logger } from '@/infras/worker/log4js'
import { processEmailFollowupJob } from '@/services/worker/emailFollowup'
import { queueService } from '@/services/worker/queue.service'
import { TaskMetricsCollector } from '@/utils/TaskMetricsCollector'
import Bull from 'bull'

// 启动 worker 进程
logger.info(`Worker ${process.pid} started`)

// YouTube 队列处理
youtubeTaskQueue.process(+YOUTUBE_CONCURRENCY, async (job) => {
  job.data.metrics = new TaskMetricsCollector()
  job.data.metrics.set('worker_pid', process.pid)
  try {
    logger.info(`Worker ${process.pid} processing YouTube job ${job.id}`)
    await Sentry.withScope((scope) => {
      scope.setTransactionName(`YouTube ${job.data.type} Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', job.data.platform)
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing YouTube job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`YouTube 任务处理失败: ${String(err)}`)
    }
  }
})
setupQueueListeners(youtubeTaskQueue, 'YouTube')

// TikTok 队列处理
tiktokTaskQueue.process(+TIKTOK_CONCURRENCY, async (job) => {
  job.data.metrics = new TaskMetricsCollector()
  job.data.metrics.set('worker_pid', process.pid)
  try {
    logger.info(`Worker ${process.pid} processing TikTok job ${job.id}`)

    await Sentry.withScope((scope) => {
      scope.setTransactionName(`TikTok ${job.data.type} Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', job.data.platform)
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing TikTok job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`TikTok 任务处理失败: ${String(err)}`)
    }
  }
})
setupQueueListeners(tiktokTaskQueue, 'TikTok')

// Instagram 队列处理
instagramTaskQueue.process(+INSTAGRAM_CONCURRENCY, async (job) => {
  try {
    job.data.metrics = new TaskMetricsCollector()
    job.data.metrics.set('worker_pid', process.pid)
    logger.info(`Worker ${process.pid} processing Instagram job ${job.id}`)

    await Sentry.withScope((scope) => {
      scope.setTransactionName(`Instagram ${job.data.type} Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', job.data.platform)
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing Instagram job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`Instagram 任务处理失败: ${String(err)}`)
    }
  }
})
setupQueueListeners(instagramTaskQueue, 'Instagram')

// Twitter 队列处理
twitterTaskQueue.process(+TWITTER_CONCURRENCY, async (job) => {
  try {
    job.data.metrics = new TaskMetricsCollector()
    job.data.metrics.set('worker_pid', process.pid)
    logger.info(`Worker ${process.pid} processing Twitter job ${job.id}`)

    await Sentry.withScope((scope) => {
      scope.setTransactionName(`Twitter ${job.data.type} Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', job.data.platform)
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing Twitter job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`Twitter 任务处理失败: ${String(err)}`)
    }
  }
})
setupQueueListeners(twitterTaskQueue, 'Twitter')

// EasyKOL Track 队列处理
easykolTrackQueue.process(+EASYKOL_TRACK_CONCURRENCY, async (job) => {
  try {
    job.data.metrics = new TaskMetricsCollector()
    job.data.metrics.set('worker_pid', process.pid)
    logger.info(`Worker ${process.pid} processing EasyKOL Track job ${job.id}`)
    await Sentry.withScope((scope) => {
      scope.setTransactionName(`EasyKOL Track Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', 'unknown')
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing EasyKOL Track job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`EasyKOL Track 任务处理失败: ${String(err)}`)
    }
  }
})
setupQueueListeners(easykolTrackQueue, 'EasyKOL')

// 长任务队列处理
longCrawlerTaskQueue.process(+LONG_CRAWLER_TASK_CONCURRENCY, async (job) => {
  try {
    job.data.metrics = new TaskMetricsCollector()
    job.data.metrics.set('worker_pid', process.pid)
    logger.info(`Worker ${process.pid} processing Long Task job ${job.id}`)
    await Sentry.withScope((scope) => {
      scope.setTransactionName(`Long Task ${job.data.type} Worker`)
      scope.setTag('taskId', job.data.id)
      scope.setTag('platform', job.data.platform)
      scope.setTag('taskType', job.data.type)
      scope.setContext('Task Params', { params: JSON.stringify(job.data.params) })
      return queueService.processJob(job)
    })
  } catch (err) {
    logger.error(`Error processing Long Task job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`长任务处理失败: ${String(err)}`)
    }
  }
})

setupQueueListeners(longCrawlerTaskQueue, 'LongCrawlerTask')

function setupQueueListeners(queue: Bull.Queue<IEmbeddingTask>, platform: string) {
  queue.on('completed', async (job) => {
    logger.info(`Worker ${process.pid} completed ${platform} job ${job.id}`)
    try {
      await queueService.handleComplete(job)
    } catch (err) {
      logger.error(
        `Error handling complete ${platform} job ${job.id} by worker ${process.pid}`,
        err,
      )
    }
  })

  queue.on('failed', async (job, err) => {
    logger.error(`Worker ${process.pid} failed ${platform} job ${job.id}`, err)
    try {
      await queueService.handleFailed(job, err)
    } catch (err) {
      logger.error(`Error handling failed ${platform} job ${job.id} by worker ${process.pid}`, err)
    }
  })

  // stalled 事件处理
  queue.on('stalled', (job) => {
    logger.warn(`${platform} job ${job.id} has stalled`, {
      taskId: job.data.id,
      attempts: job.attemptsMade,
    })
  })

  // 添加错误事件处理
  queue.on('error', (error) => {
    logger.error(`${platform} queue error:`, error)
    Sentry.captureException(error)
  })
}

// 邮件跟进队列处理
emailPlanQueue.process(5, async (job) => {
  try {
    logger.info(`Worker ${process.pid} processing Email Followup job ${job.id}`)
    await Sentry.withScope((scope) => {
      scope.setTransactionName(`Email Followup Worker`)
      scope.setTag('emailPlanId', job.data.emailPlanId)
      scope.setTag('userId', job.data.userId)
      return processEmailFollowupJob(job)
    })
  } catch (err) {
    logger.error(`Error processing Email Followup job ${job.id} by worker ${process.pid}`, err)
    if (err instanceof Error) {
      throw err
    } else {
      throw new Error(`邮件跟进任务处理失败: ${String(err)}`)
    }
  }
})

// 为邮件队列设置监听器
emailPlanQueue.on('completed', async (job) => {
  logger.info(`Worker ${process.pid} completed Email Followup job ${job.id}`)
})

emailPlanQueue.on('failed', async (job, err) => {
  logger.error(`Worker ${process.pid} failed Email Followup job ${job.id}`, err)
})

emailPlanQueue.on('stalled', (job) => {
  logger.warn(`Email Followup job ${job.id} has stalled`, {
    emailPlanId: job.data.emailPlanId,
    attempts: job.attemptsMade,
  })
})

emailPlanQueue.on('error', (error) => {
  logger.error(`Email Followup queue error:`, error)
  Sentry.captureException(error)
})
