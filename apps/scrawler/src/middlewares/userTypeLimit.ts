import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { MemberType, prisma } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

export const userTypeLimit = (userTypes: MemberType[], customMessage?: string) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      if (!user?.id) {
        throwError(StatusCodes.UNAUTHORIZED, 'User authentication required')
      }
      const membership = await prisma.userMembership.findFirst({
        where: {
          userId: user.id,
        },
      })
      if (!membership?.type) {
        throwError(StatusCodes.SERVER_ERROR, `User membership is not exist. [userId=${user.id}]`)
      }
      console.log('userTypes', userTypes, membership!.type)
      if (!userTypes.includes(membership!.type)) {
        throwError(StatusCodes.FORBIDDEN, customMessage ?? 'User type not allowed')
      }
    },
  }
}

export const freeUserLimit = () => {
  return userTypeLimit([MemberType.PAID, MemberType.ENTERPRISE], 'Free user is not allowed')
}
