import { StatusCodes, errorResponse } from '@/common/response/response'
import { PLATFORM_DAILY_LIMIT, REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'
import { KolPlatform } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

const getPlatformRateLimitKey = (platform: KolPlatform): string => {
  const today = new Date()
  const dateStr = today.toISOString().split('T')[0]
  return `${REDIS_KEY_PREFIX}platform_rate_limit:${platform}:${dateStr}`
}

/**
 * 计算当前日期剩余的秒数，用于设置Redis key的过期时间
 * 确保Redis key在当天23:59:59过期
 */
const getSecondsUntilEndOfDay = (): number => {
  const now = new Date()
  const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999)
  return Math.floor((endOfDay.getTime() - now.getTime()) / 1000) + 1 // 加1秒确保覆盖到23:59:59
}

export const platformRateLimit = () => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { platform } = request.query as { platform?: KolPlatform }

      if (!platform || !Object.values(KolPlatform).includes(platform)) {
        return
      }

      const redisKey = getPlatformRateLimitKey(platform)

      const count = await redis.incr(redisKey)
      if (count === 1) {
        const expireSeconds = getSecondsUntilEndOfDay()
        await redis.expire(redisKey, expireSeconds)
        console.log(
          `初始化平台 ${platform} 的计数器，将在今天23:59:59过期，剩余时间: ${expireSeconds}秒`,
        )
      }

      if (count % 10 === 0 || count === PLATFORM_DAILY_LIMIT) {
        console.log(`平台 ${platform} 今天已调用 ${count}/${PLATFORM_DAILY_LIMIT} 次`)
      }

      if (count > PLATFORM_DAILY_LIMIT) {
        console.warn(`平台 ${platform} 今天达到限制: ${count}/${PLATFORM_DAILY_LIMIT}，请求被拒绝`)
        return reply
          .status(429)
          .send(
            errorResponse(
              StatusCodes.TOO_MANY_REQUESTS,
              'PLATFORM_RATE_LIMIT_EXCEEDED',
              `平台 ${platform} 今日使用已经达到上限，请明天再试`,
            ),
          )
      }
    } catch (error) {
      console.error('Rate limit middleware error:', error)
    }
  }
}
