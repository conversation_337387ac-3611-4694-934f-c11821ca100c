import { BaseError } from '@/common/errors/baseError'
import { ApiResponse, StatusCodes, errorResponse } from '@/common/response/response'

import Sentry from '@/infras/sentry'
import { AssertionError } from 'assert'
import {
  FastifyError,
  FastifyInstance,
  FastifyPluginAsync,
  FastifyReply,
  FastifyRequest,
} from 'fastify'
import fp from 'fastify-plugin'

// HTTP状态码到业务状态码的映射
const httpToBusinessStatusCode = new Map<number, number>([
  [400, StatusCodes.BAD_REQUEST],
  [401, StatusCodes.UNAUTHORIZED],
  [403, StatusCodes.FORBIDDEN],
  [404, StatusCodes.ENTERPRISE_NOT_FOUND], // 默认404映射，具体错误可以在抛出时指定
  [429, StatusCodes.TOO_MANY_REQUESTS],
  [500, StatusCodes.SERVER_ERROR],
])

const errorHandler: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  fastify.setErrorHandler(
    async (
      error: Error | FastifyError | BaseError,
      request: FastifyRequest,
      reply: FastifyReply,
    ) => {
      request.log.error({
        message: error.message,
        stack: error.stack,
        path: request.url,
        method: request.method,
        params: request.params,
        query: request.query,
        body: request.body,
      })
      if (reply.statusCode >= 499) {
        Sentry.captureException(error, {
          tags: {
            path: request.url,
            method: request.method,
          },
          extra: {
            params: request.params,
            query: request.query,
            body: request.body,
          },
        })
      }

      // 处理BaseError类型的错误（我们自定义的业务错误）
      if (error instanceof BaseError) {
        const errorResp: ApiResponse = {
          statusCode: error.code,
          error: error.name,
          message: error.message,
          data: error.details || null,
        }
        return reply.status(error.httpCode).send(errorResp)
      }

      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      const fastifyError = error as Partial<FastifyError>
      let httpStatusCode = 500

      if (
        typeof fastifyError.statusCode === 'number' &&
        fastifyError.statusCode >= 400 &&
        fastifyError.statusCode < 600
      ) {
        httpStatusCode = fastifyError.statusCode
      }

      const businessStatusCode =
        httpToBusinessStatusCode.get(httpStatusCode) || StatusCodes.SERVER_ERROR

      const errorResp: ApiResponse = {
        statusCode: businessStatusCode,
        error: fastifyError.name || 'INTERNAL_SERVER_ERROR',
        message: error.message || '服务器内部错误',
        data: null,
      }

      return reply.status(httpStatusCode).send(errorResp)
    },
  )
}

export default fp(errorHandler, {
  name: 'error-handler',
  fastify: '4.x',
})
