import { prisma } from '@repo/database'

export const versionStatistic = async (request: any, reply: any) => {
  const user = (request as any).user
  const easykolversion = request.headers['easykolversion']
  if (!easykolversion) {
    return
  }

  try {
    await prisma.userPluginVersion.upsert({
      where: {
        userId: user.id,
      },
      update: {
        version: easykolversion,
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        version: easykolversion,
      },
    })

    return
  } catch (error) {
    console.log('error', error)
    return
  }
}
