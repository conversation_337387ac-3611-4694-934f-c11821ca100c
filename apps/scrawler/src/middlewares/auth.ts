import { supabase } from '@/api/supabase.js'
import { StatusCodes, errorResponse } from '@/common/response/response'
import { SUPABASE_JWT_SECRET, WHITELIST_EMAILS } from '@/config/env.ts'
import { redis } from '@/infras/redis'
import { dynamicGet } from '@/infras/remembrall'
import {
  initUser,
  initUserMembership,
  userHasInited,
  userMembershipHasInited,
} from '@/services/user.js'
import { prisma } from '@repo/database'
import { AuthError, User } from '@supabase/supabase-js'
import { FastifyReply, FastifyRequest } from 'fastify'
import jwt from 'jsonwebtoken'

export const needSupabaseAuth = async (request: FastifyRequest, reply: FastifyReply) => {
  const { x_ek_user_email, authorization } = request.headers
  if (typeof x_ek_user_email === 'string' && x_ek_user_email) {
    const user = await prisma.userInfo.findFirst({
      where: {
        email: x_ek_user_email,
      },
    })
    if (!user) {
      return reply
        .status(401)
        .send(errorResponse(StatusCodes.UNAUTHORIZED, 'USER_NOT_FOUND', 'User not found'))
    }
    // @ts-ignore
    request.user = user.supabase
    return
  }
  if (!authorization) {
    return reply
      .status(401)
      .send(
        errorResponse(StatusCodes.UNAUTHORIZED, 'NEED_AUTH', 'Authorization header is required'),
      )
  }
  const [type, token] = authorization.split(' ')
  if (type !== 'Bearer') {
    return reply
      .status(401)
      .send(errorResponse(StatusCodes.UNAUTHORIZED, 'INVALID_TOKEN_TYPE', 'Invalid token type'))
  }

  let user = undefined
  try {
    user = await authWithCache(token)
  } catch (error) {
    if (error instanceof AuthError) {
      return reply
        .status(401)
        .send(
          errorResponse(
            StatusCodes.UNAUTHORIZED,
            'TOKEN_EXPIRED',
            error?.message || 'Token expired',
          ),
        )
    }
    return reply
      .status(401)
      .send(errorResponse(StatusCodes.UNAUTHORIZED, 'UNKNOWN_ERROR', JSON.stringify(error)))
  }

  if (!(await userHasInited(user.id))) {
    await initUser(user)
  }

  if (!(await userMembershipHasInited(user.id))) {
    await initUserMembership(user)
  }

  // @ts-ignore
  request.user = user
}

export const isAdmin = async (request: FastifyRequest, reply: FastifyReply) => {
  const user = (request as any).user

  if (!user) {
    return reply
      .status(401)
      .send(
        errorResponse(
          StatusCodes.UNAUTHORIZED,
          'USER_NOT_AUTHORIZED',
          'User is not authenticated.',
        ),
      )
  }
  const isAdminUser = await isAllowedEmail(user.id)
  if (!isAdminUser) {
    return reply
      .status(403)
      .send(
        errorResponse(
          StatusCodes.FORBIDDEN,
          'NOT_ADMIN',
          'You do not have permission to access this resource.',
        ),
      )
  }
}

export const isAllowedEmail = async (userId: string) => {
  if (!userId) return false
  const user = await prisma.userInfo.findUnique({
    where: {
      userId: userId,
    },
  })
  if (!user) return false
  const whiteList = (await dynamicGet<string[]>('admin_list', 60)) || WHITELIST_EMAILS
  return whiteList.includes(user.email || '')
}

export const withAuth = (...handlers: any[]) => {
  const preHandlers = [needSupabaseAuth]
  const onResponses: any[] = []
  const preSerializations: any[] = []

  handlers.forEach((handler) => {
    if (typeof handler === 'object') {
      if (handler.preHandler) preHandlers.push(handler.preHandler)
      if (handler.onResponse) onResponses.push(handler.onResponse)
      if (handler.preSerialization) preSerializations.push(handler.preSerialization)
    } else {
      preHandlers.push(handler)
    }
  })

  return {
    preHandler: preHandlers,
    preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      let currentPayload = payload
      for (const preSerialization of preSerializations) {
        currentPayload = await preSerialization(request, reply, currentPayload)
      }
      return currentPayload
    },
    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      for (const onResponse of onResponses) {
        await onResponse(request, reply)
      }
    },
  }
}

const REDIS_JWT_PREFIX = 'jwt:'

const authWithCache = async (jwtToken: string): Promise<User> => {
  const token = verifyToken(jwtToken)
  if (!token || !token?.session_id) {
    throw new AuthError('Bad JWT Token')
  }

  const key = REDIS_JWT_PREFIX + token.session_id
  const cache = await redis.get(key)
  if (cache) {
    return JSON.parse(cache)
  }

  const { data, error } = await supabase.auth.getUser(jwtToken)
  if (error) {
    throw error
  }
  const user = data.user
  await redis.set(key, JSON.stringify(user), 'EXAT', token.exp)
  return user
}

const verifyToken = (token: string): SupabaseJwt | null => {
  try {
    return jwt.verify(token, SUPABASE_JWT_SECRET) as SupabaseJwt
  } catch (err) {
    console.log(`failed to verify supabase token`)
    return null
  }
}
