import { StatusCodes, errorResponse, serverErrorResponse } from '@/common/response/response'
import { MIN_COST_FOR_EXCLUDE_LINK, PRICE_FOR_EXCLUDE_LINK } from '@/config/env.ts'
import { QuotaCost } from '@/enums/QuotaCost'
import { MembershipService } from '@/services/membership.service'
import { findLatestByTaskType } from '@/services/similar.ts'
import TaskService from '@/services/task'
import { checkAndRecordVisit } from '@/services/visit.service'
import { DeductDynamicQuotaParams } from '@/types/memberShip'
import Logger from '@/utils/logger'
import { PaginationService } from '@/utils/pagination'
import { KolPlatform, QuotaType, TaskType, prisma } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'
// 处理查询任务失败或者为空的扣除配额情况
export const handleSearchQuota = (quotaCost: QuotaCost, quotaType: QuotaType) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      if (!user) {
        return reply
          .status(401)
          .send(
            errorResponse(StatusCodes.UNAUTHORIZED, 'NEED_AUTH', 'User authentication required'),
          )
      }
      let taskId
      let projectId
      let platform
      const routePath = request.routeOptions.url || ''
      // 通过不同的路由写入不同的quotaInfo
      switch (routePath) {
        case '/api/similars/unionSearch': {
          projectId = (request.query as any).projectId
          const { taskType } = request.query as any
          const latestTask = await findLatestByTaskType(projectId, taskType as TaskType)
          if (latestTask) {
            taskId = latestTask.id
          }
          break
        }
        case '/api/similars/singleSearch': {
          taskId = (request.body as any).taskId
          try {
            const task = await TaskService.getInstance().getTaskByTaskId(taskId)
            projectId = task.projectId
            platform = (task.params as any).platform
          } catch (error) {
            console.error('Failed to get task:', error)
          }
          break
        }
        default:
          break
      }

      (request as any).quotaInfo = {
        cost: quotaCost,
        type: quotaType,
        userId: user.id,
        taskId: taskId,
        projectId: projectId,
        platform: platform,
      }
    },

    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      const quotaInfo = (request as any).quotaInfo
      if (!quotaInfo || !quotaInfo.taskId) return // 如果quotaInfo不存在，则不进行配额返还
      if (quotaInfo.platform === KolPlatform.INSTAGRAM) return // 如果平台为Instagram，则不进行配额返还

      try {
        const rawResponse = reply.raw
        let needRefundQuota = false

        // 检查是否需要返还配额,根据singleSearch 以及 unionSearch http状态码来判断
        // TODO 后续可优化为根据响应的错误业务码来判断
        if (rawResponse.statusCode >= 400) {
          needRefundQuota = true
        }

        // 检查任务是否存在 findUniqueOrThrow
        await TaskService.getInstance().getTaskByTaskId(quotaInfo.taskId)

        const quotaLog = await prisma.quotaLog.findFirst({
          where: {
            userId: quotaInfo.userId,
            type: quotaInfo.type,
            taskId: quotaInfo.taskId,
          },
        })

        if (quotaLog) return

        if (needRefundQuota) {
          await MembershipService.getInstance().addQuota(
            quotaInfo.userId,
            quotaInfo.type,
            quotaInfo.cost,
          )
        }
      } catch (error) {
        console.error('Failed to handle search quota:', error)
      }
    },
  }
}

export const checkQuota = (
  quotaCost: QuotaCost | ((request: FastifyRequest) => QuotaCost | Promise<QuotaCost>),
  quotaType?: QuotaType,
) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user

      // 新增逻辑：企业用户访问/rate直接放行
      const routePath = request.routeOptions.url || ''
      if (routePath === '/api/similars/rate') {
        const membership = await MembershipService.getInstance().getMembership(user.id)
        if (membership?.type === 'ENTERPRISE') {
          return
        }
      }

      const actualCost = typeof quotaCost === 'function' ? await quotaCost(request) : quotaCost
      if (actualCost > 0) {
        const quotaCheck = await MembershipService.getInstance().checkMemberShipQuotaStatus(
          user.id,
          actualCost,
        )
        if (!quotaCheck.hasQuota) {
          return reply.status(400).send(quotaCheck.error)
        }
      }
    },
    preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      // 获取整个响应数据 {statusCode,error,message,data}
      (request as any).responsePayload = payload
      return payload
    },
    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      if (reply.statusCode >= 200 && reply.statusCode < 300) {
        // 存在quotaType才会做扣费操作、否则只检查配额
        if (quotaType) {
          const { params, query, body, user, responsePayload } = request as any
          // 根据quotaType 获取对应类型的responseDataRecord
          let responseDataRecord: Record<string, any> = {}
          switch (quotaType) {
            case QuotaType.CARD_QUERY: {
              const kolInfo = responsePayload?.data?.kolInfo
              responseDataRecord = {
                kolId: kolInfo?.id || '',
                platform: kolInfo?.platform || '',
                platformAccount: kolInfo?.platformAccount || '',
              }
              break
            }
            default:
              responseDataRecord = {}
              break
          }

          try {
            // 先扣费
            await MembershipService.getInstance().deductQuota({
              userId: user.id,
              type: quotaType,
              projectId: (request.body as any)?.projectId,
              taskId: (reply.raw as any)?.data?.id,
              createdBy: user.id,
              metadata: {
                requestParams: { params, query, body },
              },
              responseDataRecord: responseDataRecord,
            })
            // 再记录缓存
            if (responsePayload.statusCode === 1000) {
              switch (quotaType) {
                case QuotaType.CARD_QUERY: {
                  if (responseDataRecord.platform && responseDataRecord.platformAccount) {
                    await checkAndRecordVisit(
                      user.id,
                      responseDataRecord.platform,
                      responseDataRecord.platformAccount,
                    )
                  }
                  break
                }
                default:
                  break
              }
            }
          } catch (error) {
            console.error('Failed to deduct quota:', {
              error,
              userId: user.id,
              type: quotaType,
              responseData: responseDataRecord,
            })
          }
        }
      }
    },
  }
}

export const handleQuotaWithResponse = (quotaCost: QuotaCost, quotaType?: QuotaType) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      if (!user) {
        return reply
          .status(401)
          .send(
            errorResponse(StatusCodes.UNAUTHORIZED, 'NEED_AUTH', 'User authentication required'),
          )
      }
      const routePath = request.routeOptions.url || ''
      let cost = quotaCost
      switch (quotaType) {
        case QuotaType.EXCLUDE_LIST: {
          const { links } = request.body as any
          const count = links.split('\n').length
          cost = Math.max(
            MIN_COST_FOR_EXCLUDE_LINK,
            Math.ceil(count / 5.0) * 5 * PRICE_FOR_EXCLUDE_LINK,
          )
          console.log('exclude list cost', cost)
          break
        }
        case QuotaType.EASYKOL_DATA_TRACK_URL: {
          switch (routePath) {
            case '/api/publicationStatistics/task/track-recent': {
              const { page, pageSize } = request.body as any
              const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
                where: { userId: user.id },
              })

              if (!userGoogleSheet) {
                return reply.status(404).send(serverErrorResponse('no google sheet found'))
              }
              const { skip } = PaginationService.handlePagination({ page, pageSize })
              const result = await prisma.publicationStatisticsSheetData.count({
                where: {
                  spreadsheetId: userGoogleSheet.spreadsheetId,
                },
                orderBy: { publishDate: 'desc' },
                skip,
                take: pageSize,
              })
              cost = result * quotaCost
              break
            }
            case '/api/publicationStatistics/task/track-new': {
              const { tiktok, youtube, instagram, douyin, xhs } = request.body as any
              const count =
                (tiktok?.urls?.length || 0) +
                (youtube?.urls?.length || 0) +
                (instagram?.urls?.length || 0) +
                (douyin?.urls?.length || 0) +
                (xhs?.urls?.length || 0)
              cost = count * quotaCost
              break
            }
            default:
              break
          }
          break
        }
        case QuotaType.TT_WEB_LIST: {
          const { urls } = request.body as any
          cost = Math.ceil(urls.length / quotaCost)
          break
        }
        case QuotaType.INS_WEB_LIST: {
          const { urls } = request.body as any
          cost = Math.ceil(urls.length / quotaCost)
          break
        }
        default:
          break
      }
      const quotaCheck = await MembershipService.getInstance().checkMemberShipQuotaStatus(
        user.id,
        cost,
      )
      if (!quotaCheck.hasQuota) {
        return reply.status(400).send(quotaCheck.error)
      }

      (request as any).quotaInfo = {
        cost: cost,
        type: quotaType,
        userId: user.id,
      }
    },
    // 在响应序列化之前，将 payload 存储在 request 中, 以便在 onResponse 中使用
    preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      (request as any).responsePayload = payload
      return payload
    },

    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      if (reply.statusCode >= 200 && reply.statusCode < 300) {
        const quotaInfo = (request as any).quotaInfo
        const responsePayload = (request as any).responsePayload

        if (quotaInfo && quotaInfo.type) {
          try {
            await MembershipService.getInstance().deductQuota({
              userId: quotaInfo.userId,
              type: quotaInfo.type,
              projectId: (request.body as any)?.projectId,
              taskId: responsePayload?.data?.id,
              createdBy: quotaInfo.userId,
              count: quotaInfo.cost,
            })
          } catch (error) {
            console.error('Failed to deduct quota:', error)
          }
        }
      }
    },
  }
}

// 增加动态计算配额，根据req的参数动态计算配额
export const dynamicCheckQuota = (
  quotaCost: QuotaCost | ((req: FastifyRequest) => number),
  quotaType?: QuotaType,
) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      const actualCost = typeof quotaCost === 'function' ? quotaCost(request) : quotaCost
      const quotaCheck = await MembershipService.getInstance().checkMemberShipQuotaStatus(
        user.id,
        actualCost,
      )
      if (!quotaCheck.hasQuota) {
        return reply.status(400).send(quotaCheck.error)
      }
      (request as any).actualQuotaCost = actualCost
    },
    preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      // 获取整个响应数据 {statusCode,error,message,data}
      (request as any).responsePayload = payload
      return payload
    },
    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      if (reply.statusCode >= 200 && reply.statusCode < 300) {
        const { params, query, body, user, responsePayload, actualQuotaCost } = request as any
        if (quotaType) {
          try {
            let taskId, projectId
            switch (quotaType) {
              case QuotaType.SIMILAR_SEARCH: {
                taskId = (responsePayload as any)?.data?.id
                projectId = (responsePayload as any)?.data?.projectId
                break
              }
              case QuotaType.TT_HASH_TAG_BREAK:
              case QuotaType.INS_HASH_TAG_BREAK: {
                taskId = (responsePayload as any)?.data?.id
                projectId = (responsePayload as any)?.data?.projectId
                break
              }
              default:
                break
            }

            const deductDynamicQuotaParams: DeductDynamicQuotaParams = {
              userId: user.id,
              quotaType: quotaType,
              quota: actualQuotaCost,
              projectId: projectId,
              taskId: taskId,
              metadata: {
                requestParams: { params, query, body },
              },
            }
            await MembershipService.getInstance().deductDynamicQuota(deductDynamicQuotaParams)
          } catch (error) {
            console.error('Failed to deduct quota:', error)
          }
        }
      }
    },
  }
}

// 响应式扣费中间件 - 根据响应内容动态计算扣费
export interface ResponseBasedQuotaOptions {
  // 计算扣费的函数，根据响应内容返回扣费金额
  calculateCost: (responseData: any, request: FastifyRequest) => number | Promise<number>
  // 配额类型，可以是固定类型或动态计算
  quotaType: QuotaType | ((responseData: any, request: FastifyRequest) => QuotaType)
  // 检查配额的函数，返回需要检查的最大可能配额
  checkQuota?: (request: FastifyRequest) => number
  // 从响应中提取元数据的函数
  extractMetadata?: (responseData: any, request: FastifyRequest) => Record<string, any>
}

export const responseBasedQuota = (options: ResponseBasedQuotaOptions) => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      if (!user) {
        return reply
          .status(401)
          .send(
            errorResponse(StatusCodes.UNAUTHORIZED, 'NEED_AUTH', 'User authentication required'),
          )
      }

      // 如果提供了检查配额的函数，先检查配额
      if (options.checkQuota) {
        const maxCost = options.checkQuota(request)
        const quotaCheck = await MembershipService.getInstance().checkMemberShipQuotaStatus(
          user.id,
          maxCost,
        )
        if (!quotaCheck.hasQuota) {
          return reply.status(400).send(quotaCheck.error)
        }
      }
    },
    preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
      (request as any).responsePayload = payload
      return payload
    },
    onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
      if (reply.statusCode >= 200 && reply.statusCode < 300) {
        const { user, responsePayload } = request as any

        try {
          // 计算实际扣费
          const quota = await options.calculateCost(responsePayload, request)
          Logger.info(`Actual cost: ${quota}}`)
          if (quota > 0) {
            // 获取配额类型
            const quotaType =
              typeof options.quotaType === 'function'
                ? options.quotaType(responsePayload, request)
                : options.quotaType

            // 提取元数据
            const metadata = options.extractMetadata
              ? options.extractMetadata(responsePayload, request)
              : {}

            // 执行扣费
            await MembershipService.getInstance().deductDynamicQuota({
              userId: user.id,
              quotaType,
              quota,
              metadata,
            })
          }
        } catch (error) {
          console.error('Failed to deduct quota based on response:', error)
        }
      }
    },
  }
}
