import { StatusCodes, errorResponse } from '@/common/response/response'
import { REDIS_KEY_PREFIX, SUPERLIKE_LIMIT, SUPERLIKE_WINDOW_SIZE } from '@/config/env'
import { redis } from '@/infras/redis'
import { ProjectKolAttitude } from '@repo/database'

export const superlikeLimit = async (request: any, reply: any) => {
  const attitude = (request.body as any)?.attitude
  if (attitude !== ProjectKolAttitude.SUPERLIKE) {
    return
  }
  const user = (request as any).user

  // const memberType = await MembershipService.getInstance().getMemberType(userId)
  // if (memberType === MemberType.FREE) {
  //   return reply
  //     .status(403)
  //     .send(
  //       errorResponse(
  //         StatusCodes.FORBIDDEN,
  //         'FREE_USER_RESTRICTED',
  //         'Superlike feature is only available for paid users',
  //       ),
  //     )
  // }

  const key = `${REDIS_KEY_PREFIX}superlike:${user.id}`

  try {
    const multi = redis.multi()

    multi.zadd(key, Date.now(), `${Date.now()}:${Math.random()}`)

    const windowStart = Date.now() - +SUPERLIKE_WINDOW_SIZE * 1000
    multi.zremrangebyscore(key, 0, windowStart)

    multi.zcard(key)

    multi.expire(key, +SUPERLIKE_WINDOW_SIZE)

    const results = await multi.exec()

    const count = results?.[2][1] as number

    if (count > +SUPERLIKE_LIMIT) {
      return reply
        .status(429)
        .send(
          errorResponse(
            StatusCodes.TOO_MANY_SUPERLIKES,
            'RATE_LIMIT_EXCEEDED',
            'Superlike limit reached. Please wait.',
          ),
        )
    }
  } catch (error) {
    console.error('Rate limit error:', error)
    // Redis 出错，为了用户体验，选择放行
    return
  }
}
