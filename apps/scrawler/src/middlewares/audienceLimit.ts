import { errorResponse, StatusCodes } from '@/common/response/response'
import { MembershipService } from '@/services/membership.service'
import { MemberType } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

export const audienceLimit = async (request: FastifyRequest, reply: FastifyReply) => {
  const userId = (request as any).user?.id
  if (!userId) {
    return
  }

  const memberType = await MembershipService.getInstance().getMemberType(userId)
  if (memberType === MemberType.FREE) {
    return reply
      .status(400)
      .send(
        errorResponse(
          StatusCodes.FREE_USER_RESTRICTED,
          'FREE_USER_RESTRICTED',
          'Audience analysis feature is only available for paid users.',
        ),
      )
  }

  console.log(`[audienceLimit] ${memberType}用户 ${userId} 请求受众分析，正常处理`)
  return
}
