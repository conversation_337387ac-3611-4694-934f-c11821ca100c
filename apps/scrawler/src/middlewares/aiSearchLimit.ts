import { StatusCodes, errorResponse } from '@/common/response/response'
import { MembershipService } from '@/services/membership.service'
import { MemberType } from '@repo/database'

export const aiSearchLimit = async (request: any, reply: any) => {
  const userId = (request.user as any).id

  const memberType = await MembershipService.getInstance().getMemberType(userId)
  if (memberType === MemberType.FREE) {
    return reply
      .status(403)
      .send(
        errorResponse(
          StatusCodes.FORBIDDEN,
          'FREE_USER_RESTRICTED',
          'ai search feature is only available for paid users',
        ),
      )
  }
  return
}
