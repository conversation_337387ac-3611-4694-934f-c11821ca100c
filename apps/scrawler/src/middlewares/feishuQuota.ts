import { errorResponse, StatusCodes } from '@/common/response/response'
import { FEISHU_SHORTCUT_PUBLIC_KEY } from '@/config/env'
import { quotaKey } from '@/services/feishu'
import Logger from '@/utils/logger'
import { prisma } from '@repo/database'
import crypto from 'crypto'
import { FastifyReply, FastifyRequest } from 'fastify'

const basePublicKey = FEISHU_SHORTCUT_PUBLIC_KEY || ''

// encoded data string 的 JSON 格式
type FeishuEncodedData = {
  source: string //  来源是 base
  version: string // 固定是 V1
  packID: string // 包唯一 ID
  exp: number // 过期时间，当前时间 + 10min
}

function rsaVerySign(data: string, signData: string, publicKey: string) {
  const verify = crypto.createVerify('RSA-SHA256')
  verify.update(data)
  verify.end()
  const signatureBuffer = Buffer.from(signData, 'base64')
  return verify.verify(publicKey, signatureBuffer)
}

function verifyFeishuSignature(input: string) {
  // 要验签的签名数据
  const data = input.split('.')
  // 要签名的原始数据,base64解压缩后会得到原始数据
  const encodedData = Buffer.from(data[0], 'base64').toString('utf8')
  Logger.debug('feishu encoded data', { encodedData })
  // Base用私钥签名后得到的加密数据
  const encodedSign = data[1]
  // 验签
  return rsaVerySign(encodedData, encodedSign, basePublicKey)
}

export const feishuShortcutApiKeyMiddleware = async (
  request: FastifyRequest,
  reply: FastifyReply,
) => {
  Logger.debug('feishu request headers', { headers: request.headers })
  Logger.debug('feishu request body', { body: request.body })
  const isValid = verifyFeishuSignature(request.headers['x-base-signature'] as string)
  if (!isValid) {
    return reply.status(200).send(errorResponse(StatusCodes.UNAUTHORIZED, '非法请求'))
  }
  const body = JSON.parse(request.body as string)
  const apiKey = body.api_key
  const talentKey = request.headers['x-talent-key'] as string
  Logger.info('feishu request processing', {
    apiKey,
    talentKey,
    body: request.body,
  })

  try {
    if (apiKey && apiKey !== '{{api_key}}') {
      // sb feishu API
      // 检查 API key 是否存在
      const apiKeyRecord = await prisma.feishuShortcutApiKey.findUnique({
        where: { apiKey },
      })

      if (!apiKeyRecord) {
        return reply.status(200).send(errorResponse(StatusCodes.UNAUTHORIZED, 'API key 不合法'))
      }

      // 检查额度是否足够
      if (apiKeyRecord.remainingQuota <= 0) {
        return reply.status(200).send(errorResponse(StatusCodes.FORBIDDEN, '额度不足'))
      }

      // 扣除额度
      if (body.url.toLowerCase() !== quotaKey) {
        await prisma.feishuShortcutApiKey.update({
          where: { apiKey },
          data: {
            remainingQuota: {
              decrement: 1,
            },
          },
        })
      }
      request.body = JSON.stringify({ url: body.url, api_key: apiKey })
    } else if (talentKey) {
      // 使用 talent-key 查找或创建免费额度记录
      const freeApiKey = `FREE-${talentKey}`
      let apiKeyRecord = await prisma.feishuShortcutApiKey.findUnique({
        where: { apiKey: freeApiKey },
      })

      if (!apiKeyRecord) {
        // 创建新的免费额度记录
        apiKeyRecord = await prisma.feishuShortcutApiKey.create({
          data: {
            apiKey: freeApiKey,
            quota: 500,
            remainingQuota: 500,
          },
        })
      }

      // 检查额度是否足够
      if (apiKeyRecord.remainingQuota <= 0) {
        return reply.status(200).send(errorResponse(StatusCodes.FORBIDDEN, '额度不足'))
      }

      // 扣除额度
      if (body.url.toLowerCase() !== quotaKey) {
        await prisma.feishuShortcutApiKey.update({
          where: { apiKey: freeApiKey },
          data: {
            remainingQuota: {
              decrement: 1,
            },
          },
        })
      }
      request.body = JSON.stringify({ ...body, api_key: freeApiKey })
    } else {
      return reply.status(200).send(errorResponse(StatusCodes.UNAUTHORIZED, '非法请求'))
    }
  } catch (error) {
    Logger.error('飞书额度检查失败', error as Error)
    return reply.status(200).send(errorResponse(StatusCodes.SERVER_ERROR, '额度检查失败'))
  }
}
