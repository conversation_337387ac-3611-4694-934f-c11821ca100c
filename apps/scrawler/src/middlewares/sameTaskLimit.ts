import { StatusCodes, errorResponse } from '@/common/response/response'
import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'
import { FastifyReply, FastifyRequest } from 'fastify'

const SAME_TASK_WINDOW_SIZE = 5 //5s
const REDIS_SAME_TASK_PREFIX = `${REDIS_KEY_PREFIX}sameTask:`

export const sameTaskLimit = async (request: FastifyRequest, reply: FastifyReply) => {
  const userId = (request as any).user?.id
  const { projectId, source, platform, reason } = request.body as any

  if (!userId || !projectId || !source || !platform || !reason) {
    return
  }

  // 构造唯一的任务标识
  const taskKey = `${REDIS_SAME_TASK_PREFIX}${userId}:${projectId}:${platform}:${source}:${reason}`

  try {
    // 尝试设置键，如果键不存在，设置成功返回1，如果键已经存在，返回0
    const result = await redis.set(taskKey, '1', 'NX', 'EX', SAME_TASK_WINDOW_SIZE)

    if (!result) {
      return reply
        .status(429)
        .send(
          errorResponse(
            StatusCodes.TOO_MANY_REQUESTS,
            'DUPLICATE_TASK_REQUEST',
            'Please do not create the same task repeatedly. Please try again later.',
          ),
        )
    }
  } catch (error) {
    console.error('Same task limit error:', error)
    // Redis 出错时，为了用户体验，选择放行
    return
  }
}
