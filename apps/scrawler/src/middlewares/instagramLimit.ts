import { StatusCodes, errorResponse } from '@/common/response/response'
import { KolPlatform, prisma } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

export const instagramLimit = () => {
  return {
    preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
      const user = (request as any).user
      if (!user?.id) {
        return reply
          .status(401)
          .send(
            errorResponse(StatusCodes.UNAUTHORIZED, 'NEED_AUTH', 'User authentication required'),
          )
      }
      const platform = (request.body as any).platform
      const membership = await prisma.userMembership.findFirst({
        where: {
          userId: user.id,
        },
      })
      if (platform == KolPlatform.INSTAGRAM && membership?.type == 'FREE') {
        return reply
          .status(403)
          .send(
            errorResponse(
              StatusCodes.FORBIDDEN,
              'FREE_USER_RESTRICTED',
              'Instagram search feature is only available for paid users.',
            ),
          )
      }
    },
  }
}
