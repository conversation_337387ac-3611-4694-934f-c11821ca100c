import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { TiktokVideoDetail } from '@/api/@types/rapidapi/Tiktok'
import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import { LinkType } from '@/services/contact/contact'
import { KolPlatform } from '@repo/database'

export interface UploadRecord {
  content: string
  lineNumber: number

  linkType?: LinkType
  platform?: KolPlatform
  platformAccount?: string
  postId?: string
  kolId?: string
  postDetail?: VideoDetail | TiktokVideoDetail | IgPost

  result?: UploadResultType
  message?: string
}

export type UploadResultType = 'success' | 'failed' | 'skipped'

export interface ExcludeListStatistic {
  count: number
  tiktokCount?: number
  youtubeCount?: number
  instagramCount?: number
}
