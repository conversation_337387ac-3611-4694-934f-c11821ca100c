export type TtHashTagBreakResult = {
  uniqueIds: string[]
  cursor: number
  hasMore: boolean
  total: number
  message?: string
}

export type TtBgmBreakResult = {
  uniqueIds: string[]
  cursor: number
  hasMore: boolean
  total: number
  message?: string
}

export type TtSearchKeyWordBreakResult = {
  uniqueIds: string[]
  cursor: number
  hasMore: boolean
  total: number
  videoCount: number
  message?: string
  progress: {
    videoCount: number
  }
}

export type InsHashTagBreakResult = {
  uniqueIds: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
}

export type YoutubeHashTagBreakResult = {
  channelIds: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
}
export type InsTaggedBreakResult = {
  usernames: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
}
