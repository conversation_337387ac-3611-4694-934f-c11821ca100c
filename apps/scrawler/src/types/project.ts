import { KolPlatform, TaskType } from '@repo/database'
import { SearchSource } from './response/union-search.response'

export interface ProjectCandidate {
  kolId: string
  platformId: string
  platform: KolPlatform
  score: number
  reason?: SearchSource
}

export type ProjectCandidates = Record<
  TaskType,
  {
    kols: ProjectCandidate[]
    taskId: string
    updatedAt: Date
  }
>

export interface ProjectConfig {
  allowList?: string[]
  banList?: string[]
  /** @deprecated use kolDescription instead */
  requirement?: string
  kolDescription?: string
}
