import { KolPlatform } from '@repo/database'
// 新表头
export interface kolExportData {
  kolTitle: string // nickname
  platformAccount: string // username
  country: string // 国家/地区
  platform: KolPlatform // 平台
  numericSubscriberCount: string // 粉丝数
  videosAverageViewCount: string // 平均播放
  score: string // 相似度【分数越高越相似】
  kolEmail: string // 邮箱
  emailRecord: string // AI邮件发送记录
  officialEmailButton: string // YouTube官方邮件按钮
  lastPublishedTime: string // 最近发布时间
  weeklyPostCount: number // 周发布数量
  monthlyPostCount: number // 月发布数量
  url?: string
  commonFields?: string // 来源
  createdAt?: string // 收藏时间
}

export interface UnionSearchExportData {
  title: string | null
  description: string | null
  platform: KolPlatform
  email: string | null
  platformAccount: string | null
  score: string // 百分比
  url: string
}
