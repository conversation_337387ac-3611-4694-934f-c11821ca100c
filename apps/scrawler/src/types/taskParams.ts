import {
  PluginPublicationAudienceTaskParams,
  PublicationAudienceTaskParams,
} from '@/types/publicationStatistics'
import { TaskType } from '@repo/database'
import {
  AudienceAnalysisTaskParams,
  InsFollowingListTaskParams,
  InsHashTagBreakTaskParams,
  InsLongCrawlerTaskParams,
  InsTaggedBreakTaskParams,
  InsWebListTaskParams,
  KeywordTaskParams,
  SimilarTaskParams,
  TrackEasyKOLTaskParams,
  TtBgmBreakTaskParams,
  TtFollowersSimilarTaskParams,
  TtFollowingListTaskParams,
  TtHashTagBreakTaskParams,
  TtSearchInputBreakTaskParams,
  TtWebListTaskParams,
  YoutubeHashTagBreakTaskParams,
  YoutubeSearchInputBreakTaskParams,
} from './task.ts'

// 任务类型到对应参数类型的映射
export interface TaskTypeToParamsMap {
  [TaskType.SIMILAR]: SimilarTaskParams
  [TaskType.KEYWORD]: KeywordTaskParams
  [TaskType.HASH_TAG_BREAK]:
    | TtHashTagBreakTaskParams
    | InsHashTagBreakTaskParams
    | YoutubeHashTagBreakTaskParams
  [TaskType.SEARCH_INPUT_BREAK]: TtSearchInputBreakTaskParams | YoutubeSearchInputBreakTaskParams
  [TaskType.FOLLOWERS_SIMILAR]: TtFollowersSimilarTaskParams
  [TaskType.FOLLOWING_LIST]: TtFollowingListTaskParams | InsFollowingListTaskParams
  [TaskType.AUDIENCE_ANALYSIS]: AudienceAnalysisTaskParams
  [TaskType.EASYKOL_TRACK]: TrackEasyKOLTaskParams
  [TaskType.BGM_BREAK]: TtBgmBreakTaskParams
  [TaskType.WEB_LIST]: TtWebListTaskParams | InsWebListTaskParams
  [TaskType.TAGGED_BREAK]: InsTaggedBreakTaskParams
  [TaskType.POST_AUDIENCE]: PublicationAudienceTaskParams | PluginPublicationAudienceTaskParams
  [TaskType.LONG_CRAWLER]: InsLongCrawlerTaskParams
}

// 获取指定任务类型的参数类型
export type GetTaskParams<T extends TaskType> = TaskTypeToParamsMap[T]

/**
 * 任务参数类型的联合类型
 * 当不确定具体任务类型时可以使用此类型
 */
export type TaskParams = {
  [K in TaskType]: TaskTypeToParamsMap[K]
}[TaskType]

// 排除特定任务类型的 TaskParams 类型
export type OmitTaskType<T extends TaskType> = {
  [K in TaskType]: K extends T ? never : TaskTypeToParamsMap[K]
}[TaskType]

/**
 * 根据平台和任务类型获取精确的参数类型
 */
export interface PlatformTaskTypeMap {
  tiktok: {
    [TaskType.HASH_TAG_BREAK]: TtHashTagBreakTaskParams
    [TaskType.SEARCH_INPUT_BREAK]: TtSearchInputBreakTaskParams
    [TaskType.FOLLOWERS_SIMILAR]: TtFollowersSimilarTaskParams
    [TaskType.FOLLOWING_LIST]: TtFollowingListTaskParams
    [TaskType.BGM_BREAK]: TtBgmBreakTaskParams
    [TaskType.WEB_LIST]: TtWebListTaskParams
    [TaskType.SIMILAR]: SimilarTaskParams
    [TaskType.KEYWORD]: KeywordTaskParams
    [TaskType.AUDIENCE_ANALYSIS]: AudienceAnalysisTaskParams
    [TaskType.POST_AUDIENCE]: PublicationAudienceTaskParams | PluginPublicationAudienceTaskParams
  }
  instagram: {
    [TaskType.HASH_TAG_BREAK]: InsHashTagBreakTaskParams
    [TaskType.FOLLOWING_LIST]: InsFollowingListTaskParams
    [TaskType.WEB_LIST]: InsWebListTaskParams
    [TaskType.SIMILAR]: SimilarTaskParams
    [TaskType.KEYWORD]: KeywordTaskParams
    [TaskType.AUDIENCE_ANALYSIS]: AudienceAnalysisTaskParams
    [TaskType.TAGGED_BREAK]: InsTaggedBreakTaskParams
    [TaskType.POST_AUDIENCE]: PublicationAudienceTaskParams | PluginPublicationAudienceTaskParams
    [TaskType.LONG_CRAWLER]: InsLongCrawlerTaskParams
  }
  youtube: {
    [TaskType.SIMILAR]: SimilarTaskParams
    [TaskType.KEYWORD]: KeywordTaskParams
    [TaskType.HASH_TAG_BREAK]: YoutubeHashTagBreakTaskParams
    [TaskType.SEARCH_INPUT_BREAK]: YoutubeSearchInputBreakTaskParams
    [TaskType.AUDIENCE_ANALYSIS]: AudienceAnalysisTaskParams
    [TaskType.POST_AUDIENCE]: PublicationAudienceTaskParams | PluginPublicationAudienceTaskParams
  }
  twitter: {
    [TaskType.SIMILAR]: SimilarTaskParams
  }
}

// 支持的平台
export type SupportedPlatform = keyof PlatformTaskTypeMap

// 获取指定平台和任务类型的参数类型
export type GetPlatformTaskParams<
  P extends SupportedPlatform,
  T extends keyof PlatformTaskTypeMap[P],
> = PlatformTaskTypeMap[P][T]

/**
 * 任务参数服务，用于处理任务参数相关的逻辑
 */
export class TaskParamsService {
  // 获取任务的平台信息
  static getPlatform(
    params: any,
    defaultPlatform: 'unknown' = 'unknown',
  ): SupportedPlatform | 'unknown' {
    // 处理 EASYKOL_TRACK 任务特殊情况
    if ('spreadsheetId' in params) {
      return defaultPlatform
    }

    if (params.platform) {
      const platform = params.platform.toLowerCase()
      if (
        platform === 'youtube' ||
        platform === 'tiktok' ||
        platform === 'instagram' ||
        platform === 'twitter'
      ) {
        return platform as SupportedPlatform
      }
    }

    return defaultPlatform
  }

  //安全获取 projectId，兼容所有任务类型
  static getProjectId(params: TaskParams): string | undefined {
    if ('projectId' in params) {
      return params.projectId
    }
    return undefined
  }
}
