// import { DEFAULT_WELCOME_PAGE_URL } from '@/config/env'
// import { dynamicGet } from '@/infras/remembrall'

// /** @deprecated */
// export async function getWelcomePageConfig() {
//   const map = new Map<string, string>()
//   const defaultUrl = DEFAULT_WELCOME_PAGE_URL
//   map.set('default', defaultUrl)
//   const remoteConfig = (await dynamicGet('welcome_page_config', 60)) as object
//   Object.entries(remoteConfig).forEach(([key, value]) => {
//     map.set(key.replace(/#/g, '.'), value as string)
//   })
//   return map
// }
