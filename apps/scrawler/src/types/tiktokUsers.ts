import { EmailSourceType } from './email'
import { TtVideoBasicInfo } from './kol'

export type TtUserDetailsAndVideos = {
  userId: string
  uniqueId: string
  nickname: string
  avatar: string
  signature: string
  followerCount: number
  followingCount: number
  heartCount: number
  videoCount: number
  verified: boolean
  privateAccount: boolean
  // 社交媒体字段
  instagramId: string
  twitterId: string
  youtubeChannelTitle: string
  youtubeChannelId: string
  email?: string
  emailSource?: EmailSourceType
  videos: TtVideoBasicInfo[]
}
