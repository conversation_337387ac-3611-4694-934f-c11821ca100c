export type Prompt = {}

export type SystemPrompt = Prompt & {
  type: 'system'
  task: string
  role?: string
  context?: string
  format?: string
  constraint?: string
}

export type UserPrompt = Prompt & {
  type: 'user'
}

export const generatePrompt = (prompt: SystemPrompt | UserPrompt): string => {
  if (prompt.type == 'system') {
    if (!prompt.task) {
      throw new Error('task is required')
    }
    let result = ''
    Object.keys(prompt).forEach((key: string) => {
      if (typeof key == 'string' && key !== 'type') {
        result = append(result, prompt[key], key)
      }
    })
    return result
  }
  return ''
}

const append = (text: string, content: string, tag?: string): string => {
  if (tag) {
    return text + `\n<${tag}>\n\t${content}\n</${tag}>\n`
  } else {
    return text + '\n\n' + content
  }
}
