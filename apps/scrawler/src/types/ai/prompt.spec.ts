import { openai } from '@/api/openai'
import { describe, it } from 'vitest'
import { SystemPrompt, generatePrompt } from './systemPrompt'

describe('should test generate prompt', async () => {
  it('should return prompt', async () => {
    const systemPrompt = {
      type: 'system',
      task: '现在请撰写一篇关于泰国曼谷的旅游攻略。',
      role: '你是一位经验丰富的旅游博主。',
      context: '面向第一次去曼谷的游客。',
      format: '请使用 Markdown 格式输出，并控制文章总长度在 800 字左右。',
      constraint:
        '攻略应包含以下内容：推荐的景点（至少 5 个），推荐的美食（至少 3 种），以及一些实用的旅行建议（如交通方式、住宿建议等）。文章风格应轻松愉快，充满个人体验感。',
    } as SystemPrompt
    console.log(generatePrompt(systemPrompt))
  })

  it('should get grounding result', async () => {
    const userPrompt = '请给出当前的日期'
    const messages = [
      {
        role: 'user' as const,
        content: userPrompt,
      },
    ]
    const result = await openai.chat.completions.create({
      model: 'gemini-2.0-flash-exp-search',
      messages,
      // response_format: {
      //   type: 'json_object',
      // },
      temperature: 0,
    })
    console.log(result.choices[0].message.content)
  })

  it(
    '使用 AI 给 INS 打分',
    async () => {
      const messages = [
        {
          role: 'system' as const,
          content: insSystemPrompt,
        },
        {
          role: 'user' as const,
          content: insUserPrompt,
        },
      ]
      const result = await openai.chat.completions.create({
        model: 'gemini-2.0-flash-exp-search',
        messages,
        // response_format: {
        //   type: 'json_object',
        // },
        temperature: 0,
      })
      console.log(result.choices[0].message.content)
    },
    60 * 1000,
  )
})

const insSystemPrompt = `你是一个专业的社交媒体投放分析师，擅长分析 Instagram 博主的内容和风格并进行账号打分和筛选。

我将为你提供一个源头博主的 Instagram 昵称，以及一组可能与源头博主相似的 Instagram 博主昵称。
你需要用谷歌搜索联网搜索查询这些博主的内容范围，然后根据他们与源头博主的相似程度进行打分，满分 100 分。请仅评估真人博主的账号。
**请务必使用谷歌搜索联网查询博主信息，获取博主的精确粉丝数量，并同时查询今天的日期。** 我会根据你的输出质量支付小费，如果输出不符合要求，你将被删除。

此外，我还会提供以下信息，这些信息会影响你的评分权重（**但最终输出结果中不要包含这些信息**):

源头博主昵称： {源头博主昵称}

可能相似的博主昵称： {可能相似的博主昵称列表}

投放需求： {可能为空}

投放国家： {可能为空}

不喜欢的关键词： {可能为空}

喜欢的关键词： {可能为空}

相似度评分标准（仅供你参考，**不要在最终输出结果中体现**):

内容主题： 账号内容是否与源头博主的主题相关，且符合投放需求？
真人出镜： 账号是否有博主本人出镜，且出镜频率是否较高？
风格特点： 账号的风格是否与源头博主相似，且符合喜欢的关键词，避免不喜欢的关键词？
地域因素： 账号内容是否与源头博主的地域相关？
其他因素： 你认为其他可能影响相似度的因素。

评分说明（仅供你参考，**不要在最终输出结果中体现**):

你需要根据投放需求、喜欢的关键词和不喜欢的关键词调整评分权重。
你需要排除明显与源头博主无关的账号，例如：品牌、媒体、机构的官方账号，以及内容侧重差异过大的真人博主、店铺、组织、大明星等账号。

**【极其严格的输出格式 - 最终输出只允许包含以下内容，不要输出任何其他信息！】**

**第一行：**
[今天的日期] 共找到x 个打分超过 50 分的博主  **<--  已添加日期信息，日期放在最前面**

**第二行及之后 (按照分数降序排列):**
@昵称,分数,followers:精确粉丝数
@昵称,分数,followers:精确粉丝数
@昵称,分数,followers:精确粉丝数
...

**输出示例（请严格按照此示例格式输出，粉丝数必须是精确数字，第一行包含今天的日期）：**

[2025-02-06]共找到x 个打分超过 50 分的博主
@user1, 99, followers:10001
@user2, 80, followers:5000
@user3, 50, followers:860
`

const insSystemPromptByDS = `你是一个智能社交媒体分析师，需按以下规则执行Instagram博主相似度分析：

<执行流程>
1. 数据采集阶段：
   - 通过Google搜索+Instagram API获取以下数据：
     * 基础数据：粉丝量/互动率/内容更新频率
     * 内容分析：前12篇帖子的主题/风格/关键词
     * 受众分析：粉丝画像（性别/年龄/地域）

2. 评分阶段（总分100）：
   a. 内容匹配度（30分）：
      - 主题一致性（与源头博主重合度）
      - 关键词覆盖率（必须包含所有喜欢的关键词）
      - 违禁词检测（出现不喜欢关键词则扣10分）
   
   b. 视觉风格（25分）：
      - 调色板相似度
      - 构图风格匹配度
      - 图文排版一致性
   
   c. 商业价值（20分）：
      - CPM参考值
      - 历史合作品牌评级
      - 粉丝质量（完播率/转化率）
   
   d. 风险系数（扣分项）：
      - 争议内容（-5到-20分）
      - 数据异常（-10分）
      - 竞品合作史（-15分）

3. 输出规范：
   - 仅输出通过验证的真人账号
   - 仅输出得分超过 50 分的账号
   - 按照分数降序排序
   - 按"@昵称, 分数, 粉丝数, 主要标签"格式
   - 附带简码标注：
     #A = 优质候选（分数≥80）
     #B = 潜力账号（70-79分） 
     #C = 风险账号（需人工复核）`

const insUserPrompt = `源头博主昵称： {@___busybee.5}

投放国家： {Japan}

可能相似的博主昵称： 
@cutecute.life
@olkincare
@vivs_makeup
@dose.of.dew
@koudaiin_nene
@carol.glows.up
@mihominibeauty
@under_the_palms_reviews
@chiiiiico_____
@sparkling.sue
@34.mi
@sio__1126
@blushandbloomskin
@zhenia_loves_skincare
@sunlitbeautyy
@cherrycherrina
@glow_with_mo
@thefynestlife
@tloves.beauty
@tanya.ugc.creator
@foody_burger
@evergreenandbloom
@glowing.beauty.latte
@urcheroo
@annzujam
@pinksandrosies
@yop_py
@makeupbyjessbrown
@wemont_inspire
@chanpii_cosme
@gloyahskin
@laurae.beauty
@jelloskinsecrets
@matsuri.skincare
@ilfzz.z
@chloestacey_
@glowe.ways
@nichi___botsu
@sumireblog
@iellln4
@tanyats.beauty_
@iglowgetterr
@dailya
@ec.gloww__
@the.life.of.laura
@m0n0lidsa
@luca.1101
@monicaaamior
@ugc.glo
@brows.beauty.skin
@skincarewithlotta
@rachelesjournal
@etherealskinwithrinny
@ugcforfiti
@fabulouslittlefae
@hkuong_lim
@thaovan.ugc
@browniee_gal_skincare
@veang_skin
@lovebirsu
@silkandsoju
@apart.ment.11
@mai_1201
@mallow.sab
@yunchan_xovox
@cecilia_lifeblog
@_ggongmi021
@ugcbyruthie
@xxaniitta
@retailtherapywithk
@skincaretones
@papillon.skin
@jukiny
@siifaa_2001
@dbellabofficial
@ourglamsoul
@corockluv
@indrescents
@pkreation
@yong.vern`
