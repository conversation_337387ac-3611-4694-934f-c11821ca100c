import { IgUser } from '@/api/@types/rapidapi/Instagram.ts'
import { KolPlatform } from '@repo/database'

export enum InstagramSortType {
  TOP = 'top',
  RECENT = 'recent',
  CLIPS = 'clips',
}

export function igUserToKol(user: IgUser) {
  return {
    title: user.fullName,
    description: user.biography,
    updatedAt: new Date(),
    email: user.publicEmail || user.biographyEmail,
    historyEmails: [],
    platformAccount: user.username,
    platform: KolPlatform.INSTAGRAM,
    avatar: user.profilePicUrl,
  }
}
