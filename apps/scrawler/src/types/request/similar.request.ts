export type createTaskRequest = {
  projectId: string
  source: string
  platform: string
  regions?: string[]
  minSubscribers?: number
  maxSubscribers?: number
  lastPublishedDays?: number
  videosAverageViews?: number
  maxVideosAverageViews?: number
  minAverageLikeCount?: number
  maxAverageLikeCount?: number
  banList?: string[]
  allowList?: string[]
  channelId?: string
  kolDescription?: string
  taskRound?: number
  // tiktok mode
  ttMode?: number
  ttModeReason?: string
  // youtube mode
  ytbMode?: number
  ytbVideoIds?: string[]
  // ins mode
  insMode?: number
  // youtube channel handle
  channelHandle?: string
  // twitter mode
  twitterMode?: number
  twitterUserNames?: string[]
}

// similar task request
export interface SimilarTaskRequest {
  projectId: string
  source: string
  platform: string
  regions?: string[]
  minSubscribers?: number
  maxSubscribers?: number
  lastPublishedDays?: number
  videosAverageViews?: number
  maxVideosAverageViews?: number
  minAverageLikeCount?: number
  maxAverageLikeCount?: number
  reason?: string
  banList?: string[]
  allowList?: string[]
  kolDescription?: string
  taskRound?: number
  // tiktok mode
  ttMode?: number
  ttModeReason?: string
  // youtube mode
  ytbMode?: number
  ytbVideoIds?: string[]
  // ins mode
  insMode?: number
  // twitter mode
  twitterMode?: number
  twitterUserNames?: string[]
}
