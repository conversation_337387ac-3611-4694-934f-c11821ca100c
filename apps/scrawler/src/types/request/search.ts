export type KeywordSearchRequest = {
  platform: string
  projectId: string
  keywords: string[]
  regions?: string[]
  url?: string
  description?: string
  viewCountGte?: number
}

export type TtHashTagBreakRequest = {
  projectId: string
  platform: string
  tag: string
  reason?: string
  maxVideoCount?: number
  currentVideoCount?: number
}

// instagram 的 hashTag爆破
export type InsHashTagBreakRequest = {
  projectId: string
  platform: string
  tag: string
  reason?: string
}

export type TtSearchInputBreakRequest = {
  projectId: string
  platform: string
  searchInput: string
  sortType: 0 | 1 | 3
  publishTimeType: 0 | 1 | 7 | 30 | 90 | 180
  reason?: string
  maxVideoCount: number
}
export type TtFollowersSimilarRequest = {
  projectId: string
  platform: string
  uniqueId: string
  excludeWords?: string
  reason?: string
}
export type TtFollowingListRequest = {
  projectId: string
  platform: string
  uniqueId: string
  reason?: string
  maxCount?: number
  currentCount?: number
}
