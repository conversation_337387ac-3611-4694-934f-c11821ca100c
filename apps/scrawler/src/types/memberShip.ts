import {
  FREE_QUOTA_AFTER_FIRST_MONTH_DAYS,
  FREE_QUOTA_FIRST_DAY,
  FREE_QUOTA_FIRST_MONTH,
  FREE_QUOTA_FIRST_WEEK,
  FREE_QUOTA_FIRST_WEEK_DAYS,
  FREE_QUOTA_REGULAR, // 常规配额
} from '@/config/env'
import {
  CardSubscriptionStatus,
  Enterprise,
  EnterpriseStatus,
  MemberStatus,
  MemberType,
  QuotaType,
} from '@repo/database'

export interface UpdateMembershipParams {
  type?: MemberType
  effectiveAt?: Date
  expireAt?: Date
  accountQuota?: number
  timezone?: string
  status?: MemberStatus
  usedQuota?: number
}

export interface MemberShipInfo {
  userId: string
  email: string
  effectiveAt: Date
  expireAt: Date
  accountQuota: number
  usedQuota: number
  createdAt: Date
}

// memberShip error  messages
export const ERROR_MESSAGES = {
  USER_NOT_FOUND: 'User not found',
  MEMBERSHIP_NOT_FOUND: 'Membership not found',
  MEMBERSHIP_NOT_EFFECTIVE: 'Membership is not effective yet',
  MEMBERSHIP_EXPIRED: 'Membership has expired',
  INSUFFICIENT_QUOTA: (required: number, remaining: number) =>
    `Insufficient quota: required ${required}, remaining ${remaining}`,
  UPDATE_FAILED: (reason: string) => `Failed to update membership: ${reason}`,
  MEMBERSHIP_SUSPENDED: 'Membership is suspended, please contact admin to unlock',
  ENTERPRISE_SUSPENDED: 'Enterprise account is suspended',
  ENTERPRISE_EXPIRED: 'Enterprise account is expired',
  ENTERPRISE_NOT_FOUND: 'Enterprise information not found',
  ENTERPRISE_NOT_EFFECTIVE: 'Enterprise account is not effective yet',
  ENTERPRISE_QUOTA_EXCEEDED: 'Enterprise quota exceeded',
  ENTERPRISE_QUOTA_NOT_FOUND: 'Enterprise quota not found',
  ENTERPRISE_INSUFFICIENT_QUOTA: 'Enterprise quota insufficient',
  FREE_QUOTA_EXCEEDED: 'Free quota exceeded',
  FREE_INSUFFICIENT_QUOTA: 'Free quota insufficient',
  PAID_QUOTA_EXCEEDED: 'Paid quota exceeded',
  PAID_INSUFFICIENT_QUOTA: 'Paid quota insufficient',
  ENTERPRISE_QUOTA_NOT_ENOUGH: (required: number, remaining: number) =>
    `Enterprise quota not enough: required ${required}, remaining ${remaining}`,
  ENTERPRISE_QUOTA_NOT_ENOUGH_FOR_TASK: 'Enterprise quota not enough for task',
  NO_RELATED_KOL:
    "Due to this influencer's privacy restrictions, similarity cannot be achieved. /n Please choose another influencer.",
  ENTERPRISE_MEMBER_DAILY_LIMIT_EXCEEDED: 'Enterprise member daily usage limit exceeded',
  MEMBERSHIP_DEVICE_BAN: 'Membership is device banned',
} as const

export interface DeductQuotaParams {
  userId: string
  type: QuotaType
  projectId?: string
  taskId?: string
  createdBy?: string
  count?: number
  metadata?: Record<string, any> // 额外信息
  responseDataRecord?: Record<string, any>
}

export interface DeductDynamicQuotaParams {
  userId: string
  quotaType: QuotaType
  quota: number
  projectId?: string
  taskId?: string
  metadata?: Record<string, any>
}

export interface UserMembership {
  id: string
  accountQuota: number
  usedQuota: number
  status: MemberStatus
  effectiveAt: Date
  expireAt: Date
  createdAt: Date
  updatedAt: Date
  type: MemberType
  timezone: string
  isEnterpriseAdmin: boolean
  userId: string
  enterpriseId: string | null
  enterprise?: Enterprise | null
  enableEnterpriseQuotaDailyLimit: boolean
  enterpriseQuotaDailyLimit: number
  cardSubscriptionStatus: CardSubscriptionStatus
  cardSubscriptionEffectiveAt?: Date | null
  cardSubscriptionExpireAt?: Date | null
}

export const FREE_QUOTA_STRATEGY = {
  FIRST_DAY: +FREE_QUOTA_FIRST_DAY, // 首日配额
  FIRST_WEEK: +FREE_QUOTA_FIRST_WEEK, // 首周配额
  FIRST_MONTH: +FREE_QUOTA_FIRST_MONTH, // 首月配额
  REGULAR: +FREE_QUOTA_REGULAR, // 常规配额
  FIRST_WEEK_DAYS: +FREE_QUOTA_FIRST_WEEK_DAYS, // 首周天数
  AFTER_FIRST_MONTH_DAYS: +FREE_QUOTA_AFTER_FIRST_MONTH_DAYS, // 首月后天数
} as const

export interface DateFilter {
  gte?: Date
  lte?: Date
}

export interface DailyQuotaResult {
  date: Date
  userId: string
  email: string
  daily_usage: number
}

export interface QuotaDetailResult {
  time: Date
  quota_cost: number
  quota_type: string
  description: string | null
  email: string
  userId: string
}

export interface MemberShipEnterpriseInfo {
  hasEnterprise: boolean
  enterpriseId: string
  isEnterpriseAdmin: boolean
  enterpriseName: string
  enterpriseStatus: EnterpriseStatus
  email: string
}

export interface UserBasicInfo extends MemberShipInfo {
  email: string
  isAdmin: boolean
}
