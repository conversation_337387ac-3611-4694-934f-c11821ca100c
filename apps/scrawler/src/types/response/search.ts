import { IgUser } from '@/api/@types/rapidapi/Instagram'
import { KolInfoWithScore } from '@/types/kol'
import { TtUserDetailsAndVideos } from '@/types/tiktokUsers'
import { KolPlatform, YouTubeChannel } from '@repo/database'

export type TtSearchKeyWordBreakResponse = {
  cursor: number
  hasMore: boolean
  total: number
  authors: TtUserDetailsAndVideos[]
}

export interface CandidateItem {
  id: string
  title: string
  description: string
  email: string
  emailSource: string
  historyEmails: string[]
  platformAccount: string
  platform: KolPlatform
  createdAt: string
  updatedAt: string
  avatar: string
  tiktokUser?: TiktokUser
  youtubeChannel?: YouTubeChannel
  instagramUser?: IgUser
  score: number
}

export interface Contact {
  url: string
  root: string
  type: string
  depth: number
  content: string
  linkType: string
  updatedAt: number
  emailSource?: string
}

export interface TiktokUser {
  userId: string
  uniqueId: string
  nickname: string
  region: string
  lastPublishedTime: number
  haveCrawlered: boolean
  followerCount: number
  averagePlayCount: number
  createdAt: string
  updatedAt: string
  avatar: string
  videos: TiktokVideo[]
}

export interface TiktokVideo {
  videoId: string
  region: string
  title: string
  cover: string
  ai_dynamic_cover: string
  origin_cover: string
  play: string
  wmplay: string
  size: number
  play_count: number
  digg_count: number
  comment_count: number
  share_count: number
  download_count: number
  collect_count: number
  create_time: number
  is_ad: boolean
}

// hashtag/bgm/searchKeyWord 的返回结果
export type BreakTaskSearchResponse = {
  data: KolInfoWithScore[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasMore: boolean
  updatedAt?: Date
  message?: string
  progress?: {
    total: number
    current: number
    count: number
  }
}
