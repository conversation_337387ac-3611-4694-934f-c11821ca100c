import { KolPlatform } from '@repo/database'

export interface KolInfoWithScore {
  id: string
  title: string | null
  description: string | null
  email: string | null
  platformAccount: string | null
  platform: KolPlatform
  score: number
  region?: string
  averagePlayCount?: number
  reason?: SearchSource
  commonFields?: string
}

export enum SearchSource {
  DATABASE = 'DATABASE', // 库中
  REALTIME = 'REALTIME', // 实时
}
