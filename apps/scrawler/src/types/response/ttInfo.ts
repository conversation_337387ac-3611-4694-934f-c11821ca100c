export interface TtSimpleVideoCommentsUsers {
  userId: string
  uniqueId: string
  nickname: string
  region: string
}

export interface TtVideoCommentsUsersStatistics {
  total: number
  region: Map<string, number> // 地区统计
}

export interface TtVideoCommentsUsersAndRegionStatisticsResponse {
  videoId: string
  total: number
  region: Record<string, number> // 地区统计
  commentsUsers: TtSimpleVideoCommentsUsers[]
}
