export interface Twitter241User {
  id: string // 用户ID
  rest_id: string // REST API用的用户ID
  name: string // 显示名称，如 "Elon Musk"
  screen_name: string // 用户名/句柄，如 "elonmusk"
  description: string // 个人简介
  profile_image_url_https: string // 头像URL
  location: string // 用户设置的地理位置
  created_at: string // 账号创建时间，格式为字符串
  followers_count: number // 粉丝数量
  friends_count: number // 关注数量
  statuses_count: number // 发推数量
  media_count: number // 媒体内容数量
  listed_count: number // 被加入列表次数
  verified: boolean // 是否官方认证账号
  is_blue_verified: boolean // 是否蓝V认证（付费认证）
  profile_banner_url?: string // 个人主页横幅图片URL（可选）
  pinned_tweet_ids_str?: string[] // 置顶推文ID列表（可选）
  is_protected: boolean // 是否是私密账户
}

export interface Twitter241FollowingsResponse {
  cursor: {
    bottom: string
    top: string
  }
  result: {
    timeline: {
      instructions: Array<{
        type: string
        entries?: Array<{
          entryId: string
          sortIndex: string
          content: {
            entryType: string
            itemContent?: {
              itemType: string
              user_results?: {
                result: {
                  __typename: string
                  id: string
                  rest_id: string
                  is_blue_verified: boolean
                  profile_image_shape: string
                  legacy: {
                    created_at: string
                    default_profile: boolean
                    default_profile_image: boolean
                    description: string
                    entities: Record<string, unknown>
                    followers_count: number
                    friends_count: number
                    location: string
                    media_count: number
                    name: string
                    profile_banner_url?: string
                    profile_image_url_https: string
                    screen_name: string
                    statuses_count: number
                    verified: boolean
                    listed_count: number
                    pinned_tweet_ids_str?: string[]
                  }
                  core?: {
                    created_at: string
                    name: string
                    screen_name: string
                  }
                  avatar?: {
                    image_url: string
                  }
                  location?: {
                    location: string
                  }
                  verification?: {
                    verified: boolean
                  }
                  privacy?: {
                    protected: boolean
                  }
                }
              }
            }
          }
        }>
      }>
    }
  }
}

export interface Twitter241UserResponse {
  result: {
    data: {
      user: {
        result: {
          __typename: string
          id: string
          rest_id: string
          is_blue_verified: boolean
          profile_image_shape: string
          legacy: {
            created_at: string
            default_profile: boolean
            default_profile_image: boolean
            description: string
            entities: Record<string, unknown>
            followers_count: number
            friends_count: number
            location: string
            media_count: number
            listed_count: number
            name: string
            profile_banner_url?: string
            profile_image_url_https: string
            screen_name: string
            statuses_count: number
            verified: boolean
            pinned_tweet_ids_str?: string[]
          }
          core?: {
            created_at: string
            name: string
            screen_name: string
          }
          avatar?: {
            image_url: string
          }
          location?: {
            location: string
          }
          verification?: {
            verified: boolean
          }
          privacy?: {
            protected: boolean
          }
        }
      }
    }
  }
}
