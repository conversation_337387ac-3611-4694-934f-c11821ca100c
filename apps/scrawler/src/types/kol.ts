import {
  InstagramUserInfo,
  KolInfo,
  KolPlatform,
  TikTokUserInfo,
  YouTubeChannel,
} from '@repo/database'
/**
 * Request
 *
 * KOL
 */
export interface KOL {
  /**
   * KOL 平台数据
   */
  data: Data
  /**
   * 邮箱地址（可选）
   */
  email?: string
  /**
   * KOL ID 编号
   */
  id: string
  /**
   * 平台类型，例如 YOUTUBE, TIKTOK, INSTAGRAM
   */
  platform: 'YOUTUBE' | 'TIKTOK' | 'INSTAGRAM' | 'TWITTER'
  /**
   * 允许扩展的属性，键值对
   */
  [property: string]: any
}

/**
 * KOL 平台数据
 */
export interface Data {
  /**
   * 平台账号 Handler（可选）
   */
  handler?: string
  /**
   * 平台账号 ID（可选）
   */
  id?: string
  /**
   * 允许扩展的属性，键值对
   */
  [property: string]: any
}

export type KolInfoWithScore = KolInfo & {
  score: number
  region?: string
  lastPublishedTime?: number
  videosAverageViews?: number
  // 增加该用户的post的信息
  videos?: TtVideoBasicInfo[]
  youtubeChannel?: YouTubeChannel
  instagramUser?: InstagramUserInfo
  tiktokUser?: TikTokUserInfo
}

export interface SimilarKolInfo extends KolInfo {
  score: number
  rawScore: number
  similarities: number[] // 加权考虑进来的其他相似度
}

export interface ttSocialMediaIds {
  ins_id?: string
  ins_url?: string
  twitter_id?: string
  twitter_url?: string
  youtube_channel_id?: string
  youtube_url?: string
  youtube_channel_title?: string
}

export interface TtVideoBasicInfo {
  videoId: string // 视频的唯一标识符
  region: string // 视频所属地区
  title: string // 视频标题
  cover: string // 视频封面图片 URL
  ai_dynamic_cover: string // AI 生成的动态封面 URL
  origin_cover: string // 原始封面图片 URL
  play: string // 视频播放 URL
  wmplay: string // 带水印的视频播放 URL
  size: number // 视频文件大小
  play_count: number // 播放次数
  digg_count: number // 点赞数
  comment_count: number // 评论数
  share_count: number // 分享次数
  download_count: number // 下载次数
  collect_count: number // 收藏次数
  create_time: number // 创建时间
  is_ad: boolean // 是否为广告
}

export interface SimilarUser {
  userId: string
  uniqueId: string
  nickname: string
  region: string
  avatar: string
  signature: string
  followerCount: number
  insId: string
  youtubeChannelId: string
  twitterId: string
  count?: number // 出现次数
  averageViews?: number
  lastPublishedTime?: number
  email?: string // 邮箱 ,只抓signature中的邮箱
  videos?: TtVideoBasicInfo[]
}

// 获取KOL区域信息
export interface kolRegionInfo {
  userId: string
  username: string
  region: string
  platform: KolPlatform
}

// 国内
export type DomesticPlatforms = 'DOUYIN' | 'XHS'

// 海外
export type OverseasPlatforms = 'TIKTOK' | 'YOUTUBE' | 'INSTAGRAM' | 'TWITTER'

export const OVERSEAS_PLATFORMS = ['TIKTOK', 'YOUTUBE', 'INSTAGRAM', 'TWITTER']

export const DOMESTIC_PLATFORMS = ['DOUYIN', 'XHS']
