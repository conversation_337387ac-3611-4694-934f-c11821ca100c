import {
  EmailFollowup,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Thread,
  EmailFollowupThreadStatus,
  EmailPlan,
  EmailPlanStatus,
  EmailTemplate,
} from '@repo/database'

export interface FollowupEmail {
  subject: string
  content: string
  daysAfter: number
}

export interface UpdateTemplateCCRequest {
  cc?: string[]
  bcc?: string[]
}

export interface CreateFollowupRequest {
  followupEmails: FollowupEmail[]
}

export interface UpdateFollowupRequest {
  followupEmails: FollowupEmail[]
}

export interface GetFollowupThreadsQuery {
  status?: EmailFollowupThreadStatus
}

export interface UpdateFollowupThreadStatusRequest {
  status: EmailFollowupThreadStatus
}

export interface EmailTemplateWithFollowup extends EmailTemplate {
  emailFollowup?: EmailFollowup | null
}

export interface EmailFollowupWithTemplate extends EmailFollowup {
  emailTemplate: EmailTemplate
}

export interface EmailFollowupThreadWithDetails extends EmailFollowupThread {
  emailFollowup: EmailFollowupWithTemplate
  plans: EmailPlan[]
}

export interface TemplateFollowupStats {
  followup: EmailFollowup
  threadStats: Record<EmailFollowupThreadStatus, number>
  planStats: Record<EmailPlanStatus, number>
}

export interface CreateFollowupThreadInternalParams {
  kolId: string
  kolEmail: string
  userId: string
  userEmail: string
  templateId: string
  threadId: string
}
