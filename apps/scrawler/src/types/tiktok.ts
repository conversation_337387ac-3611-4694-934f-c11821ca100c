import { TtUserDetailsAndVideos } from '@/types/tiktokUsers.ts'
import { KolPlatform } from '@repo/database'

export interface TikTokProcessResult {
  hashtags: string[]
  searchWords: string[]
  authorCount: number
  uniqueIds: string[]
  authorsWithVideos?: TtUserDetailsAndVideos[]
}

// 定义配置接口
export interface TikTokJobConfig {
  useSearchWords?: boolean
  useHashtags?: boolean
  useTagVideos?: boolean
}

export interface TikTokUrlResult {
  isUrl: boolean
  username?: string
}

export type KolInfoCreateInput = {
  title: string | null
  description: string | null
  email: string | null
  emailSource: string | null
  historyEmails: string[]
  platformAccount: string
  platform: KolPlatform
  avatar?: string | null
}
