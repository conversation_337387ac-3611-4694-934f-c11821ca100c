export interface CreateStrategyTemplatePayload {
  name?: string
  totalRuns: number
  intervalHours: number
  isActive?: boolean
}

export interface UpdateStrategyTemplatePayload {
  name?: string
  totalRuns?: number
  intervalHours?: number
  isActive?: boolean
}

export interface UpdateTemplateStatusPayload {
  isActive: boolean
}

export interface StrategyTemplateDto {
  id: string
  userId: string
  name: string | null
  totalRuns: number
  intervalHours: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}
