import { KolPlatform, PublicationStatisticsSheetData } from '@repo/database'

export interface BatchProcessConfig {
  batchSize: number
  batchDelay: number
  concurrency: number
}

export interface UpdateVideoDataStats {
  total: number
  success: number
  failed: number
  created: number
  updated: number
  failedVideoIds: string[]
  failedVideoUrls: string[]
}

export interface UpdateVideoDataResult {
  data: PublicationStatisticsSheetData[]
  stats: UpdateVideoDataStats
}
// easykol track request params

export interface SocialMediaVideoBaseParams {
  urls: string[]
}

export interface TiktokParams extends SocialMediaVideoBaseParams {
  videosIds: string[]
}

export interface DouyinTrackParams extends SocialMediaVideoBaseParams {
  videosIds: string[]
  noteIds: string[]
}

export interface YoutubeParams extends SocialMediaVideoBaseParams {
  videosIds: string[]
  shortsIds: string[]
}

export interface InstagramParams extends SocialMediaVideoBaseParams {
  postsIds: string[]
  reelsIds: string[]
}

export interface XhsParams extends SocialMediaVideoBaseParams {
  noteIds: string[]
}

export interface UpdateVideoDataRequestParams {
  spreadsheetId: string
  youtube?: YoutubeParams
  tiktok?: TiktokParams
  instagram?: InstagramParams
  douyin?: DouyinTrackParams
  xhs?: XhsParams
  tagIds?: string[]
}

export interface GetPostUrlsType {
  type: 'all' | number
}

export interface GetPostUrlsResponse {
  spreadsheetId: string
  userId: string
  links: string[]
}

export interface EngagementMetrics {
  views: number
  likes: number
  comments: number
  favorites: number
}

export interface UpdatePublicationDataParams {
  totalCost?: number
  notes1?: string
  notes2?: string
  contactInformation?: string
}

export interface UpdatePublicationDataParamsWithIds extends UpdatePublicationDataParams {
  publicationId: string
  spreadsheetId: string
}

export interface UpdateRecentDataParams {
  page: number
  pageSize: number
}

export type TrackEasyKOLTaskRequestParams = {
  tiktok: TiktokParams
  youtube: YoutubeParams
  instagram: InstagramParams
  douyin: DouyinTrackParams
  xhs: XhsParams
  tagIds?: string[]
}

// 插件post audience
export interface PluginPublicationAudienceTaskParams {
  platform: KolPlatform
  postLink: string
  videoId: string
}

export interface PublicationAudienceTaskParams extends PluginPublicationAudienceTaskParams {
  publicationId: string
  googleSheetId: string
}
