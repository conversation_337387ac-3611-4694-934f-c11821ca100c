export interface DateFilter {
  gte?: Date
  lte?: Date
}

export interface DailyQuotaResult {
  date: Date
  userId: string
  email: string
  daily_usage: number
  card_query_usage?: number
}

export interface QuotaDetailResult {
  time: Date
  quota_cost: number
  quota_type: string
  description: string | null
  email: string
  userId: string
}

export interface EnterpriseQuotaDailyUsageResult {
  date: Date
  enterpriseName: string
  enterpriseId: string
  daily_usage: number
  card_query_usage?: number
}

export interface EnterpriseQuotaDetailResult {
  time: Date
  quota_cost: number
  quota_type: string
  description: string | null
  userId: string
  createBy: string // 创建人邮箱
  enterpriseId: string
  enterpriseName: string
}

export interface UserQuotaTypeUsageStats {
  userId: string
  membershipId: string
  email: string
  quotaTypeUsageStats: Record<string, number>
}

export interface EnterpriseQuotaTypeUsageStats {
  enterpriseId: string
  enterpriseName: string
  quotaTypeUsageStats: Record<string, number>
  memberUsageStats: UserQuotaTypeUsageStats[]
}
