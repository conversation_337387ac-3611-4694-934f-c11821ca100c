import { EnterpriseStatus } from '@repo/database'

export interface CreateEnterpriseParams {
  name: string
  description?: string
  accountQuota: number
  dailyLimit?: number
  memberUsageDailyLimit?: number
  effectiveAt?: Date
  expireAt: Date
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  status?: EnterpriseStatus
  industry?: string
  scale?: string
  address?: string
  memberEmails?: string[]
  adminEmails?: string[]
}

export interface UpdateEnterpriseParams {
  name?: string
  description?: string
  accountQuota?: number
  dailyLimit?: number
  memberUsageDailyLimit?: number
  effectiveAt?: Date
  expireAt?: Date
  status?: EnterpriseStatus
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  industry?: string
  scale?: string
  address?: string
}

export interface UpdateEnterpriseBasicInfoParams {
  name?: string
  description?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  industry?: string
  scale?: string
  address?: string
  dailyLimit?: number // 日额度
  memberUsageDailyLimit?: number // 成员日额度，-1表示无限制
}

export interface AddEnterpriseMembersParams {
  emails: string[]
}

export interface GetEnterprisesParams {
  status?: EnterpriseStatus
  keyword?: string
  skip?: number
  limit?: number
}

export interface AddEnterpriseMembersResult {
  successAddedEmails: string[]
  failedAddedEmails: string[]
  existingEmails: string[]
  members: any[]
}
