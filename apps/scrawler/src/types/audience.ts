/**
 * 国家分布统计
 * @deprecated 使用 RegionStatistic 代替
 */
export interface CountryStatistic {
  region: string
  count: number
  percentage: string
}

/**
 * 国家分布分析结果
 * @deprecated 使用 RegionAnalysisResult 代替
 */
export interface CountryAnalysisResult {
  total: number
  statistics: CountryStatistic[]
}

// 用户画像结果
export interface UserPortraitResult {
  gender: {
    male: string
    female: string
  }
  age: {
    under18: string
    age18to25: string
    age25to45: string
    above45: string
  }
}

// 受众分析
export interface AudienceAnalysisResult {
  userPortraitResult: UserPortraitResult
  regionAnalysisResult: RegionAnalysisResult
  fakeRadarData?: FakeRadarData
}

// 用户画像输入
export interface UserPortraitLLMInput {
  username: string
  avatar: string
  signature: string
}

// 用户画像输出
export interface UserPortraitLLMOutput {
  gender: {
    male: number
    female: number
  }
  age: {
    under18: number
    age18to25: number
    age25to45: number
    above45: number
  }
}

export type RegionDevelopmentLevel = 'T1' | 'T2' | 'T3'

/**
 *  各个地区分布统计
 */
export interface RegionStatistic {
  region: string
  developmentLevel: RegionDevelopmentLevel
  count: number
  percentage: string
}

/**
 * 地区发展程度分布统计
 */
export interface RegionDevelopmentStatistic {
  // 国家发展程度 T1: 发达国家 T2: 发展中国家 T3: 欠发达国家
  developmentLevel: RegionDevelopmentLevel
  count: number
  percentage: string
}

/**
 * 地区分布分析结果
 */
export interface RegionAnalysisResult {
  total: number
  statistics: RegionStatistic[]
  developmentStatistics: RegionDevelopmentStatistic[]
}

/**
 * 假雷达数据
 */
export interface FakeRadarData {
  totalCommentCount: number // 总评论数
  totalUserCount: number // 总用户数
  avgCommentUserCount: number // 每个视频平均评论用户数
  videoCount: number // 视频数
  userWithoutCountryCount: number // 无国家用户数
  userWithoutCountryRate: number | string // 无国家用户占比
  suspectedFakeCount: number // 疑似假粉数
  suspectedFakeRate: number | string // 疑似假粉占比
}
