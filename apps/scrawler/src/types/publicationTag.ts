export interface AddTagToPublicationParams {
  publicationId: string
  tagId: string
}

export interface RemoveTagFromPublicationParams {
  publicationId: string
  tagId: string
}

export interface PublicationTagResponse {
  id: string
  tagId: string
  publicationId: string
  name: string
  color: string
  createdAt: Date
}

export interface GetPublicationTagsResponse {
  items: PublicationTagResponse[]
  total: number
}
