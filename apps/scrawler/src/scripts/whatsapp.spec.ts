import InstagramApi from '@/api/instagram'
import TiktokApi from '@/api/tiktok'
import { extractPhoneNumber } from '@/services/contact/contact'
import { newExternalLink } from '@/services/contact/contact.utils'
import { KolPlatform } from '@repo/database'
import Bluebird from 'bluebird'
import * as fs from 'fs/promises'
import { it } from 'vitest'

it(
  'should return all whats app',
  async () => {
    const ids = [
      'shahla94_',
      'amani.alshehhii',
      'vogbyr',
      'mrymalameri',
      'thegamar15',
      'vogbyr',
      'saraalsudairy.mua',
      'maramalhaarbi',
      'sa9llll',
      'khadijalaw',
      'sarah.alhail',
      'zahra.bahrain',
      'fatema_alawii',
      '0wzu',
      'qat2008',
      'alljn.10',
      'model.rinad',
      'lxcv_23',
      'leen._alshehri',
      'alqahtani_203',
      'h.ii1zh',
      'itsilf9',
      't_ome23',
      'maivenmohammed55',
      'rem.1010',
      'f_m551',
      'leen._alshehri',
      'haneenalsaify',
      'anfal.alsairafi',
      'ayaah2_',
      'shaikhaaet_',
      'shorouq__1',
      'shorougayman',
      'solaflife',
      'fatmalbadi',
      'kay6n',
      'aoosh11',
      'zakiaalmerri',
      'hello_itshima',
      'angelakayal',
    ]
    const fileName = 'log.txt'
    const accounts = []
    await Bluebird.map(
      ids,
      async (id) => {
        try {
          const insUser = await InstagramApi.getInstance().getUser(id)
          if (insUser) {
            accounts.push({
              platform: KolPlatform.INSTAGRAM,
              platformAccount: insUser.username,
              description: insUser.biography,
            })
          }
        } catch (err) {
          console.log(`ins user ${id} cannot found.`)
        }
        try {
          const ttUser = await TiktokApi.getInstance().getUserDetail({ unique_id: id })
          if (ttUser) {
            accounts.push({
              platform: KolPlatform.TIKTOK,
              platformAccount: ttUser.user.uniqueId,
              description: ttUser.user.signature,
            })
          }
        } catch (err) {
          console.log(`tt user ${id} cannot found.`)
        }
      },
      { concurrency: 10 },
    )

    const extractWhatsApp = (text: string): string[] => {
      if (!text?.length) {
        return []
      }
      const pattern = /[+]?[0-9 \t]{6,}/g
      const result = text.match(pattern)
      if (!result) return []
      return Array.from(result)
    }

    await Bluebird.map(
      accounts,
      async (account) => {
        console.log(`----------[${account.platform}] ${account.platformAccount} ----------`)
        if (!account.description) {
          const msg = `${account.platform} account ${account.platformAccount} has no description: ${account.description}`
          console.log(msg)
          fs.appendFile(fileName, msg + '\n')
          return
        }
        const numbers = extractPhoneNumber(account.description)
        const numberMsg = `${account.platform} account ${account.platformAccount} has ${numbers.length} numbers in description: ${numbers.join(',')}`
        console.log(numberMsg)
        fs.appendFile(fileName, numberMsg + '\n')

        const hasKeyword = account.description.toLowerCase().includes('whatsapp')
        const keywordMsg = `There is ${hasKeyword ? '' : 'no'} keyword 'WhatsApp`
        console.log(keywordMsg)
        fs.appendFile(fileName, keywordMsg + '\n')

        let finalDecision = ''
        let finalNumber = ''
        if (hasKeyword) {
          if (numbers.length == 1) {
            finalDecision = `I think ${numbers[0]} is the whatsapp number from description: ${account.description}`
            finalNumber = numbers[0]
          } else {
            // 找到每个 whatsapp 关键词之后出现的第一个数字
            const pieces = account.description.toLowerCase().split('whatsapp')
            const nearestNumbers = []
            for (let i = 0; i < pieces.length; i++) {
              const temp = extractWhatsApp(pieces[i])
              if (temp.length > 0) {
                nearestNumbers.push(temp[0])
              }
            }
            finalDecision = `I think ${nearestNumbers.join(',')} is/are the whatsapp number from description: ${account.description}`
            finalNumber = nearestNumbers[0]
          }
        } else {
          finalDecision = `there is no keyword, I cannot figure exactly ${numbers.join(',')} which is whatsapp number from description: ${account.description}`
          finalNumber = numbers[0]
        }
        console.log(finalDecision)
        fs.appendFile(fileName, finalDecision + '\n')
        console.log(finalNumber)
        fs.appendFile(fileName, finalNumber + '\n')
      },
      { concurrency: 1 },
    )
    const existAccounts = accounts.map((i) => i.platformAccount)
    const nonExist = ids.filter((i) => !existAccounts.includes(i))
    const nonExistMsg = `there are ${nonExist.length} accounts not found: ['${nonExist.join("','")}']`
    console.log(nonExistMsg)
    fs.appendFile(fileName, nonExistMsg + '\n')
  },
  20 * 60 * 1000,
)

it(
  'should test all external links',
  async () => {
    const links = [
      // 'https://api.whatsapp.com/message/MT3CXOVD4WE6M1?autoload=1&app_absent=0',
      // 'https://iwtsp.com/***********',
      // 'https://www.snapchat.com/add/memeh4',
      // 'https://www.snapchat.com/add/thegamar15?locale=en_SA@calendar%3Dgregorian',
      // 'https://api.whatsapp.com/message/JTJUMPLR7H2IK1?autoload=1&app_absent=0',
      // 'https://www.snapchat.com/add/sa9lll?invite_id=RSw_MG_7&locale=en_SA@calendar%3Dgregorian&sid=ee0847a33d99468da031e078ea868034',
      // 'https://api.whatsapp.com/message/BVWH7J2J62LOG1?autoload=1&app_absent=0',
      // 'https://api.whatsapp.com/send/?phone=+***********&text&type=phone_number&app_absent=0',
      // 'https://www.snapchat.com/add/zah.bah?invite_id=DFI8uxy1&locale=en_AE&sid=63b4dd36596a489c9c3eebe82bf8b547',
      // 'https://api.whatsapp.com/send?phone=96893658495',
      // 'https://reach.link/lamis-alqahtani',
      // 'https://www.snapchat.com/add/shood2008?invite_id=3DhdkRI7&locale=ar_QA@numbers%3Dlatn&xp_id=1&sid=d345ead7a4534106bb62890b935a7f56',
      // 'https://www.snapchat.com/add/model.rinad?locale=en_SA@calendar%3Dgregorian&sid=7fb0fae507274e18ae54b45e78640530',
      // 'https://www.snapchat.com/add/lxcv_23',
      // 'https://www.snapchat.com/add/alqahtani_203',
      // 'https://linkjar.co/Haneen.Alzahrani',
      'https://linktr.ee/itsilf9',
      'https://www.snapchat.com/add/t.om86?locale=ar_SA@calendar%3Dgregorian',
      'https://www.snapchat.com/add/qxqx1010?invite_id=grx8QKsI&locale=ar_SA@calendar%3Dgregorian&sid=ac3c27f2e6cf44dd95032433bcccab42',
      'https://www.snapchat.com/add/fao.119?invite_id=Z5gfhtxu&locale=en_SA@calendar%3Dgregorian&xp_id=1&sid=2121a22c79864f0a8d9fa3c787b60051',
      'https://reach.link/anfalalsairafi',
      'https://reach.link/ayah-abdullah',
      'https://reach.link/shaikhaaet1',
      'https://www.snapchat.com/add/shrq.1',
      'https://www.snapchat.com/add/s.k104',
      'https://wsend.co/96894940453',
      'https://api.whatsapp.com/message/2ECE57ZGXPQSM1?autoload=1&app_absent=0',
      'https://www.snapchat.com/add/kay6na?invite_id=IDUBwFxk&locale=en_AE&sid=80fd07cfd5404be4b451ea1ff23b642f',
      'https://www.snapchat.com/add/aoosh-2001?invite_id=MJzHxiSb&xp_id=1&sid=5f141df81b824edbb589c8b20c8f16f4',
      'https://www.snapchat.com/add/z.almarri?locale=en_AE',
    ]
    await Bluebird.map(
      links,
      async (link) => {
        console.log(`---------- ${link} ----------`)
        const linkStruct = await newExternalLink(link, link)
        const result = await linkStruct.parse()
        console.log(`get result ${JSON.stringify(result)}`)
      },
      { concurrency: 1 },
    )
  },
  20 * 60 * 1000,
)
