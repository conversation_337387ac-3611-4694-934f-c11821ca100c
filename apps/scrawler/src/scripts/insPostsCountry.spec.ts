import InstagramApiV3 from '@/lib/instagramRapidApi.v3'
import { findCountryCodeSync } from '@/services/insInfo.service'
import { prisma } from '@repo/database'
import * as turf from '@turf/turf'
import Bluebird from 'bluebird'
import * as fs from 'fs'
import * as path from 'path'
import { describe, it } from 'vitest'

interface insUserCountryAndPosts {
  username: string
  region: string
  postsCountry: string[]
}

interface userWithPosts {
  username: string
  posts: any[]
}

// 将数据导出为CSV文件
function exportToCSV(data: insUserCountryAndPosts[], filePath: string): void {
  try {
    // 构建CSV头部
    const headers = ['username', 'region', 'postsCountry']

    // 转换数据为CSV行
    const csvRows = data.map((user) => {
      return [
        user.username,
        user.region,
        user.postsCountry.join(';'), // 使用分号分隔多个国家
      ]
        .map((field) => {
          // 如果字段包含逗号、双引号或换行符，则用双引号包裹并将双引号转义
          if (field && (field.includes(',') || field.includes('"') || field.includes('\n'))) {
            return `"${field.replace(/"/g, '""')}"`
          }
          return field
        })
        .join(',')
    })

    // 组合CSV内容
    const csvContent = [headers.join(','), ...csvRows].join('\n')

    // 确保目录存在
    const dir = path.dirname(filePath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // 写入文件
    fs.writeFileSync(filePath, csvContent, 'utf8')
    console.log(`CSV文件已保存至: ${filePath}`)
  } catch (error) {
    console.error('导出CSV文件失败:', error)
  }
}

export async function loadWorldGeoJSON(): Promise<any> {
  try {
    console.log('[ins] 开始从GitHub加载世界地图GeoJSON数据')
    const response = await fetch(
      'https://raw.githubusercontent.com/datasets/geo-countries/master/data/countries.geojson',
    )
    const githubData = await response.json()
    console.log('[ins] GitHub加载成功')
    return githubData
  } catch (error) {
    console.error('[ins] 加载世界地图数据完全失败:', error)
    throw new Error('无法加载世界地图数据，请检查网络连接和文件权限')
  }
}

// 收集用户位置信息的函数
async function collectUserGeoData(username: string): Promise<{
  username: string
  points: Array<{
    city: string
    lat: number
    lng: number
    country?: string
  }>
}> {
  if (!username) {
    return { username, points: [] }
  }

  try {
    const response = await InstagramApiV3.getInstance().getUserPosts({ username: username })
    const IgPosts = response.posts || []
    if (!IgPosts.length) {
      console.log(`[ins]-------no posts------- 用户 ${username} 没有帖子`)
      return { username, points: [] }
    }

    const postsWithLocation = IgPosts.filter(
      (post) =>
        post.location?.lat &&
        post.location?.lng &&
        post.location.lat !== 0 &&
        post.location.lng !== 0,
    )

    if (!postsWithLocation.length) {
      console.log(`[ins]-------no location------- 用户 ${username} 没有带位置的帖子`)
      return { username, points: [] }
    }
    const postsToProcess = postsWithLocation.sort((a, b) => b.created_at - a.created_at)
    const points = postsToProcess
      .filter(
        (post) =>
          post.location &&
          typeof post.location.lat === 'number' &&
          typeof post.location.lng === 'number',
      )
      .map((post) => {
        return {
          lat: post.location!.lat as number,
          lng: post.location!.lng as number,
          city: post.location!.name as string,
        }
      })

    return { username, points }
  } catch (error) {
    console.error(`[ins] 获取用户 ${username} 位置失败:`, error)
    return { username, points: [] }
  }
}

async function getUserPostsRegion(
  username: string,
  worldGeoJSON: any,
): Promise<{
  topCountries: string[]
}> {
  try {
    const userData = await collectUserGeoData(username)
    if (!userData.points.length) {
      return { topCountries: [] }
    }

    // 使用经纬度计算国家
    const pointsWithCountry = await Promise.all(
      userData.points.map(async (point) => {
        // 创建GeoJSON点
        const turfPoint = turf.point([point.lng, point.lat])
        // 使用findCountryCodeSync来确定点所在的国家
        const country = findCountryCodeSync(turfPoint, worldGeoJSON)
        return {
          ...point,
          country: country || undefined,
        }
      }),
    )

    // 统计每个国家出现的次数
    const countryCount: Record<string, number> = {}

    pointsWithCountry.forEach((point) => {
      if (point.country) {
        countryCount[point.country] = (countryCount[point.country] || 0) + 1
      }
    })

    // 转换为数组并按出现次数排序
    const sortedCountries = Object.entries(countryCount)
      .sort((a, b) => b[1] - a[1])
      .map(([country]) => country)

    // 获取前三个最常出现的国家
    const topThreeCountries = sortedCountries.slice(0, 3)

    return { topCountries: topThreeCountries }
  } catch (error) {
    console.error(`[getUserPostsRegion] 获取用户 ${username} 帖子地区失败:`, error)
    return { topCountries: [] }
  }
}

describe('region rate', () => {
  it(
    'should be a function',
    async () => {
      const insUSer = await prisma.instagramUserInfo.findMany({
        take: 100,
        skip: 100,
        where: {
          region: {
            not: '',
          },
        },
        select: {
          username: true,
          region: true,
        },
      })
      insUSer.forEach(async (user) => {
        console.log(`get user ${user.username} region success,region:${user.region}`)
      })

      const userWithCountryAndPosts: insUserCountryAndPosts[] = []
      // 加载世界地图数据
      const worldGeoJSON = await loadWorldGeoJSON()
      // 处理用户数据，获取帖子地区
      await Bluebird.map(
        insUSer,
        async (user) => {
          try {
            const postsRegion = await getUserPostsRegion(user.username, worldGeoJSON)
            userWithCountryAndPosts.push({
              username: user.username,
              region: user.region,
              postsCountry: postsRegion.topCountries,
            })
            console.log(
              `获取用户 ${user.username} 帖子区域成功，前三个国家: ${postsRegion.topCountries.join(', ')}`,
            )
          } catch (error) {
            console.error(`获取用户 ${user.username} 帖子区域失败:`, error)
            userWithCountryAndPosts.push({
              username: user.username,
              region: user.region,
              postsCountry: [],
            })
          }
        },
        { concurrency: 10 },
      )

      // 统计帖子地区与用户资料地区一致的比例（使用第一个国家进行比较）
      const matchingUsers = userWithCountryAndPosts.filter(
        (user) =>
          user.postsCountry.length > 0 && user.region && user.postsCountry[0] === user.region,
      )

      const matchRate = matchingUsers.length / 100

      console.log(`地区匹配率: ${(matchRate * 100).toFixed(2)}%`)
      console.log(`匹配用户数: ${matchingUsers.length}/${100}`)

      // 输出不匹配的用户情况
      const mismatchUsers = userWithCountryAndPosts.filter(
        (user) =>
          user.postsCountry.length > 0 && user.region && user.postsCountry[0] !== user.region,
      )

      console.log('不匹配的用户:')
      mismatchUsers.forEach((user) => {
        console.log(
          `用户: ${user.username}, 资料地区: ${user.region}, 帖子地区: ${user.postsCountry.join(', ')}`,
        )
      })

      // 导出结果到CSV文件
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const csvFilePath = path.resolve(
        __dirname,
        `../../../data/ins_posts_country_${timestamp}.csv`,
      )
      exportToCSV(userWithCountryAndPosts, csvFilePath)
      console.log(`分析结果已导出到: ${csvFilePath}`)
    },
    { timeout: 1000_000 },
  )
})
