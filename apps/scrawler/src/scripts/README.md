# 默认标签初始化脚本

## 概述

`init_default_tag.ts` 脚本用于为数据库中的所有用户和企业初始化默认的 CreatorTag 记录。

## 功能

该脚本会：

1. **为个人用户创建默认标签**：为每个 UserInfo 表中的用户创建个人级别的默认标签
2. **为企业用户创建默认标签**：为每个企业的每个成员创建企业级别的默认标签
3. **避免重复创建**：检查已存在的标签，只创建缺失的标签
4. **分批处理**：采用分批处理模式，避免一次性加载大量数据，降低系统负载和内存占用
5. **详细日志输出**：显示处理进度和统计信息，包括批次进度和执行时间
6. **错误容错**：单个记录或批次失败不会影响其他数据的处理

## 默认标签

脚本会创建以下5个默认标签：

- **Overpriced** (#FF7570) - 价格过高
- **Low ROI** (#FF8119) - 低投资回报率
- **High ROI** (#35BD4C) - 高投资回报率
- **Campaign A** (#27B0E7) - 活动A
- **Campaign B** (#B790FA) - 活动B

## 使用方法

### 1. 配置环境变量

首先创建 `.env` 文件：

```bash
# 在 apps/scrawler 目录下创建 .env 文件
cd apps/scrawler
cp .env.example .env  # 如果有示例文件
# 或者直接创建
echo 'DATABASE_URL="postgresql://username:password@localhost:5432/database_name"' > .env
```

### 2. 通过 npm script 运行

```bash
cd apps/scrawler
pnpm run init-default-tags
```

### 3. 直接运行

```bash
cd apps/scrawler
npx tsx src/scripts/init_default_tag.ts
```

## 环境要求

1. **环境变量**：
   - 在项目根目录或 `apps/scrawler` 目录下创建 `.env` 文件
   - 配置正确的 `DATABASE_URL`，例如：`DATABASE_URL="postgresql://username:password@localhost:5432/database_name"`
   - 脚本会自动加载 `.env` 文件中的环境变量
2. **数据库访问权限**：脚本需要对 CreatorTag、UserInfo、Enterprise 等表的读写权限
3. **Node.js 环境**：需要 Node.js 和相关依赖包

## 执行流程

1. **连接数据库**：测试数据库连接
2. **获取用户总数**：统计 UserInfo 表中的用户总数
3. **分批处理个人用户**：
   - 按配置的批次大小（默认100）分批获取用户数据
   - 对每批用户检查已有的默认标签并创建缺失的标签
   - 每批处理后显示进度并暂停避免数据库过载
4. **获取企业总数**：统计 Enterprise 表中的企业总数
5. **分批处理企业用户**：
   - 按较小的批次大小（默认50）分批获取企业和成员数据
   - 为每个企业的每个成员创建企业级别的默认标签
   - 检查已有标签，避免重复创建
6. **输出统计报告**：显示详细的处理结果统计，包括执行时间

## 输出示例

```
🚀 开始执行默认标签初始化脚本 (分批处理模式)...
✅ 数据库连接成功
🔗 数据库连接: postgresql://***:***@localhost:5432/database_name
⚙️  配置信息:
   - 批次大小: 100
   - 批次延迟: 500ms
   - 默认标签: Overpriced, Low ROI, High ROI, Campaign A, Campaign B

开始为所有个人用户初始化默认标签...
找到 1500 个用户，将分批处理，每批 100 个

处理个人用户批次 1/15 (1-100)
  ✓ 用户 user-123 创建了 5 个标签
  ✓ 用户 user-124 创建了 5 个标签
批次完成: 成功 98/100, 失败 2, 总进度 7%

处理个人用户批次 2/15 (101-200)
  ✓ 用户 user-205 创建了 3 个标签
批次完成: 成功 195/200, 失败 5, 总进度 13%
...

个人用户默认标签初始化完成: 成功 1480/1500 个, 失败 20 个, 共创建 7200 个标签

开始为所有企业用户初始化默认标签...
找到 20 个企业，将分批处理，每批 50 个企业

处理企业批次 1/1 (1-20)
  处理企业: 示例企业A (enterprise-abc, 15 个成员)
    ✓ 成员 user-789 创建了 5 个企业标签
    ✓ 成员 user-790 创建了 5 个企业标签
  处理企业: 示例企业B (enterprise-def, 25 个成员)
企业批次完成: 处理了 20 个企业, 总进度 100%

企业用户默认标签初始化完成: 成功 800 个成员, 失败 0 个, 共创建 4000 个标签

📊 执行完成统计:
⏱️  执行时间: 45 秒
👤 个人用户: 成功 1480 个, 失败 20 个, 创建标签 7200 个
🏢 企业用户: 成功 800 个, 失败 0 个, 创建标签 4000 个
📈 总计: 成功 2280 个, 失败 20 个, 创建标签 11200 个
⚠️  存在 20 个处理失败的记录，请检查上述错误日志

🔌 数据库连接已关闭
✅ 脚本执行完成
```

## 配置参数

脚本提供以下可调整的配置参数：

- **BATCH_SIZE**: 每批处理的记录数（默认：100）
- **BATCH_DELAY**: 每批处理后的延迟时间，单位毫秒（默认：500ms）

可以根据数据库性能和系统负载调整这些参数。

## 注意事项

1. **幂等性**：脚本可以多次安全运行，不会创建重复的标签
2. **分批处理**：采用分批处理避免一次性加载大量数据，减少内存占用
3. **错误容错**：单个记录或批次失败不会影响其他数据的处理
4. **数据库负载控制**：批次间延迟和较小的批次大小有效控制数据库负载
5. **进度追踪**：提供详细的批次进度和统计信息
6. **备份建议**：在生产环境运行前建议备份数据库

## 故障排除

### 常见错误

1. **数据库连接失败**

   - 检查 `.env` 文件是否存在，并包含正确的 `DATABASE_URL`
   - 确认 `DATABASE_URL` 格式：`postgresql://username:password@host:port/database`
   - 确认数据库服务是否正常运行
   - 检查网络连接和防火墙设置

2. **环境变量加载失败**

   - 确保 `.env` 文件位于正确的位置（项目根目录或 `apps/scrawler` 目录）
   - 检查 `.env` 文件权限是否正确
   - 确保 `dotenv` 包已安装

3. **权限不足**

   - 确保数据库用户有足够的权限
   - 检查表的读写权限
   - 验证数据库连接字符串中的用户名和密码

4. **依赖包问题**
   - 运行 `pnpm install` 确保依赖包安装完整
   - 检查 TypeScript 和 tsx 是否正确安装

### 调试选项

脚本包含详细的日志输出，可以通过查看控制台输出来诊断问题：

- 每个用户的处理状态
- 标签创建详情
- 错误信息和堆栈跟踪
- 最终统计报告
