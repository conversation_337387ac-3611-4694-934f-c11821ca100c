import { DefaultTags } from '@/services/tag_and_note/tag'
import { MemberType, prisma } from '@repo/database'
import 'dotenv/config'

// 配置参数
const BATCH_SIZE = 100 // 每批处理的记录数
const BATCH_DELAY = 500 // 每批处理后的延迟时间（毫秒）

/**
 * 延迟函数
 */
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

/**
 * 为指定用户和企业初始化默认标签
 */
async function initDefaultTagsForUser(userId: string, enterpriseId: string) {
  try {
    // 检查已存在的标签
    const existingTags = await prisma.creatorTag.findMany({
      where: {
        name: { in: DefaultTags.map((tag) => tag.name) },
        enterpriseId: enterpriseId,
        userId,
        deletedAt: null,
      },
    })

    // 过滤出需要创建的新标签
    const existingTagNames = existingTags.map((tag) => tag.name)
    const newTags = DefaultTags.filter((tag) => !existingTagNames.includes(tag.name))

    if (newTags.length === 0) {
      return 0
    }

    // 批量创建新标签
    await prisma.creatorTag.createMany({
      data: newTags.map((tag) => ({
        name: tag.name,
        color: tag.color,
        userId,
        enterpriseId: enterpriseId || '',
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    })

    return newTags.length
  } catch (error) {
    console.error(
      `为用户 ${userId}${enterpriseId ? ` (企业: ${enterpriseId})` : ''} 初始化默认标签失败:`,
      error,
    )
    throw error
  }
}

/**
 * 分批获取用户总数
 */
async function getUserCount(): Promise<number> {
  return prisma.userInfo.count()
}

/**
 * 分批处理所有个人用户初始化默认标签
 */
async function initDefaultTagsForAllUsers() {
  console.log('开始为所有个人用户初始化默认标签...')

  // 获取用户总数
  const totalUsers = await getUserCount()
  console.log(`找到 ${totalUsers} 个用户，将分批处理，每批 ${BATCH_SIZE} 个`)

  let successCount = 0
  let errorCount = 0
  let totalTagsCreated = 0
  let processedCount = 0

  // 分批处理
  for (let offset = 0; offset < totalUsers; offset += BATCH_SIZE) {
    console.log(
      `\n处理个人用户批次 ${Math.floor(offset / BATCH_SIZE) + 1}/${Math.ceil(totalUsers / BATCH_SIZE)} (${offset + 1}-${Math.min(offset + BATCH_SIZE, totalUsers)})`,
    )

    try {
      // 分批获取用户数据
      const users = await prisma.userInfo.findMany({
        select: {
          userId: true,
        },
        where: {
          membership: {
            type: {
              not: MemberType.ENTERPRISE,
            },
          },
        },
        skip: offset,
        take: BATCH_SIZE,
      })

      // 处理当前批次的用户
      for (const user of users) {
        try {
          const tagsCreated = await initDefaultTagsForUser(user.userId, '')
          totalTagsCreated += tagsCreated
          successCount++

          if (tagsCreated > 0) {
            console.log(`  ✓ 用户 ${user.userId} 创建了 ${tagsCreated} 个标签`)
          }
        } catch (error) {
          errorCount++
          console.error(`  ✗ 用户 ${user.userId} 处理失败:`, error)
        }
        processedCount++
      }

      // 显示批次进度
      console.log(
        `批次完成: 成功 ${successCount}/${processedCount}, 失败 ${errorCount}, 总进度 ${Math.round((processedCount / totalUsers) * 100)}%`,
      )

      // 批次间延迟，避免数据库过载
      if (offset + BATCH_SIZE < totalUsers) {
        await delay(BATCH_DELAY)
      }
    } catch (error) {
      console.error(`处理用户批次 ${Math.floor(offset / BATCH_SIZE) + 1} 失败:`, error)
      // 批次失败时记录错误但继续处理下一批次
      errorCount += BATCH_SIZE
    }
  }

  console.log(
    `\n个人用户默认标签初始化完成: 成功 ${successCount}/${totalUsers} 个, 失败 ${errorCount} 个, 共创建 ${totalTagsCreated} 个标签`,
  )
  return { successCount, errorCount, totalTagsCreated }
}

/**
 * 分批获取企业总数
 */
async function getEnterpriseCount(): Promise<number> {
  return prisma.enterprise.count()
}

/**
 * 分批处理所有企业用户初始化默认标签
 */
async function initDefaultTagsForAllEnterprises() {
  console.log('\n开始为所有企业用户初始化默认标签...')

  // 获取企业总数
  const totalEnterprises = await getEnterpriseCount()
  console.log(
    `找到 ${totalEnterprises} 个企业，将分批处理，每批 ${Math.min(BATCH_SIZE, 50)} 个企业`,
  )

  let successCount = 0
  let errorCount = 0
  let totalTagsCreated = 0
  let processedEnterprises = 0

  const enterpriseBatchSize = Math.min(BATCH_SIZE, 50) // 企业批次较小，因为每个企业可能有多个成员

  // 分批处理企业
  for (let offset = 0; offset < totalEnterprises; offset += enterpriseBatchSize) {
    console.log(
      `\n处理企业批次 ${Math.floor(offset / enterpriseBatchSize) + 1}/${Math.ceil(totalEnterprises / enterpriseBatchSize)} (${offset + 1}-${Math.min(offset + enterpriseBatchSize, totalEnterprises)})`,
    )

    try {
      // 分批获取企业及其成员数据
      const enterprises = await prisma.enterprise.findMany({
        select: {
          id: true,
          name: true,
          members: {
            select: {
              userId: true,
            },
            orderBy: {
              createdAt: 'asc',
            },
            take: 1,
          },
        },
        skip: offset,
        take: enterpriseBatchSize,
      })

      for (const enterprise of enterprises) {
        console.log(
          `  处理企业: ${enterprise.name} (${enterprise.id}, ${enterprise.members.length} 个成员)`,
        )
        const owner = enterprise.members[0]
        if (!owner) {
          console.error(`    ✗ 企业 ${enterprise.id} 没有找到所有者`)
          continue
        }
        try {
          const tagsCreated = await initDefaultTagsForUser(owner.userId, enterprise.id)
          totalTagsCreated += tagsCreated
          successCount++

          if (tagsCreated > 0) {
            console.log(`    ✓ 成员 ${owner.userId} 创建了 ${tagsCreated} 个企业标签`)
          }
        } catch (error) {
          errorCount++
          console.error(`    ✗ 企业 ${enterprise.id} 成员 ${owner.userId} 处理失败:`, error)
        }
        processedEnterprises++
      }

      // 显示批次进度
      console.log(
        `企业批次完成: 处理了 ${enterprises.length} 个企业, 总进度 ${Math.round((processedEnterprises / totalEnterprises) * 100)}%`,
      )

      // 批次间延迟，避免数据库过载
      if (offset + enterpriseBatchSize < totalEnterprises) {
        await delay(BATCH_DELAY)
      }
    } catch (error) {
      console.error(`处理企业批次 ${Math.floor(offset / enterpriseBatchSize) + 1} 失败:`, error)
      // 批次失败时继续处理下一批次
    }
  }

  console.log(
    `\n企业用户默认标签初始化完成: 成功 ${successCount} 个成员, 失败 ${errorCount} 个, 共创建 ${totalTagsCreated} 个标签`,
  )
  return { successCount, errorCount, totalTagsCreated }
}

/**
 * 主函数 - 执行完整的默认标签初始化
 */
async function main() {
  console.log('🚀 开始执行默认标签初始化脚本 (分批处理模式)...')
  const startTime = Date.now()

  try {
    // 检查数据库连接
    await prisma.$connect()
    console.log('✅ 数据库连接成功')

    // 验证数据库URL是否存在
    const databaseUrl = process.env.DATABASE_URL
    if (!databaseUrl) {
      throw new Error('❌ 未找到 DATABASE_URL 环境变量，请检查 .env 文件配置')
    }
    console.log(`🔗 数据库连接: ${databaseUrl.replace(/\/\/.*@/, '//***:***@')}`) // 隐藏敏感信息

    // 显示配置信息
    console.log(`⚙️  配置信息:`)
    console.log(`   - 批次大小: ${BATCH_SIZE}`)
    console.log(`   - 批次延迟: ${BATCH_DELAY}ms`)
    console.log(`   - 默认标签: ${DefaultTags.map((t) => t.name).join(', ')}`)

    // 1. 为所有个人用户初始化默认标签
    const userResult = await initDefaultTagsForAllUsers()

    // 2. 为所有企业用户初始化默认标签
    const enterpriseResult = await initDefaultTagsForAllEnterprises()

    // 计算执行时间
    const executionTime = Math.round((Date.now() - startTime) / 1000)

    // 总结报告
    console.log('\n📊 执行完成统计:')
    console.log(`⏱️  执行时间: ${executionTime} 秒`)
    console.log(
      `👤 个人用户: 成功 ${userResult.successCount} 个, 失败 ${userResult.errorCount} 个, 创建标签 ${userResult.totalTagsCreated} 个`,
    )
    console.log(
      `🏢 企业用户: 成功 ${enterpriseResult.successCount} 个, 失败 ${enterpriseResult.errorCount} 个, 创建标签 ${enterpriseResult.totalTagsCreated} 个`,
    )
    console.log(
      `📈 总计: 成功 ${userResult.successCount + enterpriseResult.successCount} 个, 失败 ${userResult.errorCount + enterpriseResult.errorCount} 个, 创建标签 ${userResult.totalTagsCreated + enterpriseResult.totalTagsCreated} 个`,
    )

    if (userResult.errorCount + enterpriseResult.errorCount > 0) {
      console.log(
        `⚠️  存在 ${userResult.errorCount + enterpriseResult.errorCount} 个处理失败的记录，请检查上述错误日志`,
      )
    }
  } catch (error) {
    console.error('❌ 脚本执行失败:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
    console.log('🔌 数据库连接已关闭')
  }
}

// 如果直接运行此脚本
main()
  .then(() => {
    console.log('✅ 脚本执行完成')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ 脚本执行异常:', error)
    process.exit(1)
  })
