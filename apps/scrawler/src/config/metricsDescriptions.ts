/**
 * 任务指标描述配置文件
 * 按任务类型组织，提供指标key到中文描述的映射
 */

// ins visiual
export const InstagramVisualTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  project_all_rated_user_count: '项目中已评价过的博主总数',

  // 第一轮相关用户获取
  source_user_related_users_count: '源用户相关用户数量',
  first_round_related_users_count: '第一轮项目去重后获取相关用户数量',
  analyze_source_kol_vertical_duration: '获取源KOL垂直领域描述耗时(秒)',

  // 第一轮帖子获取与分析
  first_round_fetch_user_posts_duration: '第一轮获取用户帖子耗时(秒)',
  first_round_analyze_visual_similarity_duration: '第一轮分析视觉相似度耗时(秒)',
  first_round_after_visual_similarity_filter_count: '第一轮视觉相似过滤后剩余用户数量',
  first_round_fetch_user_details_duration: '第一轮获取用户详情耗时(秒)',
  upsert_first_round_userInfo_and_kolInfo_duration: '第一轮更新KOL信息和用户信息耗时(秒)',
  first_round_after_filter_db_users_count: '第一轮数据库过滤后剩余用户数量',

  // 第二轮相关用户获取（如果round > 1）
  second_round_related_source_users: '第二至四轮相关用户的爆破源用户',
  second_round_related_users_count: '第二至四轮相关用户数量',
  second_round_fetch_user_posts_duration: '第二至四轮获取用户帖子耗时(秒)',
  second_round_analyze_users_batch_duration: '第二至四轮批量分析用户耗时(秒)',
  second_round_after_visual_similarity_filter_count: '第二至四轮视觉相似过滤后剩余用户数量',
  second_round_fetch_user_details_duration: '第二轮获取用户详情耗时(秒)',
  upsert_second_round_userInfo_and_kolInfo_duration: '第二轮更新KOL信息和用户信息耗时(秒)',
  second_round_after_filter_db_users_count: '第二轮数据库过滤后剩余用户数量',

  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
  all_vision_results: '所有视觉相似度分析结果',
}

/**
 * Instagram普通相似任务指标描述
 */
export const InstagramNormalTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  project_all_rated_user_count: '项目中已评价过的博主总数',

  // 获取相关用户阶段
  fetch_related_users_duration: '获取相关用户耗时(秒)',
  source_user_related_users_count: '源用户相关用户数量',
  after_project_filter_related_users_count: '项目去重后的相关用户数量',

  // 官方账号过滤
  filter_official_account_duration: '过滤官方账号耗时(秒)',
  official_account_count: '官方账号数量',
  official_accounts: '官方账号列表',
  after_filter_official_account_count: '过滤官方账号后剩余用户数量',

  // 获取用户详情
  fetch_related_users_details_duration: '获取相关用户详细信息耗时(秒)',

  // KOL信息更新
  upsert_kol_info_duration: '更新KOL信息耗时(秒)',

  // 数据库过滤
  after_db_filter_count: '数据库过滤(粉丝数、地区、平均点赞)后用户数量',

  // 最终结果
  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',
  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
}

/**
 * Instagram嵌入相似任务指标描述
 */
export const InstagramEmbeddingTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  project_all_rated_user_count: '项目中已评价过的博主总数',

  // 获取相关用户阶段
  source_user_related_users_count: '源用户相关用户数量',
  after_project_filter_related_users_count: '项目去重后的相关用户数量',

  // 获取用户帖子数据
  fetch_related_users_posts_duration: '获取相关用户帖子耗时(秒)',
  all_users_count: '所有用户数量（包括源用户）',

  // 四维度分析
  analyze_blogger_dimensions_duration: '分析博主四维度耗时(秒)',
  all_users_with_dimensions_count: '有四维度分析结果的用户数量',

  // 生成向量嵌入
  generate_embeddings_duration: '生成向量嵌入耗时(秒)',
  users_with_embeddings_count: '生成嵌入成功的用户数量',

  // Milvus向量检索
  milvus_search_duration: 'Milvus向量搜索耗时(秒)',
  search_result_count: '向量搜索结果数量',

  // Rerank重新排序
  rerank_query_text: '重新排序使用的查询文本',
  documents_map_count: '重新排序文档数量',
  rerank_duration: '重新排序耗时(秒)',

  // 最终结果
  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  all_users_with_dimensions: '所有用户的四维度分析结果',
  search_result: '向量搜索结果',
}

/**
 * YouTube嵌入相似任务指标描述
 */
export const YoutubeEmbeddingTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  get_project_all_rated_channel_ids_duration: '获取项目已评价频道ID耗时(秒)',
  project_all_rated_channel_count: '项目中已评价过的频道总数',

  // 获取源频道和相关频道
  get_source_channel_duration: '获取源频道信息耗时(秒)',
  get_related_channels_duration: '获取相关频道耗时(秒)',
  source_channel_related_channels_count: '源频道相关频道数量',
  after_rated_filter_related_channels_count: '项目去重后剩余相关频道数',

  // 数据更新
  upsert_kol_info_and_channel_vectors_duration: '更新KOL信息和频道向量耗时(秒)',

  // 向量搜索
  get_valid_dense_vector_duration: '获取有效密集向量耗时(秒)',
  vector_search_duration: '向量搜索耗时(秒)',
  milvus_vector_search_result_count: 'Milvus向量搜索返回结果数量',
  vector_search_result_count: '向量搜索结果数量',

  // 数据库查询和过滤
  db_query_kol_info_duration: '查询KOL信息耗时(秒)',
  after_db_filter_count: '数据库过滤后剩余频道数',

  // 最终结果
  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
}

/**
 * YouTube视觉相似任务指标描述
 */
export const YoutubeVisualTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 获取项目信息
  get_project_all_rated_channel_ids_duration: '获取项目已评价频道ID耗时(秒)',
  project_all_rated_channel_count: '项目中已评价过的频道总数',

  // 获取源频道
  get_source_channel_duration: '获取源频道信息耗时(秒)',

  // 获取三要素
  three_elements: '视觉分析三要素(描述、允许列表、禁止列表)',

  // 获取相关频道
  get_related_channels_duration: '获取相关频道耗时(秒)',
  source_channel_related_channels_count: '源频道相关频道数量',
  after_rated_filter_related_channels_count: '项目去重后剩余相关频道数',

  // 数据更新
  upsert_kol_info_and_channel_vectors_duration: '更新KOL信息和频道向量耗时(秒)',

  // 视觉相似度分析
  analyze_visual_similarity_duration: '分析视觉相似度耗时(秒)',
  visual_similarity_batch_count: '视觉相似度分析批次数',
  after_visual_similarity_filter_count: '视觉相似过滤后剩余频道数量',

  // 数据库查询和过滤
  db_query_kol_info_duration: '查询KOL信息耗时(秒)',
  after_db_filter_count: '数据库过滤后剩余频道数',

  // 最终结果
  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
  visual_results: '视觉相似度分析结果',
}

/**
 * TikTok嵌入相似任务指标描述
 */
export const TiktokEmbeddingTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  get_project_all_rated_ids_duration: '获取项目已评价账号ID耗时(秒)',
  project_all_rated_ids_count: '项目中已评价过的账号总数',

  // 获取源作者视频信息
  get_source_videos_duration: '获取源作者视频耗时(秒)',
  source_videos_count: '源作者视频数量',

  // 获取标签信息
  get_hashtags_duration: '提取视频标签耗时(秒)',
  hashtags_count: '提取标签数量',

  // 搜索相关账号
  search_words_unique_ids_count: '通过关键词搜索获取的账号数量',
  hashtags_unique_ids_count: '通过标签搜索获取的账号数量',
  tag_videos_unique_ids_count: '通过标签视频获取的账号数量',
  search_words_duration: '关键词搜索耗时(秒)',
  hashtags_search_duration: '标签搜索耗时(秒)',
  tag_videos_search_duration: '标签视频搜索耗时(秒)',

  // 合并相关账号
  all_unique_ids_count: '所有收集到的相关账号数量',
  filtered_unique_ids_count: '过滤项目已评价后的账号数量',

  // 获取作者详情
  get_authors_with_videos_duration: '获取账号详情和视频耗时(秒)',
  authors_count: '获取到的作者数量',

  // 处理源作者信息
  process_source_project_kol_duration: '处理源作者ProjectKOL信息耗时(秒)',
  insert_kol_data_duration: '插入KOL数据耗时(秒)',

  // 向量处理
  get_source_embedding_duration: '获取源作者向量耗时(秒)',
  generate_embeddings_duration: '生成新向量耗时(秒)',
  vector_search_duration: '向量搜索耗时(秒)',
  vector_search_result_count: '向量搜索结果数量',

  // 数据库查询和过滤
  db_query_kol_info_duration: '查询KOL信息耗时(秒)',
  after_db_filter_count: '数据库过滤后剩余账号数',

  // 最终结果
  final_kols_count: '最终KOL数量',

  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
}

/**
 * TikTok视觉相似任务指标描述
 */
export const TiktokVisualTaskMetrics = {
  worker_pid: 'worker进程id',
  // 系统生成指标（初始化）
  start_timestamp: '任务开始时间戳',

  // 初始阶段指标
  get_project_all_rated_ids_duration: '获取项目已评价账号ID耗时(秒)',
  project_all_rated_ids_count: '项目中已评价过的账号总数',

  // 获取项目和源作者信息
  get_project_info_duration: '获取项目信息耗时(秒)',
  get_source_author_duration: '获取源作者信息耗时(秒)',
  source_videos_count: '源作者视频数量',

  // 获取三要素
  three_elements: '视觉分析三要素(描述、允许列表、禁止列表)',
  analyze_vertical_similarity_duration: '分析垂类相似度耗时(秒)',
  analyze_video_covers_duration: '分析视频封面耗时(秒)',

  // 搜索相关账号
  search_terms_count: '搜索关键词数量',
  tags_with_ids_count: '标签ID数量',
  search_term_unique_ids_count: '通过关键词搜索获取的账号数量',
  tag_author_unique_ids_count: '通过标签获取的账号数量',
  search_terms_search_duration: '关键词搜索耗时(秒)',
  tags_search_duration: '标签搜索耗时(秒)',

  // 合并相关账号
  all_unique_ids_count: '所有收集到的相关账号数量',
  filtered_unique_ids_count: '过滤项目已评价后的账号数量',

  // 获取候选账号详情
  get_candidate_authors_duration: '获取候选账号信息耗时(秒)',
  valid_candidate_authors_count: '有效候选账号数量',

  // 视觉相似度分析
  batch_count: '批次分析数量',
  analyze_visual_similarity_duration: '分析视觉相似度总耗时(秒)',
  valid_vision_authors_count: '视觉相似有效账号数量',

  // 数据更新
  upsert_tt_users_duration: '更新TikTok用户信息耗时(秒)',
  upsert_kols_duration: '更新KOL信息耗时(秒)',
  process_source_project_kol_duration: '处理源作者项目KOL信息耗时(秒)',

  // 数据库过滤
  after_db_filter_count: '数据库过滤后剩余账号数',
  // 查询KOL信息
  get_tiktok_kol_info_duration: '查询TikTok KOL信息耗时(秒)',
  // final_kols_count: '最终KOL数量',
  final_kols_count: '最终KOL数量',
  // 系统生成指标（完成）
  completion_timestamp: '任务完成时间戳',
  total_duration: '总执行时间(秒)',

  // API调用统计
  rapid_api_stats: 'RapidAPI调用统计信息',
  all_visual_similarity_results: '视觉相似度分析结果',
}

export default {
  InstagramVisualTaskMetrics,
  InstagramNormalTaskMetrics,
  InstagramEmbeddingTaskMetrics,
  YoutubeEmbeddingTaskMetrics,
  YoutubeVisualTaskMetrics,
  TiktokEmbeddingTaskMetrics,
  TiktokVisualTaskMetrics,
}
