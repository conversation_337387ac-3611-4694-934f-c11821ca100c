import Sentry from '@/infras/sentry'
import 'dotenv/config'

// Stripe 相关配置
export const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY
export const STRIPE_PUBLISHABLE_KEY = process.env.STRIPE_PUBLISHABLE_KEY
export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET
export const STRIPE_BASIC_PLAN_PRICE_ID = process.env.STRIPE_BASIC_PLAN_PRICE_ID // 300元/月
export const STRIPE_PRO_PLAN_PRICE_ID = process.env.STRIPE_PRO_PLAN_PRICE_ID // 1000元/月
export const SUBSCRIPTION_SUCCESS_URL = process.env.SUBSCRIPTION_SUCCESS_URL
export const SUBSCRIPTION_CANCEL_URL = process.env.SUBSCRIPTION_CANCEL_URL
export const EASYKOL_STRIPE_PROTAL_RETURN_URL = process.env.EASYKOL_STRIPE_PROTAL_RETURN_URL

/**
 * Stripe配置状态接口
 */
export interface StripeConfigStatus {
  /** 配置是否完整 */
  isConfigured: boolean
  /** 是否使用测试模式 */
  isTestMode: boolean
}

/**
 * 验证生产环境和测试环境下的Stripe配置
 * 确保所有必需的Stripe配置参数都已正确设置
 * @returns 配置状态对象
 * @throws 如果必需的配置缺失则抛出错误
 */
export function validateStripeConfigForProduction(): StripeConfigStatus {
  // 判断当前环境是否为生产或测试环境
  const isProdOrTest = ['production', 'test'].includes(process.env.NODE_ENV || '')

  if (isProdOrTest) {
    // 验证必需的密钥
    if (!STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY 未设置：支付功能需要此密钥才能正常工作')
    }

    if (!STRIPE_PUBLISHABLE_KEY) {
      throw new Error('STRIPE_PUBLISHABLE_KEY 未设置：前端支付集成需要此密钥')
    }

    if (!STRIPE_WEBHOOK_SECRET) {
      throw new Error('STRIPE_WEBHOOK_SECRET 未设置：处理 Stripe Webhook 需要此密钥')
    }

    // 验证订阅计划价格ID
    if (!STRIPE_BASIC_PLAN_PRICE_ID) {
      throw new Error('STRIPE_BASIC_PLAN_PRICE_ID 未设置：基础订阅计划需要此ID')
    }

    if (!STRIPE_PRO_PLAN_PRICE_ID) {
      throw new Error('STRIPE_PRO_PLAN_PRICE_ID 未设置：高级订阅计划需要此ID')
    }

    // 验证回调URL
    if (!SUBSCRIPTION_SUCCESS_URL) {
      throw new Error('SUBSCRIPTION_SUCCESS_URL 未设置：订阅成功后需要重定向到此URL')
    }

    if (!SUBSCRIPTION_CANCEL_URL) {
      throw new Error('SUBSCRIPTION_CANCEL_URL 未设置：订阅取消后需要重定向到此URL')
    }

    // 验证密钥格式
    if (!STRIPE_SECRET_KEY.startsWith('sk_')) {
      throw new Error('STRIPE_SECRET_KEY 格式无效：应以 sk_ 开头')
    }

    if (!STRIPE_PUBLISHABLE_KEY.startsWith('pk_')) {
      throw new Error('STRIPE_PUBLISHABLE_KEY 格式无效：应以 pk_ 开头')
    }

    if (!STRIPE_WEBHOOK_SECRET.startsWith('whsec_')) {
      throw new Error('STRIPE_WEBHOOK_SECRET 格式无效：应以 whsec_ 开头')
    }

    // 检查是否在生产环境中使用测试密钥
    if (process.env.NODE_ENV === 'production' && STRIPE_SECRET_KEY.startsWith('sk_test_')) {
      console.warn('⚠️ 警告: 在生产环境中使用了 Stripe 测试密钥，这可能不是您想要的')
    }
  }

  return {
    isConfigured: Boolean(STRIPE_SECRET_KEY && STRIPE_PUBLISHABLE_KEY && STRIPE_WEBHOOK_SECRET),
    isTestMode: STRIPE_SECRET_KEY?.startsWith('sk_test_') || false,
  }
}

/**
 * 初始化Stripe配置并进行验证
 * 在应用启动时调用此函数
 * @returns Stripe配置状态
 */
export function initStripeConfig(): StripeConfigStatus {
  try {
    const stripeConfig = validateStripeConfigForProduction()
    if (stripeConfig.isTestMode && process.env.NODE_ENV === 'production') {
      console.warn('⚠️ 警告: 生产环境使用Stripe测试模式')
    } else if (stripeConfig.isConfigured) {
      console.log('✅ Stripe配置验证通过')
    }
    return stripeConfig
  } catch (err: any) {
    console.error('❌ Stripe配置验证失败:', err.message)
    Sentry.captureException(err)
    // 在生产环境中，如果Stripe配置验证失败，则退出进程
    if (process.env.NODE_ENV === 'production') {
      console.error('致命错误: 生产环境中Stripe配置无效，服务无法启动')
      process.exit(1)
    }
    return {
      isConfigured: false,
      isTestMode: false,
    }
  }
}
