// 获取表格格式
import { sheets_v4 } from 'googleapis'
import {
  EDITABLE_FIELDS,
  POST_DATA_EDITABLE_HEADER_BACKGROUND_COLOR,
  POST_DATA_HEADER_BACKGROUND_COLOR,
  POST_DATA_HEADER_LENGTH,
  POST_DATA_SHEET_HEADER,
  POST_DATA_WORK_SPACE_RANGE,
} from '../postDataConf'

// 获取连续的列区间
const getColumnRanges = (
  fields: [string, (typeof POST_DATA_SHEET_HEADER)[keyof typeof POST_DATA_SHEET_HEADER]][],
) => {
  const ranges: { start: number; end: number }[] = []
  let currentRange: { start: number; end: number } | null = null

  fields.forEach(([, field], index) => {
    if (!currentRange) {
      currentRange = { start: field.column, end: field.column + 1 }
    } else if (field.column === currentRange.end) {
      currentRange.end = field.column + 1
    } else {
      ranges.push(currentRange)
      currentRange = { start: field.column, end: field.column + 1 }
    }
  })
  if (currentRange) ranges.push(currentRange)
  return ranges
}

//这里获取post data sheet的表格样式和数据样式
export async function getPostDataSheetStyle(sheetId: number): Promise<sheets_v4.Schema$Request[]> {
  return [
    // 统一设置列宽
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_WORK_SPACE_RANGE.startColumnIndex,
          endIndex: POST_DATA_WORK_SPACE_RANGE.endColumnIndex,
        },
        properties: { pixelSize: POST_DATA_WORK_SPACE_RANGE.columnPixelSize },
        fields: 'pixelSize',
      },
    },
    // 统一设置所有行高
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'ROWS',
          startIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
          endIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
        },
        properties: {
          pixelSize: POST_DATA_WORK_SPACE_RANGE.rowPixelSize,
        },
        fields: 'pixelSize',
      },
    },
    // 设置表头行样式 A1-T1
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: 0,
          endRowIndex: 1,
          startColumnIndex: 0,
          endColumnIndex: POST_DATA_HEADER_LENGTH,
        },
        cell: {
          userEnteredFormat: {
            backgroundColor: POST_DATA_HEADER_BACKGROUND_COLOR,
            textFormat: {
              bold: true,
              fontSize: 11,
            },
            horizontalAlignment: 'CENTER',
            verticalAlignment: 'MIDDLE',
          },
        },
        fields:
          'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)',
      },
    },
    // 设置可编辑列的背景颜色
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: 0,
          endRowIndex: 1,
          startColumnIndex: POST_DATA_SHEET_HEADER['Cost($)'].column,
          endColumnIndex: POST_DATA_SHEET_HEADER['Free Note 2'].column + 1,
        },
        cell: {
          userEnteredFormat: {
            backgroundColor: POST_DATA_EDITABLE_HEADER_BACKGROUND_COLOR,
            textFormat: {
              bold: true,
              fontSize: 11,
            },
            horizontalAlignment: 'CENTER',
            verticalAlignment: 'MIDDLE',
          },
        },
        fields:
          'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)',
      },
    },
    // 设置联系方式列的背景颜色
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: 0,
          endRowIndex: 1,
          startColumnIndex: POST_DATA_SHEET_HEADER['Contact'].column,
          endColumnIndex: POST_DATA_SHEET_HEADER['Contact'].column + 1,
        },
        cell: {
          userEnteredFormat: {
            backgroundColor: POST_DATA_EDITABLE_HEADER_BACKGROUND_COLOR,
            textFormat: {
              bold: true,
              fontSize: 11,
            },
            horizontalAlignment: 'CENTER',
            verticalAlignment: 'MIDDLE',
          },
        },
        fields:
          'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment,verticalAlignment)',
      },
    },
    // 评论国家分布列
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['Audience Profile'].column, // R列
          endIndex: POST_DATA_SHEET_HEADER['Creator Profile'].column,
        },
        properties: {
          pixelSize: POST_DATA_SHEET_HEADER['Audience Profile'].pixelSize, // 设置为360px
        },
        fields: 'pixelSize',
      },
    },
    // 博主profile
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['Creator Profile'].column, // Q列
          endIndex: POST_DATA_SHEET_HEADER['Contact'].column,
        },
        properties: {
          pixelSize: POST_DATA_SHEET_HEADER['Creator Profile'].pixelSize, // 设置为360px
        },
        fields: 'pixelSize',
      },
    },
    // 联系方式的列宽
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['Contact'].column, // R列
          endIndex: POST_DATA_SHEET_HEADER['Contact'].column + 1,
        },
        properties: {
          pixelSize: POST_DATA_SHEET_HEADER['Contact'].pixelSize,
        },
        fields: 'pixelSize',
      },
    },
    // 用户输入链接 User Input Link
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['User Input Link'].column, // S列
          endIndex: POST_DATA_SHEET_HEADER['User Input Link'].column + 1,
        },
        properties: {
          pixelSize: POST_DATA_SHEET_HEADER['User Input Link'].pixelSize, // 400px
        },
        fields: 'pixelSize',
      },
    },
    // 标签 Labels
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['Labels'].column, // T列
          endIndex: POST_DATA_SHEET_HEADER['Labels'].column + 1,
        },
        properties: {
          pixelSize: POST_DATA_SHEET_HEADER['Labels'].pixelSize, // 400px
        },
        fields: 'pixelSize',
      },
    },
    // 隐藏 ID 列
    {
      updateDimensionProperties: {
        range: {
          sheetId,
          dimension: 'COLUMNS',
          startIndex: POST_DATA_SHEET_HEADER['ID'].column,
          endIndex: POST_DATA_SHEET_HEADER['ID'].column + 1,
        },
        properties: {
          hiddenByUser: POST_DATA_SHEET_HEADER['ID'].hidden,
        },
        fields: 'hiddenByUser',
      },
    },
    // 保护表头区域 第0-1行
    {
      addProtectedRange: {
        protectedRange: {
          range: {
            sheetId,
            startRowIndex: 0,
            endRowIndex: 1,
            startColumnIndex: 0,
            endColumnIndex: POST_DATA_HEADER_LENGTH,
          },
          warningOnly: false,
          description: '保护表头区域',
          editors: {},
        },
      },
    },
    // 添加列保护 A-E
    {
      addProtectedRange: {
        protectedRange: {
          range: {
            sheetId,
            startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
            endRowIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
            startColumnIndex: POST_DATA_SHEET_HEADER['ID'].column, // A列
            endColumnIndex: POST_DATA_SHEET_HEADER['CPM'].column + 1, // E列
          },
          warningOnly: false,
          description: '保护基本信息和成本列 A-E',
          editors: {},
        },
      },
    },
    // 添加列保护 I-Q
    {
      addProtectedRange: {
        protectedRange: {
          range: {
            sheetId,
            startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
            endRowIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
            startColumnIndex: POST_DATA_SHEET_HEADER['Region'].column,
            endColumnIndex: POST_DATA_SHEET_HEADER['Creator Profile'].column + 1,
          },
          warningOnly: false,
          description: '保护其他数据列 I-Q',
          editors: {},
        },
      },
    },
    // 添加列保护 S-T
    {
      addProtectedRange: {
        protectedRange: {
          range: {
            sheetId,
            startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
            endRowIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
            startColumnIndex: POST_DATA_SHEET_HEADER['User Input Link'].column,
            endColumnIndex: POST_DATA_SHEET_HEADER['Labels'].column + 1,
          },
          warningOnly: false,
          description: '保护数据列 S-T',
          editors: {},
        },
      },
    },
    // 添加播放量单元格批注
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
          endRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex + 1,
          startColumnIndex: POST_DATA_SHEET_HEADER['Views'].column, // 播放量
          endColumnIndex: POST_DATA_SHEET_HEADER['Views'].column + 1,
        },
        cell: {
          note: POST_DATA_SHEET_HEADER['Views']?.note,
        },
        fields: 'note',
      },
    },
    // 添加cost单元格批注
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
          endRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex + 1,
          startColumnIndex: POST_DATA_SHEET_HEADER['CPM'].column, // E列 (CPM)
          endColumnIndex: POST_DATA_SHEET_HEADER['CPM'].column + 1,
        },
        cell: {
          note: POST_DATA_SHEET_HEADER['CPM']?.note,
        },
        fields: 'note',
      },
    },
    // 添加单元格批注
    {
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex,
          endRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex + 1,
          startColumnIndex: POST_DATA_SHEET_HEADER['Free Note 1'].column,
          endColumnIndex: POST_DATA_SHEET_HEADER['Free Note 2'].column + 1,
        },
        cell: {
          note: POST_DATA_SHEET_HEADER['Free Note 1']?.note,
        },
        fields: 'note',
      },
    },
    // 冻结前三列
    {
      updateSheetProperties: {
        properties: {
          sheetId,
          gridProperties: {
            frozenColumnCount: POST_DATA_SHEET_HEADER['Platform'].column,
            rowCount: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
            columnCount: POST_DATA_WORK_SPACE_RANGE.endColumnIndex,
          },
        },
        fields: 'gridProperties',
      },
    },
  ]
}

// 设置投放数据的行格式
export async function applyPostDataSheetDataRowsStyle(
  sheets: sheets_v4.Sheets,
  spreadsheetId: string,
  sheetId: number,
  dataRowCount: number,
  sheetData: any[],
): Promise<void> {
  // 1. 基础格式请求
  // 仅读字段
  const readOnlyFields = Object.entries(POST_DATA_SHEET_HEADER)
    .filter(([key]) => !EDITABLE_FIELDS.includes(key))
    .sort(([, a], [, b]) => a.column - b.column)

  // 基础格式请求 - 所有数据居中显示（包括可编辑字段）
  const allFields = Object.entries(POST_DATA_SHEET_HEADER).sort(
    ([, a], [, b]) => a.column - b.column,
  )

  const baseFormatRequests = getColumnRanges(allFields).map((range) => ({
    repeatCell: {
      range: {
        sheetId,
        startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex + 1,
        endRowIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
        startColumnIndex: range.start,
        endColumnIndex: range.end,
      },
      cell: {
        userEnteredFormat: {
          verticalAlignment: 'MIDDLE',
          horizontalAlignment: 'CENTER',
          textFormat: { fontSize: 11 },
          borders: {
            bottom: { style: 'SOLID', color: { red: 0.8, green: 0.8, blue: 0.8 } },
            left: { style: 'SOLID', color: { red: 0.8, green: 0.8, blue: 0.8 } },
            right: { style: 'SOLID', color: { red: 0.8, green: 0.8, blue: 0.8 } },
          },
        },
      },
      fields: 'userEnteredFormat(verticalAlignment,horizontalAlignment,textFormat,borders)',
    },
  }))

  //  特殊格式请求 - 评论国家分布列
  const commentDistributionFormat = {
    repeatCell: {
      range: {
        sheetId,
        startRowIndex: 1, // 从第1行开始
        endRowIndex: 1 + dataRowCount,
        startColumnIndex: POST_DATA_SHEET_HEADER['Audience Profile'].column,
        endColumnIndex: POST_DATA_SHEET_HEADER['Audience Profile'].column + 1,
      },
      cell: {
        userEnteredFormat: {
          wrapStrategy: 'WRAP',
          verticalAlignment: 'MIDDLE',
          horizontalAlignment: 'LEFT',
          textFormat: { fontSize: 11 },
        },
      },
      fields: 'userEnteredFormat(wrapStrategy,verticalAlignment,horizontalAlignment,textFormat)',
    },
  }

  //  链接请求
  const linkRequests = sheetData.map((data, index) => ({
    repeatCell: {
      range: {
        sheetId,
        startRowIndex: 1 + index, // 从第1行开始
        endRowIndex: 2 + index,
        startColumnIndex: POST_DATA_SHEET_HEADER['Post Link'].column,
        endColumnIndex: POST_DATA_SHEET_HEADER['Post Link'].column + 1,
      },
      cell: {
        userEnteredValue: {
          stringValue: data.influencer?.replace(/^@/, '') || data.nickName || '',
        },
        userEnteredFormat: {
          textFormat: {
            foregroundColor: { red: 0.06, green: 0.45, blue: 0.87 },
            underline: true,
            link: { uri: data.postLink || '' },
          },
          horizontalAlignment: 'CENTER',
        },
      },
      fields: 'userEnteredValue,userEnteredFormat(textFormat,horizontalAlignment)',
    },
  }))

  // 修改数字格式化请求
  const decimalFormatFields = ['CPM', 'Cost($)']
  const integerFormatFields = ['Views', 'Likes', 'Comments', 'Favorites', 'Shares']

  // 两位小数的格式请求
  const decimalFormatRanges = getColumnRanges(
    allFields.filter(([key]) => decimalFormatFields.includes(key)),
  )

  const decimalFormatRequests = decimalFormatRanges.map((range) => ({
    repeatCell: {
      range: {
        sheetId,
        startRowIndex: 1,
        endRowIndex: 1 + dataRowCount,
        startColumnIndex: range.start,
        endColumnIndex: range.end,
      },
      cell: {
        userEnteredFormat: {
          numberFormat: { type: 'NUMBER', pattern: '#,##0.00' },
          horizontalAlignment: 'CENTER',
        },
      },
      fields: 'userEnteredFormat(numberFormat,horizontalAlignment)',
    },
  }))

  // 整数的格式请求 - 使用K和M单位
  const integerFormatRanges = getColumnRanges(
    readOnlyFields.filter(([key]) => integerFormatFields.includes(key)),
  )

  const integerFormatRequests = integerFormatRanges.map((range) => ({
    repeatCell: {
      range: {
        sheetId,
        startRowIndex: 1,
        endRowIndex: 1 + dataRowCount,
        startColumnIndex: range.start,
        endColumnIndex: range.end,
      },
      cell: {
        userEnteredFormat: {
          // numberFormat: {
          //   type: 'NUMBER',
          //   pattern: '[<1000]#,##0;[<1000000]0.0,"K";0.00,,"M"',
          // },
          numberFormat: { type: 'NUMBER', pattern: '#,##0' }, // 整数格式
          horizontalAlignment: 'CENTER',
        },
      },
      fields: 'userEnteredFormat(numberFormat,horizontalAlignment)',
    },
  }))

  //  CPM公式请求 - 修改格式为两位小数
  const formulaRequests = Array(dataRowCount)
    .fill(null)
    .map((_, index) => ({
      repeatCell: {
        range: {
          sheetId,
          startRowIndex: 1 + index, // 从第1行开始
          endRowIndex: 2 + index,
          startColumnIndex: POST_DATA_SHEET_HEADER['CPM'].column,
          endColumnIndex: POST_DATA_SHEET_HEADER['CPM'].column + 1,
        },
        cell: {
          userEnteredFormat: {
            numberFormat: { type: 'NUMBER', pattern: '#,##0.00' }, // 改为两位小数
            horizontalAlignment: 'CENTER',
            verticalAlignment: 'MIDDLE',
          },
          userEnteredValue: {
            formulaValue: `=IF(${POST_DATA_SHEET_HEADER['Views'].letter}${2 + index}>0, (${POST_DATA_SHEET_HEADER['Cost($)'].letter}${2 + index}/(${POST_DATA_SHEET_HEADER['Views'].letter}${2 + index}/1000)), 0)`,
          },
        },
        fields:
          'userEnteredFormat(numberFormat,horizontalAlignment,verticalAlignment),userEnteredValue',
      },
    }))

  // Cost($) 列的数据验证请求
  const costValidationRequest = {
    setDataValidation: {
      range: {
        sheetId,
        startRowIndex: POST_DATA_WORK_SPACE_RANGE.startRowIndex + 1,
        endRowIndex: POST_DATA_WORK_SPACE_RANGE.endRowIndex,
        startColumnIndex: POST_DATA_SHEET_HEADER['Cost($)'].column,
        endColumnIndex: POST_DATA_SHEET_HEADER['Cost($)'].column + 1,
      },
      rule: {
        condition: {
          type: 'NUMBER_GREATER_THAN_EQ',
          values: [{ userEnteredValue: '0' }],
        },
        showCustomUi: true,
        strict: true,
      },
    },
  }

  //  合并所有请求并执行
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        ...baseFormatRequests,
        commentDistributionFormat,
        ...decimalFormatRequests,
        ...integerFormatRequests,
        ...linkRequests,
        ...formulaRequests,
        costValidationRequest,
      ],
    },
  })
}
