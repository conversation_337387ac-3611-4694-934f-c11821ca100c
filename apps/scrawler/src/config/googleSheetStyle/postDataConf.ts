// post data 工作区范围
export const POST_DATA_WORK_SPACE_RANGE = {
  startRowIndex: 0,
  endRowIndex: 200,
  startColumnIndex: 0,
  endColumnIndex: 26,
  rowPixelSize: 30,
  columnPixelSize: 120,
}

// post data 表头
export const POST_DATA_SHEET_HEADER = {
  ID: { column: 0, letter: 'A', value: 'ID', pixelSize: 120, hidden: true },
  'Post Date': { column: 1, letter: 'B', value: 'Post Date', pixelSize: 120 },
  'Post Link': { column: 2, letter: 'C', value: 'Post Link', pixelSize: 120 },
  Platform: { column: 3, letter: 'D', value: 'Platform', pixelSize: 120 },
  CPM: { column: 4, letter: 'E', value: 'CPM', pixelSize: 120, note: 'CPM = Cost / (Views/1000)' },
  'Cost($)': { column: 5, letter: 'F', value: 'Cost ($)', pixelSize: 120 },
  'Free Note 1': {
    column: 6,
    letter: 'G',
    value: 'Free Note 1',
    pixelSize: 120,
    note: 'Free Note 1',
  },
  'Free Note 2': {
    column: 7,
    letter: 'H',
    value: 'Free Note 2',
    pixelSize: 120,
    note: 'Free Note 2 ',
  },
  Region: { column: 8, letter: 'I', value: 'Region', pixelSize: 120 },
  ER: { column: 9, letter: 'J', value: 'ER', pixelSize: 120 },
  Views: {
    column: 10,
    letter: 'K',
    value: 'Views',
    pixelSize: 120,
    note: 'On Instagram, a "Post" specifically refers to image-and-text content, which does not have view counts. The data we provide is derived by multiplying the number of likes by 15, so please do not treat it as a strict metric.',
  },
  Likes: { column: 11, letter: 'L', value: 'Likes', pixelSize: 120 },
  Comments: { column: 12, letter: 'M', value: 'Comments', pixelSize: 120 },
  Favorites: { column: 13, letter: 'N', value: 'Favorites', pixelSize: 120 },
  Shares: { column: 14, letter: 'O', value: 'Shares', pixelSize: 120 },
  'Audience Profile': {
    column: 15,
    letter: 'P',
    value: 'Audience Profile',
    pixelSize: 400,
  },
  'Creator Profile': { column: 16, letter: 'Q', value: 'Creator Profile', pixelSize: 400 },
  Contact: { column: 17, letter: 'R', value: 'Contact', pixelSize: 200 },
  'User Input Link': { column: 18, letter: 'S', value: 'User Input Link', pixelSize: 500 },
  Labels: { column: 19, letter: 'T', value: 'Labels', pixelSize: 400 },
} as const

//  定义可编辑字段列表
export const EDITABLE_FIELDS = ['Cost($)', 'Free Note 1', 'Free Note 2', 'Contact']

// post data 表头长度
export const POST_DATA_HEADER_LENGTH = Object.keys(POST_DATA_SHEET_HEADER).length

// post data 表头背景颜色
export const POST_DATA_HEADER_BACKGROUND_COLOR = {
  red: 164 / 255,
  green: 194 / 255,
  blue: 244 / 255,
}

// post data 可编辑列背景颜色
export const POST_DATA_EDITABLE_HEADER_BACKGROUND_COLOR = {
  red: 255 / 255,
  green: 255 / 255,
  blue: 255 / 255,
}
