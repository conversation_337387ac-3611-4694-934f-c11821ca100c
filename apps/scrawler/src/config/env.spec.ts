import { supabase } from '@/api/supabase'
import { EMAIL_PERSONALIZATION_BASIC_PROMPT } from '@/config/env.ts'
import { describe, expect, it } from 'vitest'

describe('some env tests', () => {
  it('should read multi line value', () => {
    expect(EMAIL_PERSONALIZATION_BASIC_PROMPT.length).gt(100)
  })

  it('should verify supabase auth token', async () => {
    const token =
      'eyJhbGciOiJIUzI1NiIsImtpZCI6IkRPZWM0dEdzclJ3bUVtZjciLCJ0eXAiOiJKV1QifQ.eyJpc3MiOiJodHRwczovL2F2em5saWlhd2F5cndqcXF4emN3LnN1cGFiYXNlLmNvL2F1dGgvdjEiLCJzdWIiOiIwOGI5YjQ0Yi1lY2FjLTQ4MWItOTM5ZC1jY2E0ZDNlNTcxZWQiLCJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNzM3NjA1MjY0LCJpYXQiOjE3Mzc2MDE2NjQsImVtYWlsIjoieGlhb2hhb3hpbmdAaWZ0ZWNoLmlvIiwicGhvbmUiOiIiLCJhcHBfbWV0YWRhdGEiOnsicHJvdmlkZXIiOiJnb29nbGUiLCJwcm92aWRlcnMiOlsiZ29vZ2xlIl19LCJ1c2VyX21ldGFkYXRhIjp7ImF2YXRhcl91cmwiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NLOEtYM2t4SzVHOHlfdklkckhybHNEeUc2X0dMWWRkREhISVlvLU1YWTJyWDZqRXc9czk2LWMiLCJjdXN0b21fY2xhaW1zIjp7ImhkIjoiaWZ0ZWNoLmlvIn0sImVtYWlsIjoieGlhb2hhb3hpbmdAaWZ0ZWNoLmlvIiwiZW1haWxfdmVyaWZpZWQiOnRydWUsImZ1bGxfbmFtZSI6IuiClueak-aYnyIsImlzcyI6Imh0dHBzOi8vYWNjb3VudHMuZ29vZ2xlLmNvbSIsIm5hbWUiOiLogpbnmpPmmJ8iLCJwaG9uZV92ZXJpZmllZCI6ZmFsc2UsInBpY3R1cmUiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NLOEtYM2t4SzVHOHlfdklkckhybHNEeUc2X0dMWWRkREhISVlvLU1YWTJyWDZqRXc9czk2LWMiLCJwcm92aWRlcl9pZCI6IjEwMDQ1OTQxMzA1NjgyNjk5ODU1OCIsInN1YiI6IjEwMDQ1OTQxMzA1NjgyNjk5ODU1OCJ9LCJyb2xlIjoiYXV0aGVudGljYXRlZCIsImFhbCI6ImFhbDEiLCJhbXIiOlt7Im1ldGhvZCI6Im9hdXRoIiwidGltZXN0YW1wIjoxNzM3NjAxNjY0fV0sInNlc3Npb25faWQiOiIwN2M0MTIyMi0xZDJhLTRiZTYtOTQ4My00Nzg3YTBhNzVmMDciLCJpc19hbm9ueW1vdXMiOmZhbHNlfQ.Ekjl7TcBByj2HGqO8H3GV4AHX9uq-V59evQPOBExvEY'
    const key =
      '43pHm0xjDjtg2hZBmgwON36mG8RVS040lcJUugGS1Yt083Gu2z4nxzNo/j3Bto40G4ogcOJV4vCoqRh50m3Q7A=='
    const jwt = require('jsonwebtoken')
    const result = jwt.verify(token, key)
    console.log(result)
    const user = await supabase.auth.getUser(token)
    console.log(user)
    // const session = await supabase.auth.getSession()
    // session.data.session?.access_token
    // const user = session.data.session?.user
    // console.log(user)
  })
})
