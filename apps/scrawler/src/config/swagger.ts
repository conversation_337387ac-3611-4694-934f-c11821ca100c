import { FastifyDynamicSwaggerOptions } from '@fastify/swagger'
import { FastifySwaggerUiOptions } from '@fastify/swagger-ui'
import { TSchema, Type } from '@sinclair/typebox'

export const FullResponseSchema = <T extends TSchema>(dataSchema?: T) => {
  return {
    ...globalResponses,
    200: CommonResponseSchema(dataSchema || Type.Any()),
  }
}

export const CommonResponseSchema = <T extends TSchema>(dataSchema: T) =>
  Type.Object({
    statusCode: Type.Number(),
    error: Type.Union([Type.Null(), Type.String()]),
    message: Type.String(),
    data: dataSchema,
  })

// 定义通用的错误响应 schema
const ErrorResponseSchema = Type.Object({
  statusCode: Type.Number(),
  error: Type.String(),
  message: Type.String(),
  data: Type.Any(),
})

// 定义通用的成功响应 schema
const SuccessResponseSchema = Type.Object({
  statusCode: Type.Number(),
  error: Type.String(),
  message: Type.String(),
  data: Type.Any(),
})

export const swaggerOptions: FastifyDynamicSwaggerOptions = {
  openapi: {
    info: {
      title: 'EasyKol API',
      description: 'EasyKol Backend API Documentation',
      version: '1.0.0',
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        ErrorResponse: ErrorResponseSchema,
        SuccessResponse: SuccessResponseSchema,
      },
      responses: {
        BadRequest: {
          description: 'Bad Request',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
        Unauthorized: {
          description: 'Unauthorized',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
        Forbidden: {
          description: 'Forbidden',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
        NotFound: {
          description: 'Not Found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
        ValidationError: {
          description: 'Validation Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
        InternalServerError: {
          description: 'Internal Server Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ErrorResponse',
              },
            },
          },
        },
      },
    },
  },
}

// 定义全局响应配置
export const globalResponses = {
  '200': SuccessResponseSchema,
  '400': ErrorResponseSchema,
  '500': ErrorResponseSchema,
}

export const swaggerUiOptions: FastifySwaggerUiOptions = {
  routePrefix: '/api/swagger',
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false,
  },
  staticCSP: true,
  transformStaticCSP: (header: any) => header,
}
