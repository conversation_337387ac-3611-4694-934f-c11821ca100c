/**
 * default allowed emails
 */
export const DEFAULT_ALLOWED_EMAILS = `<EMAIL>,xiaoh<PERSON><EMAIL>,<EMAIL>,<EMAIL>`

/**
 * Slack 通知中需要 @ 的用户信息
 * 包含用户 ID 和用户名
 */
export interface SlackUser {
  id: string
  name: string
}

/**
 * 从环境变量JSON字符串解析Slack用户列表
 * 默认值为李颖的用户信息
 */
export const SLACK_NOTIFY_USERS: SlackUser[] = (() => {
  try {
    // 从环境变量直接获取JSON字符串
    const jsonString = process.env.SLACK_NOTIFY_USERS_JSON || '[{"id":"U07F03DMRLN","name":"李颖"}]'

    // 尝试解析JSON字符串
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('解析SLACK_NOTIFY_USERS_JSON失败，使用默认值:', error)
    // 解析失败时使用默认值
    return [
      {
        id: 'U07F03DMRLN',
        name: '李颖',
      },
    ]
  }
})()
