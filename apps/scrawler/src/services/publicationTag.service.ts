import { PaginationParams } from '@/types/pagination'
import { GetPublicationTagsResponse, PublicationTagResponse } from '@/types/publicationTag'
import { PaginationService } from '@/utils/pagination'
import { KolPlatform, prisma } from '@repo/database'
import assert from 'assert'

const MAX_TAGS_PER_PUBLICATION = 5

export interface GetPublicationsParams extends PaginationParams {
  ids?: string[]
  platforms?: string[]
}

export const publicationTagService = {
  async addTagToPublication(
    userId: string,
    publicationId: string,
    tagId: string,
  ): Promise<PublicationTagResponse> {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('标签不存在或无权限操作')
    }

    const existingTag = await prisma.publicationTag.findUnique({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })

    if (existingTag) {
      throw new Error('该标签已添加到投放数据')
    }

    const tagsCount = await prisma.publicationTag.count({
      where: {
        publicationId,
      },
    })

    if (tagsCount >= MAX_TAGS_PER_PUBLICATION) {
      throw new Error(`每个投放数据最多添加${MAX_TAGS_PER_PUBLICATION}个标签`)
    }

    const publicationTag = await prisma.publicationTag.create({
      data: {
        publicationId,
        tagId,
      },
    })

    return {
      id: publicationTag.id,
      tagId: publicationTag.tagId,
      publicationId: publicationTag.publicationId,
      name: tag.name,
      color: tag.color,
      createdAt: publicationTag.createdAt,
    } as PublicationTagResponse
  },

  async removeTagFromPublication(
    userId: string,
    publicationId: string,
    tagId: string,
  ): Promise<void> {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('标签不存在或无权限操作')
    }

    const publicationTag = await prisma.publicationTag.findUnique({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })

    if (!publicationTag) {
      throw new Error('该投放数据未添加此标签')
    }

    await prisma.publicationTag.delete({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })
  },

  async getPublicationTags(userId: string, publicationId: string) {
    const publicationTags = await prisma.publicationTag.findMany({
      where: {
        publicationId,
      },
      include: {
        tag: true,
      },
    })

    const tags: PublicationTagResponse[] = publicationTags.map((pt) => ({
      id: pt.id,
      tagId: pt.tagId,
      publicationId: pt.publicationId,
      name: pt.tag.name,
      color: pt.tag.color,
      createdAt: pt.createdAt,
    }))

    return {
      items: tags,
      total: tags.length,
    } as GetPublicationTagsResponse
  },

  async getTagPublications(userId: string, params: GetPublicationsParams) {
    const { ids = [], platforms = [], ...pagination } = params

    const userGoogleSheet = await prisma.userGoogleSheet.findFirst({
      where: {
        userId,
        status: 'ACTIVE',
      },
    })

    if (!userGoogleSheet) {
      throw new Error('未找到有效的 Google Sheet 配置')
    }

    // 验证所有标签是否存在且属于该用户
    if (ids.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: {
            in: ids,
          },
          createdBy: userId,
        },
      })

      assert(tags.length === ids.length, new Error('部分标签不存在或无权限访问'))
    }

    // 验证平台是否有效
    if (platforms.length > 0) {
      const invalidPlatforms = platforms.filter(
        (platform) => !Object.values(KolPlatform).includes(platform as KolPlatform),
      )
      if (invalidPlatforms.length > 0) {
        throw new Error(`无效的平台参数: ${invalidPlatforms.join(', ')}`)
      }
    }

    const { skip, pageSize, page } = PaginationService.handlePagination(pagination)

    const where = {
      AND: [
        {
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
        ...(ids.length > 0
          ? [
              {
                tags: {
                  some: {
                    tagId: {
                      in: ids,
                    },
                  },
                },
              },
            ]
          : []),
        ...(platforms.length > 0
          ? [
              {
                platform: {
                  in: platforms as KolPlatform[],
                },
              },
            ]
          : []),
      ],
    }

    const [publications, total] = await Promise.all([
      prisma.publicationStatisticsSheetData.findMany({
        where,
        include: {
          tags: {
            include: {
              tag: true,
            },
          },
          kol: true,
        },
        skip,
        take: pageSize,
        orderBy: {
          publishDate: 'desc',
        },
      }),
      prisma.publicationStatisticsSheetData.count({
        where,
      }),
    ])

    return PaginationService.handlePaginatedResponse(publications, total, page, pageSize)
  },

  async getAllPostLinks(userId: string, tagId: string) {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    assert(tag, new Error('标签不存在或无权限访问'))

    const postLinks = await prisma.publicationStatisticsSheetData.findMany({
      where: {
        postLink: {
          not: null,
        },
        tags: {
          some: {
            tagId: tagId,
          },
        },
      },
      select: {
        postLink: true,
        platform: true,
        publishDate: true,
      },
      orderBy: {
        publishDate: 'desc',
      },
    })

    return {
      items: postLinks,
      total: postLinks.length,
    }
  },
}
