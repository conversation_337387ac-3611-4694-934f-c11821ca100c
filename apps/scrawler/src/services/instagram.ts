import { IgTaggedUser, IgUser } from '@/api/@types/rapidapi/Instagram.ts'
import RapidApiStatsCollector from '@/api/ApiCallStat.ts'
import InstagramApi from '@/api/instagram'
import { logFilter } from '@/api/util'
import {
  FILTER_OFFICIAL_ACCOUNT,
  INSTAGRAM_API_CONCURRENCY,
  INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS,
  INS_DIMENSION_BATCH_CONCURRENCY,
  INS_DIMENSION_BATCH_SIZE,
  INS_RAPID_API_V3_CONCURRENCY,
  INS_ROUND_USER_COUNT,
  INS_VISIUAL_SIMILARITY_BATCH_SIZE,
  INS_VISIUAL_SIMILARITY_CONCURRENCY,
  LONG_CRAWLER_INS_BATCH_SIZE,
  PROJECT_THREE_ELEMENTS_TYPE,
} from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'
import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import {
  BloggerDimensionsAnalysisResultWithFlag,
  VisualSimilarityOutput,
  analyzeVisualSimilarityService,
} from '@/services/aiTools/visualSimilarity'
import KolService from '@/services/kol.ts'
import { PublicationStatsService } from '@/services/publicationStats.service'
import { findCurrentTasksByProjectId } from '@/services/similar.ts'
import { ApiCode } from '@/types/apiCode'
import { InsHashTagBreakResult, InsTaggedBreakResult } from '@/types/breakResult'
import { EmailSourceType } from '@/types/email'
import { InstagramSortType } from '@/types/instagram'
import { DeductDynamicQuotaParams, ERROR_MESSAGES } from '@/types/memberShip'
import { ProjectConfig } from '@/types/project'
import { createTaskRequest } from '@/types/request/similar.request.ts'
import {
  InsFollowingListTaskParams,
  InsFollowingListTaskResult,
  InsHashTagBreakTaskParams,
  InsHashTagBreakTaskResult,
  InsLongCrawlerBatchLogs,
  InsLongCrawlerFilters,
  InsLongCrawlerProgress,
  InsLongCrawlerTaskParams,
  InsTaggedBreakTaskParams,
  InsTaggedBreakTaskResult,
  InsWebListTaskResult,
} from '@/types/task.ts'
import { InstagramTaskResult } from '@/types/task/instagramTask'
import { GetPlatformTaskParams, GetTaskParams } from '@/types/taskParams'
import { TaskMetricsCollector } from '@/utils/TaskMetricsCollector'
import { parseUrlUtils } from '@/utils/parseUrl'
import { retryUtil } from '@/utils/retry'
import {
  KolInfo,
  KolPlatform,
  Prisma,
  QuotaType,
  SimilarChannelTask,
  SimilarChannelTaskStatus,
  TaskType,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import Bull from 'bull'
import dayjs from 'dayjs'
import apiQuota from './apiQuota'
import EmailService from './email'
import { getExcludeIds } from './excludeList'
import { JinaV4MilvusData, JinaV4MilvusService } from './jinaMilvus.service'
import { MembershipService } from './membership.service'
import { ProjectKolService } from './projectKol.service'
import { getEmbeddingForInstagram } from './worker/jina-embedding'

class InstagramService {
  private static instance: InstagramService

  static getInstance() {
    if (!InstagramService.instance) {
      InstagramService.instance = new InstagramService()
    }
    return InstagramService.instance
  }

  async getKolInfo(userIds: string[]): Promise<KolInfo[]> {
    const validUserIds = logFilter(
      userIds,
      (user) => typeof user === 'string' && user.length > 0,
      'ins filter username',
    )
    const existing = await prisma.kolInfo.findMany({
      where: {
        platformAccount: {
          in: validUserIds,
        },
        platform: KolPlatform.INSTAGRAM,
      },
    })
    if (existing.length !== userIds.length) {
      console.error(`kol info is missing! ${existing.length} != ${userIds.length}`)
      console.log(
        JSON.stringify(
          existing.map((i) => i.platformAccount).filter((i) => i && !userIds.includes(i)),
        ),
      )
    }
    return existing
  }

  async upsertKolInfo(users: IgUser[], userId: string) {
    const validUsers = logFilter(
      users,
      (user) => !!user?.username && typeof user.username === 'string' && user.username.length > 0,
      'ins filter username',
    )
    const usernames = validUsers.map((u) => u.username)
    const exists = await prisma.kolInfo.findMany({
      where: {
        platformAccount: {
          in: usernames,
        },
        platform: 'INSTAGRAM',
      },
    })
    const data = validUsers.map((u) => {
      return {
        title: u.fullName,
        description: u.biography,
        email: u.email,
        emailSource: u.emailSource,
        historyEmails: [],
        platformAccount: u.username,
        platform: KolPlatform.INSTAGRAM,
        avatar: u.profilePicUrl,
      }
    })
    const existUsernames = exists.map((u) => u.platformAccount)
    const emailMap = new Map(exists.map((u) => [u.platformAccount, u.email]))
    const kolIdMap = new Map(exists.map((u) => [u.platformAccount, u.id]))
    const inserts = data.filter((k) => !existUsernames.includes(k.platformAccount))
    const updates = data.filter((k) => existUsernames.includes(k.platformAccount))
    console.log(`insert ${inserts.length} kol info`)
    const created = await prisma.kolInfo.createManyAndReturn({
      data: inserts,
      skipDuplicates: true,
    })
    Bluebird.map(created, (insert) => {
      return EmailService.getInstance().addEmailAuditLog(
        userId,
        insert.id,
        insert.email,
        EmailSourceType.USER_SUBMIT,
      )
    })
    console.log(`update ${updates.length} kol info`)
    await Bluebird.map(
      updates,
      async (update) => {
        try {
          const kolId = kolIdMap.get(update.platformAccount)
          if (!kolId) {
            console.log(`cannot find kol id for ${update.platformAccount}`)
            return
          }
          const oldEmail = emailMap.get(update.platformAccount)
          const newEmail = update.email

          // Only store old email in history if we have both emails and they're different
          const shouldArchiveOldEmail = oldEmail && newEmail && oldEmail !== newEmail
          const historyEmailsUpdate = shouldArchiveOldEmail
            ? { historyEmails: { push: oldEmail } }
            : {}

          // Use new email if available, otherwise keep existing email
          const emailToUse = newEmail || oldEmail

          // audit log
          EmailService.getInstance().addEmailAuditLog(
            userId,
            kolId,
            update.email,
            update.emailSource as EmailSourceType,
          )
          await prisma.kolInfo.update({
            where: { id: kolId },
            data: {
              title: update.title,
              description: update.description,
              email: emailToUse,
              emailSource: emailToUse == oldEmail ? undefined : update.emailSource,
              platformAccount: update.platformAccount,
              platform: KolPlatform.INSTAGRAM,
              avatar: update.avatar,
              emailUpdatedAt: emailToUse == oldEmail ? undefined : new Date(),
              ...historyEmailsUpdate,
            },
          })
        } catch (err) {
          Sentry.captureException(err)
          console.log(`upsertKolInfo failed: ${err}`)
          return
        }
      },
      { concurrency: INSTAGRAM_API_CONCURRENCY },
    )
  }

  public async createKolById(
    userId: string | undefined,
    username: string,
    collector?: RapidApiStatsCollector,
  ): Promise<KolInfo | null> {
    try {
      if (!username) {
        throw new Error('username is required')
      }

      const user = await InstagramRapidApiV3.getInstance().getUserWithoutAbout(
        { username },
        collector,
      )
      if (!user) {
        throw new Error('user not found: ' + username)
      }

      const kolData = {
        title: user.fullName,
        description: user.biography,
        avatar: user.profilePicUrl,
        platformAccount: user.username,
        platform: KolPlatform.INSTAGRAM,
        updatedAt: new Date(),
      }

      const exsitingKol = await prisma.kolInfo.findFirst({
        where: {
          platform: KolPlatform.INSTAGRAM,
          platformAccount: username,
        },
      })

      if (exsitingKol) {
        const existingEmails = (exsitingKol.historyEmails as string[]) || []

        const emailFields: {
          email: string | undefined
          emailSource: string | undefined
          emailUpdatedAt: Date | undefined
          historyEmails: string[] | undefined
        } = {
          email: undefined,
          emailSource: undefined,
          emailUpdatedAt: undefined,
          historyEmails: undefined,
        }
        const needUpdate = user.email && user.email != exsitingKol.email
        if (needUpdate) {
          emailFields.email = user.email
          emailFields.emailSource = user.emailSource
          emailFields.emailUpdatedAt = new Date()
          emailFields.historyEmails = [...new Set([...existingEmails, user.email as string])]
          EmailService.getInstance().addEmailAuditLog(
            userId,
            exsitingKol.id,
            user.email,
            user.emailSource as EmailSourceType,
          )
        }
        return prisma.kolInfo.update({
          where: { id: exsitingKol.id },
          data: {
            ...kolData,
            ...emailFields,
          },
        })
      }

      return prisma.kolInfo.create({
        data: {
          ...kolData,
          email: user.email,
          emailSource: user.emailSource,
          historyEmails: [],
        },
      })
    } catch (err) {
      Sentry.captureException(err)
      console.log(`createKolById failed: ${err}`)
      return null
    }
  }

  async upsertUsers(users: IgUser[]): Promise<void> {
    // 过滤掉 ID 为 null 或 undefined 的用户，并获取有效的 ID 列表
    const validUsers = users.filter((u) => u?.id != null)
    const userIds = validUsers.map((u) => u.id!)

    // 如果过滤后没有有效的 userIds，则直接返回，避免 Prisma 查询错误
    if (userIds.length === 0) {
      console.warn('upsertUsers: No valid user IDs found after filtering.')
      return
    }

    const existing = await prisma.instagramUserInfo.findMany({
      where: {
        id: {
          in: userIds,
        },
      },
      select: {
        id: true,
      },
    })
    const existingIds = existing.map((u) => u.id)
    // 使用过滤后的 validUsers 来构建 data
    const data = validUsers.map((u) => {
      const recentPosts = u.posts?.slice(0, 10)
      let averageCommentCount = 0,
        averageLikeCount = 0
      if (recentPosts && recentPosts.length > 0) {
        averageCommentCount =
          recentPosts?.reduce((sum, num) => sum + num.comment_count, 0) / recentPosts?.length
        averageLikeCount =
          recentPosts?.reduce((sum, num) => sum + num.like_count, 0) / recentPosts?.length
      }

      // 计算发布统计
      const publicationStats = PublicationStatsService.calculatePublicationStats(
        u.posts ?? [],
        KolPlatform.INSTAGRAM,
      )

      return {
        id: u.id,
        username: u.username,
        profilePicUrl: u.profilePicUrl,
        fullName: u.fullName,
        region: u.region ?? '',
        followerCount: u.followerCount ?? undefined,
        averageLikeCount: averageLikeCount ?? undefined,
        averageCommentCount: averageCommentCount ?? undefined,
        lastPublishedTime: u.lastPublishedTime ?? undefined,
        posts: u.posts ?? [],
        publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
        accountInfo: {
          accountType: u.accountType,
          isVerified: u.isVerified,
          isBusiness: u.isBusiness,
          category: u.category,
        },
      }
    })
    const insertData = data.filter((user) => !existingIds.includes(user.id))
    const updateData = data.filter((user) => existingIds.includes(user.id))
    await prisma.instagramUserInfo.createMany({
      data: insertData as any,
      skipDuplicates: true,
    })

    updateData.forEach(async (data) => {
      try {
        const { id, ...dataWithoutId } = data
        await prisma.instagramUserInfo.update({
          where: { id: id },
          data: dataWithoutId as any,
        })
      } catch (err) {
        Sentry.captureException(err)
      }
    })
  }

  async superlikeSourceUser(task: SimilarChannelTask, params: createTaskRequest) {
    const kol = await KolService.getInstance().getKolByPlatformAndId(params.source, 'INSTAGRAM')
    if (!kol) {
      throw new Error(`kol not found: ${JSON.stringify(params)}`)
    }
    const existing = await prisma.projectKol.findFirst({
      where: {
        projectId: task.projectId,
        kolId: kol.id,
      },
    })
    if (existing) {
      await prisma.projectKol.update({
        where: {
          id: existing.id,
        },
        data: {
          attitude: 'SUPERLIKE',
          rateBy: task.createdBy!,
          updatedAt: new Date(),
          similarTaskId: task.id,
        },
      })
    } else {
      await prisma.projectKol.create({
        data: {
          attitude: 'SUPERLIKE',
          rateBy: task.createdBy!,
          lastSimilarAt: new Date(),
          kolId: kol.id,
          projectId: task.projectId,
          similarTaskId: task.id,
        },
      })
    }
  }

  // 最后返回的肯定是candidates
  public async processVisualSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const collector = new RapidApiStatsCollector()
    const sourcetUser = await InstagramApi.getInstance().getUserWithPosts(params.source, collector)
    if (!sourcetUser) {
      throw new Error('source user not exist!' + params.source)
    }

    // ratedUsernames
    const [projectKolRateUseNames, candidateUsernames, excludeChannelIds] = await Promise.all([
      ProjectKolService.getProjectKolUniques(task.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      }),
      this.getPreviousTasksCandidatesUsernames(task.projectId),
      getExcludeIds(task.createdBy!, KolPlatform.INSTAGRAM),
    ])

    let ratedUsernames = [...projectKolRateUseNames, ...candidateUsernames, ...excludeChannelIds]
    metrics.set('project_all_rated_user_count', ratedUsernames.length)

    const rawRelated = await InstagramApi.getInstance().getUserRelatedUsers(
      sourcetUser.username,
      collector,
    )
    if (!rawRelated?.length) {
      throw new Error(ERROR_MESSAGES.NO_RELATED_KOL)
    }
    metrics.set('source_user_related_users_count', rawRelated.length)
    // rawRelated 需要过滤掉已经评价过的用户
    const firstRoundRelatedUsers = rawRelated.filter((i) => !ratedUsernames.includes(i.username))

    ratedUsernames = [
      ...new Set([...ratedUsernames, ...firstRoundRelatedUsers.map((i) => i.username)]),
    ]
    metrics.set('first_round_related_users_count', firstRoundRelatedUsers.length)

    const round = params.taskRound || 1
    await metrics.withPhase('analyze_source_kol_vertical', () =>
      this.analyzeSourceKolVertical(sourcetUser, params),
    )

    const fisrtRoundUsesrWithPosts = await metrics.withPhase('first_round_fetch_user_posts', () =>
      this.fetchUserPosts(firstRoundRelatedUsers),
    )

    const firstVisionResult = await metrics.withPhase('first_round_analyze_visual_similarity', () =>
      this.analyzeUsersBatch(fisrtRoundUsesrWithPosts, params),
    )

    const firstVisionValidResults = firstVisionResult.filter(
      (result) => result.score > 0 && result.username != params.source,
    )

    if (firstVisionValidResults.length === 0) {
      return []
    }
    metrics.set('first_round_after_visual_similarity_filter_count', firstVisionValidResults.length)

    // get valid users with posts
    const firstRoundValidUserWithPosts = fisrtRoundUsesrWithPosts.filter((item) =>
      firstVisionValidResults.some((result) => result.username === item.username),
    )

    // fuillfill user details
    const firstRoundValidFullfilledUserWithPosts = await metrics.withPhase(
      'first_round_fetch_user_details',
      async () => this.fetchUsersDetails(firstRoundValidUserWithPosts),
    )

    // update first round  userInfo and kolInfo
    await metrics.withPhase('upsert_first_round_userInfo_and_kolInfo', () =>
      Promise.all([
        this.upsertKolInfo(firstRoundValidFullfilledUserWithPosts as any, task.createdBy!),
        this.upsertUsers(firstRoundValidFullfilledUserWithPosts as any),
      ]),
    )

    // filter  db  first round
    const firstVisionValidUsernames = firstVisionValidResults.map((user) => user.username)
    const conditions = {
      username: {
        in: firstVisionValidUsernames,
      },
      followerCount: {
        gte: params.minSubscribers ? Number(params.minSubscribers) : undefined,
        lte: params.maxSubscribers ? Number(params.maxSubscribers) : undefined,
      },
      region: params.regions?.length ? { in: params.regions } : undefined,
      averageLikeCount: {
        gte: params.minAverageLikeCount ? Number(params.minAverageLikeCount) : undefined,
        lte: params.maxAverageLikeCount ? Number(params.maxAverageLikeCount) : undefined,
      },
    }

    // get first round filter db valid users
    const firstRoundFinalUsers = await prisma.instagramUserInfo.findMany({
      where: conditions,
    })

    // get related source user names
    let relatedSourceUserNames: string[] = []
    if (firstRoundFinalUsers.length === 0) {
      relatedSourceUserNames = firstVisionValidUsernames
    } else {
      relatedSourceUserNames = firstRoundFinalUsers.map((user) => user.username)
      metrics.set('first_round_after_filter_db_users_count', firstRoundFinalUsers.length)
      // add firstVisionValidUsernames to relatedSourceUserNames
      firstVisionValidUsernames.forEach((username) => {
        if (!relatedSourceUserNames.includes(username)) {
          relatedSourceUserNames.push(username)
        }
      })
    }

    const allVisionResults: VisualSimilarityOutput[] = firstVisionResult
    const finalUsers = firstRoundFinalUsers

    if (round > 1) {
      const { secondVisionResults, secondRoundValidUsersWithPosts } =
        await this.performMultiRoundAnalysis(
          relatedSourceUserNames,
          round,
          params,
          metrics,
          ratedUsernames,
          collector,
        )

      const secondVisionValidResults = secondVisionResults.filter(
        (result) => result.score > 0 && result.username != params.source,
      )

      if (secondVisionValidResults.length > 0) {
        // fuillfill user details
        const secondRoundValidFullfilledUserWithPosts = await metrics.withPhase(
          'second_round_fetch_user_details',
          async () => this.fetchUsersDetails(secondRoundValidUsersWithPosts),
        )

        // update second round  userInfo and kolInfo
        await metrics.withPhase('upsert_second_round_userInfo_and_kolInfo', () =>
          Promise.all([
            this.upsertKolInfo(secondRoundValidFullfilledUserWithPosts as any, task.createdBy!),
            this.upsertUsers(secondRoundValidFullfilledUserWithPosts as any),
          ]),
        )

        // filter  db  second round
        const secondVisionValidUsernames = secondVisionValidResults.map((user) => user.username)
        const secondRoundConditions = {
          username: {
            in: secondVisionValidUsernames,
          },
          followerCount: {
            gte: params.minSubscribers ? Number(params.minSubscribers) : undefined,
            lte: params.maxSubscribers ? Number(params.maxSubscribers) : undefined,
          },
          region: params.regions?.length ? { in: params.regions } : undefined,
          averageLikeCount: {
            gte: params.minAverageLikeCount ? Number(params.minAverageLikeCount) : undefined,
            lte: params.maxAverageLikeCount ? Number(params.maxAverageLikeCount) : undefined,
          },
        }

        // get second round filter db valid users
        const secondRoundFinalUsers = await prisma.instagramUserInfo.findMany({
          where: secondRoundConditions,
        })

        // visual similarity results and final users
        finalUsers.push(...secondRoundFinalUsers)
        allVisionResults.push(...secondVisionResults)
        metrics.set('second_round_after_filter_db_users_count', secondRoundFinalUsers.length)
      }
    }

    metrics.set('all_vision_results', allVisionResults)
    // 补充缺少粉丝数的博主的粉丝数
    await Bluebird.map(
      finalUsers,
      async (user) => {
        if (
          user.followerCount == 0 &&
          dayjs().subtract(+INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS, 'day').unix() >
            user.updatedAt.getTime()
        ) {
          const newUser = await InstagramApi.getInstance().getUser(user.id, collector)
          if (newUser?.followerCount) {
            console.log(`更新 ins 用户 ${user.username} 粉丝数到 ${newUser.followerCount}`)
            user.followerCount = newUser.followerCount
            await prisma.instagramUserInfo.update({
              where: {
                id: user.id,
              },
              data: {
                followerCount: newUser.followerCount,
              },
            })
          }
        }
      },
      { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
    )
    metrics?.set('rapid_api_stats', collector.getStats())

    const kols = await prisma.kolInfo.findMany({
      where: {
        platform: KolPlatform.INSTAGRAM,
        platformAccount: {
          in: finalUsers.map((u) => u.username),
        },
      },
    })

    if (!kols?.length) {
      return []
    }

    metrics.set('final_kols_count', kols.length)
    const result = kols
      .map((kol) => ({
        kolId: kol.id,
        platform: kol.platform,
        platformId: kol.platformAccount || '',
        score: (allVisionResults.find((i) => i.username === kol.platformAccount)?.score || 0) / 100,
      }))
      .sort((a, b) => b.score - a.score)

    return result
  }

  public async processNormalSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>

    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const collector = new RapidApiStatsCollector()

    // ratedUsernames
    const [projectKolRateUseNames, candidateUsernames, excludeChannelIds] = await Promise.all([
      ProjectKolService.getProjectKolUniques(task.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      }),
      this.getPreviousTasksCandidatesUsernames(task.projectId),
      getExcludeIds(task.createdBy!, KolPlatform.INSTAGRAM),
    ])

    let ratedUsernames = [...projectKolRateUseNames, ...candidateUsernames, ...excludeChannelIds]
    metrics.set('project_all_rated_user_count', ratedUsernames.length)

    const sourcetUser = await InstagramApi.getInstance().getUserWithPosts(params.source, collector)
    if (!sourcetUser) {
      throw new Error('source user not exist!' + params.source)
    }

    const rawRelated = await metrics.withPhase('fetch_related_users', () =>
      InstagramApi.getInstance().getUserRelatedUsers(sourcetUser.username, collector),
    )
    if (!rawRelated?.length) {
      throw new Error(ERROR_MESSAGES.NO_RELATED_KOL)
    }
    metrics.set('source_user_related_users_count', rawRelated.length)
    const afterProjectFilterRelatedUsers = rawRelated.filter(
      (i) => !ratedUsernames.includes(i.username),
    )
    let afterProjectFilterRelatedUserNames = afterProjectFilterRelatedUsers.map(
      (user) => user.username,
    )

    metrics.set(
      'after_project_filter_related_users_count',
      afterProjectFilterRelatedUserNames.length,
    )
    ratedUsernames = [...new Set([...ratedUsernames, ...afterProjectFilterRelatedUserNames])]

    if (FILTER_OFFICIAL_ACCOUNT === 'true') {
      console.log('ins normal similar 过滤官方账号')
      // 官方账号
      const officialAccount = await metrics.withPhase('filter_official_account', () =>
        analyzeVisualSimilarityService.filterOfficialAccount(afterProjectFilterRelatedUserNames),
      )
      metrics.set('official_account_count', officialAccount.length)
      metrics.set('official_accounts', officialAccount)
      // 过滤官方账号
      if (officialAccount.length > 0) {
        afterProjectFilterRelatedUserNames = afterProjectFilterRelatedUserNames.filter(
          (username) => !officialAccount.includes(username),
        )
        console.log(
          `ins normal similar 过滤官方账号后，剩余用户: ${afterProjectFilterRelatedUserNames.length}`,
        )
      }
    }
    metrics.set('after_filter_official_account_count', afterProjectFilterRelatedUserNames.length)
    let relatedUsers: (IgUser | null)[] = await metrics.withPhase(
      'fetch_related_users_details',
      () =>
        Bluebird.map(
          afterProjectFilterRelatedUserNames,
          async (username) => {
            try {
              return await InstagramApi.getInstance().getUserWithPosts(username, collector)
            } catch (error) {
              Sentry.captureException(error)
              return null
            }
          },
          { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
        ),
    )

    // filter null and username
    relatedUsers = relatedUsers.filter(
      (user) => user != null && user.username && user.username.trim() !== '',
    )

    if (!relatedUsers?.length) {
      throw new Error('normal similar  get 0 users')
    }

    await metrics.withPhase('upsert_kol_info', () =>
      Promise.all([
        this.upsertKolInfo(relatedUsers as IgUser[], task.createdBy!),
        this.upsertUsers(relatedUsers as IgUser[]),
      ]),
    )

    const similarUsernames = relatedUsers.map((user) => user!.username)

    const conditions = {
      username: {
        in: similarUsernames,
      },
      followerCount: {
        gte: params.minSubscribers ? Number(params.minSubscribers) : undefined,
        lte: params.maxSubscribers ? Number(params.maxSubscribers) : undefined,
      },
      region: params.regions?.length ? { in: params.regions } : undefined,
      averageLikeCount: {
        gte: params.minAverageLikeCount ? Number(params.minAverageLikeCount) : undefined,
        lte: params.maxAverageLikeCount ? Number(params.maxAverageLikeCount) : undefined,
      },
    }

    const users = await prisma.instagramUserInfo.findMany({
      where: conditions,
    })

    if (!users?.length) {
      return []
    }
    metrics.set('after_db_filter_count', users.length)

    await Bluebird.map(
      users,
      async (user) => {
        if (
          user.followerCount == 0 &&
          dayjs().subtract(+INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS, 'day').unix() >
            user.updatedAt.getTime()
        ) {
          const newUser = await InstagramApi.getInstance().getUser(user.id, collector)
          if (newUser?.followerCount) {
            console.log(`更新 ins 用户 ${user.username} 粉丝数到 ${newUser.followerCount}`)
            user.followerCount = newUser.followerCount
            await prisma.instagramUserInfo.update({
              where: {
                id: user.id,
              },
              data: {
                followerCount: newUser.followerCount,
              },
            })
          }
        }
      },
      { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
    )
    metrics?.set('rapid_api_stats', collector.getStats())

    const kols = await prisma.kolInfo.findMany({
      where: {
        platform: KolPlatform.INSTAGRAM,
        platformAccount: {
          in: users.map((u) => u.username),
        },
      },
    })

    if (!kols?.length) {
      return []
    }
    metrics.set('final_kols_count', kols.length)

    const result = kols
      .map((kol) => ({
        kolId: kol.id,
        platform: kol.platform,
        platformId: kol.platformAccount || '',
        score: 1,
      }))
      .sort((a, b) => b.score - a.score)

    return result
  }

  public async processEmbeddingSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
      embeddingScore?: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const { source } = params
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })

    // ratedUsernames
    const [projectKolRateUseNames, candidateUsernames, excludeChannelIds] = await Promise.all([
      ProjectKolService.getProjectKolUniques(task.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      }),
      this.getPreviousTasksCandidatesUsernames(task.projectId),
      getExcludeIds(task.createdBy!, KolPlatform.INSTAGRAM),
    ])

    const ratedUsernames = [...projectKolRateUseNames, ...candidateUsernames, ...excludeChannelIds]
    metrics.set('project_all_rated_user_count', ratedUsernames.length)

    const sourcetUser = await InstagramApi.getInstance().getUserWithPosts(source)
    if (!sourcetUser) {
      throw new Error('source user not exist!' + params.source)
    }

    const rawRelated = await metrics.withPhase('fetch_related_users', () =>
      InstagramApi.getInstance().getUserRelatedUsers(sourcetUser.username),
    )
    if (!rawRelated?.length) {
      throw new Error(ERROR_MESSAGES.NO_RELATED_KOL)
    }
    metrics.set('source_user_related_users_count', rawRelated.length)
    const afterProjectFilterRelatedUsers = rawRelated.filter(
      (i) => !ratedUsernames.includes(i.username),
    )

    const afterProjectFilterRelatedUserNames = afterProjectFilterRelatedUsers.map(
      (user) => user.username,
    )

    metrics.set(
      'after_project_filter_related_users_count',
      afterProjectFilterRelatedUserNames.length,
    )

    const relatedUsersWithPosts = await metrics.withPhase('fetch_related_users_posts', async () => {
      const usersWithPosts = await Bluebird.map(
        afterProjectFilterRelatedUsers,
        async (user) => {
          try {
            const userWithPosts = await InstagramRapidApiV3.getInstance().getUserWithPosts({
              username: user.username,
            })
            return userWithPosts
          } catch (error) {
            console.error(`获取用户 ${user.username} 的 posts 失败:`, error)
            Sentry.captureException(error)
            return null
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )
      return usersWithPosts.filter(
        (user): user is IgUser =>
          user !== null && Array.isArray(user.posts) && user.posts.length > 0,
      )
    })

    if (!relatedUsersWithPosts.length) {
      throw new Error('无法获取任何 related users 的 posts 数据')
    }

    const allUsersWithPosts = [...relatedUsersWithPosts, sourcetUser]
    metrics.set('all_users_count', allUsersWithPosts.length)

    const visualAnalysisInput = allUsersWithPosts
      .filter((user) => user.posts && user.posts.length > 0)
      .map((user) => ({
        username: user.username,
        posts: user?.posts?.slice(0, 6),
        biography: user.biography,
        id: user.id || '',
        fullname: user.fullName || '',
        url: user.username ? `https://www.instagram.com/${user.username}/` : '',
      }))

    // 分析四维度
    const allUsersWithDimensions = await metrics.withPhase(
      'analyze_blogger_dimensions',
      async () => {
        const batchSize = +INS_DIMENSION_BATCH_SIZE
        const batches = Array.from(
          { length: Math.ceil(visualAnalysisInput.length / batchSize) },
          (_, i) => visualAnalysisInput.slice(i * batchSize, (i + 1) * batchSize),
        )
        const analysisResults = await Bluebird.map(
          batches,
          async (batch, batchIndex) => {
            try {
              const results = await analyzeVisualSimilarityService.analyzeBloggerDimensionsBatch(
                batch.map((user) => ({
                  username: user.username,
                  posts: user.posts,
                  biography: user.biography,
                  id: user.id,
                  fullname: user.fullname,
                  url: user.url,
                })),
              )

              return results
            } catch (error) {
              console.error(`处理批次 ${batchIndex + 1} 的视觉分析失败:`, error)
              Sentry.captureException(error)
              return []
            }
          },
          { concurrency: +INS_DIMENSION_BATCH_CONCURRENCY },
        )
        return analysisResults
          .flat()
          .filter(
            (result) => result.isAnalyzedSuccessfully && result.bloggerID && result.corePositioning,
          )
      },
    )
    metrics.set('all_users_with_dimensions_count', allUsersWithDimensions.length)
    metrics.set('all_users_with_dimensions', allUsersWithDimensions)

    const usersWithEmbeddings = await metrics.withPhase('generate_embeddings', async () => {
      const embeddingMap = new Map<string, number[]>()
      await Bluebird.map(
        allUsersWithDimensions,
        async (dimensionResult) => {
          try {
            if (!dimensionResult || !dimensionResult.bloggerID) {
              return
            }
            // 拼接 corePositioning 和 videoAnalysis
            const textParts = [dimensionResult.corePositioning]
            if (dimensionResult.videoAnalysis && dimensionResult.videoAnalysis.length > 0) {
              textParts.push(...dimensionResult.videoAnalysis)
            }
            const combinedText = textParts.join(' ')

            if (!combinedText || combinedText.trim().length === 0) {
              console.warn(`用户 ${dimensionResult.bloggerID} 没有有效的文本数据`)
              return
            }
            const embedding = await getEmbeddingForInstagram(combinedText)
            if (embedding && embedding.length > 0) {
              embeddingMap.set(dimensionResult.bloggerID, embedding)
            }
          } catch (error) {
            console.error(
              `为用户 ${dimensionResult?.bloggerID || '未知'} 生成 embedding 失败:`,
              error,
            )
          }
        },
        { concurrency: 25 },
      )
      return embeddingMap
    })
    metrics.set('users_with_embeddings_count', usersWithEmbeddings.size)

    const usersWithData = allUsersWithPosts
      .map((user) => {
        const dimensionResult = allUsersWithDimensions.find(
          (result) => result && result.bloggerID === user.username,
        )
        const embedding = usersWithEmbeddings.get(user.username)
        if (!embedding || embedding.length === 0) {
          console.warn(`用户 ${user.username} 没有有效的 embedding，跳过`)
          return null
        }
        return {
          user,
          embedding,
          dimensionResult,
        }
      })
      .filter((item) => item !== null) as Array<{
      user: IgUser
      embedding: number[]
      dimensionResult?: BloggerDimensionsAnalysisResultWithFlag
    }>

    await Promise.all([
      this.upsertUsersWithEmbedding(usersWithData),
      this.upsertKolInfo(relatedUsersWithPosts, task.createdBy!),
    ])

    console.log(`[searchInstagramUsersWithEmbedding] 等待 Milvus 数据同步...`)
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 按优先级获取源用户向量：1. 当前批次生成的 -> 2. Milvus库存 -> 3. 现场生成
    let sourceUserEmbedding: number[] | null = null

    const sourceUserInBatch = usersWithEmbeddings.get(source)
    if (sourceUserInBatch && sourceUserInBatch.length > 0) {
      sourceUserEmbedding = sourceUserInBatch
      console.log(`使用当前批次中源用户 ${source} 的向量，维度: ${sourceUserEmbedding.length}`)
    } else {
      sourceUserEmbedding = await JinaV4MilvusService.getDenseVectorsByUniqueId(
        source,
        KolPlatform.INSTAGRAM,
      )
      if (sourceUserEmbedding && sourceUserEmbedding.length > 0) {
        console.log(
          `使用 Milvus 库存中源用户 ${source} 的向量，维度: ${sourceUserEmbedding.length}`,
        )
      } else {
        console.warn(
          `源用户 ${source} 的向量在当前批次和 Milvus 中均未找到，尝试使用四维度分析结果生成`,
        )
        const sourceUserDimensionResult = allUsersWithDimensions.find(
          (result) => result && result.bloggerID === source,
        )
        if (sourceUserDimensionResult && sourceUserDimensionResult.corePositioning) {
          console.log(`使用源用户 ${source} 的 corePositioning 现场生成向量`)
          sourceUserEmbedding = await getEmbeddingForInstagram(
            sourceUserDimensionResult.corePositioning,
          )
          if (sourceUserEmbedding && sourceUserEmbedding.length > 0) {
            console.log(`成功为源用户 ${source} 现场生成向量，维度: ${sourceUserEmbedding.length}`)
            try {
              const sourceUserData = {
                user: sourcetUser,
                embedding: sourceUserEmbedding,
                dimensionResult: sourceUserDimensionResult,
              }
              await this.upsertUsersWithEmbedding([sourceUserData])
              console.log(`成功将源用户 ${source} 的现场生成向量插入到 Milvus`)
            } catch (milvusError) {
              console.warn(
                `插入源用户 ${source} 向量到 Milvus 失败，但不影响当前搜索:`,
                milvusError,
              )
            }
          } else {
            console.error(`为源用户 ${source} 现场生成向量失败`)
          }
        } else {
          console.error(`源用户 ${source} 的维度分析结果未找到或无效，无法生成向量`)
        }
      }
    }

    // 如果仍然没有有效向量，抛出错误
    if (!sourceUserEmbedding || sourceUserEmbedding.length === 0) {
      throw new Error(`获取源用户 ${source} 的向量失败: 当前批次、Milvus库存、现场生成均失败`)
    }

    const filter = {
      taskType: TaskType.SIMILAR,
      platform: KolPlatform.INSTAGRAM,
      regions: params.regions,
      minFollowers: params.minSubscribers,
      maxFollowers: params.maxSubscribers,
      minAverageLikeCount: params.minAverageLikeCount,
      maxAverageLikeCount: params.maxAverageLikeCount,
      // lastPublishedAfter: Number(INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS)
      //   ? Math.floor(Date.now() / 1000) -
      //     Number(INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS) * 24 * 60 * 60
      //   : undefined,
      ratedIds: ratedUsernames,
    }
    const searchResult = await metrics.withPhase(
      'milvus_search',
      async () =>
        await JinaV4MilvusService.advancedFilterSearchForInstagramJinaV4(
          [sourceUserEmbedding],
          filter,
        ),
    )

    if (!searchResult?.length) {
      console.warn('没有有效的搜索结果')
      return []
    }
    // milvus search result
    metrics.set('search_result_count', searchResult.length)
    metrics.set(
      'search_result',
      searchResult.map((item) => ({
        account: item.account,
        score: item.score,
      })),
    )
    // const documentsMap = new Map<string, string>()
    // for (const result of searchResult) {
    //   if (result.meta?.corePositioning) {
    //     documentsMap.set(result.account, result.meta.corePositioning)
    //   }
    // }

    // // rerank query text
    // const queryText =
    //   allUsersWithDimensions.find((result) => result.bloggerID === source)?.corePositioning || ''
    // metrics.set('rerank_query_text', queryText)
    // metrics.set('documents_map_count', documentsMap.size)

    // let rerankedResults
    // try {
    //   rerankedResults = await metrics.withPhase(
    //     'rerank',
    //     async () => await RerankService.rerankInstagram(documentsMap, queryText, 70),
    //   )
    //   if (!rerankedResults?.length) {
    //     throw new Error('Rerank结果为空')
    //   }
    // } catch (error: any) {
    //   rerankedResults = searchResult.map((item) => ({
    //     bloggerId: item.account,
    //     score: item.score,
    //   }))
    // }

    // if (!rerankedResults.length) {
    //   console.warn('[processEmbeddingSimilarJob] 没有有效的结果可用')
    //   return []
    // }

    // const rerankedSearchResults = rerankedResults
    //   .map((item) => {
    //     const bloggerId = item.bloggerId
    //     const originalResult = searchResult.find((r) => r.account === bloggerId)
    //     if (originalResult) {
    //       return {
    //         ...originalResult,
    //         score: item.score,
    //       }
    //     }
    //     return null
    //   })
    //   .filter((item) => item !== null)

    const users = await prisma.instagramUserInfo.findMany({
      where: {
        username: {
          in: searchResult.map((result) => result!.account),
        },
      },
    })

    const kols = await prisma.kolInfo.findMany({
      where: {
        platform: KolPlatform.INSTAGRAM,
        platformAccount: {
          in: users.map((u) => u.username),
        },
      },
    })

    if (!kols?.length) {
      return []
    }
    metrics.set('final_kols_count', kols.length)

    const result = kols
      .map((kol) => {
        const rerankedResult = searchResult.find(
          (result) => result?.account === kol.platformAccount,
        )
        return {
          kolId: kol.id,
          platform: kol.platform,
          platformId: kol.platformAccount || '',
          score: rerankedResult?.score || 0,
        }
      })
      .sort((a, b) => b.score - a.score)

    return result
  }

  public async insProcessHashTagBreakJob(
    taskParams: InsHashTagBreakTaskParams,
    taskId: string,
  ): Promise<InsHashTagBreakTaskResult> {
    console.log(`[instagramProcessHashTagBreakJob] 开始处理标签 ${taskParams.tag}`)
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的Instagram用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      })
      console.log(
        `[instagramProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的用户`,
      )

      const options = {
        ratedUniqueIds: projectKolUniques || [],
        sortType: taskParams.sortType,
      }

      const result: InsHashTagBreakResult = await this.getUniqueAuthorsFromHashtag(
        taskParams.tag,
        taskParams.paginationToken,
        taskParams.maxVideoCount ?? 100,
        taskParams.currentVideoCount ?? 0,
        options,
      )

      const { uniqueIds } = result

      const users = await Bluebird.map(
        uniqueIds,
        async (username) => {
          try {
            return await InstagramApi.getInstance().getUser(username)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      const validUsers = users.filter((user) => user !== null && user?.username) as IgUser[]

      // 处理kolInfo以及insUserInfo数据
      await Promise.all([
        this.upsertKolInfo(validUsers, task.createdBy!),
        this.upsertUsers(validUsers),
      ])
      // 更新posts
      await this.updateUserPosts(validUsers.map((user) => user.username))

      const response: InsHashTagBreakTaskResult = {
        taskId: taskId,
        tag: taskParams.tag,
        uniqueIds: result.uniqueIds,
        paginationToken: result.paginationToken,
        hasMore: result.hasMore,
        progress: result.progress,
      }

      console.log(
        `[instagramProcessHashTagBreakJob] 处理完成，共处理 ${result.uniqueIds.length} 个用户`,
      )
      return response
    } catch (error) {
      console.error(`[instagramProcessHashTagBreakJob] 处理标签 ${taskParams.tag} 时出错:`, error)
      throw error
    }
  }

  public async insProcessTaggedBreakJob(
    taskParams: InsTaggedBreakTaskParams,
    taskId: string,
  ): Promise<InsTaggedBreakTaskResult> {
    console.log(`[instagramProcessTaggedBreakJob] 开始处理标签 ${taskParams.username}`)
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的Instagram用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      })
      console.log(
        `[instagramProcessTaggedBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的用户`,
      )

      projectKolUniques.push(taskParams.username)

      const options = {
        ratedUniqueIds: projectKolUniques || [],
      }
      const result: InsTaggedBreakResult = await this.getTaggedUsers(
        taskParams.username,
        taskParams.paginationToken,
        taskParams.maxVideoCount,
        taskParams.currentVideoCount,
        options,
      )

      const { usernames } = result

      const users = await Bluebird.map(
        usernames,
        async (username) => {
          try {
            return await InstagramApi.getInstance().getUser(username)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      const validUsers = users.filter((user) => user !== null && user?.username) as IgUser[]

      // 处理kolInfo以及insUserInfo数据
      await Promise.all([
        this.upsertKolInfo(validUsers, task.createdBy!),
        this.upsertUsers(validUsers),
      ])
      // 更新posts
      await this.updateUserPosts(validUsers.map((user) => user.username))

      const response: InsTaggedBreakTaskResult = {
        taskId: taskId,
        username: taskParams.username,
        usernames: result.usernames,
        paginationToken: result.paginationToken,
        hasMore: result.hasMore,
        total: result.usernames.length,
        message: result.message,
        progress: result.progress,
      }

      console.log(
        `[instagramProcessTaggedBreakJob] 处理完成，共处理 ${result.usernames.length} 个用户`,
      )
      return response
    } catch (error) {
      console.error(
        `[instagramProcessTaggedBreakJob] 处理标签 ${taskParams.username} 时出错:`,
        error,
      )
      throw error
    }
  }

  /**
   * 从Instagram的hashtag获取用户
   * @param hashtag 标签名
   * @param initialPaginationToken 初始分页令牌
   * @param maxUniqueAuthors 最大获取的唯一作者数
   * @param options 选项，包括要排除的已评价用户ID列表
   */
  private async getUniqueAuthorsFromHashtag(
    hashtag: string,
    initialPaginationToken: string,
    maxVideoCount: number,
    currentVideoCount: number,
    options: {
      ratedUniqueIds: string[]
      sortType: InstagramSortType
    },
  ): Promise<InsHashTagBreakResult> {
    const uniqueAuthors = new Set<string>()
    let hasMore = true
    let currentPaginationToken = initialPaginationToken
    let message = ''
    let ratedUniqueIdsSet: Set<string> | null = null
    let videoCount = 0
    let total = 0

    if (options.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      ratedUniqueIdsSet = new Set<string>(options.ratedUniqueIds)
    }

    while (videoCount < maxVideoCount && hasMore) {
      try {
        const hashtagOptions = {
          pagination_token: currentPaginationToken || undefined,
          feed_type: options.sortType as 'top' | 'recent' | 'clips',
          url_embed_safe: false,
        }
        const response = await InstagramRapidApiV3.getInstance().getHashTagVideos(
          hashtag,
          hashtagOptions,
        )
        console.log('current total', response.total)
        console.log('current pagination token', response.pagination_token)
        if (response.total > total) {
          total = response.total
        }

        if (!response || response.count === 0 || !response.items) {
          hasMore = false
          message = '没有更多内容,请求V3接口失败or数据为空'
          break
        }

        const posts = response.items || []
        posts.map((post) => {
          if (post.user && post.user.username) {
            const username = post.user.username
            if (ratedUniqueIdsSet && ratedUniqueIdsSet.has(username)) {
              return
            } else {
              uniqueAuthors.add(username)
            }
          }
        })

        videoCount += posts.length
        console.log(
          `标签 ${hashtag} 当前获取到 ${posts.length} 个帖子，${uniqueAuthors.size} 个唯一作者`,
        )

        currentPaginationToken = response.pagination_token || initialPaginationToken
        hasMore = !!response.pagination_token

        if (videoCount >= maxVideoCount) {
          message = `获取uniqueIds成功，数量达到上限,${maxVideoCount},hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log(`获取uniqueIds成功，数量达到上限: ${maxVideoCount}`)
          break
        }

        if (!hasMore) {
          message = `没有更多内容,hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log('没有更多内容')
          break
        }
      } catch (error) {
        console.error(`获取标签 ${hashtag} 的内容时出错: ${error}`)
        hasMore = false
        message = `获取标签 ${hashtag} 的内容时出错: ${error}`
        break
      }
    }

    const uniqueIds = Array.from(uniqueAuthors)
    console.log(`[getUniqueAuthorsFromHashtag] 获取到 ${uniqueIds.length} 个用户ID`)

    return {
      uniqueIds,
      hasMore,
      paginationToken: currentPaginationToken,
      total: uniqueIds.length,
      message,
      progress: {
        total: total,
        current: uniqueIds.length,
        count: maxVideoCount + currentVideoCount,
      },
    }
  }

  /**
   * 从Instagram的标记内容获取用户
   * @param username 用户名
   * @param initialPaginationToken 初始分页令牌
   * @param maxUniqueAuthors 最大获取的唯一作者数
   * @param options 选项，包括要排除的已评价用户ID列表
   */
  private async getTaggedUsers(
    username: string,
    initialPaginationToken: string,
    maxVideoCount: number = 100,
    currentVideoCount: number = 0,
    options: {
      ratedUniqueIds: string[]
    },
  ): Promise<InsTaggedBreakResult> {
    const uniqueAuthors = new Set<string>()
    let hasMore = true
    let currentPaginationToken = initialPaginationToken
    let message = ''
    let ratedUniqueIdsSet: Set<string> | null = null
    let videoCount = 0
    let total = 0
    if (options.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      ratedUniqueIdsSet = new Set<string>(options.ratedUniqueIds)
    }
    let rawCount = 0
    while (videoCount < maxVideoCount && hasMore) {
      try {
        const taggedOptions = {
          pagination_token: currentPaginationToken || undefined,
        }
        const response = await InstagramRapidApiV3.getInstance().getTaggedUsers(
          username,
          taggedOptions,
        )
        if (!response || response.count === 0 || !response.items || response.items.length === 0) {
          hasMore = false
          message = '没有更多被标记内容,请求V3接口失败or数据为空'
          break
        }

        if (response.total > total) {
          total = response.total
        }
        const items = response.items as IgTaggedUser[]
        rawCount += items.length
        items.forEach((item) => {
          if (item.username && !item.is_private) {
            // 仅添加私密账号
            if (!(ratedUniqueIdsSet && ratedUniqueIdsSet.has(item.username))) {
              uniqueAuthors.add(item.username)
            }
          }
        })

        videoCount += items.length

        console.log(
          `用户 ${username} 的标记内容当前获取到 ${response.items.length} 个帖子，${uniqueAuthors.size} 个唯一作者`,
        )

        currentPaginationToken = response.pagination_token || ''
        hasMore = !!response.pagination_token

        if (videoCount >= maxVideoCount) {
          message = `获取usernames成功，数量达到上限,${maxVideoCount},hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log(`获取usernames成功，数量达到上限: ${maxVideoCount}`)
          break
        }

        if (!hasMore) {
          message = `没有更多内容,hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log('没有更多内容')
          break
        }
      } catch (error) {
        console.error(`获取用户 ${username} 的标记内容时出错: ${error}`)
        hasMore = false
        message = `获取用户 ${username} 的标记内容时出错: ${error}`
        break
      }
    }

    const usernames = Array.from(uniqueAuthors)
    console.log(`[getTaggedUsers] 获取到 ${usernames.length} 个用户ID`)

    return {
      usernames,
      hasMore,
      paginationToken: currentPaginationToken,
      total: usernames.length,
      message,
      progress: {
        total: total,
        current: usernames.length,
        count: maxVideoCount + currentVideoCount,
      },
    }
  }

  // 返回的是similarChannelTask的result
  // meta字段在handleComplete中处理
  public async insProcessFollowingListJob(
    job: Bull.Job<IEmbeddingTask>,
  ): Promise<InsFollowingListTaskResult> {
    const { id, params, metrics } = job.data
    const taskParams = params as InsFollowingListTaskParams
    const collector = new RapidApiStatsCollector()
    const { username, paginationToken, currentCount, maxCount } = taskParams
    const task = await prisma.similarChannelTask.findUnique({
      where: { id },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的Instagram用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.INSTAGRAM],
      })
      console.log(
        `[instagramProcessFollowingListJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的用户`,
      )

      const options = {
        ratedUniqueIds: projectKolUniques || [],
      }

      const result = await this.getFollowingUsers(
        username,
        paginationToken,
        maxCount,
        options,
        collector,
      )
      if (!result.uniqueIds.length) {
        throw new Error('获取关注列表失败或列表为空')
      }

      const users = await Bluebird.map(
        result.uniqueIds,
        async (username) => {
          try {
            return await InstagramApi.getInstance().getUser(username, collector)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      const validUsers = users.filter((user) => user !== null && user?.username) as IgUser[]

      await Promise.all([
        this.upsertKolInfo(validUsers, task.createdBy!),
        this.upsertUsers(validUsers),
      ])

      await this.updateUserPosts(validUsers.map((user) => user.username))

      const response: InsFollowingListTaskResult = {
        taskId: id,
        username: username,
        uniqueIds: result.uniqueIds,
        paginationToken: result.paginationToken,
        hasMore: result.hasMore,
        progress: {
          total: taskParams.total || 0,
          current: result.uniqueIds.length,
          count: (currentCount || 0) + (maxCount || 0),
        },
      }
      console.log(
        `[instagramProcessFollowingListJob] 处理完成，共处理 ${result.uniqueIds.length} 个用户`,
      )
      metrics.set('rapid_api_stats', collector.getStats())
      return response
    } catch (error) {
      console.error(`[instagramProcessFollowingListJob] 任务 ${id} 处理失败:`, error)
      throw error
    }
  }

  public async insProcessWebListJob(
    params: GetPlatformTaskParams<'instagram', 'WEB_LIST'>,
    taskId: string,
  ): Promise<InsWebListTaskResult> {
    const urls = params.urls || []
    if (urls.length === 0) {
      throw new Error('urls is empty')
    }
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    const projectKolUniques = await ProjectKolService.getProjectKolUniques(params.projectId, {
      platform: [KolPlatform.INSTAGRAM],
    })

    console.log(
      `[instagramProcessWebListJob] 获取到 ${projectKolUniques.length} 该项目下已经评价过的用户`,
    )

    const usernames = await parseUrlUtils.extractInstagramUsernames(urls)
    const filteredUsernames = usernames.filter((username) => !projectKolUniques.includes(username))
    if (filteredUsernames.length === 0) {
      throw new Error('无法从提供的URL中提取有效的Instagram用户名')
    }
    try {
      const users = await Bluebird.map(
        filteredUsernames,
        async (username) => {
          try {
            return await InstagramApi.getInstance().getUser(username)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      const validUsers = users.filter((user) => user !== null && user?.username) as IgUser[]

      await Promise.all([
        this.upsertKolInfo(validUsers, task.createdBy!),
        this.upsertUsers(validUsers),
      ])

      await this.updateUserPosts(validUsers.map((user) => user.username))

      const response: InsWebListTaskResult = {
        taskId: taskId,
        usernames: filteredUsernames,
        message: 'success',
      }

      console.log(
        `[instagramProcessWebListJob] 处理完成，共处理 ${filteredUsernames.length} 个用户`,
      )
      return response
    } catch (error) {
      console.error(`[instagramProcessFollowingListJob] 任务 ${taskId} 处理失败:`, error)
      throw error
    }
  }

  private async getFollowingUsers(
    username: string,
    paginationToken: string,
    maxCount: number = 100,
    options: {
      ratedUniqueIds: string[]
    },
    collector?: RapidApiStatsCollector,
  ): Promise<{
    uniqueIds: string[]
    hasMore: boolean
    paginationToken: string
    total: number
    message: string
    count: number
  }> {
    const uniqueAuthors = new Set<string>()
    let count = 0
    let hasMore = true
    let currentPaginationToken = paginationToken
    let message = ''
    let ratedUniqueIdsSet: Set<string> | null = null

    if (options.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      ratedUniqueIdsSet = new Set<string>(options.ratedUniqueIds)
    }

    while (count < maxCount && hasMore) {
      try {
        const followingResponse = await InstagramRapidApiV3.getInstance().getFollowings(
          { username },
          { pagination_token: currentPaginationToken },
          collector,
        )

        if (
          !followingResponse ||
          !followingResponse.followings ||
          followingResponse.followings.length === 0
        ) {
          hasMore = false
          message = '没有更多内容,请求接口失败或数据为空'
          break
        }

        const followings = followingResponse.followings
        count += followings.length
        followings.forEach((following) => {
          if (following.username) {
            // 跳过已评价的用户
            if (ratedUniqueIdsSet && ratedUniqueIdsSet.has(following.username)) {
              return
            }
            // 只添加非私密账号
            if (!following.is_private) {
              uniqueAuthors.add(following.username)
            }
          }
        })

        console.log(
          `用户 ${username} 当前获取到 ${followings.length} 个关注，${uniqueAuthors.size} 个唯一非私密作者`,
        )

        currentPaginationToken = followingResponse.pagination_token || ''
        hasMore = !!followingResponse.pagination_token

        if (count >= maxCount) {
          message = `获取uniqueIds成功，数量达到上限,${maxCount},hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log(`获取uniqueIds成功，数量达到上限: ${maxCount}`)
          break
        }

        if (!hasMore) {
          message = `没有更多内容,hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log('没有更多内容')
          break
        }
      } catch (error) {
        console.error(`获取用户 ${username} 的关注列表时出错: ${error}`)
        hasMore = false
        message = `获取用户 ${username} 的关注列表时出错: ${error}`
        break
      }
    }

    const uniqueIds = Array.from(uniqueAuthors)
    console.log(`[getFollowingUsers] 获取到 ${uniqueIds.length} 个用户ID`)

    return {
      uniqueIds,
      hasMore,
      paginationToken: currentPaginationToken,
      total: uniqueIds.length,
      message,
      count,
    }
  }

  private async encoding(user: IgUser): Promise<string> {
    return this.encodingBio(user)
  }

  private encodingBio(user: IgUser): string {
    return this.removeEmoji(user.biography)
  }

  private encodingTags(user: IgUser): string {
    let result: string = ''
    if (user.posts) {
      user.posts.forEach((post) => {
        result += ' ' + post.tags?.join(' ')
      })
    }
    return result.replace(/[#]/g, '')
  }

  private removeEmoji(input: string) {
    return input.replace(/[\p{Emoji_Presentation}\p{Emoji}\u200d]+/gu, '')
  }

  /**
   * 更新Instagram用户的帖子信息
   * @param usernames 要更新的用户名列表
   * @param collector API调用统计收集器
   * @returns 更新成功的用户数量
   */
  public async updateUserPosts(
    usernames: string[],
    collector?: RapidApiStatsCollector,
  ): Promise<number> {
    if (!usernames || usernames.length === 0) {
      console.log('[updateUserPosts] 没有提供用户名，跳过更新')
      return 0
    }

    console.log(`[updateUserPosts] 开始更新 ${usernames.length} 个用户的帖子信息`)

    let successCount = 0

    try {
      await Bluebird.map(
        usernames,
        async (username) => {
          try {
            const userPosts = await InstagramRapidApiV3.getInstance().getUserPosts(
              { username },
              { url_embed_safe: true },
              collector,
            )

            if (!userPosts || !userPosts.posts || userPosts.posts.length === 0) {
              console.log(`[updateUserPosts] 用户 ${username} 没有帖子信息`)
              return
            }

            await prisma.instagramUserInfo.updateMany({
              where: { username },
              data: {
                posts: userPosts.posts as any,
                updatedAt: new Date(),
              },
            })

            console.log(
              `[updateUserPosts] 成功更新用户 ${username} 的帖子信息，共 ${userPosts.posts.length} 条`,
            )
            successCount++
          } catch (error) {
            console.error(`[updateUserPosts] 更新用户 ${username} 的帖子信息失败:`, error)
            Sentry.captureException(error)
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      console.log(
        `[updateUserPosts] 完成更新，成功更新 ${successCount}/${usernames.length} 个用户的帖子信息`,
      )
      return successCount
    } catch (error) {
      console.error(`[updateUserPosts] 批量更新用户帖子信息失败:`, error)
      Sentry.captureException(error)
      return successCount
    }
  }

  /**
   * 分析源KOL的垂类信息，并更新搜索参数
   */
  private async analyzeSourceKolVertical(currentUser: IgUser, params: createTaskRequest) {
    console.log(`开始分析源博主 ${currentUser.username} 的垂类信息...`)
    const sourceInput = {
      id: currentUser.id,
      username: currentUser.username,
      fullname: currentUser.fullName || '',
      biography: currentUser.biography || '',
      url: currentUser.profilePicUrl || '',
      posts: currentUser.posts || [],
    }

    const project = await prisma.project.findFirstOrThrow({
      where: { id: params.projectId, deletedAt: null },
      select: {
        config: true,
      },
    })

    const projectCfg = (project.config ?? {}) as Partial<ProjectConfig>

    try {
      const verticalAnalysisResult = await retryUtil.retryWithValidator(
        () => {
          if (PROJECT_THREE_ELEMENTS_TYPE === 'ai') {
            console.log(
              `[instagramProcessSimilarJob] 开始视觉分析源作者 ${currentUser.username} 的垂类信息...`,
            )
            return analyzeVisualSimilarityService.analysisUserVeticalSimilarity(sourceInput)
          } else {
            console.log(
              `[instagramProcessSimilarJob] 直接从项目配置或任务参数获取 ${currentUser.username} 的垂类信息...`,
            )
            return Promise.resolve({
              success: true,
              data: {
                kolDescription: projectCfg.kolDescription ?? params.kolDescription ?? '',
                allowList: projectCfg.allowList ?? params.allowList ?? [],
                banList: projectCfg.banList ?? params.banList ?? [],
              },
            })
          }
        },
        (result: any) => {
          return result.success && result.data && result.data.kolDescription
        },
        3, // 重试次数
        1000, // 初始延迟
        2, // 退避系数
        (result: any, error: any, retryCount: number, nextDelay: number) => {
          console.warn(`Instagram垂类分析第 ${retryCount} 次重试`, {
            result,
            error,
            nextDelay,
          })
        },
      )

      if (verticalAnalysisResult.success && verticalAnalysisResult.data) {
        params.kolDescription =
          verticalAnalysisResult.data.kolDescription ?? params.kolDescription ?? ''
        params.allowList = verticalAnalysisResult.data.allowList ?? params.allowList ?? []
        params.banList = verticalAnalysisResult.data.banList ?? params.banList ?? []
        console.log(`源博主垂类分析成功，更新参数: ${JSON.stringify(params)}`)
      } else {
        console.warn(
          '源博主垂类分析失败，将使用原始参数:',
          verticalAnalysisResult.error || 'Unknown error',
        )
      }
    } catch (error) {
      console.error('源博主垂类分析最终失败，将使用原始参数:', error)
    }

    return sourceInput
  }

  // 补齐用户帖子信息
  private async fetchUserPosts(users: IgUser[]): Promise<IgUser[]> {
    const usersWithPostsResults = await Bluebird.map(
      users,
      async (user) => {
        try {
          console.log(`[fetchUserPosts] 开始获取用户 ${user.username} 的帖子信息...`)
          const posts = await InstagramRapidApiV3.getInstance().getPosts({
            username: user.username,
          })
          if (!posts) {
            console.warn(`用户 ${user.username} 未找到或信息不完整，跳过`)
            return null
          }
          user.posts = posts
          return user
        } catch (error) {
          console.error(`获取用户 ${user.username} 的详细信息失败:`, error)
          return null
        }
      },
      { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
    )

    const validUsers = usersWithPostsResults.filter((result): result is IgUser => result !== null)

    const totalUsers = users.length
    const successCount = validUsers.length
    console.log(
      `请求完成: ${totalUsers} 个用户中，成功获取 ${successCount} 个，失败 ${totalUsers - successCount} 个`,
    )
    return validUsers
  }

  // 补齐用户帖子信息
  private async fetchUsersDetails(users: IgUser[]): Promise<IgUser[]> {
    const usersWithPostsResults = await Bluebird.map(
      users,
      async (user) => {
        try {
          const userDetails = await InstagramRapidApiV3.getInstance().getUser({
            username: user.username,
          })
          if (!userDetails) {
            console.warn(`用户 ${user.username} 未找到或信息不完整，跳过`)
            return null
          }
          // 补齐用户信息
          user.followerCount = userDetails.followerCount
          user.biography = userDetails.biography
          user.biographyEmail = userDetails.biographyEmail
          user.region = userDetails.region
          user.publicEmail = userDetails.publicEmail
          user.email = userDetails.email
          user.emailSource = userDetails.emailSource
          user.category = userDetails.category
          user.accountType = userDetails.accountType
          user.isBusiness = userDetails.isBusiness
          return user
        } catch (error) {
          console.error(`获取用户 ${user.username} 的详细信息失败:`, error)
          return null
        }
      },
      { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
    )

    const validUsers = usersWithPostsResults.filter((result): result is IgUser => result !== null)

    const totalUsers = users.length
    const successCount = validUsers.length
    console.log(
      `请求完成: ${totalUsers} 个用户中，成功获取 ${successCount} 个，失败 ${totalUsers - successCount} 个`,
    )
    return validUsers
  }
  /**
   * 对一批用户进行视觉相似度分析
   */
  private async analyzeUsersBatch(
    users: IgUser[],
    params: createTaskRequest,
  ): Promise<VisualSimilarityOutput[]> {
    const batchSize = +INS_VISIUAL_SIMILARITY_BATCH_SIZE
    const batches = Array.from({ length: Math.ceil(users.length / batchSize) }, (_, i) =>
      users.slice(i * batchSize, (i + 1) * batchSize),
    )

    console.log(`将用户分成 ${batches.length} 批进行AI视觉相似度分析，每批最多 ${batchSize} 个用户`)

    const analysisResults = await Bluebird.map(
      batches,
      async (batch, batchIndex) => {
        try {
          console.log(`开始处理第 ${batchIndex + 1}/${batches.length} 批用户...`)
          const targetInputs = batch.map((item) => ({
            username: item.username,
            posts: item.posts || [],
          }))
          const options = {
            banList: params.banList,
            allowList: params.allowList,
            kolDescription: params.kolDescription,
            countries: params.regions,
          }
          console.log(`调用AI分析第 ${batchIndex + 1} 批的 ${targetInputs.length} 个用户...`)
          const batchResult = await analyzeVisualSimilarityService.analyzeVisualSimilarity(
            targetInputs,
            options,
          )
          console.log(`第 ${batchIndex + 1} 批AI分析完成，获得 ${batchResult.length} 个结果`)
          return batchResult
        } catch (error) {
          console.error(`处理第 ${batchIndex + 1} 批时出错:`, error)
          Sentry.captureException(error)
          return []
        }
      },
      { concurrency: +INS_VISIUAL_SIMILARITY_CONCURRENCY },
    )

    const allResults = analysisResults.flat()
    console.log(`所有批次处理完成，共获得 ${allResults.length} 个AI分析结果`)

    return allResults
  }

  /**
   * perform multi round analysis,get new related users from high score users
   */
  private async performMultiRoundAnalysis(
    relatedSourceUserNames: string[],
    maxRounds: number,
    params: createTaskRequest,
    metrics: TaskMetricsCollector, // metrics collector
    ratedUsernames?: string[], // rated usernames
    collector?: RapidApiStatsCollector,
  ): Promise<{
    secondVisionResults: VisualSimilarityOutput[]
    secondRoundValidUsersWithPosts: IgUser[]
  }> {
    const roundRelatedUsers: IgUser[] = [] // add unique users
    const successfulUsers: string[] = [] // success get related users,source users
    const maxUsersForCurrentRound = (maxRounds - 1) * Number(INS_ROUND_USER_COUNT) // max get related users
    const existingRelatedUserNames = new Set(ratedUsernames || [])

    // for get related unique users
    for (const username of relatedSourceUserNames) {
      if (roundRelatedUsers.length >= maxUsersForCurrentRound) {
        console.log(
          `达到当前轮次最大用户数: ${roundRelatedUsers.length}/${maxUsersForCurrentRound}`,
        )
        break
      }

      try {
        const relatedUsers = await InstagramApi.getInstance().getUserRelatedUsers(
          username,
          collector,
        )

        if (relatedUsers && relatedUsers.length > 0) {
          // filter exist users
          const filteredUsers = relatedUsers.filter(
            (user) => user.username && !existingRelatedUserNames.has(user.username),
          )

          if (filteredUsers.length > 0) {
            roundRelatedUsers.push(...filteredUsers)
            filteredUsers.forEach((user) => {
              if (user.username) existingRelatedUserNames.add(user.username)
            })
            successfulUsers.push(username)
          }
        }
      } catch (error) {
        console.error(`从用户 ${username} 获取相关用户失败:`, error)
      }
    }

    if (!roundRelatedUsers?.length) {
      console.log('后续轮次分析未能获取到任何相关用户，将只返回第一轮结果')
      return {
        secondVisionResults: [],
        secondRoundValidUsersWithPosts: [],
      }
    }
    metrics.set('second_round_related_source_users', successfulUsers)
    metrics.set('second_round_related_users_count', roundRelatedUsers.length)

    const secondRoundUsersWithPosts = await metrics.withPhase(
      'second_round_fetch_user_posts',
      async () => await this.fetchUserPosts(roundRelatedUsers.slice(0, maxUsersForCurrentRound)),
    )

    if (!secondRoundUsersWithPosts?.length) {
      return {
        secondVisionResults: [],
        secondRoundValidUsersWithPosts: [],
      }
    }

    const secondVisionResult = await metrics.withPhase(
      'second_round_analyze_users_batch',
      async () => await this.analyzeUsersBatch(secondRoundUsersWithPosts, params),
    )
    const secondVisionValidResults = secondVisionResult.filter((result) => result.score > 0)
    metrics.set(
      'second_round_after_visual_similarity_filter_count',
      secondVisionValidResults.length,
    )

    // valid users with posts
    const secondVisionResultUsernames = new Set(secondVisionValidResults.map((r) => r.username))
    const secondRoundValidUsersWithPosts = secondRoundUsersWithPosts.filter((item) =>
      secondVisionResultUsernames.has(item.username),
    )

    return {
      secondVisionResults: secondVisionResult,
      secondRoundValidUsersWithPosts: secondRoundValidUsersWithPosts,
    }
  }

  /**
   * 检查 Instagram RapidAPI 配额
   */
  public async getInsQuota() {
    const quota = await InstagramRapidApiV3.getInstance().getQuota()
    if (quota.total) {
      SlackClient.getInstance().checkApiQuota(quota, 'Instagram RapidAPI')
      // upsert daily quota count
      apiQuota.upsertDailyQuotaCount(quota.used, ApiCode.INS_API_V3).catch((error) => {
        Sentry.captureException(error)
      })
    }
  }

  /**
   * 处理Instagram长时间爬取任务
   * @param job 任务数据
   * @returns 处理结果
   */
  public async processLongCrawlerJob(job: any) {
    const { id, params } = job.data

    try {
      console.info(`[processLongCrawlerJob] 开始处理Instagram爬取任务 ${id}`)

      // 获取任务信息和进度
      const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })

      const { projectId, filters, seedUsers } = params
      let maxQuotaCost = params.maxQuotaCost

      // 从meta中获取进度信息
      const progress = task.meta as unknown as InsLongCrawlerProgress

      if (!progress.pendingUsers) progress.pendingUsers = [...seedUsers]
      if (!progress.processedUsers) progress.processedUsers = []
      if (!progress.matchedUsers) progress.matchedUsers = []
      if (!progress.totalProcessed) progress.totalProcessed = 0
      if (!progress.totalMatched) progress.totalMatched = 0
      if (!progress.lastProcessedAt) progress.lastProcessedAt = new Date().toISOString()
      if (!progress.currentBatch) progress.currentBatch = 0
      if (!progress.batchLogs) progress.batchLogs = []
      // init all processed users
      if (!progress.allProcessedUsers) progress.allProcessedUsers = []
      // init hasConsumedQuotaCount
      if (!progress.hasConsumedQuotaCount) progress.hasConsumedQuotaCount = 0

      // project related
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(projectId, {
        platform: [KolPlatform.INSTAGRAM],
      })

      console.info(
        `[processLongCrawlerJob] 项目 ${projectId} 已评价用户数: ${projectKolUniques.length}`,
      )

      // init processed users
      let processedSet = new Set([
        ...progress.allProcessedUsers,
        ...progress.processedUsers,
        ...projectKolUniques,
      ])

      // pending users x users per batch
      const batchSize = +LONG_CRAWLER_INS_BATCH_SIZE
      let singleIterationQuotaCost = batchSize * 10

      // loop until pendingUsers is empty
      while (progress.pendingUsers.length > 0) {
        const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
        // loop check quota,user can through api to update quota
        const updatedParams = task.params as unknown as InsLongCrawlerTaskParams
        const updatedMaxQuotaCost = updatedParams.maxQuotaCost
        if (updatedMaxQuotaCost !== maxQuotaCost) {
          maxQuotaCost = updatedMaxQuotaCost // update maxQuotaCost
          console.info(
            `[processLongCrawlerJob] 配额已更新: ${maxQuotaCost} -> ${updatedMaxQuotaCost}`,
          )
        }
        // get batch users
        // 还要比较剩余的任务次数
        const currentBatchSize = Math.floor(
          Math.min(
            batchSize,
            progress.pendingUsers.length,
            (maxQuotaCost - progress.hasConsumedQuotaCount) / 10,
          ),
        )

        if (currentBatchSize === 0) {
          console.info(
            `[processLongCrawlerJob] 当前批次大小为零，无法继续处理。已消耗: ${progress.hasConsumedQuotaCount}，最大配额: ${maxQuotaCost}`,
          )
          break
        }
        const currentBatch = progress.pendingUsers.splice(0, currentBatchSize)

        singleIterationQuotaCost = currentBatchSize * 10
        if (maxQuotaCost - progress.hasConsumedQuotaCount < singleIterationQuotaCost) {
          console.info(
            `[processLongCrawlerJob] 配额不足以继续处理，已消耗: ${progress.hasConsumedQuotaCount}，最大配额: ${updatedMaxQuotaCost}`,
          )
          break
        }

        // update progress
        progress.currentBatch += 1
        progress.totalProcessed += currentBatch.length
        progress.lastProcessedAt = new Date().toISOString()
        // record processed users
        currentBatch.forEach((username: string) => processedSet.add(username))
        progress.processedUsers.push(...currentBatch)
        console.info(
          `[processLongCrawlerJob] 开始处理第 ${progress.currentBatch + 1} 批，共 ${currentBatch.length} 个用户`,
        )
        // get related users
        const relatedUsers = await this.getBatchRelatedUsers(currentBatch)

        if (relatedUsers.length === 0) {
          console.info(
            `[processLongCrawlerJob] 从当前批次的${currentBatch.length}个用户中获取到0个相关用户，跳过处理`,
          )

          // empty related users batch logs
          const batchLog: InsLongCrawlerBatchLogs = {
            batchNumber: progress.currentBatch,
            processedUsers: currentBatch,
            relatedUsersCount: 0,
            uniqueRelatedUsersCount: 0,
            aiFilteredCount: 0,
            matchedCount: 0,
            timestamp: new Date().toISOString(),
            quotaCost: 0,
            numberOfRuns: 0,
            message: 'related users is empty,skip this batch handle',
            success: false,
          }

          if (!progress.batchLogs) progress.batchLogs = []
          progress.batchLogs.push(batchLog)

          console.info(
            `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取0个相关用户，无法继续处理`,
          )

          // update all processed users,in order to rm duplicates
          progress.allProcessedUsers = Array.from(processedSet)
          await prisma.similarChannelTask.update({
            where: { id: task.id },
            data: { meta: { ...progress } },
          })

          continue
        }

        // get related usernames
        const relatedUsernames = relatedUsers.map((user) => user.username)

        // filter processed usernames
        const newRelatedUsernames = relatedUsernames.filter(
          (username) => !processedSet.has(username),
        )
        const newRelatedUsers = relatedUsers.filter((user) =>
          newRelatedUsernames.includes(user.username),
        )

        // check if newRelatedUsers is empty
        if (newRelatedUsers.length === 0) {
          console.info(
            `[processLongCrawlerJob] 从当前批次的${currentBatch.length}个用户中获取到${relatedUsers.length}个相关用户，但全部都已处理过，跳过处理`,
          )

          // empty new related users batch logs
          const batchLog: InsLongCrawlerBatchLogs = {
            batchNumber: progress.currentBatch,
            processedUsers: currentBatch,
            relatedUsersCount: relatedUsers.length,
            uniqueRelatedUsersCount: 0,
            aiFilteredCount: 0,
            matchedCount: 0,
            timestamp: new Date().toISOString(),
            quotaCost: 0,
            numberOfRuns: 0,
            message: 'all related users have been processed before, skip this batch handle',
            success: false,
          }

          if (!progress.batchLogs) progress.batchLogs = []
          progress.batchLogs.push(batchLog)

          console.info(
            `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取${relatedUsers.length}个相关用户，但全部都已处理过，无法继续处理`,
          )

          // update all processed users,in order to rm duplicates
          progress.allProcessedUsers = Array.from(processedSet)
          await prisma.similarChannelTask.update({
            where: { id: task.id },
            data: { meta: { ...progress } },
          })

          continue
        }

        // add new related usernames to processed set
        processedSet = new Set([...processedSet, ...newRelatedUsernames])

        // get matched users and ai filtered usernames
        const { matchedUsers, aiFilteredUsernames } = await this.processLongCrawlerBatchUsers(
          newRelatedUsers,
          filters,
          task.createdBy!,
        )

        // handler matched users
        if (matchedUsers.length > 0) {
          // update matched users list
          const matchedUsernames = matchedUsers.map((user) => user.username)
          progress.matchedUsers.push(...matchedUsernames)
          progress.totalMatched += matchedUsers.length

          // TODO: 将matchedUsers 添加到 task的candidates 字段中
        }

        // handle pending users
        if (aiFilteredUsernames.length > 0) {
          progress.pendingUsers.push(...aiFilteredUsernames)
        }

        // batch logs
        const batchLog: InsLongCrawlerBatchLogs = {
          batchNumber: progress.currentBatch,
          processedUsers: currentBatch,
          relatedUsersCount: relatedUsers.length,
          uniqueRelatedUsersCount: newRelatedUsernames.length,
          aiFilteredCount: aiFilteredUsernames.length,
          matchedCount: matchedUsers.length,
          timestamp: new Date().toISOString(),
          quotaCost: 0, // consume quota cost, default 0
          numberOfRuns: 0, // consume quota runs, default 0
          message: '', // message
          success: true,
        }
        //  merge pendingUsers and  processedUsers and remove seedUsers
        const allAIFilteredUsers = [...progress.pendingUsers, ...progress.processedUsers].filter(
          (username) => !seedUsers.includes(username) && !progress.matchedUsers.includes(username),
        )

        const candidatesData = {
          timestamp: new Date().toISOString(),
          AIfilterUsers: allAIFilteredUsers,
          matchUsers: progress.matchedUsers,
        }

        // update all processed users,in order to rm duplicates
        progress.allProcessedUsers = Array.from(processedSet)

        // deduct quota
        let quotaDeductionSuccessful = true
        try {
          const deductDynamicQuotaParams: DeductDynamicQuotaParams = {
            userId: task.createdBy!,
            quotaType: QuotaType.LONG_CRAWLER,
            quota: singleIterationQuotaCost,
            projectId: projectId,
            taskId: task.id,
            metadata: {
              ...batchLog,
            },
          }
          // deduct quota
          await MembershipService.getInstance().deductDynamicQuota(deductDynamicQuotaParams)
          console.info(
            `[processLongCrawlerJob] 成功扣除 ${singleIterationQuotaCost} 配额，累计已消耗 ${progress.hasConsumedQuotaCount} 配额`,
          )

          batchLog.quotaCost = singleIterationQuotaCost
          batchLog.numberOfRuns = batchSize
          batchLog.message = 'success'
          batchLog.success = true
          progress.hasConsumedQuotaCount += singleIterationQuotaCost
        } catch (error: unknown) {
          console.error(`[processLongCrawlerJob] 扣除配额失败:`, error)
          // deduct quota failed
          batchLog.quotaCost = 0
          batchLog.numberOfRuns = 0
          quotaDeductionSuccessful = false // flag false
          progress.hasConsumedQuotaCount += 0
          batchLog.message = `deduct quota failed,pause task: ${error instanceof Error ? error.message : 'unknown error'}`
          batchLog.success = false
        }

        if (!progress.batchLogs) progress.batchLogs = []
        progress.batchLogs.push(batchLog)

        console.info(
          `[processLongCrawlerJob] 批次#${batchLog.batchNumber}: 处理${currentBatch.length}个用户，获取${relatedUsers.length}个相关用户(新${newRelatedUsernames.length}个)，AI过滤后${aiFilteredUsernames.length}个，匹配${matchedUsers.length}个，配额消耗${batchLog.quotaCost}`,
        )

        const updatedTask = await prisma.similarChannelTask.update({
          where: { id: task.id },
          data: {
            // if quota deduction failed, update task status to PAUSING
            ...(quotaDeductionSuccessful ? {} : { status: SimilarChannelTaskStatus.PAUSING }),
            meta: {
              ...progress,
            },
            candidate: candidatesData,
          },
        })

        console.info(
          `[processLongCrawlerJob] 第 ${progress.currentBatch} 批处理完成，已更新数据库进度信息`,
        )
        // check if task is not processing
        if (updatedTask.status !== SimilarChannelTaskStatus.PROCESSING) {
          console.info(`[processLongCrawlerJob] 任务 ${id} 的状态为${updatedTask.status}, 退出循环`)
          break
        }
      }

      // end update
      progress.allProcessedUsers = Array.from(processedSet)

      const endUpdateTask = await prisma.similarChannelTask.update({
        where: { id: task.id },
        data: { meta: { ...progress } },
      })

      const finalParams = endUpdateTask.params as unknown as InsLongCrawlerTaskParams
      const finalMaxQuotaCost = finalParams.maxQuotaCost

      // update message
      let endReason = ''
      if (finalMaxQuotaCost - progress.hasConsumedQuotaCount <= singleIterationQuotaCost) {
        endReason = `配额${finalMaxQuotaCost}不足或耗尽，任务完成`
      } else if (progress.pendingUsers.length === 0) {
        endReason = '任务完成，pendingUsers 为空，无法获取更多用户'
      } else if (endUpdateTask.status === SimilarChannelTaskStatus.PAUSING) {
        endReason = `用户主动暂停任务,任务状态${endUpdateTask.status},已处理 ${progress.totalProcessed} 个用户，匹配 ${progress.totalMatched} 个，待处理队列还有 ${progress.pendingUsers.length} 个`
      } else if (endUpdateTask.status === SimilarChannelTaskStatus.COMPLETING) {
        endReason = `任务主动结束任务，任务状态${endUpdateTask.status},已处理 ${progress.totalProcessed} 个用户，匹配 ${progress.totalMatched} 个，待处理队列还有 ${progress.pendingUsers.length} 个`
      } else {
        endReason = `任务状态${endUpdateTask.status},已处理 ${progress.totalProcessed} 个用户，匹配 ${progress.totalMatched} 个，待处理队列还有 ${progress.pendingUsers.length} 个`
      }

      // return result
      const result = {
        endReason,
      }

      console.info(`[processLongCrawlerJob] ${result.endReason}`)
      return result
    } catch (error) {
      console.error(`[processLongCrawlerJob] 处理Instagram爬取任务 ${id} 失败:`, error)
      throw error
    }
  }

  /**
   * 并发获取多个用户的相关用户
   * @param usernames 用户名列表
   * @returns 相关用户列表
   */
  private async getBatchRelatedUsers(usernames: string[]): Promise<IgUser[]> {
    // 限制处理的用户数量，避免API调用过多
    const usersToProcess = usernames.slice(0, Math.min(5, usernames.length))

    try {
      const relatedUsersResults = await Bluebird.map(
        usersToProcess,
        async (username) => {
          try {
            const relatedUsers = await InstagramApi.getInstance().getUserRelatedUsers(username)
            if (relatedUsers && relatedUsers.length > 0) {
              return relatedUsers.filter((user) => user && user.username)
            }
            return []
          } catch (error) {
            console.error(`[getBatchRelatedUsers] 获取用户 ${username} 的相关用户失败:`, error)
            Sentry.captureException(error)
            return []
          }
        },
        { concurrency: +INS_RAPID_API_V3_CONCURRENCY },
      )

      const flattenedUsers = relatedUsersResults.flat()
      const userMap = new Map<string, IgUser>()

      flattenedUsers.forEach((user) => {
        if (user.username && !userMap.has(user.username)) {
          userMap.set(user.username, user)
        }
      })

      const allRelatedUsers = Array.from(userMap.values())

      console.info(
        `[getBatchRelatedUsers] 从 ${usersToProcess.length} 个用户中获取到 ${allRelatedUsers.length} 个相关用户（去重前 ${flattenedUsers.length} 个）`,
      )
      return allRelatedUsers
    } catch (error) {
      console.error(`[getBatchRelatedUsers] 获取相关用户失败:`, error)
      Sentry.captureException(error)
      return []
    }
  }

  /**
   * 处理一批用户，进行筛选并返回匹配的用户
   * @param usernames 用户名列表
   * @param filters 筛选条件
   * @param createdBy 创建者ID
   * @returns 包含匹配用户和通过AI筛选的用户名的对象
   */
  private async processLongCrawlerBatchUsers(
    relatedUsers: IgUser[],
    filters: InsLongCrawlerFilters,
    createdBy: string,
  ): Promise<{
    matchedUsers: IgUser[]
    aiFilteredUsernames: string[]
  }> {
    try {
      // fetch user posts
      const validUsersWithPosts = await this.fetchUserPosts(relatedUsers)

      // apply ai filter
      const usersAfterAiFilter = await this.applyAiFilter(validUsersWithPosts, filters)

      if (usersAfterAiFilter.length === 0) {
        console.info(`[processBatchUsers] 所有 ${validUsersWithPosts.length} 个用户都未通过AI筛选`)
        return { matchedUsers: [], aiFilteredUsernames: [] }
      }

      // get ai filtered usernames
      const aiFilteredUsernames = usersAfterAiFilter.map((user) => user.username)

      // fuillfill user details
      const analyzedUsernames = new Set(usersAfterAiFilter.map((r) => r.username))
      let analyzedUsersData = validUsersWithPosts.filter((item) =>
        analyzedUsernames.has(item.username),
      )

      console.log(`需要补齐用户信息的用户: ${analyzedUsersData.length}`)
      // fuillfill user details
      analyzedUsersData = await this.fetchUsersDetails(analyzedUsersData)

      // apply demographic filters
      const matchedUsers = this.applyDemographicFilters(analyzedUsersData, filters)

      console.info(
        `[processBatchUsers] 处理了 ${relatedUsers.length} 个用户，其中 ${validUsersWithPosts.length} 个有效，${usersAfterAiFilter.length} 个通过AI筛选，${matchedUsers.length} 个最终匹配，获取到 ${relatedUsers.length} 个相关用户`,
      )

      // save matched users to database
      if (matchedUsers.length > 0) {
        await Promise.all([
          this.upsertKolInfo(matchedUsers, createdBy),
          this.upsertUsers(matchedUsers),
        ])
      }

      return {
        matchedUsers,
        aiFilteredUsernames,
      }
    } catch (error) {
      console.error(`[processBatchUsers] 批量处理用户失败:`, error)
      Sentry.captureException(error)
      return { matchedUsers: [], aiFilteredUsernames: [] }
    }
  }

  /**
   * 应用AI筛选
   * @param users 用户列表
   * @param filters 筛选条件
   * @returns 通过AI筛选的用户列表
   */
  private async applyAiFilter(users: IgUser[], filters: InsLongCrawlerFilters): Promise<IgUser[]> {
    if (users.length === 0) {
      console.warn(`[applyAiFilter] 没有用户可供AI筛选`)
      return []
    }

    const batchSize = +INS_VISIUAL_SIMILARITY_BATCH_SIZE
    const batches = Array.from({ length: Math.ceil(users.length / batchSize) }, (_, i) =>
      users.slice(i * batchSize, (i + 1) * batchSize),
    )

    console.info(
      `[applyAiFilter] 将${users.length}个用户分成${batches.length}批进行AI视觉相似度分析，每批最多${batchSize}个用户`,
    )

    try {
      const analysisResults = await Bluebird.map(
        batches,
        async (batch, batchIndex) => {
          try {
            console.info(`[applyAiFilter] 开始处理第${batchIndex + 1}/${batches.length}批用户...`)
            const targetInputs = batch.map((item) => ({
              username: item.username,
              posts: item.posts || [],
            }))

            // AI分析选项
            const options = {
              allowList: [],
              banList: [],
              kolDescription: filters.kolDescription || '',
            }

            console.info(
              `[applyAiFilter] 调用AI分析第${batchIndex + 1}批的${targetInputs.length}个用户...`,
            )
            const batchResult = await analyzeVisualSimilarityService.analyzeVisualSimilarity(
              targetInputs,
              options,
            )

            console.info(
              `[applyAiFilter] 第${batchIndex + 1}批AI分析完成，获得${batchResult.length}个结果`,
            )
            return batchResult.map((result) => ({
              username: result.username,
              score: result.score / 100,
            }))
          } catch (error) {
            console.error(`[applyAiFilter] 处理第${batchIndex + 1}批时出错:`, error)
            Sentry.captureException(error)
            return []
          }
        },
        { concurrency: +INS_VISIUAL_SIMILARITY_CONCURRENCY },
      )

      // 过滤掉分数为0的结果
      const allResults = analysisResults.flat().filter((result) => result.score > 0)

      console.info(`[applyAiFilter] 所有批次处理完成，共获得${allResults.length}个AI分析结果`)

      // 返回通过AI筛选的用户
      return users.filter((user) => allResults.some((result) => result.username === user.username))
    } catch (error) {
      console.error(`[applyAiFilter] AI批量分析失败:`, error)
      Sentry.captureException(error)
      return []
    }
  }

  /**
   * 应用人口统计学筛选（粉丝数、地区等）
   * @param users 用户列表
   * @param filters 筛选条件
   * @returns 通过筛选的用户列表
   */
  private applyDemographicFilters(users: IgUser[], filters: InsLongCrawlerFilters): IgUser[] {
    return users.filter((user) => {
      // 筛选：粉丝数范围
      if (filters.followerRange) {
        const { min = 0, max = Infinity } = filters.followerRange
        if (user.followerCount < min || user.followerCount > max) {
          console.debug(
            `[applyDemographicFilters] 用户 ${user.username} 粉丝数 ${user.followerCount} 不在范围内 [${min}, ${max}]`,
          )
          return false
        }
      }

      // 筛选：地区
      if (filters.regions.length > 0) {
        // 如果用户没有地区信息或地区不在目标列表中，则过滤掉
        if (!user.region || !filters.regions.includes(user.region)) {
          console.debug(
            `[applyDemographicFilters] 用户 ${user.username} 地区 ${user.region || '未知'} 不在目标地区列表中`,
          )
          return false
        }
      }

      // 筛选：平均点赞数
      if (filters.averageLikeCount) {
        const { min = 0, max = Infinity } = filters.averageLikeCount
        // 计算用户帖子的平均点赞数
        let avgLikeCount = 0
        if (user.posts && user.posts.length > 0) {
          const totalLikes = user.posts.reduce((sum, post) => sum + (post.like_count || 0), 0)
          avgLikeCount = totalLikes / user.posts.length
        }

        // 应用过滤条件
        if (avgLikeCount < min || avgLikeCount > max) {
          console.debug(
            `[applyDemographicFilters] 用户 ${user.username} 平均点赞数 ${avgLikeCount.toFixed(0)} 不在范围内 [${min}, ${max}]`,
          )
          return false
        }
      }

      return true
    })
  }

  /**
   * 获取历史任务的candidatesUsername
   */
  private async getPreviousTasksCandidatesUsernames(projectId: string): Promise<string[]> {
    const previousTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    if (!previousTasks.length) {
      return []
    }
    const allResults = previousTasks
      .map((i) => i.result as unknown as InstagramTaskResult)
      .filter((i) => i)
    const allCandidates = previousTasks.map((i) => {
      return i.candidate as unknown as Array<{
        kolId: string
        platform: KolPlatform
        platformId: string
        score: number
      }>
    })
    const allResultUsernames = allResults
      .flatMap((result) => result.candidates || [])
      .filter((candidate: any) => candidate.username)
      .map((candidate: any) => candidate.username as string)
    const allCandidatesUsernames = allCandidates
      .filter(
        (
          c,
        ): c is Array<{
          kolId: string
          platform: KolPlatform
          platformId: string
          score: number
        }> => Array.isArray(c),
      )
      .flatMap((c) => c.map((i) => i.platformId))
    return [...allCandidatesUsernames, ...allResultUsernames]
  }

  async upsertUsersWithEmbedding(
    usersWithData: Array<{
      user: IgUser
      embedding: number[]
      dimensionResult?: BloggerDimensionsAnalysisResultWithFlag
    }>,
  ): Promise<string[]> {
    try {
      // 1. 过滤有效用户
      const validItems = usersWithData.filter(
        (item) => item.user?.id != null && item.embedding && item.embedding.length > 0,
      )

      if (validItems.length === 0) {
        console.warn('[upsertUsersWithEmbedding] 没有有效的用户数据')
        return []
      }

      // 2. 提取用户数据
      const validUsers = validItems.map((item) => item.user)

      // 3. 并行处理数据库操作和Milvus插入
      const dbOperationsPromise = Promise.resolve().then(async () => {
        const userIds = validUsers.map((u) => u.id!)
        const existing = await prisma.instagramUserInfo.findMany({
          where: { id: { in: userIds } },
          select: { id: true },
        })
        const existingIds = existing.map((u) => u.id)

        // 构建数据库操作数据
        const data = validUsers.map((u) => {
          const recentPosts = u.posts?.slice(0, 10)
          let averageCommentCount = 0,
            averageLikeCount = 0
          if (recentPosts && recentPosts.length > 0) {
            averageCommentCount =
              recentPosts.reduce((sum, num) => sum + num.comment_count, 0) / recentPosts.length
            averageLikeCount =
              recentPosts.reduce((sum, num) => sum + num.like_count, 0) / recentPosts.length
          }

          // 计算发布统计
          const publicationStats = PublicationStatsService.calculatePublicationStats(
            u.posts ?? [],
            KolPlatform.INSTAGRAM,
          )

          return {
            id: u.id,
            username: u.username,
            profilePicUrl: u.profilePicUrl,
            fullName: u.fullName,
            region: u.region ?? '',
            followerCount: u.followerCount ?? undefined,
            averageLikeCount: averageLikeCount ?? undefined,
            averageCommentCount: averageCommentCount ?? undefined,
            lastPublishedTime: u.lastPublishedTime ?? undefined,
            posts: u.posts ?? [],
            publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
            accountInfo: {
              accountType: u.accountType,
              isVerified: u.isVerified,
              isBusiness: u.isBusiness,
              category: u.category,
            },
          }
        })

        const insertData = data.filter((user) => !existingIds.includes(user.id))
        const updateData = data.filter((user) => existingIds.includes(user.id))

        const dbOperations = []

        // 插入新数据
        if (insertData.length > 0) {
          dbOperations.push(
            prisma.instagramUserInfo.createMany({
              data: insertData as any,
              skipDuplicates: true,
            }),
          )
        }

        // 更新现有数据
        updateData.forEach((data) => {
          const { id, ...dataWithoutId } = data
          dbOperations.push(
            prisma.instagramUserInfo
              .update({
                where: { id: id },
                data: dataWithoutId as any,
              })
              .catch((err) => {
                Sentry.captureException(err)
                return null
              }),
          )
        })

        // 并行执行所有数据库操作
        const results = await Promise.allSettled(dbOperations)
        const successCount = results.filter((r) => r.status === 'fulfilled').length
        const failCount = results.length - successCount

        return {
          success: successCount,
          failed: failCount,
          insertCount: insertData.length,
          updateCount: updateData.length,
        }
      })

      // 4. 准备Milvus数据
      const milvusPromise = Promise.resolve().then(async () => {
        const milvusDataToInsert: JinaV4MilvusData[] = validItems.map((item) => {
          const { user, embedding, dimensionResult } = item
          const publicationStats = PublicationStatsService.calculatePublicationStats(
            user.posts ?? [],
            KolPlatform.INSTAGRAM,
          )
          const averageLikeCount = user.posts?.length
            ? user.posts.reduce((sum: number, post: any) => sum + (post.like_count || 0), 0) /
              user.posts.length
            : 0

          const milvusData = {
            userId: user.id!,
            account: user.username,
            nickname: user.fullName || user.username,
            platform: KolPlatform.INSTAGRAM,
            region: user.region || '',
            followerCount: user.followerCount || 0,
            averagePlayCount: averageLikeCount,
            averageLikeCount: averageLikeCount,
            lastPublishedTime: user.lastPublishedTime || publicationStats.lastPublishedTime || 0,
            videoTexts:
              user.posts
                ?.slice(0, 10)
                .map((post: any) => (post.caption?.text || '').slice(0, 500))
                .join(' ')
                .slice(0, 6000) || '',
            email: user.email || '',
            denseVector: embedding,
            sparseVector: { indices: [], values: [] },
            meta: dimensionResult
              ? {
                  corePositioning: dimensionResult.corePositioning,
                  videoAnalysis: dimensionResult.videoAnalysis,
                  language: dimensionResult.language,
                }
              : undefined,
            createdAt: Math.floor(Date.now() / 1000),
            updatedAt: Math.floor(Date.now() / 1000),
          }
          // 添加详细日志用于调试
          console.log(
            `[upsertUsersWithEmbedding] 准备插入 Milvus 数据 - 用户: ${user.username}, 粉丝数: ${milvusData.followerCount}, 平均点赞数: ${milvusData.averageLikeCount}, 地区: ${milvusData.region || '空'}, 向量维度: ${embedding.length}`,
          )

          return milvusData
        })

        if (milvusDataToInsert.length === 0) {
          return { deleteCount: 0, insertCount: 0 }
        }

        const milvusResult =
          await JinaV4MilvusService.bulkInsertJinaV4WithDelete(milvusDataToInsert)
        return {
          deleteCount: milvusResult.deleteResult.delete_cnt,
          insertCount: milvusResult.insertResult.succ_index.length,
        }
      })

      // 5. 并行执行数据库和Milvus操作
      const [dbResult, milvusResult] = await Promise.allSettled([
        dbOperationsPromise,
        milvusPromise,
      ])

      // 6. 处理结果
      if (dbResult.status === 'fulfilled') {
        console.log(
          `[upsertUsersWithEmbedding] 数据库操作完成: 成功 ${dbResult.value.success}/${dbResult.value.success + dbResult.value.failed} (插入: ${dbResult.value.insertCount}, 更新: ${dbResult.value.updateCount})`,
        )
      } else {
        console.error('[upsertUsersWithEmbedding] 数据库操作失败:', dbResult.reason)
        Sentry.captureException(dbResult.reason)
      }

      if (milvusResult.status === 'fulfilled') {
        console.log(
          `[upsertUsersWithEmbedding] Milvus操作完成: 删除 ${milvusResult.value.deleteCount} 条，插入 ${milvusResult.value.insertCount} 条`,
        )
      } else {
        console.error('[upsertUsersWithEmbedding] Milvus操作失败:', milvusResult.reason)
        Sentry.captureException(milvusResult.reason)
      }

      // 7. 返回处理成功的用户名列表
      return validItems.map((item) => item.user.username)
    } catch (error) {
      console.error('[upsertUsersWithEmbedding] 处理失败:', error)
      Sentry.captureException(error)
      return []
    }
  }
}

export default InstagramService
