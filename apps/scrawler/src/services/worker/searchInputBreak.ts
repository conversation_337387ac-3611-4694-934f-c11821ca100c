import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import {
  TtSearchInputBreakTaskParams,
  YoutubeSearchInputBreakTaskParams,
  YoutubeSearchInputBreakTaskResult,
} from '@/types/task'
import { TtSearchInputBreakTaskResult } from '@/types/task.ts'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

export async function handleSearchInputBreakComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data
  const task = await prisma.similarChannelTask.update({
    where: { id },
    data: {
      status: SimilarChannelTaskStatus.RESULT_READY,
      result: result,
    },
  })
  const taskParams = task.params as TtSearchInputBreakTaskParams | YoutubeSearchInputBreakTaskParams

  if (!task) {
    throw new Error(`task ${id} not found`)
  }
  switch (taskParams.platform) {
    case KolPlatform.TIKTOK:
      await handleTiktokSearchInputBreakComplete(task, result)
      break
    case KolPlatform.YOUTUBE:
      await handleYoutubeSearchInputBreakComplete(task, result)
  }
  await TaskService.getInstance().finishTask(id, result)
  console.log(`[handleComplete] SEARCH_INPUT_BREAK 任务 ${id} 处理完成，状态已更新为 COMPLETED`)
  return
}

export async function handleTiktokSearchInputBreakComplete(
  task: any,
  result: TtSearchInputBreakTaskResult,
) {
  const uniqueIds = task.result.uniqueIds as string[]
  const projectId = task.projectId
  const taskResult = result as unknown as TtSearchInputBreakTaskResult
  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.TIKTOK,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleComplete] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.TIKTOK,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.SEARCH_INPUT_BREAK,
      },
    },
  })

  const metaData = {
    cursor: taskResult.cursor,
    hasMore: taskResult.hasMore,
    searchInput: taskResult.searchInput,
    total: taskResult.totalAuthorCount,
    updatedAt: new Date(),
    progress: taskResult.progress,
  }

  if (existingCandidate) {
    console.log(`[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.SEARCH_INPUT_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}

export async function handleYoutubeSearchInputBreakComplete(
  task: any,
  result: YoutubeSearchInputBreakTaskResult,
) {
  const channelIds = task.result.channelIds as string[]
  const projectId = task.projectId
  const taskResult = result as unknown as YoutubeSearchInputBreakTaskResult
  if (!channelIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 channelIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.YOUTUBE,
      platformAccount: { in: channelIds },
    },
  })

  console.log(
    `[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = channelIds
    .map((channelId) => {
      const kol = kolInfoMap.get(channelId)
      if (!kol) {
        console.warn(`[handleComplete] 未找到 KolInfo 记录: ${channelId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.YOUTUBE,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.SEARCH_INPUT_BREAK,
      },
    },
  })

  const metaData = {
    hasMore: taskResult.hasMore,
    searchInput: taskResult.searchInput,
    total: taskResult.total,
    updatedAt: new Date(),
    progress: taskResult.progress,
    token: taskResult.paginationToken,
  }

  if (existingCandidate) {
    console.log(`[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleComplete] SEARCH_INPUT_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.SEARCH_INPUT_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
