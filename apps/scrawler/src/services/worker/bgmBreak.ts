import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import { TtBgmBreakTaskResult } from '@/types/task.ts'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理BGM_BREAK类型任务完成
 * 处理TikTok的BGM爆破任务结果
 */
export async function handleBgmBreakComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleBgmBreakComplete] 处理 BGM_BREAK 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`task ${id} not found`)
    }

    await handleTiktokBgmBreak(task, result as TtBgmBreakTaskResult)

    await TaskService.getInstance().finishTask(id, result)
    console.log(`[handleBgmBreakComplete] BGM_BREAK 任务 ${id} 处理完成，状态已更新为 COMPLETED`)
  } catch (error) {
    console.error(`[handleBgmBreakComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理TikTok的BGM_BREAK任务
 */
async function handleTiktokBgmBreak(task: any, result: TtBgmBreakTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.TIKTOK,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleTiktokBgmBreak] BGM_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleTiktokBgmBreak] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.TIKTOK,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.BGM_BREAK,
      },
    },
  })

  const metaData = {
    cursor: result.cursor,
    hasMore: result.hasMore,
    musicUrl: result.musicUrl,
    message: result.message,
    total: uniqueIds.length,
    updatedAt: new Date(),
  }

  if (existingCandidate) {
    console.log(`[handleTiktokBgmBreak] BGM_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleTiktokBgmBreak] BGM_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.BGM_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
