import { JINA_API_KEY } from '@/config/env'
import retry from 'async-retry'

interface JinaEmbeddingResponse {
  model: string
  object: string
  usage: {
    total_tokens: number
  }
  data: Array<{
    object: string
    index: number
    embedding: number[]
  }>
}

export async function getJinaEmbedding(
  text: string,
  task: string = 'text-matching',
): Promise<number[]> {
  return retry(
    async () => {
      try {
        if (!text || text.trim() === '') {
          return []
        }

        text = text
          .replace(
            /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu,
            '',
          )
          .replace(/[^\p{L}\p{N}\s.,?!-]/gu, ' ')
          .replace(/\s+/g, ' ')
          .trim()

        if (text.length > 20000) {
          text = text.substring(0, 20000)
        }

        const response = await fetch('https://api.jina.ai/v1/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${JINA_API_KEY}`,
          },
          body: JSON.stringify({
            model: 'jina-embeddings-v4',
            task: task,
            late_chunking: false,
            truncate_dim: null,
            input: [{ text: text }],
          }),
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`Jina Embedding API error: ${response.status} - ${errorText}`)
        }
        console.log(`[JinaEmbedding] 成功请求 Jina Embedding API`)
        const data: JinaEmbeddingResponse = await response.json()

        if (!data.data || !data.data[0] || !data.data[0].embedding) {
          throw new Error('Invalid response from Jina Embedding API')
        }

        const embedding = data.data[0].embedding
        if (embedding.length !== 2048) {
          console.warn(`警告：期望2048维向量，但得到${embedding.length}维`)
        }

        return embedding
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        console.error(
          `Jina Embedding request failed for text "${text.slice(0, 100)}...": ${errorMessage}`,
        )
        throw error
      }
    },
    {
      retries: 3,
      factor: 2,
      minTimeout: 1_000,
      maxTimeout: 20_000,
      onRetry: (error: Error, attempt) => {
        console.warn(`Retrying Jina Embedding due to error: ${error.message}. Attempt ${attempt}`)
      },
    },
  )
}

export async function getEmbeddingForInstagram(text: string): Promise<number[]> {
  return getJinaEmbedding(text, 'text-matching')
}
