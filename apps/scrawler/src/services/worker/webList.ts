import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import { InsWebListTaskResult, TtWebListTaskResult } from '@/types/task.ts'
import { TaskParamsService } from '@/types/taskParams'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理 WEB_LIST 类型任务完成
 */
export async function handleWebListComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleWebListComplete] 处理 WEB_LIST 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`task ${id} not found`)
    }

    const platform = TaskParamsService.getPlatform(task.params)

    if (platform === 'tiktok') {
      await handleTiktokWebList(task, result as TtWebListTaskResult)
    } else if (platform === 'instagram') {
      await handleInstagramWebList(task, result as InsWebListTaskResult)
    } else {
      throw new Error(`不支持的平台: ${platform}`)
    }

    await TaskService.getInstance().finishTask(id, result)
    console.log(`[handleWebListComplete] WEB_LIST 任务 ${id} 处理完成，状态已更新为 COMPLETED`)
  } catch (error) {
    console.error(`[handleWebListComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理 TikTok 的 WEB_LIST 任务
 */
async function handleTiktokWebList(task: any, result: TtWebListTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || uniqueIds.length === 0) {
    throw new Error(`task ${task.id} 没有找到任何uniqueIds`)
  }

  if (!projectId) {
    throw new Error(`task ${task.id} 缺少必要的数据 projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.TIKTOK,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleTiktokWebList] WEB_LIST 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleTiktokWebList] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.TIKTOK,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.WEB_LIST,
      },
    },
  })

  const metaData = {
    message: result.message,
    total: uniqueIds.length,
    updatedAt: new Date(),
  }

  if (existingCandidate) {
    console.log(`[handleTiktokWebList] WEB_LIST 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleTiktokWebList] WEB_LIST 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.WEB_LIST,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}

/**
 * 处理 Instagram 的 WEB_LIST 任务
 */
async function handleInstagramWebList(task: any, result: InsWebListTaskResult) {
  const usernames = result.usernames
  const projectId = task.projectId

  if (!usernames || usernames.length === 0) {
    throw new Error(`task ${task.id} 没有找到任何usernames`)
  }

  if (!projectId) {
    throw new Error(`task ${task.id} 缺少必要的数据 projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.INSTAGRAM,
      platformAccount: { in: usernames },
    },
  })

  if (kolInfos.length === 0) {
    throw new Error(`task ${task.id} 没有找到任何KolInfo记录`)
  }

  console.log(
    `[handleInstagramWebList] WEB_LIST 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const candidatesData = kolInfos.map((kol) => ({
    kolId: kol.id,
    platform: KolPlatform.INSTAGRAM,
    platformId: kol.platformAccount,
  }))

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.WEB_LIST,
      },
    },
  })

  const metaData = {
    message: result.message,
    total: usernames.length,
    updatedAt: new Date(),
  }

  if (existingCandidate) {
    console.log(`[handleInstagramWebList] WEB_LIST 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleInstagramWebList] WEB_LIST 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.WEB_LIST,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
