import { describe, expect, it } from 'vitest'
import { removePopularTags } from './embedding'

describe('removePopularTags', () => {
  it('应该正确去除所有热门标签并处理各种情况', () => {
    const testCases = [
      {
        input: '这是一个 #foryou #viral 视频 #fyp #trending 测试 #foryoupage #viralvideo',
        expected: '这是一个 视频 测试',
      },
      {
        input: '这是一个 #有趣 的视频 #搞笑 #foryou',
        expected: '这是一个 #有趣 的视频 #搞笑',
      },
      {
        input: '测试 #FoRYoU #ViRaL #FYP',
        expected: '测试',
      },
      {
        input: '#foryou 这是测试文本 #viral',
        expected: '这是测试文本',
      },
      {
        input: '这是一个没有热门标签的普通文本',
        expected: '这是一个没有热门标签的普通文本',
      },
      {
        input: '',
        expected: '',
      },
      {
        input: '#foryou #viral #fyp #trending',
        expected: '',
      },
      {
        input: '测试#foryou#viral#fyp文本',
        expected: '测试文本',
      },
      {
        input: '混合大小写 #ForYou #VIRAL #fyp #TrEnDiNg',
        expected: '混合大小写',
      },
      {
        input: '多个连续标签 #foryou#viral#fyp 中间有文字 #trending#viralvideo',
        expected: '多个连续标签 中间有文字',
      },
      {
        input: '  多余的  空格  #foryou  测试  ',
        expected: '多余的 空格 测试',
      },
    ]

    testCases.forEach(({ input, expected }) => {
      expect(removePopularTags(input)).toBe(expected)
    })
  })
})
