import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import {
  InsHashTagBreakTaskResult,
  TtHashTagBreakTaskResult,
  YoutubeHashtagBreakTaskResult,
} from '@/types/task.ts'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理HASH_TAG_BREAK类型任务完成
 * 包括TikTok和Instagram的处理逻辑
 */
export async function handleHashTagBreakComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleHashTagBreakComplete] 处理 HASH_TAG_BREAK 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`task ${id} not found`)
    }

    if (task.isTiktok) {
      await handleTiktokHashTagBreak(task, result as TtHashTagBreakTaskResult)
    } else if (task.isInstagram) {
      await handleInstagramHashTagBreak(task, result as InsHashTagBreakTaskResult)
    } else if (task.isYoutube) {
      await handleYoutubeHashTagBreak(task, result as YoutubeHashtagBreakTaskResult)
    } else {
      throw new Error(`task ${id} 平台类型不支持`)
    }

    await TaskService.getInstance().finishTask(id, result)
    console.log(
      `[handleHashTagBreakComplete] HASH_TAG_BREAK 任务 ${id} 处理完成，状态已更新为 COMPLETED`,
    )
  } catch (error) {
    console.error(`[handleHashTagBreakComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理TikTok的HASH_TAG_BREAK任务
 */
async function handleTiktokHashTagBreak(task: any, result: TtHashTagBreakTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.TIKTOK,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleTiktokHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )
  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleTiktokHashTagBreak] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.TIKTOK,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
      },
    },
  })

  const metaData = {
    cursor: result.cursor,
    hasMore: result.hasMore,
    tag: result.tag,
    total: result.total,
    updatedAt: new Date(),
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleTiktokHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleTiktokHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}

/**
 * 处理Instagram的HASH_TAG_BREAK任务
 */
async function handleInstagramHashTagBreak(task: any, result: InsHashTagBreakTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.INSTAGRAM,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleInstagramHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )
  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleInstagramHashTagBreak] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.INSTAGRAM,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
      },
    },
  })

  const metaData = {
    paginationToken: result.paginationToken,
    hasMore: result.hasMore,
    tag: result.tag,
    total: uniqueIds.length,
    updatedAt: new Date(),
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleInstagramHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleInstagramHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}

/**
 * 处理 YouTube 的 HASH_TAG_BREAK 任务
 */
async function handleYoutubeHashTagBreak(task: any, result: YoutubeHashtagBreakTaskResult) {
  const channelIds = result.channelIds
  const projectId = task.projectId

  if (!channelIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 channelIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.YOUTUBE,
      platformAccount: { in: channelIds },
    },
  })

  console.log(
    `[handleYoutubeHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )
  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = channelIds
    .map((channelId) => {
      const kol = kolInfoMap.get(channelId)
      if (!kol) {
        console.warn(`[handleYoutubeHashTagBreak] 未找到 KolInfo 记录: ${channelId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.YOUTUBE,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
      },
    },
  })

  const metaData = {
    paginationToken: result.paginationToken,
    hasMore: result.hasMore,
    tag: result.tag,
    total: channelIds.length,
    updatedAt: new Date(),
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleYoutubeHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleYoutubeHashTagBreak] HASH_TAG_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
