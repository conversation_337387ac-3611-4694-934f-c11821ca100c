import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task'
import { GetTaskParams } from '@/types/taskParams'
import { prisma, SimilarChannelTaskStatus } from '@repo/database'
import Bull from 'bull'

/**
 * 处理视频受众分析任务完成后的操作
 * @param job 任务对象
 */
export async function handlePostAudienceComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id } = job.data
  const params = job.data.params as GetTaskParams<'POST_AUDIENCE'>
  const { videoId, platform } = params
  const result = job.data.result

  try {
    console.log(
      `[handlePostAudienceComplete] 开始处理视频受众分析任务 ${id}, 平台: ${platform}, 视频ID: ${videoId}`,
    )

    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`任务 ${id} 不存在`)
    }

    // 判断参数类型，仅对包含googleSheetId的任务更新PublicationStatisticsSheetData表
    if ('googleSheetId' in params && params.googleSheetId) {
      const googleSheetId = params.googleSheetId

      const existingRecord = await prisma.publicationStatisticsSheetData.findFirst({
        where: {
          videoId: videoId,
          spreadsheetId: googleSheetId,
        },
      })

      if (existingRecord) {
        await prisma.publicationStatisticsSheetData.update({
          where: {
            id: existingRecord.id,
          },
          data: {
            countryData: result,
          },
        })
        console.log(
          `[handlePostAudienceComplete] 已更新 PublicationStatisticsSheetData 表中 videoId=${videoId} 的 countryData 字段`,
        )
      } else {
        // 不存在记录则记录警告
        console.warn(
          `[handlePostAudienceComplete] 警告：未找到 videoId=${videoId}, spreadsheetId=${googleSheetId} 的记录，countryData 未能保存`,
        )
      }
    } else {
      console.log(
        `[handlePostAudienceComplete] 插件任务类型，无需更新 PublicationStatisticsSheetData 表`,
      )
    }

    // 构建结果元数据
    const postAudienceResult = {
      ...result,
      googleSheetId: 'googleSheetId' in params ? params.googleSheetId : undefined,
      publicationId: 'publicationId' in params ? params.publicationId : undefined,
      videoId,
      platform,
      postLink: params.postLink,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    // 完成任务
    await TaskService.getInstance().finishTask(id, result, postAudienceResult)
    console.log(`[handlePostAudienceComplete] 任务 ${id} 处理完成，受众分析结果已保存`)
  } catch (error) {
    console.error(`[handlePostAudienceComplete] 处理任务 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}
