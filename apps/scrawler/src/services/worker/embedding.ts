import { TikTokVideo } from '@/api/@types/rapidapi/Tiktok'
import { Channel } from '@/api/@types/rapidapi/Youtube.ts'
import { azureOpenai, openai } from '@/api/openai'
import TiktokApi from '@/api/tiktok'
import { EMBEDDING_CONCURRENCY } from '@/config/env'
import { PopularTags } from '@/enums/PopularTags'
import Sentry from '@/infras/sentry'
import { TtUserDetailsAndVideos } from '@/types/tiktokUsers'
import retry from 'async-retry'
import Bluebird from 'bluebird'
import { decode, encode } from 'gpt-3-encoder' //增加token计算
import { CreateEmbeddingResponse } from 'openai/resources/embeddings.mjs'

export async function getEmbedding(
  text: string,
  model: string = 'text-embedding-3-large',
): Promise<number[]> {
  return retry(
    async () => {
      try {
        if (!text || text.trim() === '') {
          return []
        }

        text = text
          .replace(
            /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu,
            '',
          )
          .replace(/[^\p{L}\p{N}\s.,?!-]/gu, ' ')
          .replace(/\s+/g, ' ')
          .trim()

        const tokens = encode(text)

        if (tokens.length > 6000) {
          const truncatedTokens = tokens.slice(0, 6000)
          text = decode(truncatedTokens)
        }

        let response: CreateEmbeddingResponse
        try {
          response = await openai.embeddings.create({
            model: model,
            input: [text],
          })
        } catch (e) {
          Sentry.captureException(e)
          response = await azureOpenai.embeddings.create({
            model: model,
            input: [text],
          })
        }
        return response.data[0].embedding
      } catch (error: any) {
        console.error(
          `Embedding request failed for text "${text.slice(0, 100)}...": ${error.message}`,
        )
        throw error
      }
    },
    {
      retries: 3,
      factor: 2,
      minTimeout: 1_000,
      maxTimeout: 20_000,
      onRetry: (error: Error, attempt) => {
        console.warn(`Retrying due to error: ${error.message}. Attempt ${attempt}`)
      },
    },
  )
}
// 使用更快的Bluebird并发生成嵌入，优化加快
export async function generateEmbeddingsForYoutube(channels: Channel[]): Promise<number[][]> {
  if (channels.length > 0) {
    console.log(`embedding with ${channels[0].videos.length} videos`)
  }
  return Bluebird.map(
    channels,
    async (channel: Channel) => {
      try {
        const videoTitles = (channel.videos ?? [])
          .map((video) => video.title)
          .filter((title) => title && title.trim().length > 0) // 过滤掉空标题

        if (videoTitles.length === 0) {
          console.warn(`No valid titles found for channel ${channel.channelId}`)
          return []
        }

        const concatenatedTitles = videoTitles.join(' ')
        return await getEmbedding(concatenatedTitles)
      } catch (error) {
        console.error(`Error generating embedding for channel ${channel.channelId}:`, error)
        return []
      }
    },
    { concurrency: +EMBEDDING_CONCURRENCY },
  )
}

// 去除热门标签的函数，这些标签可能需要手动维护。目前不做存储.
// 使用 PopularTags 枚举去除热门标签
export function removePopularTags(text: string): string {
  let processedText = text
  for (const tag of PopularTags) {
    const tagPattern = new RegExp(`#${tag}\\b`, 'gi')
    processedText = processedText.replace(tagPattern, '')
  }
  return processedText.replace(/\s+/g, ' ').trim()
}

export async function generateEmbeddingsForTikTok(
  usersWithVideos: TtUserDetailsAndVideos[],
): Promise<number[][]> {
  const processUserVideos = async (userWithVideos: TtUserDetailsAndVideos): Promise<number[]> => {
    try {
      const videoTitles = userWithVideos.videos.map((video) => video.title)
      if (videoTitles.length === 0) {
        return []
      }

      const concatenatedTitles = videoTitles.join(' ')
      // 去除影响比较大的tag和蹭热度的tag
      const cleanedTitles = removePopularTags(concatenatedTitles)
      return await getEmbedding(cleanedTitles)
    } catch (error) {
      console.error(`为TikTok用户 ${userWithVideos.uniqueId} 生成嵌入时出错:`, error)
      Sentry.captureException(error)
      return []
    }
  }

  return await Bluebird.map(usersWithVideos, processUserVideos, {
    concurrency: +EMBEDDING_CONCURRENCY,
  })
}

export async function getEmbeddingsFromTtVideos(uniqueId: string): Promise<number[]> {
  const videos: TikTokVideo[] | null = await TiktokApi.getInstance().getUserVideos({
    unique_id: uniqueId,
    count: 20,
    cursor: 0,
  })

  if (!videos || videos.length === 0) {
    throw new Error('No videos found for the user')
  }
  const videoTitles = videos.map((video) => video.title || '')
  const cleanedTitles = removePopularTags(videoTitles.join(' '))
  return await getEmbedding(cleanedTitles)
}

/**
 * 直接生成TikTok用户的嵌入向量映射，无需依赖中间数组和索引匹配
 * 每个用户直接生成对应的embedding并添加到映射中
 * @param usersWithVideos TikTok用户数据数组
 * @returns 返回一个Map，键为uniqueId，值为对应的嵌入向量
 */
export async function generateDirectEmbeddingMapForTikTok(
  usersWithVideos: TtUserDetailsAndVideos[],
): Promise<Map<string, number[]>> {
  console.log(`开始为${usersWithVideos.length}个TikTok用户直接生成嵌入向量映射`)

  // 创建uniqueId到embedding的映射
  const embeddingMap = new Map<string, number[]>()

  // 并行处理用户数据，直接生成embedding并添加到映射中
  await Bluebird.map(
    usersWithVideos,
    async (userWithVideos) => {
      try {
        const videoTitles = userWithVideos.videos.map((video) => video.title)
        if (videoTitles.length === 0) {
          console.warn(`用户 ${userWithVideos.uniqueId} 没有视频标题数据，跳过生成嵌入向量`)
          return
        }

        const concatenatedTitles = videoTitles.join(' ')
        // 去除影响比较大的tag和蹭热度的tag
        const cleanedTitles = removePopularTags(concatenatedTitles)
        const embedding = await getEmbedding(cleanedTitles)

        if (embedding && embedding.length > 0) {
          embeddingMap.set(userWithVideos.uniqueId, embedding)
        } else {
          console.warn(`用户 ${userWithVideos.uniqueId} 生成的嵌入向量无效或为空`)
        }
      } catch (error) {
        console.error(`为TikTok用户 ${userWithVideos.uniqueId} 生成嵌入向量时出错:`, error)
        Sentry.captureException(error)
      }
    },
    { concurrency: +EMBEDDING_CONCURRENCY },
  )

  console.log(`成功生成${embeddingMap.size}个有效的TikTok用户嵌入向量映射`)
  return embeddingMap
}
