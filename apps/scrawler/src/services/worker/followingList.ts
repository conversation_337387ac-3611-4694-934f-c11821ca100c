import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import { InsFollowingListTaskResult, TtFollowingListTaskResult } from '@/types/task.ts'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理FOLLOWING_LIST类型任务完成
 * 包括TikTok和Instagram的处理逻辑
 */
export async function handleFollowingListComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleFollowingListComplete] 处理 FOLLOWING_LIST 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`task ${id} not found`)
    }

    if (task.isTiktok) {
      await handleTiktokFollowingList(task, result)
    } else if (task.isInstagram) {
      await handleInstagramFollowingList(task, result as InsFollowingListTaskResult)
    } else {
      throw new Error(`task ${id} 平台类型不支持`)
    }

    await TaskService.getInstance().finishTask(id, result, job.data.metrics.getMetrics() ?? {})
    console.log(
      `[handleFollowingListComplete] FOLLOWING_LIST 任务 ${id} 处理完成，状态已更新为 COMPLETED`,
    )
  } catch (error) {
    console.error(`[handleFollowingListComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理TikTok的FOLLOWING_LIST任务
 */
async function handleTiktokFollowingList(task: any, result: TtFollowingListTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.TIKTOK,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleTiktokFollowingList] FOLLOWING_LIST 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleTiktokFollowingList] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.TIKTOK,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.FOLLOWING_LIST,
      },
    },
  })

  const metaData = {
    uniqueId: task.params.uniqueId,
    total: result.total,
    followingCount: result.following,
    time: result.time,
    hasMore: result.hasMore,
    updatedAt: new Date(),
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleTiktokFollowingList] FOLLOWING_LIST 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleTiktokFollowingList] FOLLOWING_LIST 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.FOLLOWING_LIST,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}

/**
 * 处理Instagram的FOLLOWING_LIST任务
 */
async function handleInstagramFollowingList(task: any, result: InsFollowingListTaskResult) {
  const uniqueIds = result.uniqueIds
  const projectId = task.projectId

  if (!uniqueIds || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 uniqueIds or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.INSTAGRAM,
      platformAccount: { in: uniqueIds },
    },
  })

  console.log(
    `[handleInstagramFollowingList] FOLLOWING_LIST 任务 ${task.id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
  )

  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
  const candidatesData = uniqueIds
    .map((uniqueId) => {
      const kol = kolInfoMap.get(uniqueId)
      if (!kol) {
        console.warn(`[handleInstagramFollowingList] 未找到 KolInfo 记录: ${uniqueId}`)
        return null
      }
      return {
        kolId: kol.id,
        platform: KolPlatform.INSTAGRAM,
        platformId: kol.platformAccount,
      }
    })
    .filter(Boolean)

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.FOLLOWING_LIST,
      },
    },
  })

  const metaData = {
    username: result.username,
    paginationToken: result.paginationToken,
    hasMore: result.hasMore,
    updatedAt: new Date(),
    total: candidatesData.length,
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleInstagramFollowingList] FOLLOWING_LIST 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleInstagramFollowingList] FOLLOWING_LIST 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.FOLLOWING_LIST,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
