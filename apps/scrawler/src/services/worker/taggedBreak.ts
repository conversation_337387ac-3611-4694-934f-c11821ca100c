import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import TaskService from '@/services/task.ts'
import { InsTaggedBreakTaskResult } from '@/types/task.ts'
import { KolPlatform, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理TAGGED_BREAK类型任务完成
 * 主要处理Instagram的被标记用户爆破任务
 */
export async function handleTaggedBreakComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleTaggedBreakComplete] 处理 TAGGED_BREAK 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    if (!task) {
      throw new Error(`task ${id} not found`)
    }

    if (task.isInstagram) {
      await handleInstagramTaggedBreak(task, result as InsTaggedBreakTaskResult)
    } else {
      throw new Error(`task ${id} 平台类型不支持`)
    }

    await TaskService.getInstance().finishTask(id, result)
    console.log(
      `[handleTaggedBreakComplete] TAGGED_BREAK 任务 ${id} 处理完成，状态已更新为 COMPLETED`,
    )
  } catch (error) {
    console.error(`[handleTaggedBreakComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理Instagram的TAGGED_BREAK任务
 */
async function handleInstagramTaggedBreak(task: any, result: InsTaggedBreakTaskResult) {
  const usernames = result.usernames
  const projectId = task.projectId

  if (!usernames || !projectId) {
    throw new Error(`task ${task.id} result缺少必要的数据 usernames or projectId`)
  }

  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      platform: KolPlatform.INSTAGRAM,
      platformAccount: { in: usernames },
    },
  })

  // 创建一个映射来保存原始顺序
  const usernameOrderMap = new Map(usernames.map((username, index) => [username, index]))

  // 根据原始顺序对 kolInfos 进行排序
  const sortedKolInfos = [...kolInfos].sort((a, b) => {
    const orderA = usernameOrderMap.get(a.platformAccount!) ?? Infinity
    const orderB = usernameOrderMap.get(b.platformAccount!) ?? Infinity
    return orderA - orderB
  })
  console.log(
    `[handleInstagramTaggedBreak] TAGGED_BREAK 任务 ${task.id} 找到 ${sortedKolInfos.length} 个 KolInfo 记录`,
  )

  const candidatesData = sortedKolInfos.map((kol) => ({
    kolId: kol.id,
    platform: KolPlatform.INSTAGRAM,
    platformId: kol.platformAccount,
  }))

  const existingCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.TAGGED_BREAK,
      },
    },
  })

  const metaData = {
    paginationToken: result.paginationToken,
    hasMore: result.hasMore,
    username: result.username,
    total: usernames.length,
    updatedAt: new Date(),
    progress: result.progress,
  }

  if (existingCandidate) {
    console.log(`[handleInstagramTaggedBreak] TAGGED_BREAK 任务 ${task.id} 更新现有记录`)

    const existingCandidates = (existingCandidate.candidates as any) || {}
    const existingKols = existingCandidates.kols || []

    const mergedKols = [...existingKols, ...candidatesData]
    metaData.total = mergedKols.length

    await prisma.projectCandidate.update({
      where: {
        id: existingCandidate.id,
      },
      data: {
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: mergedKols,
        },
        meta: metaData,
        updatedAt: new Date(),
      },
    })
  } else {
    console.log(`[handleInstagramTaggedBreak] TAGGED_BREAK 任务 ${task.id} 创建新记录`)
    await prisma.projectCandidate.create({
      data: {
        projectId: projectId,
        type: TaskType.TAGGED_BREAK,
        candidates: {
          taskId: task.id,
          updatedAt: new Date(),
          kols: candidatesData,
        },
        meta: metaData,
      },
    })
  }
}
