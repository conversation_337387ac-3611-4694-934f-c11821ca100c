import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { logger } from '@/infras/worker/log4js'
import { prisma, SimilarChannelTaskStatus } from '@repo/database'
import Bull from 'bull'

export async function handleLongCrawlerComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleLongCrawlerComplete] 处理 LONG_CRAWLER 类型任务 ${id}`)

  try {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id },
      select: {
        id: true,
        status: true,
        result: true,
        meta: true,
        createdAt: true,
      },
    })

    if (!task) {
      logger.error(`[handleLongCrawlerComplete] 任务 ${id} 不存在`)
      return
    }

    const timeCostInMilliseconds = Date.now() - task.createdAt.getTime()

    // PAUSING -> PAUSED 状态只更新结果
    if (task.status === SimilarChannelTaskStatus.PAUSING) {
      logger.info(`[handleLongCrawlerComplete] 任务 ${id} 状态为暂停，保留当前状态并更新结果`)

      await prisma.similarChannelTask.update({
        where: { id },
        data: {
          status: SimilarChannelTaskStatus.PAUSED,
          result: {
            ...((task.result as any) || {}),
            ...result,
            timeCost: timeCostInMilliseconds,
            pauseReason: '用户手动暂停',
          },
        },
      })
    } else {
      // PROCESSING/COMPLETING -> COMPLETED
      logger.info(`[handleLongCrawlerComplete] 任务 ${id} 状态为 ${task.status}，更新为已完成`)

      await prisma.similarChannelTask.update({
        where: { id },
        data: {
          status: SimilarChannelTaskStatus.COMPLETED,
          result: {
            ...((task.result as any) || {}),
            ...result,
            timeCost: timeCostInMilliseconds,
            completedAt: new Date().toISOString(),
            completionReason:
              task.status === SimilarChannelTaskStatus.PROCESSING
                ? '任务正常完成'
                : '任务在非处理状态下完成',
          },
        },
      })
    }

    logger.info(`[handleLongCrawlerComplete] 任务 ${id} 处理完成`)
  } catch (error) {
    logger.error(`[handleLongCrawlerComplete] 处理任务 ${id} 时出错:`, error)
    throw error
  }
}
