import { logTime } from '@/api/util'
import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { publicationService } from '@/services/PublicationStatistics.service'
import TaskService from '@/services/task.ts'
import { TrackEasyKOLTaskParams } from '@/types/task.ts'
import { SimilarChannelTaskStatus, prisma } from '@repo/database'
import Bull from 'bull'

/**
 * 处理EASYKOL_TRACK类型的任务
 * 用于异步处理视频数据更新
 */
export async function processEasyKOLTrackJob(job: Bull.Job<IEmbeddingTask>) {
  const { id, createdBy } = job.data
  const params = job.data.params as unknown as TrackEasyKOLTaskParams
  console.log(`[processEasyKOLTrackJob] 开始处理视频数据跟踪任务 ${id}`)

  try {
    // 使用publicationService.updateVideoData处理视频数据更新
    const result = await logTime(
      publicationService.updateVideoData(createdBy, params),
      '[sj:publication]updateVideoData',
    )

    console.log(
      `[processEasyKOLTrackJob] 任务 ${id} 处理完成，成功: ${result.stats.success}, 失败: ${result.stats.failed}`,
    )

    // 将结果保存到任务数据中
    job.data.result = {
      taskId: id,
      spreadsheetId: params.spreadsheetId,
      stats: result.stats,
      timestamp: new Date().toISOString(),
    }

    return result
  } catch (error) {
    console.error(`[processEasyKOLTrackJob] 处理任务 ${id} 失败:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 处理EASYKOL_TRACK任务完成
 */
export async function handleEasyKOLTrackComplete(job: Bull.Job<IEmbeddingTask>) {
  const { id, result } = job.data

  console.log(`[handleEasyKOLTrackComplete] 处理 EASYKOL_TRACK 类型任务 ${id}`)

  try {
    // 更新任务状态为已完成
    await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        result: result,
      },
    })

    // 完成任务
    await TaskService.getInstance().finishTask(id, result)

    console.log(`[handleEasyKOLTrackComplete] 任务 ${id} 处理完成，状态已更新为 COMPLETED`)
  } catch (error) {
    console.error(`[handleEasyKOLTrackComplete] 处理任务完成 ${id} 失败:`, error)
    Sentry.captureException(error)
  }
}
