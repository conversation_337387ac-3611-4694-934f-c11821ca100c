import { describe, it } from 'vitest'

describe('process Job', () => {
  describe('get youtube videos', () => {
    it('Should return channelsInfo object', async () => {
      const channelUrl = 'https://www.youtube.com/channel/UC1FQWQ3y3-e8l1pCtWhJt5A'
      // const result = await YoutubeService.getInstance().youtubeProcessJob(channelUrl)
      // console.log(result)
      // console.log(result.length)
    }, 100_000) // 将超时时间设为10秒
  })
})
