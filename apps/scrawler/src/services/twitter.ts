import { TWITTER_SIMILAR_FOLLOWING_COUNT } from '@/config/env'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { SimilarTaskParams } from '@/types/task'
import { Twitter241User } from '@/types/twitter/rapid'
import {
  KolInfo,
  KolPlatform,
  ProjectKolAttitude,
  TaskType,
  TwitterUser,
  prisma,
} from '@repo/database'
import Bull from 'bull'
import { twitter241Api } from '../lib/twitter241Api'
import { retryUtil } from '../utils/retry'
import { twitterDescriptionAnalysisService } from './aiTools/twitterDescriptionAnalysis'
import { ProjectKolService } from './projectKol.service'
import { findCurrentTasksByProjectId } from './similar'

class TwitterService {
  private static instance: TwitterService

  static getInstance(): TwitterService {
    if (!TwitterService.instance) {
      TwitterService.instance = new TwitterService()
    }
    return TwitterService.instance
  }

  /**
   * 处理Twitter相似任务 - 视觉模式
   */
  async processVisualSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id } = job.data
    const params = job.data.params as SimilarTaskParams

    const task = await prisma.similarChannelTask.findUniqueOrThrow({
      where: { id },
    })

    // 获取源用户ID（用于获取关注列表）
    let { source, projectId, banList = [], allowList = [], kolDescription = '' } = params

    if (!source) {
      console.error('[Twitter] 未提供源用户')
      return []
    }

    const sourceUser = await retryUtil.retryWithValidator(
      () => twitter241Api.getUserByScreenName(source),
      (result) => !!result,
      3,
      1000,
      2,
      (result: any, error: any, retryCount: number, nextDelay: number) => {
        if (error) {
          console.warn(
            `[Twitter] 获取源用户失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试`,
            error,
          )
        } else if (!result) {
          console.warn(
            `[Twitter] 获取源用户返回空结果，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试`,
          )
        }
      },
    )

    if (!sourceUser) {
      throw new Error('not found soure user')
    }

    // 如果三要素都为空，则获取源博主描述并用AI生成
    if (!kolDescription || kolDescription.trim() === '') {
      console.log('[Twitter] 三要素为空，开始获取源博主信息并生成三要素')
      if (sourceUser.description) {
        const generatedElements = await twitterDescriptionAnalysisService.generateThreeElements(
          sourceUser.description,
        )
        banList = generatedElements.banList
        allowList = generatedElements.allowList
        kolDescription = generatedElements.kolDescription

        console.log('[Twitter] AI生成的三要素:', {
          banList,
          allowList,
          kolDescription,
        })
      } else {
        console.warn('[Twitter] 无法获取源博主描述，使用默认空值')
      }
    }

    const [projectRatedChannelIds, previousCandidatesChannelIds] = await Promise.all([
      ProjectKolService.getProjectKolUniques(task.projectId, {
        platform: [KolPlatform.TWITTER],
      }),
      this.getPreviousTasksCandidatesUserNames(task.projectId),
    ])

    const allRatedChannelIds = [
      ...new Set([...projectRatedChannelIds, ...previousCandidatesChannelIds]),
    ]

    console.log(`[Twitter] 开始获取用户 ${source} 的关注列表`)
    const followingUsers = await twitter241Api.getFollowingsWithPagination(
      source,
      Number(TWITTER_SIMILAR_FOLLOWING_COUNT),
    )

    if (!followingUsers.length) {
      console.warn(`[Twitter] 用户 ${source} 没有关注列表`)
      return []
    }
    console.log(`[Twitter] 获取到 ${followingUsers.length} 个关注用户`)

    await Promise.all([
      this.upsertTwitterUsers([sourceUser, ...followingUsers]),
      this.upsertKolInfos([sourceUser, ...followingUsers]),
    ])

    const filteredUsers = followingUsers.filter(
      (user) => !allRatedChannelIds.includes(user.screen_name),
    )

    if (!filteredUsers.length) {
      console.log('[Twitter] 所有用户都已被评分')
      return []
    }

    console.log(`[Twitter] 过滤后剩余 ${filteredUsers.length} 个用户需要分析`)

    const highScoreUsers = await twitterDescriptionAnalysisService.batchAnalyzeDescriptions(
      filteredUsers.map((user) => ({
        screen_name: user.screen_name,
        description: user.description || '',
      })),
      banList,
      allowList,
      kolDescription,
    )

    if (!highScoreUsers.length) {
      console.log('[Twitter] 简介分析后不存在与源博主相似的用户')
      return []
    }

    console.log(`[Twitter] 简介分析后找到 ${highScoreUsers.length} 个与源博主相似的用户`)

    const conditions = {
      screenName: {
        in: highScoreUsers.map((user) => user.screen_name),
      },
      followersCount: {
        gte: params.minSubscribers ? Number(params.minSubscribers) : undefined,
        lte: params.maxSubscribers ? Number(params.maxSubscribers) : undefined,
      },
      statusesCount: {
        gte: 25,
      },
    }
    const finalUsers = await prisma.twitterUser.findMany({
      where: conditions,
      select: {
        screenName: true,
      },
    })

    if (!finalUsers.length) {
      console.log('[Twitter] 过滤后不存在与源博主相似的用户')
      return []
    }

    console.log(`[Twitter] 过滤后找到 ${finalUsers.length} 个与源博主相似的用户`)
    // find kol infos
    const kolInfos = await prisma.kolInfo.findMany({
      where: {
        platform: KolPlatform.TWITTER,
        platformAccount: {
          in: finalUsers.map((user) => user.screenName),
        },
      },
    })

    if (!kolInfos?.length) {
      console.log('[Twitter] kolInfos is empty')
      return []
    }
    await this.processSourceProjectKol(task.createdBy ?? '', source, projectId, id)

    return kolInfos.map((kol) => ({
      kolId: kol.id,
      platform: KolPlatform.TWITTER,
      platformId: kol.platformAccount || '',
      score: (highScoreUsers.find((u) => u.screen_name === kol.platformAccount)?.score || 0) / 100,
    }))
  }

  /**
   * 批量 upsert KolInfo 记录
   */
  private async upsertKolInfos(userResponses: Twitter241User[]): Promise<KolInfo[]> {
    try {
      if (!userResponses?.length) {
        console.log('userResponses is empty')
        return []
      }

      //TODO email handle
      const kolInfoData = userResponses.map((user) => ({
        platformAccount: user.screen_name,
        title: user.name,
        description: user.description,
        avatar: user.profile_image_url_https,
        platform: KolPlatform.TWITTER,
      }))

      const results = await prisma.$transaction(
        kolInfoData.map((data) =>
          prisma.kolInfo.upsert({
            where: {
              platform_platformAccount: {
                platform: data.platform,
                platformAccount: data.platformAccount,
              },
            },
            update: {
              title: data.title,
              description: data.description,
              avatar: data.avatar,
              updatedAt: new Date(),
            },
            create: {
              platform: data.platform,
              platformAccount: data.platformAccount,
              title: data.title,
              description: data.description,
              avatar: data.avatar,
            },
          }),
        ),
      )

      return results
    } catch (error) {
      console.error(`[Twitter] 批量 upsert KolInfo 失败:`, error)
      return []
    }
  }

  /**
   * 批量 upsert TwitterUser 记录
   */
  private async upsertTwitterUsers(userResponses: Twitter241User[]): Promise<TwitterUser[]> {
    try {
      if (!userResponses?.length) {
        console.log('userResponses is empty')
        return []
      }

      const twitterUserData = userResponses.map((user) => {
        return {
          id: user.id,
          restId: user.rest_id,
          screenName: user.screen_name,
          name: user.name,
          description: user.description,
          location: user.location,
          profileImageUrl: user.profile_image_url_https,
          profileBannerUrl: user.profile_banner_url,
          url: undefined,
          isProtected: user.is_protected || false,
          isBlueVerified: user.is_blue_verified || false,
          followersCount: user.followers_count || 0,
          friendsCount: user.friends_count || 0,
          statusesCount: user.statuses_count || 0,
          mediaCount: user.media_count || 0,
          listedCount: user.listed_count || 0,
          accountCreatedAt: user.created_at ? new Date(user.created_at) : null,
          rawData: {},
        }
      })

      const results = await prisma.$transaction(
        twitterUserData.map((data) =>
          prisma.twitterUser.upsert({
            where: { restId: data.restId },
            update: {
              screenName: data.screenName,
              description: data.description,
              location: data.location,
              profileImageUrl: data.profileImageUrl,
              profileBannerUrl: data.profileBannerUrl,
              url: data.url,
              isProtected: data.isProtected,
              isBlueVerified: data.isBlueVerified,
              followersCount: data.followersCount,
              friendsCount: data.friendsCount,
              statusesCount: data.statusesCount,
              mediaCount: data.mediaCount,
              listedCount: data.listedCount,
              accountCreatedAt: data.accountCreatedAt,
              rawData: data.rawData,
            },
            create: {
              id: data.id,
              restId: data.restId,
              screenName: data.screenName,
              name: data.name,
              description: data.description,
              location: data.location,
              profileImageUrl: data.profileImageUrl,
              profileBannerUrl: data.profileBannerUrl,
              url: data.url,
              isProtected: data.isProtected,
              isBlueVerified: data.isBlueVerified,
              followersCount: data.followersCount,
              friendsCount: data.friendsCount,
              statusesCount: data.statusesCount,
              mediaCount: data.mediaCount,
              listedCount: data.listedCount,
              accountCreatedAt: data.accountCreatedAt,
              rawData: data.rawData,
            },
          }),
        ),
      )

      return results
    } catch (error) {
      console.error(`[Twitter] 批量 upsert TwitterUser 失败:`, error)
      return []
    }
  }

  /**
   * 获取历史任务的candidatesUsername
   */
  private async getPreviousTasksCandidatesUserNames(projectId: string): Promise<string[]> {
    const previousTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    if (!previousTasks.length) {
      return []
    }
    const allCandidatesUserNames: string[] = []

    for (const task of previousTasks) {
      if (task.candidate && Array.isArray(task.candidate)) {
        // 如果candidates是数组，直接处理
        (
          task.candidate as Array<{
            kolId: string
            platform: KolPlatform
            platformId: string
            score: number
          }>
        )
          .filter((candidate) => candidate.platform === KolPlatform.TIKTOK)
          .forEach((candidate) => {
            if (candidate.platformId) {
              allCandidatesUserNames.push(candidate.platformId)
            }
          })
      }
    }

    return allCandidatesUserNames
  }

  private async processSourceProjectKol(
    userId: string,
    source: string,
    projectId: string,
    taskId: string,
  ) {
    const kol = await prisma.kolInfo.findFirst({
      where: {
        AND: [{ platformAccount: source }, { platform: KolPlatform.TWITTER }],
      },
    })

    if (kol) {
      await prisma.projectKol.upsert({
        where: {
          projectId_kolId: {
            projectId: projectId,
            kolId: kol.id,
          },
        },
        update: {
          attitude: ProjectKolAttitude.SUPERLIKE,
          lastSimilarAt: new Date(),
          similarTaskId: taskId,
          rateBy: userId,
        },
        create: {
          projectId: projectId,
          kolId: kol.id,
          similarTaskId: taskId,
          attitude: ProjectKolAttitude.SUPERLIKE,
          rateBy: userId,
          lastSimilarAt: new Date(),
        },
      })
    }
  }
}

export default TwitterService
