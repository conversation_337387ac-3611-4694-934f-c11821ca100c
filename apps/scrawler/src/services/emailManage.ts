import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import {
  <PERSON>ailFollowup,
  EmailFollowupThread,
  EmailFollowupThreadStatus,
  EmailPlan,
  EmailPlanStatus,
  EmailTemplate,
  Prisma,
  prisma,
} from '@repo/database'

export interface FollowupEmail {
  subject: string
  content: string
  daysAfter: number
}

export interface CreateFollowupParams {
  userId: string
  emailTemplateId: string
  followupEmails: FollowupEmail[]
}

export interface UpdateTemplateCCParams {
  templateId: string
  userId: string
  cc?: string[]
  bcc?: string[]
}

export interface CreateFollowupThreadParams {
  kolId: string
  kolEmail: string
  userId: string
  userEmail: string
  followupId: string
  threadId: string
  followupEmails: FollowupEmail[]
}

export interface CreateEmailPlanParams {
  userId: string
  from: string
  to: string
  subject: string
  content: string
  cc?: string[]
  bcc?: string[]
  threadId: string
  sendAt: Date
}

class EmailManageService {
  /**
   * 更新邮件模板的抄送列表
   */
  async updateTemplateCCList(params: UpdateTemplateCCParams): Promise<EmailTemplate> {
    const { templateId, userId, cc, bcc } = params

    // 验证模板存在且属于用户
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        createdBy: userId,
        deletedAt: null,
      },
    })

    if (!template) {
      throwError(StatusCodes.BAD_REQUEST, 'Email template not found or access denied')
    }

    // 更新抄送列表
    return await prisma.emailTemplate.update({
      where: { id: templateId },
      data: {
        cc: cc ?? template.cc,
        bcc: bcc ?? template.bcc,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * 获取邮件模板的抄送列表
   */
  async getTemplateCCList(
    templateId: string,
    userId: string,
  ): Promise<Pick<EmailTemplate, 'id' | 'cc' | 'bcc'> | null> {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
        createdBy: userId,
        deletedAt: null,
      },
      select: {
        id: true,
        cc: true,
        bcc: true,
      },
    })

    return template
  }

  /**
   * 创建或更新邮件跟进配置
   */
  async upsertEmailFollowup(params: CreateFollowupParams): Promise<EmailFollowup> {
    const { userId, emailTemplateId, followupEmails } = params

    // 验证模板存在
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: emailTemplateId,
        createdBy: userId,
        deletedAt: null,
      },
    })

    if (!template) {
      throw new Error('Email template not found or access denied')
    }

    // 使用 upsert 方法
    return await prisma.emailFollowup.upsert({
      where: {
        emailTemplateId: emailTemplateId,
      },
      update: {
        followupsEmails: followupEmails as unknown as Prisma.JsonArray,
        updatedAt: new Date(),
      },
      create: {
        userId,
        emailTemplateId,
        followupsEmails: followupEmails as unknown as Prisma.JsonArray,
      },
    })
  }

  /**
   * 获取邮件模板的跟进配置
   */
  async getEmailFollowup(templateId: string, userId: string): Promise<EmailFollowup | null> {
    return await prisma.emailFollowup.findFirst({
      where: {
        emailTemplateId: templateId,
        userId,
      },
    })
  }

  /**
   * 创建邮件跟进线程
   */
  async createFollowupThread(params: CreateFollowupThreadParams): Promise<EmailFollowupThread> {
    const { kolId, kolEmail, userId, userEmail, followupId, threadId, followupEmails } = params

    // 验证跟进配置存在
    const followup = await prisma.emailFollowup.findFirst({
      where: {
        id: followupId,
        userId,
      },
      include: {
        emailTemplate: true,
      },
    })

    if (!followup || !followup.emailTemplate) {
      throwError(StatusCodes.BAD_REQUEST, 'Email followup configuration not found')
    }

    // 创建跟进线程
    const thread = await prisma.emailFollowupThread.create({
      data: {
        kolId,
        kolEmail,
        userId,
        userEmail,
        followupId,
        threadId,
        followupEmails: followupEmails as unknown as Prisma.JsonArray,
        status: EmailFollowupThreadStatus.PENDING,
      },
    })

    // 创建邮件计划
    const now = new Date()
    const emailPlans = followupEmails.map((email) => {
      const scheduledSendAt = new Date(now)
      scheduledSendAt.setDate(scheduledSendAt.getDate() + email.daysAfter)

      return {
        userId,
        from: userEmail,
        to: kolEmail,
        subject: email.subject,
        content: email.content,
        cc: followup.emailTemplate.cc, // 抄送
        bcc: followup.emailTemplate.bcc, // 密送
        threadId: thread.id,
        status: EmailPlanStatus.PENDING,
        planTime: scheduledSendAt, // 计划发送时间 - 等待 Prisma 模型更新后启用
        sendTime: null, // 实际发送时间，初始为null - 等待 Prisma 模型更新后启用
        createdAt: now,
        updatedAt: now,
      }
    })

    if (emailPlans.length > 0) {
      await prisma.emailPlan.createMany({
        data: emailPlans,
      })
    }

    return thread
  }

  /**
   * 获取用户的所有跟进线程
   */
  async getUserFollowupThreads(
    userId: string,
    status?: EmailFollowupThreadStatus,
  ): Promise<EmailFollowupThread[]> {
    return await prisma.emailFollowupThread.findMany({
      where: {
        userId,
        ...(status && { status }),
      },
      include: {
        emailFollowup: {
          include: {
            emailTemplate: true,
          },
        },
        plans: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
  }

  /**
   * 更新跟进线程状态
   */
  async updateFollowupThreadStatus(
    threadId: string,
    userId: string,
    status: EmailFollowupThreadStatus,
  ): Promise<EmailFollowupThread> {
    const thread = await prisma.emailFollowupThread.findFirst({
      where: {
        id: threadId,
        userId,
      },
    })

    if (!thread) {
      throw new Error('Followup thread not found or access denied')
    }

    return await prisma.emailFollowupThread.update({
      where: { id: threadId },
      data: {
        status,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * 获取待发送的邮件计划
   * 获取90秒内需要发送的邮件计划
   */
  async getPendingEmailPlans(limit: number = 100): Promise<Array<{ id: string; userId: string }>> {
    const now = new Date()
    const futureTime = new Date(now.getTime() + 90 * 1000) // 90秒后的时间

    // 获取待发送的邮件计划
    const plansToSend = await prisma.emailPlan.findMany({
      where: {
        status: EmailPlanStatus.PENDING,
        planTime: {
          lte: futureTime, // 计划发送时间在90秒内
        },
      },
      select: {
        id: true,
        userId: true,
      },
      take: limit,
      orderBy: {
        planTime: 'asc', // 按计划发送时间升序排序
      },
    })

    return plansToSend
  }

  /**
   * 获取特定时间范围内待发送的邮件计划
   */
  async getPendingEmailPlansByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<Array<{ id: string; userId: string }>> {
    return await prisma.emailPlan.findMany({
      where: {
        status: EmailPlanStatus.PENDING,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        id: true,
        userId: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    })
  }

  /**
   * 更新邮件计划状态
   */
  async updateEmailPlanStatus(planId: string, status: EmailPlanStatus): Promise<EmailPlan> {
    return await prisma.emailPlan.update({
      where: { id: planId },
      data: {
        status,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * 更新邮件计划的实际发送时间
   * 注意：actualSentAt 字段需要在 Prisma schema 更新后启用
   */
  async updateEmailPlanSentTime(planId: string): Promise<EmailPlan> {
    // TODO: 等待 Prisma 模型更新后，取消注释下面的 actualSentAt 字段
    return await prisma.emailPlan.update({
      where: { id: planId },
      data: {
        sendTime: new Date(), // 设置实际发送时间 - 等待模型更新
        status: EmailPlanStatus.SENT, // 更新状态为已发送
        updatedAt: new Date(),
      },
    })
  }

  /**
   * 批量更新邮件计划的实际发送时间
   * 注意：actualSentAt 字段需要在 Prisma schema 更新后启用
   */
  async updateEmailPlansSentTime(planIds: string[]): Promise<void> {
    // TODO: 等待 Prisma 模型更新后，取消注释下面的 actualSentAt 字段
    await prisma.emailPlan.updateMany({
      where: {
        id: { in: planIds },
      },
      data: {
        sendTime: new Date(), // 设置实际发送时间 - 等待模型更新
        status: EmailPlanStatus.SENT,
        updatedAt: new Date(),
      },
    })
  }

  /**
   * 删除邮件跟进配置
   */
  async deleteEmailFollowup(followupId: string, userId: string): Promise<void> {
    const followup = await prisma.emailFollowup.findFirst({
      where: {
        id: followupId,
        userId,
      },
    })

    if (!followup) {
      throw new Error('Email followup not found or access denied')
    }

    // 软删除相关的线程和计划
    await prisma.$transaction([
      // 取消所有待发送的计划
      prisma.emailPlan.updateMany({
        where: {
          thread: {
            followupId,
          },
          status: EmailPlanStatus.PENDING,
        },
        data: {
          status: EmailPlanStatus.CANCELED,
          updatedAt: new Date(),
        },
      }),
      // 删除跟进配置
      prisma.emailFollowup.delete({
        where: { id: followupId },
      }),
    ])
  }

  /**
   * 获取模板的所有跟进线程统计
   */
  async getTemplateFollowupStats(templateId: string, userId: string) {
    const followup = await prisma.emailFollowup.findFirst({
      where: {
        emailTemplateId: templateId,
        userId,
      },
    })

    if (!followup) {
      return null
    }

    const threads = await prisma.emailFollowupThread.groupBy({
      by: ['status'],
      where: {
        followupId: followup.id,
      },
      _count: {
        id: true,
      },
    })

    const plans = await prisma.emailPlan.groupBy({
      by: ['status'],
      where: {
        thread: {
          followupId: followup.id,
        },
      },
      _count: {
        id: true,
      },
    })

    return {
      followup,
      threadStats: threads.reduce(
        (acc, curr) => {
          acc[curr.status] = curr._count.id
          return acc
        },
        {} as Record<EmailFollowupThreadStatus, number>,
      ),
      planStats: plans.reduce(
        (acc, curr) => {
          acc[curr.status] = curr._count.id
          return acc
        },
        {} as Record<EmailPlanStatus, number>,
      ),
    }
  }
}

export default new EmailManageService()
