import { handleUnknownError } from '@/common/errorHandler'
import {
  MILVUS_LIMIT,
  MILVUS_THRESHOLD,
  MILVUS_TOPK,
  TIKTOK_MILVUS_SEARCH_LIMIT,
  TIKTOK_MILVUS_SEARCH_TOPK,
  TIKTOK_SINGLE_VECTOR_SEARCH_RESULT_LIMIT,
  YOUTUBE_EMBEDDING_MIN_SCORE,
  YOUTUBE_MILVUS_SEARCH_LIMIT,
  YOUTUBE_MILVUS_SEARCH_TOPK,
} from '@/config/env'
import { milvusClient } from '@/infras/milvus'
import { createTaskRequest } from '@/types/request/similar.request.ts'
import { KeywordTaskParams } from '@/types/task'
import { KolPlatform, TaskType } from '@repo/database'
import {
  CollectionData,
  ConsistencyLevelEnum,
  DataType,
  FieldType,
  HybridSearchReq,
  MutationResult,
  RowData,
  SearchSimpleReq,
  SparseFloatVector,
} from '@zilliz/milvus2-sdk-node'
import { Mutex } from 'async-mutex'
import Bluebird from 'bluebird'

const client = milvusClient()
const collectionName = 'easykol_milvus_prod'
const mutex = new Mutex()

// 定义增强的 MilvusData 类型用于批量插入
export interface EnhancedMilvusData {
  // 主键和基本信息
  id?: string // milvus 生成
  userId: string // 唯一不可变
  account: string // 唯一可变
  platform: string // 平台
  nickname?: string // 昵称

  // 用户统计数据
  followerCount?: number
  averagePlayCount?: number
  lastPublishedTime?: number

  // 地理信息
  region?: string

  // 内容数据
  signature?: string
  videoTexts?: string

  // 邮箱
  email?: string

  // 向量数据
  denseVector: number[] // 稠密向量
  sparseVector?: SparseFloatVector // 稀疏向量

  meta?: Record<string, any>

  // 预留字段
  reserved1?: string
  reserved2?: string
  reserved3?: string

  createdAt?: number
  updatedAt?: number
}

// 定义全文搜索请求参数
export interface FullTextSearchParams {
  text: string
  platform?: string
  region?: string
  minFollowers?: number
  maxFollowers?: number
  minAveragePlayCount?: number
  lastPublishedAfter?: number
  uniqueIds?: string[]
  limit?: number
}

/**
 * 创建集合
 */
async function createCollection() {
  const fields: FieldType[] = [
    // 主键和基本信息
    {
      name: 'id',
      data_type: DataType.VarChar,
      is_primary_key: true,
      max_length: 100,
      autoID: true,
      description: '主键',
    },
    {
      name: 'userId',
      data_type: DataType.VarChar,
      max_length: 100,
      nullable: false,
      description: '用户ID',
    },
    {
      name: 'account',
      data_type: DataType.VarChar,
      max_length: 255,
      nullable: false,
      description: '账号',
    },
    {
      name: 'platform',
      data_type: DataType.VarChar,
      max_length: 50,
      nullable: false,
      description: '平台',
    },
    {
      name: 'nickname',
      data_type: DataType.VarChar,
      max_length: 255,
      nullable: true,
      description: '昵称',
    },

    // 用户统计数据
    { name: 'followerCount', data_type: DataType.Int64, nullable: true, description: '粉丝数' },
    {
      name: 'averagePlayCount',
      data_type: DataType.Int64,
      nullable: true,
      description: '平均播放量',
    },
    {
      name: 'lastPublishedTime',
      data_type: DataType.Int64,
      nullable: true,
      description: '最后发布时间',
    },

    // 地理信息
    {
      name: 'region',
      data_type: DataType.VarChar,
      max_length: 100,
      nullable: true,
      description: '地区',
    },

    // 内容数据
    {
      name: 'signature',
      data_type: DataType.VarChar,
      max_length: 1000,
      description: '签名',
      nullable: true,
    },
    {
      name: 'videoTexts',
      data_type: DataType.VarChar,
      max_length: 20000,
      description: '视频拼接后的文本',
      nullable: true,
    }, // 视频拼接后的文本

    // email
    {
      name: 'email',
      data_type: DataType.VarChar,
      max_length: 255,
      description: '邮箱',
      nullable: true,
    },

    // 向量数据
    {
      name: 'dense_vector',
      data_type: DataType.FloatVector,
      type_params: { dim: '3072' },
      description: '稠密向量',
    }, // 稠密向量
    { name: 'sparse_vector', data_type: DataType.SparseFloatVector, description: '稀疏向量' }, // 稀疏向量

    // 元数据
    { name: 'meta', data_type: DataType.JSON, nullable: true, description: '元数据' }, // 存储其他扩展字段

    // 预留字段
    {
      name: 'reserved1',
      data_type: DataType.VarChar,
      max_length: 1000,
      description: '预留字段1',
      nullable: true,
    },
    {
      name: 'reserved2',
      data_type: DataType.VarChar,
      max_length: 1000,
      description: '预留字段2',
      nullable: true,
    },
    {
      name: 'reserved3',
      data_type: DataType.VarChar,
      max_length: 1000,
      description: '预留字段3',
      nullable: true,
    },

    // 时间戳
    { name: 'createdAt', data_type: DataType.Int64, description: '创建时间' },
    { name: 'updatedAt', data_type: DataType.Int64, description: '更新时间' },
  ]

  await client.createCollection({
    collection_name: collectionName,
    description: 'Social media user data with enhanced metadata',
    fields,
  })
}

/**
 * 检查集合是否存在
 */
async function checkCollectionExists(): Promise<boolean> {
  const collections = await client.showCollections()
  return collections.data.some((collection: CollectionData) => collection.name === collectionName)
}

/**
 * 创建索引
 */
async function createIndex() {
  // 为稠密向量创建索引
  const denseIndexParams = {
    metric_type: 'COSINE',
    index_type: 'HNSW',
    params: JSON.stringify({ M: 32, efConstruction: 500 }),
  }

  await client.createIndex({
    index_name: 'idx_dense_vector',
    collection_name: collectionName,
    field_name: 'dense_vector',
    extra_params: denseIndexParams,
  })

  // 为稀疏向量创建索引
  const sparseIndexParams = {
    metric_type: 'IP',
    index_type: 'SPARSE_INVERTED_INDEX',
  }

  await client.createIndex({
    index_name: 'idx_sparse_vector',
    collection_name: collectionName,
    field_name: 'sparse_vector',
    extra_params: sparseIndexParams,
  })

  // 为常用过滤字段创建索引
  const scalarFields = [
    'userId',
    'account',
    'email',
    'platform',
    'region',
    'followerCount',
    'lastPublishedTime',
    'averagePlayCount',
  ]

  for (const field of scalarFields) {
    await client.createIndex({
      index_name: `idx_${field}`,
      collection_name: collectionName,
      field_name: field,
      index_type: 'INVERTED',
    })
  }
}

/**
 * 加载集合
 */
async function loadCollection() {
  await client.loadCollectionSync({
    collection_name: collectionName,
  })
}

/**
 * 初始化集合
 */
export async function initCollection() {
  await mutex.runExclusive(async () => {
    const exists = await checkCollectionExists()
    if (!exists) {
      console.log(`Collection ${collectionName} does not exist. Creating...`)
      await createCollection()
      await createIndex()
      await loadCollection()
      console.log(`Collection ${collectionName} created and loaded.`)
    } else {
      console.log(`Collection ${collectionName} already exists. Skipping creation.`)
    }
  })
}

/**
 * 断开连接
 */
export async function disconnect() {
  await mutex.runExclusive(async () => {
    try {
      await client.closeConnection()
      console.log('Disconnected from Milvus server.')
    } catch (error) {
      console.error(handleUnknownError(error, 'MilvusService.disconnect'))
    }
  })
}

/**
 * 批量插入增强的用户数据到Milvus集合（先删除后插入）
 * @param influencers EnhancedMilvusData[] - 要插入数组
 * @returns Promise<{deleteResult: MutationResult, insertResult: MutationResult}>
 * @throws Error 如果输入无效或操作失败
 */
export async function bulkInsertEnhancedWithDelete(
  influencers: EnhancedMilvusData[],
): Promise<{ deleteResult: MutationResult; insertResult: MutationResult }> {
  // 判空
  if (!influencers || influencers.length === 0) {
    throw new Error('Invalid input: influencers cannot be empty')
  }

  // 过滤掉稠密向量为空的数据
  influencers = influencers.filter((influencer) => {
    const hasEmbedding = influencer?.denseVector?.length > 0
    if (!hasEmbedding) {
      console.warn(`用户 ${influencer.id || '未知'} 的稠密向量为空，已过滤`)
    }
    return hasEmbedding
  })

  // 将 EnhancedMilvusData 转换为 RowData 类型，并处理字段长度限制
  const rowData = influencers
    .map((influencer, index) => {
      try {
        const data: RowData = {}

        if (!influencer.userId || !influencer.account || !influencer.platform) {
          console.warn(`用户索引 ${index} 缺少必填字段(userId/account/platform)，将被跳过`)
          return null
        }

        // 字段映射处理
        Object.entries(influencer).forEach(([key, value]) => {
          if (value === undefined) return
          if (key === 'denseVector') {
            data['dense_vector'] = value
            return
          }
          if (key === 'sparseVector') {
            const sparseVector = value as SparseFloatVector
            if (
              sparseVector &&
              Array.isArray((sparseVector as any).indices) &&
              Array.isArray((sparseVector as any).values) &&
              (sparseVector as any).indices.length === (sparseVector as any).values.length
            ) {
              data['sparse_vector'] = sparseVector
            } else {
              console.warn(`用户 ${influencer.id || '未知'} 的稀疏向量格式无效，将被忽略`)
            }
            return
          }

          if (typeof value === 'string') {
            const maxLengths = {
              videoTexts: 6000,
              signature: 600,
              reserved1: 600,
              reserved2: 600,
              reserved3: 600,
              default: 100,
            }
            const maxLength = maxLengths[key as keyof typeof maxLengths] || maxLengths.default
            if (!value.trim()) {
              data[key] = ''
              return
            }

            if (value.length > maxLength) {
              data[key] = value.slice(0, maxLength)
              console.warn(
                `用户 ${influencer.account || '未知'} 的 ${key} 字段已被截断，原长度: ${value.length} 截断后长度: ${data[key].length}`,
              )
            } else {
              data[key] = value
            }
            return
          }
          if (key === 'meta') {
            try {
              data[key] = typeof value === 'string' ? JSON.parse(value) : value
            } catch (e) {
              console.warn(
                `用户 ${influencer.account || '未知'} 的 meta 字段 JSON 解析失败，将忽略此字段`,
              )
            }
            return
          }

          data[key] = value
        })

        // 设置时间戳
        data['createdAt'] = influencer.createdAt || Math.floor(Date.now() / 1000)
        data['updatedAt'] = influencer.updatedAt || Math.floor(Date.now() / 1000)

        return data
      } catch (error) {
        console.error(`处理用户 ${influencer.account || '未知'} 数据时出错:`, error)
        return null
      }
    })
    .filter((data): data is RowData => data !== null)

  if (rowData.length === 0) {
    throw new Error('所有数据处理后均无效')
  }

  try {
    // 第一步：删除现有数据
    console.log('开始删除现有数据...')
    const deleteExpressions = rowData.map(
      (data) => `(account == "${data.account}" && platform == "${data.platform}")`,
    )
    const deleteExpression = deleteExpressions.join(' || ')

    const deleteResult = await client.deleteEntities({
      collection_name: collectionName,
      expr: deleteExpression,
      timeout: 60_000,
    })

    console.log(`删除操作完成，删除数量: ${deleteResult.delete_cnt}`)

    // 第二步：插入新数据
    console.log('开始插入新数据...')
    const batchSize = 100
    const results: MutationResult[] = []
    const failedRecords: { index: number; reason: string; data: any }[] = []

    for (let i = 0; i < rowData.length; i += batchSize) {
      const batch = rowData.slice(i, i + batchSize)
      console.log(`正在插入第 ${i + 1} 到 ${i + batch.length} 条数据，共 ${rowData.length} 条`)
      try {
        const result = await client.insert({
          collection_name: collectionName,
          data: batch,
          timeout: 60_000,
        })

        if (result.err_index && result.err_index.length > 0) {
          result.err_index.forEach((failedIndex) => {
            const absoluteIndex = i + Number(failedIndex)
            const failedRecord = rowData[Number(failedIndex)]
            const failureReason = result.status?.reason || '未知错误'

            failedRecords.push({
              index: absoluteIndex,
              reason: failureReason,
              data: {
                ...failedRecord,
                dense_vector: Array.isArray(failedRecord.dense_vector)
                  ? `[Vector with ${failedRecord.dense_vector.length} elements]`
                  : 'null',
                sparse_vector: failedRecord.sparse_vector
                  ? JSON.stringify(failedRecord.sparse_vector)
                  : 'null',
              },
            })

            console.error(`\n记录 ${absoluteIndex} 插入失败:`)
            console.error(`- 账号: ${failedRecord.account}`)
            console.error(`- 用户ID: ${failedRecord.userId}`)
            console.error(`- 错误原因: ${failureReason}`)
          })
        }

        results.push(result)
      } catch (batchError: unknown) {
        const errorMessage = batchError instanceof Error ? batchError.message : '批处理错误'
        console.error(`批次 ${i + 1} 到 ${i + batch.length} 处理失败:`, errorMessage)

        batch.forEach((record, batchIndex) => {
          const absoluteIndex = i + batchIndex
          failedRecords.push({
            index: absoluteIndex,
            reason: errorMessage,
            data: {
              ...record,
              dense_vector: Array.isArray(record.dense_vector)
                ? `[Vector with ${record.dense_vector.length} elements]`
                : 'null',
              sparse_vector: record.sparse_vector ? JSON.stringify(record.sparse_vector) : 'null',
            },
          })
        })
      }

      if (i + batchSize < rowData.length) {
        await new Promise((resolve) => setTimeout(resolve, 500))
      }
    }

    // 合并所有批次的结果
    const insertResult: MutationResult = {
      succ_index: results.flatMap((r) => r.succ_index || []),
      err_index: results.flatMap((r) => r.err_index || []),
      status: results[results.length - 1].status,
      IDs: results[0]?.IDs || [],
      acknowledged: results.every((r) => r.acknowledged),
      insert_cnt: results.reduce((sum, r) => sum + (parseInt(r.insert_cnt) || 0), 0).toString(),
      delete_cnt: '0',
      upsert_cnt: '0',
      timestamp: results[results.length - 1].timestamp,
    }

    // 打印失败统计信息
    if (failedRecords.length > 0) {
      console.error('\n插入失败统计:')
      console.error(`总失败数: ${failedRecords.length}`)

      const errorStats = failedRecords.reduce(
        (acc, curr) => {
          acc[curr.reason] = (acc[curr.reason] || 0) + 1
          return acc
        },
        {} as Record<string, number>,
      )

      console.error('\n错误原因统计:')
      Object.entries(errorStats).forEach(([reason, count]) => {
        console.error(`- ${reason}: ${count} 条记录`)
      })

      console.error('\n前5个失败记录示例:')
      failedRecords.slice(0, 5).forEach((record, index) => {
        console.error(`\n失败记录 ${index + 1}:`)
        console.error(`- 索引: ${record.index}`)
        console.error(`- 错误原因: ${record.reason}`)
        console.error(`- 数据结构:`)
        console.error(JSON.stringify(record.data, null, 2))
      })
    }

    console.log(
      `\n数据操作完成:\n- 删除记录数: ${deleteResult.delete_cnt}\n- 插入成功: ${insertResult.succ_index.length}\n- 插入失败: ${failedRecords.length}`,
    )

    return {
      deleteResult,
      insertResult,
    }
  } catch (error) {
    console.error('操作失败:', error)
    if (error instanceof Error) {
      console.error('错误类型:', error.constructor.name)
      console.error('错误消息:', error.message)
      console.error('错误堆栈:', error.stack)
    }
    throw error
  }
}

/**
 * 混合搜索（同时使用稠密向量和稀疏向量）
 */
export async function hybridSearch(
  denseEmbedding: number[],
  sparseEmbedding: SparseFloatVector, // 稀疏向量格式
  taskRequest: createTaskRequest | KeywordTaskParams,
  uniqueIds: string[] = [],
): Promise<
  Array<{
    id: string
    account: string
    platform: string
    nickname?: string
    followerCount?: number
    averagePlayCount?: number
    region?: string
    lastPublishedTime?: number
    email?: string
    score: number
  }>
> {
  console.log(`[hybridSearch] 开始混合搜索, platform: ${taskRequest.platform}`)

  // 构建基本过滤条件
  let condition = `platform == "${taskRequest.platform}"`

  // 添加 uniqueIds 过滤
  if (uniqueIds.length > 0) {
    condition += ` and id not in ${JSON.stringify(uniqueIds)}`
  }

  // 添加地区过滤
  if ('regions' in taskRequest && taskRequest.regions && taskRequest.regions.length > 0) {
    condition += ` and region in ${JSON.stringify(taskRequest.regions)}`
  }

  // 添加粉丝数过滤
  if ('minSubscribers' in taskRequest && taskRequest.minSubscribers) {
    condition += ` and followerCount >= ${taskRequest.minSubscribers}`
  }
  if ('maxSubscribers' in taskRequest && taskRequest.maxSubscribers) {
    condition += ` and followerCount <= ${taskRequest.maxSubscribers}`
  }

  // 添加平均播放量过滤
  if ('videosAverageViews' in taskRequest && taskRequest.videosAverageViews) {
    condition += ` and averagePlayCount >= ${taskRequest.videosAverageViews}`
  }
  if ('maxVideosAverageViews' in taskRequest && taskRequest.maxVideosAverageViews) {
    condition += ` and averagePlayCount <= ${taskRequest.maxVideosAverageViews}`
  }

  // 添加最近发布时间过滤
  if ('lastPublishedDays' in taskRequest && taskRequest.lastPublishedDays) {
    const timestamp = Math.floor(Date.now() / 1000) - taskRequest.lastPublishedDays * 24 * 60 * 60
    condition += ` and lastPublishedTime >= ${timestamp}`
  }

  // 添加邮箱过滤（如果需要只返回有邮箱的用户）
  // 如果需要过滤有邮箱的用户，可以添加: condition += ` and email != ""`

  console.log(`[hybridSearch] 过滤条件: ${condition}`)

  // 创建AnnSearchRequest实例
  const denseSearchReq = {
    data: [denseEmbedding],
    anns_field: 'dense_vector',
    param: {
      metric_type: 'COSINE',
      params: { nprobe: 10 },
    },
    limit: +500,
    topk: +500,
  }

  const sparseSearchReq = {
    data: [sparseEmbedding],
    anns_field: 'sparse_vector',
    param: {
      metric_type: 'IP',
      params: {},
    },
    limit: +500,
  }

  // 混合搜索请求
  const searchReq = {
    collection_name: collectionName,
    expr: condition,
    output_fields: [
      'id',
      'account',
      'platform',
      'nickname',
      'followerCount',
      'averagePlayCount',
      'region',
      'lastPublishedTime',
      'email',
      'signature',
      'videoTexts',
    ],
    data: [denseSearchReq, sparseSearchReq],
  }

  const searchResults = await client.hybridSearch(searchReq as HybridSearchReq)

  if (searchResults.status.code !== 0) {
    throw new Error(
      `混合搜索失败: code=${searchResults.status.error_code}, reason=${searchResults.status.reason}`,
    )
  }

  console.log(`[hybridSearch] 搜索完成, 找到 ${searchResults.results.length} 条记录`)

  const uniqueChannelsMap = new Map<
    string,
    {
      id: string
      account: string
      platform: string
      nickname?: string
      followerCount?: number
      averagePlayCount?: number
      region?: string
      lastPublishedTime?: number
      email?: string
      score: number
    }
  >()

  for (const result of searchResults.results.flat()) {
    // 添加数据有效性检查
    if (!result) {
      console.warn('[hybridSearch] 跳过无效的搜索结果:', result)
      continue
    }

    const {
      id,
      score,
      account,
      platform,
      nickname,
      followerCount,
      averagePlayCount,
      region,
      lastPublishedTime,
      email,
    } = result

    if (!id || !account) {
      console.warn('[hybridSearch] 跳过缺少必要字段的结果:', result)
      continue
    }

    if (!uniqueChannelsMap.has(id) || score > uniqueChannelsMap.get(id)!.score) {
      uniqueChannelsMap.set(id, {
        id,
        account,
        platform: platform || '',
        nickname,
        followerCount,
        averagePlayCount,
        region,
        lastPublishedTime,
        email,
        score,
      })
    }
  }

  const uniqueChannels = Array.from(uniqueChannelsMap.values()).sort((a, b) => b.score - a.score)
  console.log(`[hybridSearch] 完成处理, 最终结果数: ${uniqueChannels.length}`)

  return uniqueChannels
}

/**
 * 高级过滤搜索
 * 支持多种过滤条件的组合
 * 支持最多30个向量，每批10个处理
 */
export async function advancedFilterSearch(
  embeddings: number[][],
  filters: {
    taskType: TaskType
    platform?: string
    regions?: string[]
    minFollowers?: number
    maxFollowers?: number
    minAveragePlayCount?: number
    maxAveragePlayCount?: number
    lastPublishedAfter?: number // 时间戳
    userIds?: string[]
    uniqueIds?: string[]
    ratedIds?: string[]
  },
): Promise<Array<{ userId: string; account: string; score: number }>> {
  console.log(`[advancedFilterSearch] 开始高级过滤搜索`)

  if (!embeddings || embeddings.length === 0) {
    console.warn(`[advancedFilterSearch] 输入的embeddings为空，返回空结果`)
    return []
  }

  const validEmbeddings = embeddings.filter((e) => e && e.length > 0)
  if (validEmbeddings.length === 0) {
    console.warn(`[advancedFilterSearch] 输入的embeddings中没有有效向量，返回空结果`)
    return []
  }

  // 限制处理的向量数量为最多30个
  const maxVectors = Math.min(validEmbeddings.length, 30)
  console.log(`[advancedFilterSearch] 将处理 ${maxVectors} 个向量，分批进行`)

  const conditions: string[] = []
  let topk = +MILVUS_TOPK
  let limit = +MILVUS_LIMIT
  let minScore = 0.5

  if (filters.platform) {
    conditions.push(`platform == "${filters.platform}"`)
    switch (filters.platform) {
      case KolPlatform.TIKTOK:
        topk = +TIKTOK_MILVUS_SEARCH_TOPK
        limit = +TIKTOK_MILVUS_SEARCH_LIMIT
        minScore = +MILVUS_THRESHOLD
        break
      case KolPlatform.YOUTUBE:
        topk = +YOUTUBE_MILVUS_SEARCH_TOPK
        limit = +YOUTUBE_MILVUS_SEARCH_LIMIT
        minScore = +YOUTUBE_EMBEDDING_MIN_SCORE
        break
      default:
        break
    }
  }

  if (filters.regions && filters.regions.length > 0) {
    conditions.push(`region in ${JSON.stringify(filters.regions)}`)
  }

  if (filters.minFollowers) {
    conditions.push(`followerCount >= ${filters.minFollowers}`)
  }

  if (filters.maxFollowers) {
    conditions.push(`followerCount <= ${filters.maxFollowers}`)
  }

  if (filters.minAveragePlayCount) {
    conditions.push(`averagePlayCount >= ${filters.minAveragePlayCount}`)
  }

  if (filters.maxAveragePlayCount) {
    conditions.push(`averagePlayCount <= ${filters.maxAveragePlayCount}`)
  }

  if (filters.lastPublishedAfter) {
    conditions.push(`lastPublishedTime >= ${filters.lastPublishedAfter}`)
  }
  if (filters.ratedIds && filters.ratedIds.length > 0) {
    if (filters.platform === KolPlatform.YOUTUBE) {
      conditions.push(`userId not in ${JSON.stringify(filters.ratedIds)}`)
    } else {
      conditions.push(`account not in ${JSON.stringify(filters.ratedIds)}`)
    }
  }
  // in 条件一般是 uniqueIds 和 userIds 二选一
  if (filters.uniqueIds && filters.uniqueIds.length > 0) {
    conditions.push(`account in ${JSON.stringify(filters.uniqueIds)}`)
  }

  if (filters.userIds && filters.userIds.length > 0) {
    conditions.push(`userId in ${JSON.stringify(filters.userIds)}`)
  }

  const condition = conditions.join(' and ')
  console.log(`[advancedFilterSearch] Milvus 搜索条件: ${condition}`)

  const batchSize = 10
  const batchCount = Math.ceil(maxVectors / batchSize)
  console.log(`[advancedFilterSearch] 分成 ${batchCount} 批处理，每批最多 ${batchSize} 个向量`)

  let allResults: any[] = []

  for (let batchIndex = 0; batchIndex < batchCount; batchIndex++) {
    const startIdx = batchIndex * batchSize
    const endIdx = Math.min(startIdx + batchSize, maxVectors)
    const batchVectors = validEmbeddings.slice(startIdx, endIdx)

    console.log(
      `[advancedFilterSearch] 处理第 ${batchIndex + 1}/${batchCount} 批，包含 ${batchVectors.length} 个向量`,
    )

    const searchReq: SearchSimpleReq = {
      metric_type: 'COSINE',
      consistency_level: ConsistencyLevelEnum.Bounded,
      vectors: batchVectors,
      anns_field: 'dense_vector',
      timeout: 30_000,
      collection_name: collectionName,
      expr: condition,
      topk: topk,
      output_fields: ['userId', 'account', 'nickname', 'videoTexts'],
      limit: limit,
      params: {
        ef: 1500,
      },
    }

    try {
      const searchResults = await client.search(searchReq)

      if (searchResults.status.code !== 0) {
        console.error(
          `[advancedFilterSearch] 批次 ${batchIndex + 1} 搜索失败: code=${searchResults.status.error_code}, reason=${searchResults.status.reason}`,
        )
        continue // 跳过失败的批次，继续处理下一批
      }

      const batchResults = searchResults.results.flat()
      console.log(
        `[advancedFilterSearch] 批次 ${batchIndex + 1} 搜索完成, 找到 ${batchResults.length} 条记录`,
      )

      // 将当前批次结果添加到总结果中
      allResults = allResults.concat(batchResults)
    } catch (error) {
      console.error(`[advancedFilterSearch] 批次 ${batchIndex + 1} 搜索异常:`, error)
    }
  }

  console.log(`[advancedFilterSearch] 所有批次搜索完成, 共找到 ${allResults.length} 条记录`)

  // 应用过滤条件
  let filteredResults

  if (filters.taskType === TaskType.SIMILAR) {
    filteredResults = allResults.filter((result) => result.score >= minScore)
  } else {
    // 处理 aiSearch的限制
    filteredResults = allResults.slice(0, +TIKTOK_SINGLE_VECTOR_SEARCH_RESULT_LIMIT)
  }

  console.log(`[advancedFilterSearch] 过滤后找到 ${filteredResults.length} 条记录`)

  const uniqueChannelsMap = new Map<string, { userId: string; account: string; score: number }>()

  for (const result of filteredResults) {
    if (!result) {
      console.warn('[advancedFilterSearch] 跳过无效的搜索结果:', result)
      continue
    }

    const { userId, account, score } = result

    if (!userId || !account) {
      console.warn('[advancedFilterSearch] 跳过缺少必要字段的结果:', result)
      continue
    }

    if (!uniqueChannelsMap.has(userId) || score > uniqueChannelsMap.get(userId)!.score) {
      uniqueChannelsMap.set(userId, {
        userId: userId,
        account: account,
        score: score,
      })
    }
  }

  const uniqueChannels = Array.from(uniqueChannelsMap.values()).sort((a, b) => b.score - a.score)
  console.log(`[advancedFilterSearch] 完成处理, 最终结果数: ${uniqueChannels.length}`)

  // 添加更详细的日志
  if (uniqueChannels.length > 0) {
    console.log(
      '[advancedFilterSearch] 返回结果示例:',
      uniqueChannels.slice(0, 2),
      '最后一条结果:',
      uniqueChannels[uniqueChannels.length - 1],
    )
  }

  return uniqueChannels
}

/**
 * 通过 uniqueId 获取用户的稠密向量和稀疏向量
 * @param uniqueId 用户的唯一标识
 * @returns 包含稠密向量和稀疏向量的对象
 */
export async function getDenseAndSparseVectorsByUniqueId(
  uniqueId: string,
  platform: KolPlatform,
): Promise<{ denseVector: number[]; sparseVector: SparseFloatVector }> {
  try {
    console.log(`[getDenseAndSparseVectorsByUniqueId] 开始查询用户 ${uniqueId} 的向量数据`)

    // 使用query替代search，避免向量维度不匹配问题
    const queryResult = await client.query({
      collection_name: collectionName,
      filter: `account == "${uniqueId.replace(/"/g, '\\"')}" and platform == "${platform}"`,
      output_fields: ['dense_vector', 'sparse_vector'],
      limit: 1,
      timeout: 30_000,
    })

    if (queryResult.status.code !== 0) {
      console.warn(
        `[getDenseAndSparseVectorsByUniqueId] 查询用户向量失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
      )
      return { denseVector: [], sparseVector: [] }
    }

    if (!queryResult.data || queryResult.data.length === 0) {
      console.warn(`[getDenseAndSparseVectorsByUniqueId] 未找到用户 ${uniqueId} 的向量数据`)
      return { denseVector: [], sparseVector: [] }
    }

    const result = queryResult.data[0]
    const denseVector = result.dense_vector
    const sparseVector = result.sparse_vector

    if (!denseVector || !sparseVector) {
      console.warn(`[getDenseAndSparseVectorsByUniqueId] 用户 ${uniqueId} 的向量数据不完整`)
      return { denseVector: [], sparseVector: [] }
    }

    console.log(`[getVectorsByUniqueId] 成功获取用户 ${uniqueId} 的向量数据`)
    return { denseVector, sparseVector }
  } catch (error) {
    console.error(`[getVectorsByUniqueId] 获取用户 ${uniqueId} 的向量数据失败:`, error)
    return { denseVector: [], sparseVector: [] }
  }
}

/**
 * 通过 uniqueId 获取用户的稠密向量
 * @param uniqueId 用户的唯一标识
 * @returns 稠密向量
 */
export async function getDenseVectorsByUniqueId(
  uniqueId: string,
  platform: KolPlatform,
): Promise<number[]> {
  try {
    console.log(`[getVectorsByUniqueId] 开始查询用户 ${uniqueId} 的向量数据`)

    const queryResult = await client.query({
      collection_name: collectionName,
      filter: `account == "${uniqueId.replace(/"/g, '\\"')}" and platform == "${platform}"`,
      output_fields: ['dense_vector'],
      limit: 1,
      timeout: 30_000,
    })

    if (queryResult.status.code !== 0) {
      console.warn(
        `[getVectorsByUniqueId] 查询用户向量失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
      )
      return []
    }
    if (!queryResult.data || queryResult.data.length === 0) {
      console.warn(`[getVectorsByUniqueId] 未找到用户 ${uniqueId} 的向量数据`)
      return []
    }
    const result = queryResult.data[0]
    const denseVector = result.dense_vector

    if (!denseVector) {
      console.warn(`[getVectorsByUniqueId] 用户 ${uniqueId} 的向量数据不完整`)
      return []
    }
    return denseVector
  } catch (error) {
    console.error(`[getVectorsByUniqueId] 获取用户 ${uniqueId} 的向量数据失败:`, error)
    return []
  }
}

/**
 * 通过 userId 获取用户的稠密向量
 * @param userId 用户的唯一标识
 * @returns 稠密向量
 */
export async function getDenseVectorsByUserId(
  userId: string,
  platform: KolPlatform,
): Promise<number[]> {
  try {
    console.log(`[getVectorsByUserId] 开始查询用户 ${userId} 的向量数据`)

    const queryResult = await client.query({
      collection_name: collectionName,
      filter: `userId == "${userId}" and platform == "${platform}"`,
      output_fields: ['dense_vector'],
      limit: 1,
      timeout: 30_000,
    })

    if (queryResult.status.code !== 0) {
      console.warn(
        `[getVectorsByUserId] 查询用户向量失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
      )
      return []
    }
    if (!queryResult.data || queryResult.data.length === 0) {
      console.warn(`[getVectorsByUserId] 未找到用户 ${userId} 的向量数据`)
      return []
    }
    const result = queryResult.data[0]
    const denseVector = result.dense_vector

    if (!denseVector) {
      console.warn(`[getVectorsByUserId] 用户 ${userId} 的向量数据不完整`)
      return []
    }
    return denseVector
  } catch (error) {
    console.error(`[getVectorsByUserId] 获取用户 ${userId} 的向量数据失败:`, error)
    return []
  }
}

/**
 * 批量获取多个用户的稠密向量
 * @param uniqueIds 用户的uniqueId数组
 * @returns Promise<{account: string; denseVector: number[]}[]> 账号和向量的映射数组
 */
export async function batchGetDenseVectors(
  uniqueIds: string[],
  platform: KolPlatform,
): Promise<{ account: string; denseVector: number[] }[]> {
  if (!uniqueIds || uniqueIds.length === 0) {
    console.warn('[batchGetDenseVectorArray] 没有提供有效的uniqueIds，返回空数组')
    return []
  }

  console.log(`[batchGetDenseVectorArray] 开始直接批量获取 ${uniqueIds.length} 个用户的向量数据`)

  try {
    // 使用Map存储account到向量的映射
    const accountVectorMap = new Map<string, number[]>()

    // 初始化Map
    uniqueIds.forEach((id) => {
      accountVectorMap.set(id, [])
    })

    const batchSize = 50
    const batchTasks = []

    for (let startIdx = 0; startIdx < uniqueIds.length; startIdx += batchSize) {
      const endIdx = Math.min(startIdx + batchSize, uniqueIds.length)
      const batchIds = uniqueIds.slice(startIdx, endIdx)

      batchTasks.push({
        batchIds,
      })
    }

    await Bluebird.map(
      batchTasks,
      async ({ batchIds }) => {
        try {
          const idsString = JSON.stringify(batchIds).replace('[', '').replace(']', '')
          const filter = `account in [${idsString}] and platform == "${platform}"`

          const queryResult = await client.query({
            collection_name: collectionName,
            filter: filter,
            output_fields: ['account', 'dense_vector'],
            limit: batchIds.length,
            timeout: 60_000,
          })

          if (queryResult.status.code !== 0) {
            console.error(
              `[batchGetDenseVectorArray] 批量查询失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
            )
            return
          }

          if (!queryResult.data || queryResult.data.length === 0) {
            console.warn(
              `[batchGetDenseVectorArray] 未找到该批次的向量数据: ${batchIds.join(', ')}`,
            )
            return
          }

          // 直接将查询结果中的account和dense_vector存入映射表
          for (const item of queryResult.data) {
            if (item.account && item.dense_vector) {
              accountVectorMap.set(item.account, item.dense_vector)
            }
          }
        } catch (error) {
          console.error(`[batchGetDenseVectorArray] 处理批次查询时出错:`, error)
        }
      },
      { concurrency: 10 }, // 控制并发查询数量
    )

    // 将Map转换为数组返回
    const vectorArray = uniqueIds.map((id) => ({
      account: id,
      denseVector: accountVectorMap.get(id) || [],
    }))

    // 统计有效向量数量
    const validVectorsCount = vectorArray.filter((vector) => vector.denseVector.length > 0).length
    console.log(
      `[batchGetDenseVectorArray] 成功获取 ${validVectorsCount}/${uniqueIds.length} 个用户的向量数据`,
    )

    return vectorArray
  } catch (error) {
    console.error('[batchGetDenseVectorArray] 批量获取向量数据失败:', error)
    throw error
  }
}

/**
 * 根据条件查询Milvus集合中的数据
 * @param collectionName 集合名称
 * @param filter 查询过滤表达式
 * @param outputFields 需要返回的字段，默认返回所有字段
 * @param limit 返回结果数量限制，默认100
 * @param offset 结果偏移量，默认0
 * @returns 查询结果
 */
export async function queryByFilter(
  collectionName: string,
  filter: string,
  outputFields: string[] = [],
  limit: number = 100,
  offset: number = 0,
): Promise<any[]> {
  console.log(`[queryByFilter] 开始查询集合 ${collectionName}, 过滤条件: ${filter}`)

  try {
    // 构建查询请求
    const queryRequest = {
      collection_name: collectionName,
      filter: filter,
      output_fields: outputFields.length > 0 ? outputFields : undefined,
      limit: limit,
      offset: offset,
      consistency_level: ConsistencyLevelEnum.Bounded, // 可根据需要调整一致性级别
    }

    // 执行查询
    const queryResult = await client.query(queryRequest)

    // 检查查询是否成功
    if (queryResult.status.code !== 0) {
      throw new Error(
        `查询失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
      )
    }

    // 检查查询结果
    if (!queryResult.data || queryResult.data.length === 0) {
      console.log(`[queryByFilter] 未找到符合条件的数据`)
      return []
    }

    console.log(`[queryByFilter] 查询成功，找到 ${queryResult.data.length} 条记录`)

    return queryResult.data
  } catch (error) {
    console.error(`[queryByFilter] 查询出错:`, error)
    throw error
  }
}
