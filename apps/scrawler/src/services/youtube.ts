import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import { Channel } from '@/api/@types/rapidapi/Youtube.ts'
import RapidApiStatsCollector from '@/api/ApiCallStat.ts'
import { logFilter } from '@/api/util'
import YoutubeApi from '@/api/youtube.ts'
import {
  CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
  YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS,
  YOUTUBE_FETCH_CHANNEL_CONCURRENCY,
  YOUTUBE_RELATED_VIDEO_COUNT,
  YOUTUBE_USER_VIDEO_COUNT,
} from '@/config/env.ts'
import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { EmailFields, createYtbKolByIdOrName } from '@/services/kolInfo.service'
import { PublicationStatsService } from '@/services/publicationStats.service'
import { findCurrentTasksByProjectId } from '@/services/similar.ts'
import { generateEmbeddingsForYoutube, getEmbedding } from '@/services/worker/embedding.ts'
import { YoutubeHashTagBreakResult } from '@/types/breakResult'
import { EmailSourceType } from '@/types/email'
import { ProjectConfig } from '@/types/project'
import {
  SimilarTaskParams,
  YoutubeHashTagBreakTaskParams,
  YoutubeHashtagBreakTaskResult,
  YoutubeSearchInputBreakTaskParams,
  YoutubeSearchInputBreakTaskResult,
} from '@/types/task.ts'
import { YoutubeTaskResult } from '@/types/task/youtubeTask'
import { GetTaskParams } from '@/types/taskParams'
import { getUnixTimestamp, parsePublishedTime } from '@/utils/date.ts'
import {
  KolInfo,
  KolPlatform,
  Prisma,
  ProjectKolAttitude,
  TaskType,
  YouTubeChannel,
  prisma,
} from '@repo/database'
import assert from 'assert'
import Bluebird from 'bluebird'
import Bull from 'bull'
import dayjs from 'dayjs'
import { BaseChannel } from 'youtubei'
import { analyzeVisualSimilarityService } from './aiTools/visualSimilarity'
import EmailService from './email'
import { getExcludeIds } from './excludeList'
import {
  EnhancedMilvusData,
  advancedFilterSearch,
  bulkInsertEnhancedWithDelete,
  getDenseVectorsByUserId,
} from './milvus.service'
import { ProjectKolService } from './projectKol.service'

const HASHTAG_DEFAULT_LANG = 'en'
const HASHTAG_DEFAULT_GEO = 'US'

class YoutubeService {
  private static instance: YoutubeService

  static getInstance(): YoutubeService {
    if (!YoutubeService.instance) {
      YoutubeService.instance = new YoutubeService()
    }
    return YoutubeService.instance
  }

  public async createKolInfosForChannels(
    userId: string | undefined,
    channelIds: string[],
  ): Promise<KolInfo[]> {
    if (!channelIds || channelIds.length === 0) {
      throw new Error('没有提供频道 ID')
    }

    // // 查询数据库中已经存在的记录
    // const existingKolInfos = await prisma.kolInfo.findMany({
    //   where: {
    //     platformAccount: {
    //       in: channelIds,
    //     },
    //     platform: KolPlatform.YOUTUBE,
    //   },
    // })

    // // 获取已经存在的 channelId
    // const existingChannelIds = existingKolInfos.map((kolInfo) => kolInfo.platformAccount)

    // // 过滤出需要创建的 channelIds
    // const channelIdsToCreate = channelIds.filter(
    //   (channelId) => !existingChannelIds.includes(channelId),
    // )

    const createdKolInfos = await Bluebird.map(
      channelIds,
      async (channelId) => {
        try {
          const kolInfo = await createYtbKolByIdOrName(userId, channelId)
          if (kolInfo) {
            console.log(`已为 channelId: ${channelId} 创建 KOL 信息`)
            return kolInfo
          } else {
            console.warn(`为 channelId: ${channelId} 创建 KOL 信息失败`)
            return null
          }
        } catch (error) {
          console.error(`为 channelId: ${channelId} 创建 KOL 信息时出错`, error)
          Sentry.captureException(error)
          return null
        }
      },
      { concurrency: 30 },
    )

    // 合并新创建的 KOL 和已经存在的 KOL 信息，过滤掉 null 值
    return createdKolInfos.filter(Boolean) as KolInfo[]
  }

  public async superlikeSourceChannel(params: SimilarTaskParams, taskId: string): Promise<void> {
    if (params?.source && params.projectId) {
      const matchingKol = await prisma.kolInfo.findFirst({
        where: {
          platformAccount: params.source,
        },
      })

      if (matchingKol) {
        await prisma.projectKol.upsert({
          where: {
            projectId_kolId: {
              projectId: params.projectId,
              kolId: matchingKol.id,
            },
          },
          update: {
            attitude: ProjectKolAttitude.SUPERLIKE,
            lastSimilarAt: new Date(),
            similarTaskId: taskId,
          },
          create: {
            projectId: params.projectId,
            kolId: matchingKol.id,
            similarTaskId: taskId,
            attitude: ProjectKolAttitude.SUPERLIKE,
            rateBy: 'system',
            lastSimilarAt: new Date(),
          },
        })
      }
    }
  }

  public async createKolById(
    channelId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<KolInfo> {
    const exist = await prisma.kolInfo.findFirst({
      where: {
        platformAccount: channelId,
        platform: KolPlatform.YOUTUBE,
      },
    })
    if (exist) {
      return exist
    }
    const channel = await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
    if (!channel) {
      throw new Error('channel not found: ' + channelId)
    }
    return prisma.kolInfo.create({
      data: {
        title: channel.title,
        description: channel.description,
        links: channel.links
          ? channel.links.map((i) => (i.link.startsWith('https://') ? i.link : `https://${i.link}`))
          : [],
        email: channel.email,
        historyEmails: channel.email ? [channel.email] : [],
        platformAccount: channel.channelId,
        platform: KolPlatform.YOUTUBE,
        avatar: channel?.avatar[0]?.url ?? '',
        emailUpdatedAt: new Date(),
      },
    })
  }

  public async youtubeProcessHashTagBreakJob(
    taskParams: YoutubeHashTagBreakTaskParams,
    taskId: string,
  ): Promise<YoutubeHashtagBreakTaskResult> {
    console.log(`[instagramProcessHashTagBreakJob] 开始处理标签 ${taskParams.tag}`)
    const collector = new RapidApiStatsCollector()
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的 YouTube 用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.YOUTUBE],
      })
      console.log(
        `[youtubeProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的频道`,
      )

      const options = {
        ratedChannelIds: projectKolUniques || [],
      }

      const result: YoutubeHashTagBreakResult = await this.getChannelsFromHashtag(
        taskParams.tag,
        taskParams.paginationToken,
        taskParams.maxVideoCount ?? 100,
        taskParams.currentVideoCount ?? 0,
        options,
        collector,
      )
      const { channelIds } = result
      const channels = await Bluebird.map(
        channelIds,
        async (channelId) => {
          try {
            return await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +YOUTUBE_FETCH_CHANNEL_CONCURRENCY },
      )

      let validChannels = channels.filter((channel) => channel !== null)
      // 过滤 1000 粉丝以上
      validChannels = logFilter(validChannels, (channel) => channel.subscriberCount > 1000)
      // 处理kolInfo以及insUserInfo数据
      await Promise.all([
        this.upsertKolInfo(task.createdBy!, validChannels),
        this.processAndUpsertChannels(validChannels),
      ])

      const response: YoutubeHashtagBreakTaskResult = {
        taskId: taskId,
        tag: taskParams.tag,
        channelIds: result.channelIds,
        paginationToken: result.paginationToken,
        hasMore: result.hasMore,
        total: result.total,
        progress: result.progress,
      }

      console.log(
        `[youtubeProcessHashTagBreakJob] 处理完成，共处理 ${result.channelIds.length} 个频道`,
      )
      return response
    } catch (error) {
      console.error(`[youtubeProcessHashTagBreakJob] 处理标签 ${taskParams.tag} 时出错:`, error)
      throw error
    }
  }

  public async youtubeProcessSearchInputBreakJob(
    taskParams: YoutubeSearchInputBreakTaskParams,
    taskId: string,
  ): Promise<YoutubeSearchInputBreakTaskResult> {
    console.log(`[youtubeProcessSearchInputBreakJob] 开始处理搜索词 ${taskParams.searchInput}`)
    const collector = new RapidApiStatsCollector()
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('task not found')
    }
    try {
      // 获取该项目下已经评价过的 YouTube 用户
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.YOUTUBE],
      })
      console.log(
        `[youtubeProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 个该项目下已经评价过的频道`,
      )

      const options = {
        ratedChannelIds: projectKolUniques || [],
      }
      const queryParams = {
        geo: taskParams.geo,
        lang: taskParams.lang,
        duration: taskParams.duration,
        sort_by: taskParams.sort_by,
        upload_date: taskParams.upload_date,
      }
      const result: YoutubeHashTagBreakResult = await this.getChannelsFromSearch(
        taskParams.searchInput,
        taskParams.paginationToken,
        taskParams.maxVideoCount ?? 100,
        taskParams.currentVideoCount ?? 0,
        taskParams.total ?? 0,
        options,
        queryParams,
        collector,
      )
      const { channelIds } = result
      const channels = await Bluebird.map(
        channelIds,
        async (channelId) => {
          try {
            return await YoutubeApi.getInstance().getChannelWithVideos(channelId, collector)
          } catch (err) {
            Sentry.captureException(err)
            console.log(err)
            return null
          }
        },
        { concurrency: +YOUTUBE_FETCH_CHANNEL_CONCURRENCY },
      )

      let validChannels = channels.filter((channel) => channel !== null)
      validChannels = logFilter(validChannels, (channel) => channel.subscriberCount > 1000)
      // 处理kolInfo以及insUserInfo数据
      await Promise.all([
        this.upsertKolInfo(task.createdBy!, validChannels),
        this.processAndUpsertChannels(validChannels),
      ])

      const response: YoutubeSearchInputBreakTaskResult = {
        taskId: taskId,
        searchInput: taskParams.searchInput,
        channelIds: result.channelIds,
        paginationToken: result.paginationToken,
        hasMore: !!result.paginationToken,
        total: result.total,
        progress: result.progress,
      }

      console.log(
        `[youtubeProcessSearchInputBreakJob] 处理完成，共处理 ${result.channelIds.length} 个频道`,
      )
      return response
    } catch (error) {
      console.error(
        `[youtubeProcessSearchInputBreakJob] 处理搜索词 ${taskParams.searchInput} 时出错:`,
        error,
      )
      throw error
    }
  }

  private async getValidDenseVector(
    sourceChannel: Channel,
    ytbVideoIds: string[] | undefined,
    sourceIds: string[],
    collector: RapidApiStatsCollector,
  ): Promise<number[]> {
    // 1. 尝试从视频ID获取向量
    let denseVector: number[] = []

    if (ytbVideoIds && ytbVideoIds.length > 0) {
      try {
        const ytbVideos = await Bluebird.map(
          ytbVideoIds,
          async (videoId) => YoutubeApi.getInstance().getVideo(videoId, collector),
          { concurrency: 10 },
        )

        // 确保过滤后的数组只包含非空VideoDetail对象
        const filteredVideos: VideoDetail[] = ytbVideos.filter(
          (video): video is VideoDetail => !!video,
        )

        if (!filteredVideos?.length) {
          throw new Error('no videos found')
        }

        const allText = filteredVideos
          .flatMap((video) => [video.title || '', video.description || ''])
          .join(' ')

        if (!allText?.length) {
          throw new Error('no concat text found')
        }

        denseVector = await getEmbedding(allText)
      } catch (error) {
        console.log('failed to generate embedding from videos:', error)
        denseVector = await getDenseVectorsByUserId(sourceIds[0], KolPlatform.YOUTUBE)
      }
    } else {
      denseVector = await getDenseVectorsByUserId(sourceIds[0], KolPlatform.YOUTUBE)
    }

    // 2. 验证向量有效性，必要时生成新的
    if (denseVector?.length) {
      console.log(`[youtubeProcessSimilarJob] denseVector is not empty, use denseVector`)
      return denseVector
    }

    console.log(
      `[youtubeProcessSimilarJob] denseVector is empty, generate embeddings for source channel`,
    )
    const embeddings = await generateEmbeddingsForYoutube([sourceChannel])

    if (embeddings?.length) {
      return embeddings[0]
    }

    throw new Error('no valid dense vector for source channel')
  }

  public async youtubeProcessSimilarJob(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const { ytbVideoIds, source, channelId } = job.data.params as SimilarTaskParams
    const collector = new RapidApiStatsCollector()

    const ytbChannelId =
      channelId ?? source ?? (await YoutubeApi.getInstance().getYoutubeChannelId(params.source))

    if (!ytbChannelId) {
      throw new Error('no source id found,task failed')
    }

    //project,candidate,exclude rated channel Ids
    const [projectRatedChannelIds, previousCandidatesChannelIds, excludeChannelIds] =
      await metrics.withPhase('get_project_all_rated_channel_ids', () =>
        Promise.all([
          ProjectKolService.getProjectKolUniques(task.projectId, {
            platform: [KolPlatform.YOUTUBE],
          }),
          this.getPreviousTasksCandidatesChannelIds(task.projectId),
          getExcludeIds(task.createdBy!, KolPlatform.YOUTUBE),
        ]),
      )
    const allRatedChannelIds = [
      ...new Set([
        ...projectRatedChannelIds,
        ...previousCandidatesChannelIds,
        ...excludeChannelIds,
      ]),
    ]
    metrics.set('project_all_rated_channel_count', allRatedChannelIds.length)

    // source channel
    const sourceChannel = await metrics.withPhase('get_source_channel', () =>
      YoutubeApi.getInstance().getChannelWithVideos(ytbChannelId, collector),
    )
    if (!sourceChannel?.videos?.length) {
      throw new Error('no videos found for source channel')
    }

    // related channels
    let relatedChannels: Channel[] = []
    if (!ytbVideoIds?.length) {
      console.log(`[youtubeProcessSimilarJob] get related channels by latest videos`)
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByRapidApi(sourceChannel, params.regions, collector),
      )
    } else {
      console.log(`[youtubeProcessSimilarJob] get related channels by videoIds`)
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByVideoIds(ytbVideoIds, params.regions, collector),
      )
    }

    if (!relatedChannels?.length) {
      throw new Error('no related channels found')
    }
    metrics.set('source_channel_related_channels_count', relatedChannels.length)

    // 确保源频道在列表中，并过滤掉已评价的频道
    const filteredRelatedChannels = relatedChannels.filter(
      (channel) => channel.channelId && !allRatedChannelIds.includes(channel.channelId),
    )
    metrics.set('after_rated_filter_related_channels_count', filteredRelatedChannels.length)

    if (!filteredRelatedChannels.some((c) => c.channelId === sourceChannel.channelId)) {
      filteredRelatedChannels.unshift(sourceChannel)
    }

    //upsert kol info and channel vectors
    await metrics.withPhase('upsert_kol_info_and_channel_vectors', async () =>
      Promise.all([
        this.upsertKolInfo(task.createdBy ?? undefined, filteredRelatedChannels),
        this.upsertChannelInfoAndVectors(filteredRelatedChannels),
      ]),
    )

    const validDenseVector = await metrics.withPhase('get_valid_dense_vector', () =>
      this.getValidDenseVector(sourceChannel, ytbVideoIds, [ytbChannelId], collector),
    )
    // add source channel
    allRatedChannelIds.push(ytbChannelId)
    const filter = {
      taskType: TaskType.SIMILAR,
      platform: KolPlatform.YOUTUBE,
      regions: params.regions,
      minFollowers: params.minSubscribers,
      maxFollowers: params.maxSubscribers,
      minAveragePlayCount: params.videosAverageViews,
      maxAveragePlayCount: params.maxVideosAverageViews,
      lastPublishedAfter: Number(YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS)
        ? Math.floor(Date.now() / 1000) - Number(YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS) * 24 * 60 * 60
        : undefined,
      ratedIds: allRatedChannelIds,
    }

    const vectorSearchResult = await metrics.withPhase('vector_search', () =>
      advancedFilterSearch([validDenseVector], filter),
    )

    if (!vectorSearchResult?.length) {
      return []
    }
    metrics.set('vector_search_result_count', vectorSearchResult.length)

    // Query KOL info with database filters
    const kols = await metrics.withPhase('db_query_kol_info', () =>
      prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          youtubeChannel: {
            channelId: {
              in: vectorSearchResult.map((i) => i.userId),
            },
          },
        },
        include: {
          youtubeChannel: true,
        },
      }),
    )

    if (!kols?.length) {
      return []
    }

    metrics.set('final_kols_count', kols.length)
    metrics.set('rapid_api_stats', collector.getStats())

    await YoutubeService.getInstance().superlikeSourceChannel(params, id)

    // Build final result
    const result = kols.map((kol) => ({
      kolId: kol.id,
      platform: kol.platform,
      platformId: kol.platformAccount || '',
      score: vectorSearchResult.find((i) => i.userId === kol.platformAccount)?.score ?? 0.0,
    }))

    return result
  }

  public async youtubeProcessSimilarJobByVisualSimilarity(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data as IEmbeddingTask
    const params = job.data.params as GetTaskParams<'SIMILAR'>
    const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
    const { ytbVideoIds, source, channelId } = job.data.params as SimilarTaskParams
    const collector = new RapidApiStatsCollector()

    const ytbChannelId =
      channelId || source || (await YoutubeApi.getInstance().getYoutubeChannelId(params.source))

    if (!ytbChannelId) {
      throw new Error('no source id found, task failed')
    }

    const project = await prisma.project.findFirstOrThrow({
      where: { id: task.projectId, deletedAt: null },
    })
    const projectCfg = (project.config ?? {}) as Partial<ProjectConfig>

    // rated,candidate,exclude channel Ids
    const [projectRatedChannelIds, previousCandidatesChannelIds, excludeChannelIds] =
      await metrics.withPhase('get_project_all_rated_channel_ids', () =>
        Promise.all([
          ProjectKolService.getProjectKolUniques(task.projectId, {
            platform: [KolPlatform.YOUTUBE],
          }),
          this.getPreviousTasksCandidatesChannelIds(task.projectId),
          getExcludeIds(task.createdBy!, KolPlatform.YOUTUBE),
        ]),
      )
    const allRatedChannelIds = [
      ...new Set([
        ...projectRatedChannelIds,
        ...previousCandidatesChannelIds,
        ...excludeChannelIds,
      ]),
    ]
    metrics.set('project_all_rated_channel_count', allRatedChannelIds.length)

    // source channel
    const sourceChannel = await metrics.withPhase('get_source_channel', () =>
      YoutubeApi.getInstance().getChannelWithVideos(ytbChannelId, collector),
    )
    if (!sourceChannel?.videos?.length) {
      throw new Error('no videos found for source channel')
    }

    // get 3 elements
    const kolDescription = params.kolDescription ?? projectCfg.kolDescription ?? ''
    const allowList = params.allowList ?? projectCfg.allowList ?? []
    const banList = params.banList ?? projectCfg.banList ?? []

    metrics.set('three_elements', {
      kolDescription,
      allowList,
      banList,
    })

    // get related channels
    let relatedChannels: Channel[] = []
    if (!ytbVideoIds?.length) {
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByRapidApi(sourceChannel, params.regions, collector),
      )
    } else {
      relatedChannels = await metrics.withPhase('get_related_channels', () =>
        this.getRelatedChannelsByVideoIds(ytbVideoIds, params.regions, collector),
      )
    }
    if (!relatedChannels?.length) {
      throw new Error('no related channels found')
    }
    metrics.set('source_channel_related_channels_count', relatedChannels.length)

    const filteredRelatedChannels = relatedChannels.filter(
      (channel) => channel.channelId && !allRatedChannelIds.includes(channel.channelId),
    )

    if (filteredRelatedChannels.length === 0) {
      throw new Error('no related channels found after rated filter')
    }
    metrics.set('after_rated_filter_related_channels_count', filteredRelatedChannels.length)

    if (!filteredRelatedChannels.some((c) => c.channelId === sourceChannel.channelId)) {
      filteredRelatedChannels.unshift(sourceChannel)
    }

    await metrics.withPhase(
      'upsert_kol_info_and_channel_vectors',
      async () =>
        await Promise.all([
          this.upsertKolInfo(task.createdBy ?? undefined, filteredRelatedChannels),
          this.processAndUpsertChannels(filteredRelatedChannels),
        ]),
    )

    const visualAnalysisInput = filteredRelatedChannels
      .filter((channel) => channel.videos && channel.videos.length > 0)
      .map((channel) => ({
        username: channel.channelHandle,
        posts: channel.videos.slice(0, 6),
      }))

    // visual similarity analysis
    const visualSimilarityResult = await metrics.withPhase(
      'analyze_visual_similarity',
      async () => {
        const batchSize = 10
        const batches = Array.from(
          { length: Math.ceil(visualAnalysisInput.length / batchSize) },
          (_, i) => visualAnalysisInput.slice(i * batchSize, (i + 1) * batchSize),
        )

        const analysisResults = await Bluebird.map(
          batches,
          async (batch, batchIndex) => {
            try {
              return await analyzeVisualSimilarityService.analyzeVisualSimilarity(batch, {
                kolDescription,
                allowList,
                banList,
              })
            } catch (error) {
              console.error(`process batch ${batchIndex + 1} error:`, error)
              return []
            }
          },
          { concurrency: 50 },
        )
        return analysisResults.flat()
      },
    )

    // filter valid results
    const validResults = visualSimilarityResult.filter(
      (result: any) => result.score === 100 && result.username !== sourceChannel.channelHandle,
    )

    if (validResults.length === 0) {
      return []
    }

    metrics.set('after_visual_similarity_filter_count', validResults.length)
    metrics.set('visual_results', visualSimilarityResult)

    const channelHandleToId = new Map(
      filteredRelatedChannels.map((c) => [c.channelHandle, c.channelId]),
    )
    const channelScores = validResults
      .map((result: any) => ({
        id: channelHandleToId.get(result.username) || '',
        score: result.score / 100,
      }))
      .filter((item: { id: string }) => item.id)

    const kols = await metrics.withPhase('db_query_kol_info', () =>
      prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          youtubeChannel: {
            channelId: {
              in: channelScores.map((i) => i.id),
            },
            numericSubscriberCount: {
              gte: params.minSubscribers,
              lte: params.maxSubscribers,
            },
            videosAverageViewCount: {
              gte: params.videosAverageViews,
              lte: params.maxVideosAverageViews,
            },
            country: params.regions?.length ? { in: params.regions } : undefined,
            lastPublishedTime: {
              gte: dayjs().subtract(+YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS, 'day').unix(),
            },
          },
        },
        include: { youtubeChannel: true },
      }),
    )

    metrics.set('after_db_filter_count', kols.length)
    metrics.set('final_kols_count', kols.length)
    metrics.set('rapid_api_stats', collector.getStats())

    await YoutubeService.getInstance().superlikeSourceChannel(params, id)

    return kols
      .map((kol: any) => ({
        kolId: kol.id,
        platform: kol.platform,
        platformId: kol.platformAccount || '',
        score: channelScores.find((i) => i.id === kol.platformAccount)?.score || 0.0,
      }))
      .sort((a, b) => b.score - a.score)
  }

  private async getRelatedChannelsByRapidApi(
    sourceChannel: Channel,
    regions: string[] | undefined,
    collector?: RapidApiStatsCollector,
  ): Promise<Channel[]> {
    const videos = sourceChannel.videos.slice(0, YOUTUBE_USER_VIDEO_COUNT)
    if (videos.length == 0) {
      throw new Error('[rapidapi]cannot get videos for source channel: ' + sourceChannel.channelId)
    }
    if (videos.length < YOUTUBE_USER_VIDEO_COUNT) {
      console.warn(
        `[rapidapi]get videos for source channel ${sourceChannel.channelId} is insufficient, got ${videos.length}`,
      )
    }

    // concurrent get related videos
    const allRelatedVideos = await Bluebird.map(
      videos.map((i) => i.videoId),
      async (videoId) => {
        try {
          const related = await YoutubeApi.getInstance().getVideoRelatedVideo(
            videoId,
            YOUTUBE_RELATED_VIDEO_COUNT,
            collector,
          )

          console.log(
            `[rapidapi]get ${related.length} videos by source channel ${sourceChannel.channelId}'s source video ${videoId}`,
          )
          return related.slice(0, YOUTUBE_RELATED_VIDEO_COUNT)
        } catch (err) {
          Sentry.captureException(err)
          console.log(`getRelatedChannelsByRapidApi failed: ${err}`)
          return []
        }
      },
      { concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS },
    )

    const channelIds = [...new Set(allRelatedVideos.flat().map((v) => v.channelId))]
    console.log(
      `[rapidapi]filter ${channelIds.length} channels from ${allRelatedVideos.flat().length} videos`,
    )
    const allRelatedChannels = await Bluebird.map(
      channelIds,
      async (relatedChannelId) => {
        try {
          if (relatedChannelId) {
            return await YoutubeApi.getInstance().getChannelWithVideos(relatedChannelId, collector)
          } else {
            return undefined
          }
        } catch (error) {
          Sentry.captureException(error)
          console.log(`getRelatedChannelsByRapidApi failed: ${error}`)
          return undefined
        }
      },
      {
        concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
      },
    )
    return allRelatedChannels.filter((c): c is Channel => !!c && !!c?.videos?.length)
  }

  private async getRelatedChannelsByVideoIds(
    videoIds: string[],
    regions: string[] | undefined,
    collector?: RapidApiStatsCollector,
  ): Promise<Channel[]> {
    assert(videoIds.length > 0, new Error('videoIds cannot be empty'))

    const allRelatedVideos = await Bluebird.map(
      videoIds,
      async (videoId) => {
        try {
          const related = await YoutubeApi.getInstance().getVideoRelatedVideo(
            videoId,
            YOUTUBE_RELATED_VIDEO_COUNT,
            collector,
          )
          console.log(`[rapidapi]get related videos  ${related.length} videos by ${videoId}`)
          return related.slice(0, YOUTUBE_RELATED_VIDEO_COUNT)
        } catch (err) {
          Sentry.captureException(err)
          console.log(`getRelatedChannelsByRapidApi failed: ${err}`)
          return []
        }
      },
      { concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS },
    )

    const channelIds = [...new Set(allRelatedVideos.flat().map((v) => v.channelId))]
    console.log(
      `[rapidapi]filter ${channelIds.length} channels from ${allRelatedVideos.flat().length} videos`,
    )
    const allRelatedChannels = await Bluebird.map(
      channelIds,
      async (relatedChannelId) => {
        try {
          if (relatedChannelId) {
            return await YoutubeApi.getInstance().getChannelWithVideos(relatedChannelId, collector)
          } else {
            return undefined
          }
        } catch (error) {
          Sentry.captureException(error)
          console.log(`getRelatedChannelsByRapidApi failed: ${error}`)
          return undefined
        }
      },
      {
        concurrency: +CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS,
      },
    )
    return allRelatedChannels.filter((c): c is Channel => !!c && !!c?.videos?.length)
  }

  public async updateOfficialEmail(
    channel: YouTubeChannel,
  ): Promise<Omit<YouTubeChannel, 'videos'>> {
    const fullInfo = await YoutubeApi.getInstance().getChannel(channel.channelId)
    if (!fullInfo) {
      console.log('[rapidapi]channel not found: ' + channel.channelId)
    }
    return prisma.youTubeChannel.update({
      where: { channelId: channel.channelId },
      data: { officialEmail: fullInfo?.hasEmail ?? false },
    })
  }

  public async updatePublishTime(
    channel: YouTubeChannel,
  ): Promise<Omit<YouTubeChannel, 'videos'> | undefined> {
    const fullInfo = await YoutubeApi.getInstance().getChannelWithVideos(channel.channelId)
    if (!fullInfo) {
      console.log('[rapidapi]channel not found: ' + channel.channelId)
      return undefined
    }
    let publishTime = 0
    if (fullInfo.videos && fullInfo.videos.length > 0) {
      publishTime = getUnixTimestamp(parsePublishedTime(fullInfo.videos[0].publishedTimeText))
      if (publishTime) {
        return prisma.youTubeChannel.update({
          where: { channelId: channel.channelId },
          data: { lastPublishedTime: publishTime },
        })
      }
    }
    return undefined
  }

  private async processAndUpsertChannels(channels: Channel[]) {
    const processedChannels = channels
      .map((channel: Channel) => {
        try {
          // 计算发布统计
          const publicationStats = PublicationStatsService.calculatePublicationStats(
            channel.videos ?? [],
            KolPlatform.YOUTUBE,
          )
          return {
            channelId: channel.channelId,
            channelName: channel.title,
            channelHandle: channel.channelHandle ?? '',
            channelDescription: channel.description ?? '',
            numericSubscriberCount: BigInt(channel.subscriberCount ?? 0),
            country: channel.country ?? '',
            haveCrawlered: false,
            lastPublishedTime: BigInt(channel.lastUpdatedAt ?? 0),
            videosAverageViewCount: BigInt(channel.videosAverageViewCount ?? 0),
            officialEmail: !!channel.hasEmail,
            videos: (channel.videos ?? []) as Prisma.InputJsonValue,
            publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        } catch (err) {
          console.log(`failed to upsert youtube channel ${channel.channelId}: ${err}`)
          Sentry.captureException(err)
          return null
        }
      })
      .filter((i) => i) as YouTubeChannel[]

    // 获取所有已存在的channelId
    const existingChannels = await prisma.youTubeChannel.findMany({
      where: { channelId: { in: processedChannels.map((c) => c.channelId) } },
      select: { channelId: true },
    })
    const existingChannelIds = new Set(existingChannels.map((c) => c.channelId))

    // 分离需要更新和需要插入数据
    const channelsToUpdate = processedChannels.filter((c) => existingChannelIds.has(c.channelId))
    const channelsToInsert = processedChannels.filter((c) => !existingChannelIds.has(c.channelId))

    // 批量更新已存在的记录
    if (channelsToUpdate.length > 0) {
      await Bluebird.map(
        channelsToUpdate,
        async (channel) => {
          try {
            await prisma.youTubeChannel.update({
              where: { channelId: channel.channelId },
              data: {
                channelName: channel.channelName,
                channelHandle: channel.channelHandle,
                channelDescription: channel.channelDescription,
                numericSubscriberCount: channel.numericSubscriberCount,
                country: channel.country,
                lastPublishedTime: channel.lastPublishedTime,
                videosAverageViewCount: channel.videosAverageViewCount,
                officialEmail: channel.officialEmail,
                videos: channel.videos as Prisma.InputJsonValue,
                publicationStats: channel.publicationStats as unknown as Prisma.InputJsonValue,
                updatedAt: channel.updatedAt,
              },
            })
          } catch (err) {
            Sentry.captureException(err)
            console.log(`upsertChannels failed: ${err}`)
          }
        },
        { concurrency: 50 },
      )
      console.log(`update ${channelsToUpdate.length} channels`)
    }

    // 批量插入新记录
    if (channelsToInsert.length > 0) {
      const insertResult = await prisma.youTubeChannel.createMany({
        data: channelsToInsert.map((channel) => ({
          ...channel,
          videos: channel.videos as Prisma.InputJsonValue,
          publicationStats: channel.publicationStats as unknown as Prisma.InputJsonValue,
        })),
        skipDuplicates: true,
      })
      console.log(`insert ${insertResult.count} youtube channels`)
    }
  }

  private async upsertKolInfo(userId: string | undefined, channels: Channel[]) {
    // 通用的 ID 是 UC 开头的长 ID
    const channelIds = channels.map((i) => i.channelId)
    const exists = await prisma.kolInfo.findMany({
      where: {
        platformAccount: {
          in: channelIds,
        },
      },
      select: { platformAccount: true, email: true, historyEmails: true },
    })
    const existIds = exists.map((i) => i.platformAccount)
    const existMap = new Map(exists.map((i) => [i.platformAccount, i]))
    const updates = channels.filter((i) => existIds.includes(i.channelId))
    const inserts = channels.filter((i) => !existIds.includes(i.channelId))
    const insertKols = inserts.map((i) => {
      return {
        title: i.title,
        description: i.description,
        email: i.email,
        emailSource: i.emailSource,
        links: i.links
          ? i.links.map((i) => (i.link.startsWith('https://') ? i.link : `https://${i.link}`))
          : [],
        platformAccount: i.channelId,
        createdAt: new Date(),
        updatedAt: new Date(),
        avatar: i.avatar?.length ? i.avatar[0].url : '',
        platform: KolPlatform.YOUTUBE,
      }
    })
    await prisma.kolInfo.createMany({ data: insertKols, skipDuplicates: true })
    await Bluebird.map(
      updates,
      async (update) => {
        try {
          const exist = existMap.get(update.channelId)
          const emailFields: EmailFields = {
            email: undefined,
            emailSource: undefined,
            emailUpdatedAt: undefined,
            historyEmails: undefined,
          }
          const needUpdateEmail = update.email && update.email !== exist?.email
          if (needUpdateEmail) {
            emailFields.email = update.email
            emailFields.emailSource = update.emailSource as EmailSourceType
            emailFields.emailUpdatedAt = new Date()
            emailFields.historyEmails = [
              ...new Set([...(exist?.historyEmails ?? []), update.email as string]),
            ]
            // email audit log
            EmailService.getInstance().addEmailAuditLog(
              userId,
              update.channelId,
              update.email,
              update.emailSource as EmailSourceType,
            )
          }

          await prisma.kolInfo.update({
            where: {
              platform_platformAccount: {
                platformAccount: update.channelId,
                platform: KolPlatform.YOUTUBE,
              },
            },
            data: {
              title: update.title,
              description: update.description,
              platformAccount: update.channelId,
              platform: KolPlatform.YOUTUBE,
              avatar: update.avatar?.length ? update.avatar[0].url : '',
              links: update.links
                ? update.links.map((i) =>
                    i.link.startsWith('https://') ? i.link : `https://${i.link}`,
                  )
                : [],
              ...emailFields,
              historyEmails: emailFields.historyEmails ?? exist?.historyEmails,
            },
          })
        } catch (err) {
          Sentry.captureException(err)
          console.log(`upsertKolInfo failed: ${err}`)
        }
      },
      { concurrency: 50 },
    )
  }

  private async upsertChannelInfoAndVectors(channels: Channel[]) {
    console.log(
      `[upsertChannelInfoAndVectors] 开始处理 ${channels.length} 个YouTube频道数据并生成向量存储`,
    )
    try {
      // 创建频道数据映射，方便后续查询
      const channelMap = new Map<string, Channel>()
      channels.forEach((channel) => {
        channelMap.set(channel.channelId, channel)
      })

      // 并行处理频道数据和生成embedding映射
      const [processedChannels, embeddingMap] = await Promise.all([
        // 处理频道基础数据
        Promise.resolve().then(() => {
          const youtubeChannels = channels
            .map((channel: Channel) => {
              try {
                // Calculate publication stats for the channel
                const publicationStats = PublicationStatsService.calculatePublicationStats(
                  channel.videos,
                  KolPlatform.YOUTUBE,
                )

                return {
                  channelId: channel.channelId,
                  channelName: channel.title,
                  channelHandle: channel.channelHandle ?? '',
                  channelDescription: channel.description ?? '',
                  numericSubscriberCount: BigInt(channel.subscriberCount ?? 0),
                  country: channel.country ?? '',
                  haveCrawlered: false,
                  lastPublishedTime: BigInt(
                    channel.lastUpdatedAt || publicationStats.lastPublishedTime || 0,
                  ),
                  videosAverageViewCount: BigInt(channel.videosAverageViewCount ?? 0),
                  officialEmail: !!channel.hasEmail,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  videos: channel.videos as Prisma.InputJsonValue,
                  publicationStats: publicationStats as unknown as Prisma.InputJsonValue,
                }
              } catch (err) {
                console.log(`处理YouTube频道数据失败 ${channel.channelId}: ${err}`)
                Sentry.captureException(err)
                return null
              }
            })
            .filter((i) => i) as YouTubeChannel[]
          console.log(
            `[upsertChannelInfoAndVectors] 成功处理 ${youtubeChannels.length} 个频道基础数据`,
          )
          return youtubeChannels
        }),
        // 生成嵌入向量映射
        (async () => {
          try {
            // 使用现有的generateEmbeddingsForYoutube方法生成embedding
            const embeddings = await generateEmbeddingsForYoutube(channels)
            const embeddingMap = new Map<string, number[]>()

            channels.forEach((channel, index) => {
              if (embeddings[index] && embeddings[index].length > 0) {
                embeddingMap.set(channel.channelId, embeddings[index])
              }
            })

            console.log(
              `[upsertChannelInfoAndVectors] 成功生成 ${embeddingMap.size} 个嵌入向量映射`,
            )
            return embeddingMap
          } catch (error) {
            console.error(`[upsertChannelInfoAndVectors] 生成嵌入向量失败: ${error}`)
            Sentry.captureException(error)
            return new Map<string, number[]>()
          }
        })(),
      ])

      console.log(`[upsertChannelInfoAndVectors] 成功生成 ${embeddingMap.size} 个嵌入向量映射`)

      // 将处理好的数据与向量匹配
      const channelsWithEmbeddings = processedChannels
        .map((channel) => {
          const embedding = embeddingMap.get(channel.channelId)
          const originalChannel = channelMap.get(channel.channelId)

          if (embedding && embedding.length > 0 && originalChannel) {
            return {
              channel,
              originalChannel,
              embedding,
            }
          }
          if (!embedding || embedding.length === 0) {
            console.warn(
              `[upsertChannelInfoAndVectors] 频道 ${channel.channelId} 没有有效的向量数据`,
            )
          }
          if (!originalChannel) {
            console.warn(`[upsertChannelInfoAndVectors] 找不到频道 ${channel.channelId} 的原始数据`)
          }
          return null
        })
        .filter(
          (
            item,
          ): item is {
            channel: YouTubeChannel
            originalChannel: Channel
            embedding: number[]
          } => item !== null,
        )

      console.log(
        `[upsertChannelInfoAndVectors] 成功匹配 ${channelsWithEmbeddings.length} 个有效频道数据和向量`,
      )

      // 批量处理，每次处理200个频道
      const batchSize = 200
      const batches = []
      for (let i = 0; i < channelsWithEmbeddings.length; i += batchSize) {
        batches.push(channelsWithEmbeddings.slice(i, i + batchSize))
      }

      // 处理YouTube频道数据
      for (const batch of batches) {
        const channelIds = batch.map((item) => item.channel.channelId)
        const existingChannels = await prisma.youTubeChannel.findMany({
          where: { channelId: { in: channelIds } },
          select: { channelId: true },
        })
        const existingChannelIds = new Set(existingChannels.map((c) => c.channelId))

        const channelsToUpdate = batch.filter((item) =>
          existingChannelIds.has(item.channel.channelId),
        )
        const channelsToInsert = batch.filter(
          (item) => !existingChannelIds.has(item.channel.channelId),
        )

        if (channelsToUpdate.length > 0) {
          await Bluebird.map(
            channelsToUpdate,
            async (item) => {
              try {
                await prisma.youTubeChannel.update({
                  where: { channelId: item.channel.channelId },
                  data: {
                    channelName: item.channel.channelName,
                    channelHandle: item.channel.channelHandle,
                    channelDescription: item.channel.channelDescription,
                    numericSubscriberCount: item.channel.numericSubscriberCount,
                    country: item.channel.country,
                    lastPublishedTime: item.channel.lastPublishedTime,
                    videosAverageViewCount: item.channel.videosAverageViewCount,
                    officialEmail: item.channel.officialEmail,
                    updatedAt: item.channel.updatedAt,
                    videos: item.channel.videos as Prisma.InputJsonValue,
                    publicationStats: item.channel.publicationStats as Prisma.InputJsonValue,
                  },
                })
              } catch (err) {
                Sentry.captureException(err)
                console.error(`更新频道 ${item.channel.channelId} 失败:`, err)
              }
            },
            { concurrency: 50 },
          )
          console.log(`[upsertChannelInfoAndVectors] 已更新 ${channelsToUpdate.length} 个频道数据`)
        }
        if (channelsToInsert.length > 0) {
          try {
            const insertResult = await prisma.youTubeChannel.createMany({
              data: channelsToInsert.map((item) => item.channel) as any,
              skipDuplicates: true,
            })
            console.log(`[upsertChannelInfoAndVectors] 已插入 ${insertResult.count} 个新频道数据`)
          } catch (error) {
            console.error(`[upsertChannelInfoAndVectors] 批量插入频道数据失败:`, error)
            Sentry.captureException(error)
          }
        }
      }

      // 准备Milvus数据
      const milvusDataToInsert = channelsWithEmbeddings
        .map((item) => {
          try {
            const { originalChannel, embedding, channel } = item
            // 构建EnhancedMilvusData对象
            const data = {
              userId: channel.channelId,
              account: channel.channelHandle || '',
              nickname: channel.channelName || '',
              platform: KolPlatform.YOUTUBE,
              region: channel.country || '',
              followerCount: Number(channel.numericSubscriberCount) || 0,
              averagePlayCount: Number(channel.videosAverageViewCount) || 0,
              lastPublishedTime: Number(channel.lastPublishedTime) || 0,
              videoTexts: originalChannel.videos
                .slice(0, 10)
                .map((v) => (v.title || '').slice(0, 500))
                .join(' ')
                .slice(0, 6000),
              email: originalChannel.email || '',
              denseVector: embedding,
              sparseVector: { indices: [], values: [] },
              createdAt: Math.floor(Date.now() / 1000),
              updatedAt: Math.floor(Date.now() / 1000),
            }
            return data as EnhancedMilvusData
          } catch (mapError) {
            console.error(`[upsertChannelInfoAndVectors] 处理频道数据时出错:`, mapError)
            return null
          }
        })
        .filter((data): data is EnhancedMilvusData => data !== null)

      // 插入Milvus数据
      try {
        if (milvusDataToInsert.length === 0) {
          console.warn('[upsertChannelInfoAndVectors] 没有有效的向量数据可插入Milvus')
        } else {
          console.log(
            `[upsertChannelInfoAndVectors] 准备插入 ${milvusDataToInsert.length} 条记录到Milvus`,
          )
          // 记录第一条数据的结构以便调试
          if (milvusDataToInsert.length > 0) {
            const firstItem = milvusDataToInsert[0]
            console.log('[upsertChannelInfoAndVectors] 第一条数据的字段类型:')
            Object.entries(firstItem).forEach(([key, value]) => {
              console.log(`${key}: ${typeof value} ${Array.isArray(value) ? '(Array)' : ''}`)
              if (key === 'denseVector') {
                console.log(`denseVector length: ${value.length}`)
              }
              if (key === 'sparseVector') {
                console.log(`sparseVector structure:`, JSON.stringify(value))
              }
            })
          }

          // 使用bulkInsertEnhancedWithDelete方法进行插入
          const milvusResult = await bulkInsertEnhancedWithDelete(milvusDataToInsert)
          console.log(
            `[upsertChannelInfoAndVectors] Milvus数据操作完成:\n` +
              `- 删除记录数: ${milvusResult.deleteResult.delete_cnt}\n` +
              `- 插入成功: ${milvusResult.insertResult.succ_index.length}\n` +
              `- 插入失败: ${milvusResult.insertResult.err_index?.length || 0}`,
          )
        }
      } catch (milvusError) {
        console.error('[upsertChannelInfoAndVectors] Milvus数据插入失败:', milvusError)
        if (milvusError instanceof Error) {
          console.error(
            `[upsertChannelInfoAndVectors] 错误详情: ${milvusError.message}, 错误堆栈: ${milvusError.stack}`,
          )
        }
        Sentry.captureException(milvusError)
      }

      return channels.map((channel) => channel.channelId)
    } catch (error) {
      console.error('[upsertChannelInfoAndVectors] 整体处理失败:', error)
      Sentry.captureException(error)
      throw error
    }
  }

  async createKolByRapidApi(idOrName: string): Promise<KolInfo> {
    let id: string | undefined = idOrName
    if (!idOrName.startsWith('UC')) {
      id = await YoutubeApi.getInstance().getYoutubeChannelId(idOrName)
    }
    if (!id) {
      throw new Error(`cannot find kol info for youtube: ${idOrName}`)
    }
    const channel = await YoutubeApi.getInstance().getChannel(id)
    const updates = {
      title: channel.title,
      description: channel.description,
      email: channel.email,
      emailSource: channel.emailSource,
      historyEmails: [],
      emailUpdatedAt: new Date(),
    }
    const result = await prisma.kolInfo.upsert({
      where: {
        platform_platformAccount: { platform: KolPlatform.YOUTUBE, platformAccount: id },
      },
      update: updates,
      create: updates && {
        platform: KolPlatform.YOUTUBE,
        platformAccount: channel.channelId,
      },
    })
    return result
  }

  private async getChannelsFromHashtag(
    hashtag: string,
    initialPaginationToken: string,
    maxVideoCount: number,
    currentVideoCount: number,
    options: {
      ratedChannelIds: string[]
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakResult> {
    const channelIds = new Set<string>()

    let currentPaginationToken = initialPaginationToken
    let hasMore = true
    let message = ''
    let ratedChannelIdsSet: Set<string> | null = null
    let videoCount = 0
    let total = 0

    if (options.ratedChannelIds && options.ratedChannelIds.length > 0) {
      ratedChannelIdsSet = new Set<string>(options.ratedChannelIds)
    }

    while (videoCount < maxVideoCount && hasMore) {
      try {
        const hashtagOptions = {
          token: currentPaginationToken || undefined,
          geo: HASHTAG_DEFAULT_GEO,
          lang: HASHTAG_DEFAULT_LANG,
        }
        const response = await YoutubeApi.getInstance().getHashtag(
          hashtag,
          hashtagOptions,
          collector,
        )
        console.log('current total', response.meta.videoCount)
        console.log('current pagination token', response.continuation)
        if (response.meta.videoCount > total) {
          total = response.meta.videoCount
        }

        if (!response || !response.data || response.data.length === 0) {
          hasMore = false
          message = '没有更多内容,请求接口失败or数据为空'
          break
        }

        const videos = response.data || []
        videos.map((video) => {
          if (video.channelId) {
            const channelId = video.channelId
            if (ratedChannelIdsSet && ratedChannelIdsSet.has(channelId)) {
              return
            } else {
              channelIds.add(channelId)
            }
          }
        })

        videoCount += videos.length
        console.log(
          `标签 ${hashtag} 当前获取到 ${videos.length} 个视频，${channelIds.size} 个唯一作者`,
        )

        currentPaginationToken = response.continuation || initialPaginationToken
        hasMore = !!response.continuation

        if (videoCount >= maxVideoCount) {
          message = `获取 channelIds 成功，数量达到上限,${maxVideoCount},hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log(`获取 channelIds 成功，数量达到上限: ${maxVideoCount}`)
          break
        }

        if (!hasMore) {
          message = `没有更多内容,hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log('没有更多内容')
          break
        }
      } catch (error) {
        console.error(`获取标签 ${hashtag} 的内容时出错: ${error}`)
        hasMore = false
        message = `获取标签 ${hashtag} 的内容时出错: ${error}`
        break
      }
    }

    const uniqueChannelIds = Array.from(channelIds)
    console.log(`[getUniqueChannelFromHashtag] 获取到 ${uniqueChannelIds.length} 个用户ID`)

    return {
      channelIds: uniqueChannelIds,
      hasMore,
      paginationToken: currentPaginationToken,
      total: uniqueChannelIds.length,
      message,
      progress: {
        total: total,
        current: uniqueChannelIds.length,
        count: videoCount + currentVideoCount,
      },
    }
  }

  private async getChannelsFromSearch(
    query: string,
    initialPaginationToken: string,
    maxVideoCount: number,
    currentVideoCount: number,
    total: number,
    options: {
      ratedChannelIds: string[]
    },
    queryParams: {
      geo?: string
      lang?: string
      duration?: string
      sort_by?: string
      upload_date?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakResult> {
    const channelIds = new Set<string>()

    let currentPaginationToken = initialPaginationToken
    let hasMore = true
    let message = ''
    let ratedChannelIdsSet: Set<string> | null = null
    let videoCount = 0

    if (options.ratedChannelIds && options.ratedChannelIds.length > 0) {
      ratedChannelIdsSet = new Set<string>(options.ratedChannelIds)
    }

    const searchParams = {
      ...queryParams,
      token: initialPaginationToken,
    }

    while (videoCount < maxVideoCount && hasMore) {
      try {
        const response = await YoutubeApi.getInstance().searchVideo(query, searchParams, collector)
        console.log('current total', response.count)
        console.log('current pagination token', response.token)

        if (!response || !response.data || response.data.length === 0) {
          hasMore = false
          message = '没有更多内容,请求接口失败or数据为空'
          break
        }

        const videos = response.data || []
        videos.map((video) => {
          if (video.channelId) {
            const channelId = video.channelId
            if (ratedChannelIdsSet && ratedChannelIdsSet.has(channelId)) {
              return
            } else {
              channelIds.add(channelId)
            }
          }
        })

        videoCount += videos.length
        console.log(
          `搜索词 ${query} 当前获取到 ${videos.length} 个视频，${channelIds.size} 个唯一作者`,
        )

        currentPaginationToken = response.token || initialPaginationToken
        searchParams.token = currentPaginationToken
        console.log(`token change to ${currentPaginationToken}`)
        hasMore = !!response.token

        if (videoCount >= maxVideoCount) {
          message = `获取 channelIds 成功，数量达到上限,${maxVideoCount},hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log(`获取 channelIds 成功，数量达到上限: ${maxVideoCount}`)
          break
        }

        if (!hasMore) {
          message = `没有更多内容,hasMore:${hasMore},paginationToken:${currentPaginationToken}`
          console.log('没有更多内容')
          break
        }
      } catch (error) {
        console.error(`获取搜索词 ${query} 的内容时出错: ${error}`)
        hasMore = false
        message = `获取搜索词 ${query} 的内容时出错: ${error}`
        break
      }
    }

    const uniqueChannelIds = Array.from(channelIds)
    console.log(`[getUniqueChannelFromHashtag] 获取到 ${uniqueChannelIds.length} 个用户ID`)

    return {
      channelIds: uniqueChannelIds,
      hasMore,
      paginationToken: currentPaginationToken,
      total: uniqueChannelIds.length,
      message,
      progress: {
        total: total,
        current: videoCount + currentVideoCount,
        count: uniqueChannelIds.length,
      },
    }
  }

  public async validateTag(
    tag: string,
    options: { geo?: string; lang?: string },
  ): Promise<boolean> {
    const hashtag = await YoutubeApi.getInstance().getHashtag(tag, options)
    return !!hashtag?.meta?.videoCount
  }

  /**
   * 获取历史任务的candidatesUsername
   */
  private async getPreviousTasksCandidatesChannelIds(projectId: string): Promise<string[]> {
    const previousTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    if (!previousTasks.length) {
      return []
    }
    const allResults = previousTasks
      .map((i) => i.result as unknown as YoutubeTaskResult)
      .filter((i) => i)
    const allCandidates = previousTasks.map((i) => {
      return i.candidate as unknown as Array<{
        kolId: string
        platform: KolPlatform
        platformId: string
        score: number
      }>
    })
    const allResultUsernames = allResults
      .flatMap((result) => result.candidates || [])
      .filter((candidate: any) => candidate.username)
      .map((candidate: any) => candidate.username as string)
    const allCandidatesUsernames = allCandidates
      .filter(
        (
          c,
        ): c is Array<{
          kolId: string
          platform: KolPlatform
          platformId: string
          score: number
        }> => Array.isArray(c),
      )
      .flatMap((c) => c.map((i) => i.platformId))
    return [...allCandidatesUsernames, ...allResultUsernames]
  }
}

export default YoutubeService

export type ChannelWithCount = BaseChannel & {
  count: number
  viewCount: number
}
