import { prisma, ProjectKolAttitude } from '@repo/database'

export async function filterUnreadKolIds(projectId: string, kolIds: string[]) {
  if (!kolIds.length) return []

  const readKolIds = await prisma.projectKol.findMany({
    where: {
      projectId,
      kolId: {
        in: kolIds,
      },
      attitude: {
        in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.DISLIKE, ProjectKolAttitude.SUPERLIKE],
      },
    },
    select: {
      kolId: true
    }
  })

  const readKolIdSet = new Set(readKolIds.map(kol => kol.kolId))
  return kolIds.filter(id => !readKolIdSet.has(id))
}