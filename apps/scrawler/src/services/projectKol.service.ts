import { KolPlatform, ProjectKol, ProjectKolAttitude, prisma } from '@repo/database'
import { PaginationService } from '../utils/pagination'

/**
 * 获取项目ProjectKol列表
 * @param projectId 项目ID
 * @param options 筛选选项
 * @returns 项目ProjectKol列表
 */
async function getProjectKols(
  projectId: string,
  options?: {
    page?: number
    pageSize?: number
    attitude?: ProjectKolAttitude[]
  },
): Promise<{
  data: ProjectKol[]
  total: number
}> {
  const project = await prisma.project.findUnique({
    where: { id: projectId, deletedAt: null },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  const attitudes = options?.attitude || [
    ProjectKolAttitude.LIKE,
    ProjectKolAttitude.DISLIKE,
    ProjectKolAttitude.SUPERLIKE,
  ]

  const { page, pageSize, skip } = PaginationService.handlePagination({
    page: options?.page,
    pageSize: options?.pageSize,
  })

  const [projectKols, total] = await Promise.all([
    prisma.projectKol.findMany({
      where: {
        projectId: project.id,
        attitude: { in: attitudes },
      },
      include: {
        KolInfo: true,
      },
      skip,
      take: pageSize,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.projectKol.count({
      where: {
        projectId: project.id,
        attitude: { in: attitudes },
      },
    }),
  ])

  return {
    data: projectKols,
    total,
  }
}

/**
 * 获取项目中下所有已经评价过的kol uniqueId
 * @param projectId 项目ID
 * @param options 筛选选项
 * @returns 平台账号ID列表
 */
async function getProjectKolUniques(
  projectId: string,
  options?: {
    attitude?: ProjectKolAttitude[]
    platform?: KolPlatform[]
  },
): Promise<string[]> {
  const project = await prisma.project.findUnique({
    where: { id: projectId, deletedAt: null },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  const attitudes = options?.attitude || [
    ProjectKolAttitude.LIKE,
    ProjectKolAttitude.DISLIKE,
    ProjectKolAttitude.SUPERLIKE,
  ]

  const platforms = options?.platform || [
    KolPlatform.YOUTUBE,
    KolPlatform.TIKTOK,
    KolPlatform.INSTAGRAM,
    KolPlatform.TWITTER,
  ]

  const projectKols = await prisma.projectKol.findMany({
    where: {
      projectId: project.id,
      attitude: { in: attitudes },
      KolInfo: {
        platform: { in: platforms },
      },
    },
    select: {
      KolInfo: {
        select: {
          platformAccount: true,
          platform: true,
        },
      },
    },
  })

  const uniqueIds: string[] = projectKols
    .filter((projectKol) => projectKol.KolInfo !== null)
    .map((projectKol) => projectKol.KolInfo!.platformAccount)
    .filter((id): id is string => id !== null)

  return uniqueIds
}

export const ProjectKolService = {
  getProjectKols,
  getProjectKolUniques,
}
