import { getAllSenders, newGoogleOAuth2Client } from '@/api/gmail'
import {
  EmailCredentialType,
  Provider,
  ProviderCredential,
  ScopeType,
  prisma,
} from '@repo/database'

export const prepareCredential = async (
  credential: ProviderCredential & {
    Provider: Provider
  },
) => {
  if (credential.Provider.scopes.includes(ScopeType.GMAIL)) {
    console.log('Prepare credential for Gmail')
    const client = await newGoogleOAuth2Client({
      accessToken: credential.accessToken!,
      refreshToken: credential.refreshToken!,
    })
    const emails = await getAllSenders(client)
    await Promise.all(
      emails.map(async (email) => {
        if (!email.sendAsEmail) {
          return
        }
        await prisma.emailSender.upsert({
          where: {
            email_createdBy: {
              email: email.sendAsEmail,
              createdBy: credential.createdBy,
            },
          },
          create: {
            email: email.sendAsEmail,
            type: EmailCredentialType.GMAIL,
            providerCredentialId: credential.id,
            createdBy: credential.createdBy,
          },
          update: {
            email: email.sendAsEmail,
            type: EmailCredentialType.GMAIL,
            providerCredentialId: credential.id,
            createdBy: credential.createdBy,
          },
        })
      }),
    )
    console.log('Emails populated', emails.length)
  }
}
