import {
  CreateStrategyTemplatePayload,
  StrategyTemplateDto,
  UpdateStrategyTemplatePayload,
  UpdateTemplateStatusPayload,
} from '@/types/strategyTemplate'
import { prisma } from '@repo/database'

export async function createStrategyTemplate(
  userId: string,
  payload: CreateStrategyTemplatePayload,
): Promise<StrategyTemplateDto> {
  const template = await prisma.userTrackStrategyTemplate.create({
    data: {
      userId,
      name: payload.name,
      totalRuns: payload.totalRuns,
      intervalHours: payload.intervalHours,
    },
  })

  return template
}

export async function updateStrategyTemplate(
  userId: string,
  payload: UpdateStrategyTemplatePayload,
): Promise<StrategyTemplateDto> {
  const template = await prisma.userTrackStrategyTemplate.update({
    where: {
      userId,
    },
    data: {
      ...payload,
    },
  })

  return template
}

export async function getStrategyTemplate(userId: string): Promise<StrategyTemplateDto> {
  const template = await prisma.userTrackStrategyTemplate.findUnique({
    where: {
      userId,
    },
  })

  if (!template) {
    return prisma.userTrackStrategyTemplate.create({
      data: {
        userId,
        totalRuns: 7,
        intervalHours: 24,
        isActive: true,
      },
    })
  }

  return template
}

export async function updateTemplateStatus(
  userId: string,
  payload: UpdateTemplateStatusPayload,
): Promise<StrategyTemplateDto> {
  let template = await prisma.userTrackStrategyTemplate.findUnique({
    where: { userId },
  })

  if (!template) {
    template = await prisma.userTrackStrategyTemplate.create({
      data: {
        userId,
        totalRuns: 7,
        intervalHours: 24,
        isActive: payload.isActive,
      },
    })
  } else {
    template = await prisma.userTrackStrategyTemplate.update({
      where: { userId },
      data: { isActive: payload.isActive },
    })
  }

  return template
}

const strategyTemplateService = {
  createStrategyTemplate,
  updateStrategyTemplate,
  getStrategyTemplate,
  updateTemplateStatus,
}

export default strategyTemplateService
