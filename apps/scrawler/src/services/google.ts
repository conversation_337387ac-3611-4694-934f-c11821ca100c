import Sentry from '@/infras/sentry'
import { google } from 'googleapis'
import nodemailer from 'nodemailer'

// 配置 OAuth2 客户端
const oAuth2Client = new google.auth.OAuth2(
  'YOUR_CLIENT_ID',
  'YOUR_CLIENT_SECRET',
  'YOUR_REDIRECT_URL',
)

// 设置 token 和 refreshToken
oAuth2Client.setCredentials({
  access_token: 'YOUR_ACCESS_TOKEN',
  refresh_token: 'YOUR_REFRESH_TOKEN',
})

// 验证 token 和 refreshToken
async function verifyTokens() {
  try {
    const tokenInfo = await oAuth2Client.getTokenInfo(oAuth2Client.credentials.access_token!)
    console.log('Token is valid:', tokenInfo)
  } catch (error) {
    console.error('Error validating token:', error)
  }
}

// 发送邮件
async function sendEmail() {
  try {
    const accessToken = await oAuth2Client.getAccessToken()

    const transport = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        type: 'OAuth2',
        user: '<EMAIL>',
        clientId: 'YOUR_CLIENT_ID',
        clientSecret: 'YOUR_CLIENT_SECRET',
        refreshToken: 'YOUR_REFRESH_TOKEN',
        accessToken: accessToken.token,
      },
    })

    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Test Email from Node.js',
      text: 'Hello from Node.js',
    }

    const result = await transport.sendMail(mailOptions)
    console.log('Email sent:', result)
  } catch (error) {
    Sentry.captureException(error)
    console.error('Error sending email:', error)
  }
}

// 验证 tokens 并发送邮件
async function main() {
  await verifyTokens()
  await sendEmail()
}

main().catch(console.error)
