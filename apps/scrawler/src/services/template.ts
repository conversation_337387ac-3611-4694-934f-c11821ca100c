import { prisma } from '@repo/database'
import { User } from '@supabase/supabase-js'

export const createTemplate = async (params: {
  name?: string
  subject: string
  content: string
  tags?: string[]
  createdBy: User
  belongsToProjectId?: string
  cc?: string[]
}) => {
  if (!params.belongsToProjectId) {
    // 如果未指定项目，则使用用户创建的第一个默认项目
    const defaultProject = await prisma.projectMembership.findFirst({
      where: {
        userId: params.createdBy.id,
      },
      orderBy: {
        createdAt: 'asc',
      },
    })
    params.belongsToProjectId = defaultProject?.id ?? ''
  }
  return prisma.emailTemplate.create({
    data: {
      name: params.name,
      subject: params.subject ?? '',
      content: params.content ?? '',
      tags: params.tags ?? [],
      createdBy: params.createdBy.id,
      belongsToProjectId: params.belongsToProjectId,
      cc: params.cc ?? [],
    },
    include: {
      belongsToProject: true,
    },
  })
}

export const deleteTemplate = async (id: string, createBy: string) => {
  // 先查后更新
  const template = await prisma.emailTemplate.findFirst({
    where: {
      id,
      deletedAt: null,
    },
  })

  if (!template) {
    throw new Error('template not found or have been deleted')
  }

  if (template.createdBy !== createBy) {
    throw new Error('only creator can delete template')
  }

  return prisma.emailTemplate.update({
    where: { id },
    data: {
      deletedAt: new Date(),
    },
  })
}
