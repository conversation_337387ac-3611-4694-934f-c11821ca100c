import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'
import { describe, it } from 'vitest'

describe('should handle time zone', () => {
  it('should get current timezone', async () => {
    dayjs.extend(utc)
    dayjs.extend(timezone)

    const serverTimezone = dayjs.tz.guess()
    const now = new Date()
    console.log(`now function get time:` + now.toLocaleTimeString())
    const serverNow = dayjs(now, serverTimezone)

    const userTimezone = 'America/New_York'
    const userNow = dayjs(serverNow).tz(userTimezone)

    if (userNow.date() != serverNow.date()) {
      console.log('user date is different with our server!')
    } else {
      console.log(
        `user hour is different from server by ${userNow.hour() - serverNow.hour()}(- means before, + means after)`,
      )
    }
    console.log('server time is:' + serverNow.format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]'))
    console.log('user time is:' + userNow.format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]'))
  })
})
