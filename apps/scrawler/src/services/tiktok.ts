import { TiktokUser } from '@/api/@types/rapidapi/Tiktok.ts'
import TiktokApi from '@/api/tiktok'
import { handleUnknownError } from '@/common/errorHandler.ts'
import {
  PROJECT_THREE_ELEMENTS_TYPE,
  TIKTOK_DEFAULT_LAST_PUBLISHED_DAYS,
  TIKTOK_MAX_CONCURRENCY,
  TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT,
  TIKTOK_SEARCH_WORDS_VIDEOS_COUNT,
  TIKTOK_USE_HASHTAGS,
  TIKTOK_USE_SEARCH_WORDS,
  TIKTOK_USE_TAG_VIDEOS,
} from '@/config/env.ts'
import Sentry from '@/infras/sentry'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import {
  EnhancedMilvusData,
  advancedFilterSearch,
  bulkInsertEnhancedWithDelete,
  getDenseVectorsByUserId,
  queryByFilter,
} from '@/services/milvus.service'
import {
  generateDirectEmbeddingMapForTikTok,
  generateEmbeddingsForTikTok,
} from '@/services/worker/embedding.ts'
import {
  getAuthorsWithVideos,
  getHashtagVideosFiltered,
  getSearchVideosFiltered,
  getTikTokHashTagsWithIds,
  getTikTokKeywords,
  getTikTokSearchVideos,
  getTikTokSearchVideosForKeywordBreak,
  getUniqueIdsFromHashTagForTtHashTagBreak,
  getUniqueIdsFromMusicForTtBgmBreak,
  getUserAndVideos,
} from '@/services/worker/tiktokBreak.service'
import { EmailSourceType } from '@/types/email'
import { TtVideoBasicInfo } from '@/types/kol'
import { ProjectConfig } from '@/types/project'
import { createTaskRequest } from '@/types/request/similar.request.ts'
import {
  SimilarTaskParams,
  TtBgmBreakTaskParams,
  TtBgmBreakTaskResult,
  TtFollowersSimilarTaskParams,
  TtFollowersSimilarTaskResult,
  TtFollowingListTaskParams,
  TtFollowingListTaskResult,
  TtHashTagBreakTaskParams,
  TtHashTagBreakTaskResult,
  TtSearchInputBreakTaskParams,
  TtSearchInputBreakTaskResult,
  TtWebListTaskParams,
  TtWebListTaskResult,
} from '@/types/task'
import { KolInfoCreateInput } from '@/types/tiktok'
import { TtUserDetailsAndVideos } from '@/types/tiktokUsers'
import { parseUrlUtils } from '@/utils/parseUrl'
import { retryUtil } from '@/utils/retry'
import {
  KolInfo,
  KolPlatform,
  Prisma,
  ProjectKolAttitude,
  TaskType,
  TikTokUserInfo,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import Bull from 'bull'
import dayjs from 'dayjs'
import { VisualSimilarityOutput, analyzeVisualSimilarityService } from './aiTools/visualSimilarity'
import EmailService from './email'
import { getExcludeIds } from './excludeList'
import { EmailFields } from './kolInfo.service'
import { ProjectKolService } from './projectKol.service'
import { PublicationStatsService } from './publicationStats.service'
import { findCurrentTasksByProjectId, getFollowingUsers, getSimilarUsers } from './similar'

class TiktokService {
  private static instance: TiktokService

  public static getInstance(): TiktokService {
    if (!TiktokService.instance) {
      TiktokService.instance = new TiktokService()
    }
    return TiktokService.instance
  }

  // 处理和更新 TikTokUserInfo用户信息,包括用户基本信息和视频信息
  public async upsertTtUsers(authorsWithVideos: TtUserDetailsAndVideos[]): Promise<string[]> {
    const processedUsers = authorsWithVideos
      .map(this.processUserData.bind(this))
      .filter((user): user is Omit<TikTokUserInfo, 'createdAt' | 'updatedAt'> => user !== null)

    // 将用户分成小批次处理
    const batchSize = 200
    const batches = []
    for (let i = 0; i < processedUsers.length; i += batchSize) {
      batches.push(processedUsers.slice(i, i + batchSize))
    }

    const allResults = []

    // 按批次处理
    for (const batch of batches) {
      const batchResults = await Bluebird.map(
        batch,
        async (userData) => {
          try {
            const { userId, uniqueId, ...dataWithoutIds } = userData
            const existingUser = await prisma.tikTokUserInfo.findUnique({
              where: { userId: userId },
            })

            let result
            if (existingUser) {
              result = await prisma.tikTokUserInfo.update({
                where: { userId: userId },
                data: {
                  ...dataWithoutIds,
                  videos: dataWithoutIds.videos as unknown as any,
                  publicationStats: dataWithoutIds.publicationStats as unknown as any,
                  uniqueId: uniqueId,
                },
              })
            } else {
              result = await prisma.tikTokUserInfo.create({
                data: {
                  ...userData,
                  videos: userData.videos as unknown as any,
                  publicationStats: userData.publicationStats as unknown as any,
                },
              })
            }

            return { success: true, data: result }
          } catch (error) {
            console.error(`处理用户 ${userData.uniqueId} 失败:`, error)
            Sentry.captureException(error)
            return { success: false, error: error }
          }
        },
        { concurrency: 30 },
      )

      allResults.push(...batchResults)
    }

    const successCount = allResults.filter((result) => result.success).length
    const failureCount = allResults.length - successCount

    console.log(`处理完成。成功: ${successCount}, 失败: ${failureCount}`)

    return processedUsers.map((user) => user.uniqueId)
  }

  public async upsertKols(
    userId: string | undefined,
    users: TtUserDetailsAndVideos[],
  ): Promise<KolInfo[]> {
    if (!users || users.length === 0) {
      console.warn('没有提供 TikTok 用户数据')
      return []
    }

    try {
      // 1. 查找已存在的 KOL 信息
      const userUniqueIds = users.map((user) => user.uniqueId)
      const existingKolInfos = await prisma.kolInfo.findMany({
        where: {
          platformAccount: {
            in: userUniqueIds,
          },
          platform: KolPlatform.TIKTOK,
        },
      })

      const existingUserIds = new Set(existingKolInfos.map((kol) => kol.platformAccount))
      const usersToProcess = users.filter((user) => !existingUserIds.has(user.uniqueId))

      // 2. 创建新的 KOL 信息
      const createdKolInfos = await Bluebird.map(
        usersToProcess,
        async (userData) => {
          try {
            const kolData = {
              title: userData.nickname,
              description: userData.signature,
              email: userData.email,
              emailSource: userData.emailSource,
              historyEmails: [],
            }

            const result = await prisma.kolInfo.create({
              data: {
                ...kolData,
                platformAccount: userData.uniqueId,
                platform: KolPlatform.TIKTOK,
              },
            })

            console.log(`已为 TikTok 用户 ID: ${userData.uniqueId} 创建 KOL 信息`)
            return result
          } catch (error) {
            console.warn(`跳过创建失败的 TikTok 用户 ID: ${userData.uniqueId}`)
            return null
          }
        },
        { concurrency: 50 },
      )

      // 3. 更新已存在的 KOL 信息
      const updatedKolInfos = await Bluebird.map(
        existingKolInfos,
        async (existingKol) => {
          try {
            const userData = users.find((user) => user.uniqueId === existingKol.platformAccount)
            if (!userData) return existingKol

            const updateData: Partial<KolInfoCreateInput> = {
              title: userData.nickname,
              description: userData.signature,
            }

            const emailFields: EmailFields = {
              email: undefined,
              emailSource: undefined,
              historyEmails: undefined,
              emailUpdatedAt: undefined,
            }
            const needUpdateEmail = userData.email && existingKol.email != userData.email
            // 如果有新的邮箱，且不在历史邮箱中，则更新
            if (needUpdateEmail) {
              emailFields.email = userData.email
              emailFields.emailSource = userData.emailSource
              emailFields.historyEmails = [
                ...new Set([...(existingKol.historyEmails || []), userData.email as string]),
              ]
              emailFields.emailUpdatedAt = new Date()
              EmailService.getInstance().addEmailAuditLog(
                userId,
                existingKol.id,
                userData.email as string,
                userData.emailSource as EmailSourceType,
              )
              // 使用对象展开运算符添加新属性
              return await prisma.kolInfo.update({
                where: { id: existingKol.id },
                data: {
                  ...updateData,
                  ...emailFields,
                  historyEmails: emailFields.historyEmails ?? existingKol.historyEmails,
                },
              })
            } else {
              // 如果没有新邮箱，只更新基本信息
              return await prisma.kolInfo.update({
                where: { id: existingKol.id },
                data: updateData,
              })
            }
          } catch (error) {
            console.warn(`更新 KOL 信息失败，用户 ID: ${existingKol.platformAccount}`)
            return existingKol
          }
        },
        { concurrency: 50 },
      )

      const validResults = [...updatedKolInfos, ...(createdKolInfos.filter(Boolean) as KolInfo[])]

      console.log(`处理完成: 总计 ${users.length} 个用户, 成功 ${validResults.length} 个`)
      return validResults
    } catch (error) {
      console.error('批量处理 KOL 信息时发生错误:', error)
      return []
    }
  }

  public async createKolInfosForTikTokUsers(
    userId: string | undefined,
    userUniqueIds: string[],
  ): Promise<KolInfo[]> {
    if (!userUniqueIds || userUniqueIds.length === 0) {
      console.warn('没有提供 TikTok 用户 uniqueIds')
      return []
    }

    try {
      // 1. 查找已存在的 KOL 信息
      const existingKolInfos = await prisma.kolInfo.findMany({
        where: {
          platformAccount: {
            in: userUniqueIds,
          },
          platform: KolPlatform.TIKTOK,
        },
      })

      const existingUserIds = new Set(existingKolInfos.map((kol) => kol.platformAccount))
      const userIdsToCreate = userUniqueIds.filter((id) => !existingUserIds.has(id))

      // 2. 创建新的 KOL 信息，错误直接跳过
      const createdKolInfos = await Bluebird.map(
        userIdsToCreate,
        async (uniqueId) => {
          try {
            const kolInfo = await this.createKolInfoFormRapidAPI(userId, uniqueId)
            if (kolInfo) {
              console.log(`已为 TikTok 用户 ID: ${uniqueId} 创建 KOL 信息`)
              return kolInfo
            }
            console.log(`跳过无效的 TikTok 用户 ID: ${uniqueId}`)
            return null
          } catch (error) {
            console.log(`跳过创建失败的 TikTok 用户 ID: ${uniqueId}`)
            return null
          }
        },
        {
          concurrency: 50,
        },
      )

      const validResults = [...existingKolInfos, ...(createdKolInfos.filter(Boolean) as KolInfo[])]

      console.log(`处理完成: 总计 ${userUniqueIds.length} 个ID, 成功 ${validResults.length} 个`)
      return validResults
    } catch (error) {
      console.log('批量创建 KOL 信息时发生错误:', error)
      return [] // 发生严重错误时返回空数组
    }
  }

  private async processSourceProjectKol(
    userId: string | undefined,
    createTaskRequest: createTaskRequest,
    taskId: string,
  ) {
    let kol = await prisma.kolInfo.findFirst({
      where: {
        AND: [{ platformAccount: createTaskRequest.source }, { platform: KolPlatform.TIKTOK }],
      },
    })

    if (!kol) {
      kol = await this.createKolInfoFormRapidAPI(userId, createTaskRequest.source)
      if (!kol) {
        throw new Error('source kol rated as superlike, create kol info failed')
      }
      await prisma.projectKol.upsert({
        where: {
          projectId_kolId: {
            projectId: createTaskRequest.projectId,
            kolId: kol.id,
          },
        },
        update: {
          attitude: ProjectKolAttitude.SUPERLIKE,
          lastSimilarAt: new Date(),
          similarTaskId: taskId,
          rateBy: userId ?? 'system',
        },
        create: {
          projectId: createTaskRequest.projectId,
          kolId: kol.id,
          similarTaskId: taskId,
          attitude: ProjectKolAttitude.SUPERLIKE,
          rateBy: userId ?? 'system',
          lastSimilarAt: new Date(),
        },
      })
    } else {
      await prisma.projectKol.upsert({
        where: {
          projectId_kolId: {
            projectId: createTaskRequest.projectId,
            kolId: kol.id,
          },
        },
        update: {
          attitude: ProjectKolAttitude.SUPERLIKE,
          lastSimilarAt: new Date(),
          similarTaskId: taskId,
          rateBy: userId ?? 'system',
        },
        create: {
          projectId: createTaskRequest.projectId,
          kolId: kol.id,
          similarTaskId: taskId,
          attitude: ProjectKolAttitude.SUPERLIKE,
          rateBy: userId ?? 'system',
          lastSimilarAt: new Date(),
        },
      })
    }
  }

  private async processSourceVectorData(
    sourceAuthor: TtUserDetailsAndVideos,
    source: string,
    embedding: number[],
  ) {
    const sourceUniqueId = sourceAuthor?.uniqueId || source
    const sourceId = sourceAuthor?.userId

    let filter: string = `platform == "TIKTOK" AND userId == "${sourceId}"`
    if (sourceId) {
      filter = `platform == "TIKTOK" AND userId == "${sourceId}"`
    } else {
      filter = `platform == "TIKTOK" AND uniqueId == "${sourceUniqueId}"`
    }

    const existingData = await queryByFilter(
      'easykol_milvus_prod',
      filter,
      [
        'userId',
        'followerCount',
        'nickname',
        'averagePlayCount',
        'lastPublishedTime',
        'region',
        'signature',
        'videoTexts',
        'email',
      ],
      1,
    )

    console.log(
      `[tiktokProcessJob] sourceAuthor ${sourceUniqueId} 存在向量数据,数据: ${JSON.stringify(existingData)}`,
    )

    if (existingData && existingData.length > 0) {
      console.log(`[tiktokProcessJob] sourceAuthor ${sourceUniqueId} 存在向量数据，开始更新`)
    } else {
      console.log(`[tiktokProcessJob] sourceAuthor ${sourceUniqueId} 不存在向量数据，开始创建`)
    }

    const userStats = sourceAuthor ? await this.processUserData(sourceAuthor) : null

    const milvusData: EnhancedMilvusData = {
      userId: sourceId || existingData?.[0]?.userId || '',
      account: sourceUniqueId,
      platform: KolPlatform.TIKTOK,
      nickname: sourceAuthor?.nickname || existingData?.[0]?.nickname || '',
      followerCount: sourceAuthor?.followerCount
        ? Number(sourceAuthor.followerCount)
        : existingData?.[0]?.followerCount || 0,
      averagePlayCount: userStats?.averagePlayCount
        ? Number(userStats.averagePlayCount)
        : existingData?.[0]?.averagePlayCount || 0,
      lastPublishedTime: userStats?.lastPublishedTime
        ? Number(userStats.lastPublishedTime)
        : existingData?.[0]?.lastPublishedTime || 0,
      region: userStats?.region || existingData?.[0]?.region || '',
      signature: sourceAuthor?.signature.slice(0, 600) || existingData?.[0]?.signature || '',
      videoTexts:
        sourceAuthor?.videos
          .map((item) => item.title.slice(0, 600))
          .join(' ')
          .slice(0, 6000) ||
        existingData?.[0]?.videoTexts ||
        '',
      email: sourceAuthor?.email ? sourceAuthor?.email : existingData?.[0]?.email || '',
      denseVector: embedding,
      sparseVector: { indices: [], values: [] },
      createdAt: existingData?.[0]?.createdAt || Math.floor(Date.now() / 1000),
      updatedAt: Math.floor(Date.now() / 1000),
    }

    const success = await bulkInsertEnhancedWithDelete([milvusData])
    const { deleteResult, insertResult } = success
    console.log(
      `[tiktokProcessJob] sourceAuthor ${sourceUniqueId} 删除结果: ${JSON.stringify(deleteResult)}`,
    )
    console.log(
      `[tiktokProcessJob] sourceAuthor ${sourceUniqueId} 插入结果: ${JSON.stringify(insertResult)}`,
    )
  }

  public async insertKolData(userId: string | undefined, users: TtUserDetailsAndVideos[]) {
    if (!users) {
      throw new Error('authorsWithVideos is null')
    }
    try {
      const filterUsers = users.filter(
        (user): user is TtUserDetailsAndVideos =>
          Boolean(user) &&
          typeof user === 'object' &&
          Boolean(user.uniqueId) &&
          Boolean(user.videos) &&
          Array.isArray(user.videos),
      )

      if (!filterUsers?.length) {
        console.warn('[insertKolData] 没有有效的用户数据')
        return
      }

      await Promise.all([
        this.upsertTtUsersWithEmbedding(filterUsers),
        this.upsertKols(userId, filterUsers),
      ])

      return
    } catch (error) {
      console.error('[insertKolData] 处理 TikTok 用户数据时发生错误:', error)
      throw new Error(`An error occurred during the process: ${handleUnknownError(error)}.`)
    }
  }

  /**
   * 同时处理并插入用户数据到TikTokUserInfo和Milvus表
   * 使用直接映射生成embedding，避免索引匹配问题
   * @param authorsWithVideos TikTok用户数据数组
   * @returns Promise<string[]> 处理的用户uniqueIds
   */
  public async upsertTtUsersWithEmbedding(
    authorsWithVideos: TtUserDetailsAndVideos[],
  ): Promise<string[]> {
    console.log(
      `[upsertTtUsersWithDirectEmbedding] 开始处理 ${authorsWithVideos.length} 个TikTok用户数据并生成向量存储`,
    )

    try {
      const authorMap = new Map<string, TtUserDetailsAndVideos>()
      authorsWithVideos.forEach((author) => {
        authorMap.set(author.uniqueId, author)
      })

      // 并行处理用户数据和生成embedding映射
      const [processedUsers, embeddingMap] = await Promise.all([
        Promise.resolve().then(() => {
          const users = authorsWithVideos
            .map(this.processUserData.bind(this))
            .filter(
              (user): user is Omit<TikTokUserInfo, 'createdAt' | 'updatedAt'> => user !== null,
            )
          console.log(`[upsertTtUsersWithDirectEmbedding] 成功处理 ${users.length} 个用户基础数据`)
          return users
        }),
        generateDirectEmbeddingMapForTikTok(authorsWithVideos),
      ])

      console.log(`[upsertTtUsersWithDirectEmbedding] 成功生成 ${embeddingMap.size} 个嵌入向量映射`)
      const usersWithEmbeddings = processedUsers
        .map((user) => {
          const embedding = embeddingMap.get(user.uniqueId)
          const originalUser = authorMap.get(user.uniqueId)

          if (embedding && embedding.length > 0 && originalUser) {
            return {
              user,
              originalUser,
              embedding,
            }
          }
          if (!embedding || embedding.length === 0) {
            console.warn(
              `[upsertTtUsersWithDirectEmbedding] 用户 ${user.uniqueId} 没有有效的向量数据`,
            )
          }
          if (!originalUser) {
            console.warn(
              `[upsertTtUsersWithDirectEmbedding] 找不到用户 ${user.uniqueId} 的原始数据`,
            )
          }
          return null
        })
        .filter(
          (
            item,
          ): item is {
            user: Omit<TikTokUserInfo, 'createdAt' | 'updatedAt'>
            originalUser: TtUserDetailsAndVideos
            embedding: number[]
          } => item !== null,
        )

      console.log(
        `[upsertTtUsersWithDirectEmbedding] 成功匹配 ${usersWithEmbeddings.length} 个有效用户数据和向量`,
      )

      // 批量处理，每次处理200个用户
      const batchSize = 200
      const batches = []
      for (let i = 0; i < usersWithEmbeddings.length; i += batchSize) {
        batches.push(usersWithEmbeddings.slice(i, i + batchSize))
      }

      const allTikTokResults = []

      // 批量处理TikTokUserInfo数据
      for (const batch of batches) {
        const batchResults = await Bluebird.map(
          batch,
          async (item) => {
            try {
              const { userId, uniqueId, ...dataWithoutIds } = item.user
              const existingUser = await prisma.tikTokUserInfo.findUnique({
                where: { userId: userId },
              })

              let result
              if (existingUser) {
                result = await prisma.tikTokUserInfo.update({
                  where: { userId: userId },
                  data: {
                    ...dataWithoutIds,
                    videos: dataWithoutIds.videos as unknown as any,
                    publicationStats: dataWithoutIds.publicationStats as unknown as any,
                    uniqueId: uniqueId,
                  },
                })
              } else {
                result = await prisma.tikTokUserInfo.create({
                  data: {
                    ...item.user,
                    videos: item.user.videos as unknown as any,
                    publicationStats: item.user.publicationStats as unknown as any,
                  },
                })
              }

              return { success: true, data: result }
            } catch (error) {
              console.error(`处理用户 ${item.user.uniqueId} 失败:`, error)
              Sentry.captureException(error)
              return { success: false, error: error }
            }
          },
          { concurrency: 50 },
        )

        allTikTokResults.push(...batchResults)
      }

      const tikTokSuccessCount = allTikTokResults.filter((result) => result.success).length
      const tikTokFailureCount = allTikTokResults.length - tikTokSuccessCount

      console.log(
        `[upsertTtUsersWithDirectEmbedding] TikTokUserInfo处理完成。成功: ${tikTokSuccessCount}, 失败: ${tikTokFailureCount}`,
      )

      // 准备Milvus数据
      const milvusDataToInsert = usersWithEmbeddings
        .map((item) => {
          try {
            const { originalUser, embedding, user } = item
            // 构建EnhancedMilvusData对象
            const data = {
              userId: user.userId,
              account: user.uniqueId,
              nickname: user.nickname || '',
              platform: KolPlatform.TIKTOK,
              region: user.region || '',
              followerCount: Number(user.followerCount) || 0,
              averagePlayCount: Number(user.averagePlayCount) || 0,
              lastPublishedTime: Number(user.lastPublishedTime) || 0,
              signature: originalUser.signature.slice(0, 600) || '',
              videoTexts: originalUser.videos
                .slice(0, 12)
                .map((v) => (v.title || '').slice(0, 500))
                .join(' ')
                .slice(0, 6000),
              email: originalUser.email || '',
              denseVector: embedding,
              sparseVector: { indices: [], values: [] },
              createdAt: Math.floor(Date.now() / 1000),
              updatedAt: Math.floor(Date.now() / 1000),
            }
            // 强制转换类型 - 因为Milvus使用autoID，运行时会自动生成id
            return data as unknown as EnhancedMilvusData
          } catch (mapError) {
            console.error(`[upsertTtUsersWithDirectEmbedding] 处理用户数据时出错:`, mapError)
            return null
          }
        })
        .filter((data): data is EnhancedMilvusData => data !== null) as EnhancedMilvusData[]

      try {
        if (milvusDataToInsert.length === 0) {
          console.warn('[upsertTtUsersWithDirectEmbedding] 没有有效的向量数据可插入Milvus')
        } else {
          // 添加更详细的调试日志
          console.log(
            `[upsertTtUsersWithDirectEmbedding] 准备插入 ${milvusDataToInsert.length} 条记录到Milvus`,
          )
          // 检查第一条数据的结构
          const firstItem = milvusDataToInsert[0]
          console.log('[upsertTtUsersWithDirectEmbedding] 第一条数据的字段类型:')
          Object.entries(firstItem).forEach(([key, value]) => {
            console.log(`${key}: ${typeof value} ${Array.isArray(value) ? '(Array)' : ''}`)
            if (key === 'denseVector') {
              console.log(`denseVector length: ${value.length}`)
            }
            if (key === 'sparseVector') {
              console.log(`sparseVector structure:`, JSON.stringify(value))
            }
          })
          // 记录完整的数据结构（排除向量数据以避免日志过大）
          console.log(
            '[upsertTtUsersWithDirectEmbedding] 第一条数据结构:',
            JSON.stringify(
              {
                ...firstItem,
                denseVector: `[Vector with ${firstItem.denseVector.length} elements]`,
              },
              null,
              2,
            ),
          )
          const milvusResult = await bulkInsertEnhancedWithDelete(milvusDataToInsert)
          if (
            milvusResult.insertResult.err_index &&
            milvusResult.insertResult.err_index.length > 0
          ) {
            console.error(
              `[upsertTtUsersWithDirectEmbedding] Milvus插入部分失败: ${milvusResult.insertResult.err_index.length} 条记录插入失败`,
            )
            // 记录前5个失败的索引及其数据
            const failedIndices = milvusResult.insertResult.err_index.slice(0, 5)
            console.error(
              `[upsertTtUsersWithDirectEmbedding] 前5个失败索引: ${failedIndices.join(', ')}`,
            )
            console.error('[upsertTtUsersWithDirectEmbedding] 失败记录示例:')
            // 如果有具体错误消息，则记录
            if (milvusResult.insertResult.status && milvusResult.insertResult.status.reason) {
              console.error(
                `[upsertTtUsersWithDirectEmbedding] 错误原因: ${milvusResult.insertResult.status.reason}`,
              )
            }
          }
          console.log(
            `[upsertTtUsersWithDirectEmbedding] Milvus数据操作完成:\n` +
              `- 删除记录数: ${milvusResult.deleteResult.delete_cnt}\n` +
              `- 插入成功: ${milvusResult.insertResult.succ_index.length}\n` +
              `- 插入失败: ${milvusResult.insertResult.err_index?.length || 0}`,
          )
        }
      } catch (milvusError) {
        console.error('[upsertTtUsersWithDirectEmbedding] Milvus数据插入失败:', milvusError)
        // 记录更详细的错误信息
        if (milvusError instanceof Error) {
          console.error(
            `[upsertTtUsersWithDirectEmbedding] 错误详情: ${milvusError.message},错误堆栈: ${milvusError.stack}`,
          )
        }
        Sentry.captureException(milvusError)
      }
      return processedUsers.map((user) => user.uniqueId)
    } catch (error) {
      console.error('[upsertTtUsersWithDirectEmbedding] 处理失败:', error)
      Sentry.captureException(error)
      throw error
    }
  }

  async processSimilarJobByEmbedding(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data
    const params = job.data.params as SimilarTaskParams

    const task = await prisma.similarChannelTask.findUniqueOrThrow({
      where: { id },
    })
    const {
      source,
      regions,
      minSubscribers,
      maxSubscribers,
      videosAverageViews,
      maxVideosAverageViews,
    } = params
    const config = {
      useSearchWords: TIKTOK_USE_SEARCH_WORDS === 'true',
      useHashtags: TIKTOK_USE_HASHTAGS === 'true',
      useTagVideos: TIKTOK_USE_TAG_VIDEOS === 'true',
    }

    // project,candidates,exclude
    const [projectRatedUniqueIds, previousCandidatesUniqueIds, excludeUniqueIds] =
      await metrics.withPhase('get_project_all_rated_ids', () =>
        Promise.all([
          ProjectKolService.getProjectKolUniques(task.projectId, {
            platform: [KolPlatform.TIKTOK],
          }),
          this.getPreviousTasksCandidatesUniqueIds(task.projectId),
          getExcludeIds(task.createdBy!, KolPlatform.TIKTOK),
        ]),
      )

    const allRatedUniqueIds = [
      ...new Set([...projectRatedUniqueIds, ...previousCandidatesUniqueIds, ...excludeUniqueIds]),
    ]
    metrics.set('project_all_rated_ids_count', allRatedUniqueIds.length)

    const videos = await metrics.withPhase('get_source_videos', () =>
      retryUtil.retryWithValidator(
        () =>
          TiktokApi.getInstance().getUserVideos({
            cursor: 0,
            count: 30,
            unique_id: source,
          }),
        (result) => Array.isArray(result) && result.length > 0,
        3, // 重试次数
        1000, // 初始延迟
        2, // 退避系数
        (result, error, retryCount, nextDelay) => {
          if (error) {
            console.warn(`获取源作者 ${source} 视频失败，第 ${retryCount} 次重试`, {
              error,
              nextDelay,
            })
          } else if (!Array.isArray(result) || result.length === 0) {
            console.warn(`获取源作者 ${source} 视频为空，第 ${retryCount} 次重试`, { nextDelay })
          }
        },
      ),
    )

    if (!videos?.length) {
      throw new Error(`源作者 ${source} 的视频数量为0`)
    }
    metrics.set('source_videos_count', videos.length)

    const hashtagsWithIds = await metrics.withPhase('get_hashtags', () =>
      getTikTokHashTagsWithIds(videos.map((video) => video.title)),
    )
    metrics.set('hashtags_count', hashtagsWithIds.length)

    const [searchWordsUniqueIds, hashtagsUniqueIds, tagVideosUniqueIds] = await Promise.all([
      (async () => {
        try {
          if (!config.useSearchWords) return []

          const keywords = await getTikTokKeywords(videos)
          if (!keywords?.length) {
            return []
          }

          const searchWords = [...keywords, ...hashtagsWithIds.map((i) => i.tagName)]
          if (searchWords.length === 0) return []

          const results = await metrics.withPhase('search_words', () =>
            Bluebird.map(
              searchWords,
              async (searchTerm) => {
                try {
                  const searchVideos = await getTikTokSearchVideos(
                    searchTerm,
                    +TIKTOK_SEARCH_WORDS_VIDEOS_COUNT,
                  )
                  return searchVideos.map((video) => video.author.unique_id)
                } catch (searchError) {
                  console.error(
                    `[tiktokProcessJobByEmbedding] 搜索关键词 "${searchTerm}" 失败:`,
                    searchError,
                  )
                  return []
                }
              },
              { concurrency: +TIKTOK_MAX_CONCURRENCY },
            ),
          )

          return results.flat()
        } catch (error) {
          console.error('[tiktokProcessJobByEmbedding] 处理searchWords搜索时发生错误:', error)
          return []
        }
      })(),
      // 处理 hashtags 搜索
      (async () => {
        try {
          if (!config.useHashtags) return []

          const hashtags = hashtagsWithIds.map((item) => item.tagName)
          if (hashtags.length === 0) return []
          const results = await metrics.withPhase('hashtags_search', () =>
            Bluebird.map(
              hashtags,
              async (hashtag) => {
                try {
                  const hashtagVideos = await getTikTokSearchVideos(
                    hashtag,
                    +TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT,
                  )
                  return hashtagVideos.map((video) => video.author.unique_id)
                } catch (error) {
                  console.error(`[tiktokProcessJobByEmbedding] 处理hashtags搜索时发生错误:`, error)
                  return []
                }
              },
              { concurrency: +TIKTOK_MAX_CONCURRENCY },
            ),
          )

          return results.flat()
        } catch (error) {
          console.error('[tiktokProcessJobByEmbedding] 处理hashtags搜索时发生错误:', error)
          return []
        }
      })(),
      (async () => {
        if (!config.useTagVideos) return []
        try {
          const tagIds = hashtagsWithIds.map((item) => item.tagId)
          if (tagIds.length === 0) return []
          const results = await metrics.withPhase('tag_videos_search', () =>
            Bluebird.map(
              tagIds,
              async (tagId) => {
                try {
                  const videos = await TiktokApi.getInstance().getHashtagVideos(tagId, 30, 0)
                  return videos?.map((video) => video.author.unique_id) ?? []
                } catch (error) {
                  console.error(`[tiktokProcessJobByEmbedding] 获取标签 ${tagId} 视频出错:`, error)
                  return []
                }
              },
              { concurrency: +TIKTOK_MAX_CONCURRENCY },
            ),
          )
          return results.flat()
        } catch (error) {
          console.error('[tiktokProcessJobByEmbedding] 处理tag视频时发生错误:', error)
          return []
        }
      })(),
    ])

    metrics.set('search_words_unique_ids_count', searchWordsUniqueIds.length)
    metrics.set('hashtags_unique_ids_count', hashtagsUniqueIds.length)
    metrics.set('tag_videos_unique_ids_count', tagVideosUniqueIds.length)

    const allUniqueIds = new Set([
      ...searchWordsUniqueIds,
      ...hashtagsUniqueIds,
      ...tagVideosUniqueIds,
    ])
    metrics.set('all_unique_ids_count', allUniqueIds.size)

    const filteredUniqueIds = new Set(
      Array.from(allUniqueIds).filter((id) => !allRatedUniqueIds.includes(id)),
    )
    metrics.set('filtered_unique_ids_count', filteredUniqueIds.size)

    const uniqueIds = [...new Set([source, ...Array.from(filteredUniqueIds)])]
    const authors = await metrics.withPhase('get_authors_with_videos', () =>
      getAuthorsWithVideos(uniqueIds),
    )
    if (!authors?.length) return []
    metrics.set('authors_count', authors.length)

    const sourceAuthor =
      authors.find((item) => item.uniqueId === source) ?? (await getAuthorsWithVideos([source]))[0]

    if (!sourceAuthor) {
      throw new Error(`源作者 ${source} 详情获取失败`)
    }

    await metrics.withPhase('process_source_project_kol', () =>
      this.processSourceProjectKol(task.createdBy ?? undefined, params, id),
    )
    await metrics.withPhase('insert_kol_data', () =>
      this.insertKolData(task.createdBy ?? undefined, authors),
    )

    let sourceEmbedding: number[] = []
    if (sourceAuthor?.userId) {
      sourceEmbedding = await metrics.withPhase('get_source_embedding', () =>
        getDenseVectorsByUserId(sourceAuthor.userId, KolPlatform.TIKTOK),
      )
    }

    if (!sourceEmbedding?.length) {
      console.log(`[tiktokProcessJobByEmbedding] 未找到源作者 ${source} 的向量数据，生成新向量`)
      const embeddings = await metrics.withPhase('generate_embeddings', () =>
        generateEmbeddingsForTikTok([sourceAuthor]),
      )
      await this.processSourceVectorData(sourceAuthor, source, sourceEmbedding)
      sourceEmbedding = embeddings[0] || []
    }

    if (!sourceEmbedding?.length) {
      throw new Error(`无法获取源作者 ${source} 的向量数据`)
    }
    allRatedUniqueIds.push(source)
    const filter = {
      taskType: TaskType.SIMILAR,
      platform: KolPlatform.TIKTOK,
      regions: regions,
      minFollowers: minSubscribers,
      maxFollowers: maxSubscribers,
      minAveragePlayCount: videosAverageViews,
      maxAveragePlayCount: maxVideosAverageViews,
      lastPublishedAfter: Math.floor(Date.now() / 1000) - 90 * 24 * 60 * 60,
      ratedIds: allRatedUniqueIds,
    }

    const vectorSearchResult = await metrics.withPhase('vector_search', () =>
      advancedFilterSearch([sourceEmbedding], filter),
    )
    metrics.set('vector_search_result_count', vectorSearchResult?.length || 0)

    if (!vectorSearchResult?.length) {
      return []
    }

    const kols = await metrics.withPhase('db_query_kol_info', () =>
      prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.TIKTOK,
          platformAccount: {
            in: vectorSearchResult.map((item) => item.account),
          },
        },
      }),
    )
    metrics.set('after_db_filter_count', kols.length)

    if (!kols?.length) {
      return []
    }
    metrics.set('final_kols_count', kols.length)
    const results = kols.map((kol) => ({
      kolId: kol.id,
      platform: kol.platform,
      platformId: kol.platformAccount || '',
      score: vectorSearchResult.find((item) => item.account === kol.platformAccount)?.score ?? 0.0,
    }))
    return results
  }

  async processSimilarJobByVisualSimilarity(job: Bull.Job<IEmbeddingTask>): Promise<
    Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>
  > {
    const { id, metrics } = job.data
    const params = job.data.params as SimilarTaskParams
    const { source, projectId } = params

    const task = await prisma.similarChannelTask.findUniqueOrThrow({
      where: { id },
    })
    const project = await prisma.project.findFirstOrThrow({
      where: { id: projectId, deletedAt: null },
    })

    const projectCfg = (project.config ?? {}) as Partial<ProjectConfig>
    //project,candidate,exclude rated channel Ids
    const [projectRatedUniqueIds, previousCandidatesUniqueIds, excludeUniqueIds] =
      await Promise.all([
        ProjectKolService.getProjectKolUniques(task.projectId, {
          platform: [KolPlatform.TIKTOK],
        }),
        this.getPreviousTasksCandidatesUniqueIds(task.projectId),
        getExcludeIds(task.createdBy!, KolPlatform.TIKTOK),
      ])

    const allRatedUniqueIds = [
      ...new Set([...projectRatedUniqueIds, ...previousCandidatesUniqueIds, ...excludeUniqueIds]),
    ]

    const authors = await retryUtil.retryWithValidator(
      () => getUserAndVideos(source),
      (result) => result !== null && Array.isArray(result.videos) && result.videos.length > 0,
      3, // 重试次数
      1000, // 初始延迟
      2, // 退避系数
      (result, error, retryCount, nextDelay) => {
        if (error) {
          console.warn(`获取用户数据失败，第 ${retryCount} 次重试`, { error, nextDelay })
        } else if (result === null) {
          console.warn(`获取用户数据为空，第 ${retryCount} 次重试`, { nextDelay })
        } else if (!Array.isArray(result.videos) || result.videos.length === 0) {
          console.warn(`获取用户视频为空，第 ${retryCount} 次重试`, { nextDelay })
        }
      },
    )

    if (!authors || !authors.videos?.length) {
      throw new Error(`无法获取源作者 ${source} 的信息或视频数量为0`)
    }

    const sourceUser = {
      id: authors.userId,
      username: authors.uniqueId,
      fullname: authors.nickname,
      biography: authors.signature || '',
      url: authors.avatar,
      posts: authors.videos,
    }

    const [verticalAnalysis, videoCoverAnalysis] = await Promise.all([
      metrics.withPhase('analyze_vertical_similarity', () =>
        retryUtil.retryWithValidator(
          () => {
            if (PROJECT_THREE_ELEMENTS_TYPE === 'ai') {
              console.log(
                `[tiktokProcessJobByVisualSimilarity] 开始视觉分析源作者 ${source} 的垂类信息...`,
              )
              return analyzeVisualSimilarityService.analysisUserVeticalSimilarity(sourceUser)
            } else {
              console.log(
                `[tiktokProcessJobByVisualSimilarity] 直接从projectConfig获取 ${source} 的垂类信息...`,
              )
              return Promise.resolve({
                success: true,
                data: {
                  kolDescription: params.kolDescription ?? projectCfg.kolDescription ?? '',
                  allowList: params.allowList ?? projectCfg.allowList ?? [],
                  banList: params.banList ?? projectCfg.banList ?? [],
                },
              })
            }
          },
          (result: any) => {
            return result.success && result.data && result.data.kolDescription
          },
          3, // 重试次数
          1000, // 初始延迟
          2, // 退避系数
          (result: any, error: any, retryCount: number, nextDelay: number) => {
            console.warn(`垂类分析第 ${retryCount} 次重试`, {
              result,
              error,
              nextDelay,
            })
          },
        ),
      ),
      metrics.withPhase('analyze_video_covers', () =>
        retryUtil.retryWithValidator(
          () =>
            analyzeVisualSimilarityService.analyzeVideoCovers(
              authors.videos
                .map((video) => ({
                  cover: video.cover || video.origin_cover || '',
                  title: video.title,
                }))
                .filter((item) => item.cover),
            ),
          (result: any) => {
            return (
              result !== null &&
              result !== undefined &&
              (Array.isArray(result.tags) || Array.isArray(result.searchTerms)) &&
              (result.tags?.length > 0 || result.searchTerms?.length > 0)
            )
          },
          3, // 重试次数
          1000, // 初始延迟
          2, // 退避系数
          (result: any, error: any, retryCount: number, nextDelay: number) => {
            console.warn(`视频封面分析第 ${retryCount} 次重试`, {
              result,
              error,
              nextDelay,
            })
          },
        ),
      ),
    ])

    if (!verticalAnalysis.success || !verticalAnalysis.data) {
      throw new Error(`源作者 ${source} 的垂类分析失败: ${verticalAnalysis.error || '未知错误'}`)
    }

    const { kolDescription, allowList, banList } = verticalAnalysis.data
    metrics.set('three_elements', { kolDescription, allowList, banList })

    if (
      !videoCoverAnalysis ||
      (!videoCoverAnalysis.tags?.length && !videoCoverAnalysis.searchTerms?.length)
    ) {
      throw new Error(`源作者 ${source} 的视频封面分析失败，没有获取到标签和搜索词`)
    }

    let { tags = [], searchTerms = [] } = videoCoverAnalysis
    metrics.set('search_terms_count', searchTerms.length)

    const tagsWithIds = await getTikTokHashTagsWithIds(authors.videos.map((video) => video.title))
    metrics.set('tags_with_ids_count', tagsWithIds.length)

    const realTags = tagsWithIds.map((item) => item.tagName)
    searchTerms = [...new Set([...tags.filter((item) => !realTags.includes(item)), ...searchTerms])]

    const [searchTermUniqueIds, tagAuthorUniqueIds] = await Promise.all([
      (async () => {
        if (searchTerms.length === 0) return []

        const results = await metrics.withPhase('search_terms_search', () =>
          Bluebird.map(
            searchTerms.slice(0, 10),
            async (term) => {
              try {
                const videos = await getSearchVideosFiltered(term, {
                  maxVideoCount: 30,
                  regions: [...new Set(params.regions || [])],
                })
                return videos.map((video) => video.author.unique_id)
              } catch (error) {
                console.error(`搜索关键词 "${term}" 失败:`, error)
                return []
              }
            },
            { concurrency: 10 },
          ),
        )

        return [...new Set(results.flat())]
      })(),
      (async () => {
        if (tagsWithIds.length === 0) return []

        try {
          console.log('开始使用tag下的视频uniqueId')
          const results = await metrics.withPhase('tags_search', () =>
            Bluebird.map(
              tagsWithIds.map((item) => item.tagId).slice(0, 10),
              async (tagId) => {
                try {
                  const videos = await getHashtagVideosFiltered(tagId, {
                    maxVideoCount: 30,
                    regions: [...new Set(params.regions || [])],
                  })
                  return videos.map((video) => video.author.unique_id)
                } catch (error) {
                  console.error(`获取标签 ${tagId} 的视频时出错:`, error)
                  return []
                }
              },
              { concurrency: 10 },
            ),
          )

          return [...new Set(results.flat())]
        } catch (error) {
          console.error('处理tag视频时发生错误:', error)
          return []
        }
      })(),
    ])

    metrics.set('search_term_unique_ids_count', searchTermUniqueIds.length)
    metrics.set('tag_author_unique_ids_count', tagAuthorUniqueIds.length)

    // rm duplicate from allRatedUniqueIds
    const allUniqueIds = [...new Set([...searchTermUniqueIds, ...tagAuthorUniqueIds])].filter(
      (id) => !allRatedUniqueIds.includes(id),
    )
    metrics.set('all_unique_ids_count', allUniqueIds.length)

    if (allUniqueIds.length === 0) return []

    const candidateAuthors = await metrics.withPhase('get_candidate_authors', () =>
      getAuthorsWithVideos(allUniqueIds),
    )
    const validCandidateAuthors = candidateAuthors.filter((author) => author && author.uniqueId)
    metrics.set('valid_candidate_authors_count', validCandidateAuthors.length)

    if (validCandidateAuthors.length === 0) return []

    const batchSize = 20
    const batches = Array.from(
      { length: Math.ceil(validCandidateAuthors.length / batchSize) },
      (_, i) => validCandidateAuthors.slice(i * batchSize, (i + 1) * batchSize),
    )
    metrics.set('batch_count', batches.length)

    const analysisResults = await metrics.withPhase('analyze_visual_similarity', () =>
      Bluebird.map(
        batches,
        async (batch, batchIndex) => {
          try {
            console.log(`开始处理第 ${batchIndex + 1}/${batches.length} 批用户...`)
            const visualAnalysisInput = batch.map((author) => ({
              username: author.uniqueId,
              posts: author.videos,
            }))

            const batchResult = await analyzeVisualSimilarityService.analyzeVisualSimilarity(
              visualAnalysisInput,
              {
                kolDescription: kolDescription ?? '',
                allowList: allowList ?? [],
                banList: banList ?? [],
              },
            )
            console.log(
              `第 ${batchIndex + 1} 批视觉相似度分析完成，获得 ${batchResult.length} 个结果`,
            )
            return batchResult
          } catch (error) {
            console.error(`处理第 ${batchIndex + 1} 批时出错:`, error)
            return []
          }
        },
        { concurrency: 30 },
      ),
    )

    const allResults = analysisResults.flat()
    metrics.set('all_visual_similarity_results', allResults)

    if (allResults.length === 0) return []

    const validVisionAuthors = allResults
      .filter((result: VisualSimilarityOutput) => result.score === 100)
      .map(
        (result: VisualSimilarityOutput) =>
          validCandidateAuthors.find(
            (author: TtUserDetailsAndVideos) => author.uniqueId === result.username,
          )!,
      )
      .filter(
        (author: TtUserDetailsAndVideos | undefined): author is TtUserDetailsAndVideos =>
          author !== undefined,
      )

    if (validVisionAuthors.length === 0) return []
    metrics.set('valid_vision_authors_count', validVisionAuthors.length)

    // upsert ttUsers and kols
    await Promise.all([
      metrics.withPhase('upsert_tt_users', () => this.upsertTtUsers(validVisionAuthors)),
      metrics.withPhase('upsert_kols', () =>
        this.upsertKols(task.createdBy ?? undefined, validVisionAuthors),
      ),
    ])

    await metrics.withPhase('process_source_project_kol', () =>
      this.processSourceProjectKol(task.createdBy ?? undefined, params, id),
    )

    const similarUsernames = validVisionAuthors.map((author) => author.uniqueId)

    const conditions = {
      uniqueId: {
        in: similarUsernames,
      },
      followerCount: {
        gte: params.minSubscribers ? Number(params.minSubscribers) : undefined,
        lte: params.maxSubscribers ? Number(params.maxSubscribers) : undefined,
      },
      region: params.regions?.length ? { in: params.regions } : undefined,
      averagePlayCount: {
        gte: params.videosAverageViews ? Number(params.videosAverageViews) : undefined,
        lte: params.maxVideosAverageViews ? Number(params.maxVideosAverageViews) : undefined,
      },
      lastPublishedTime: {
        gte: dayjs().subtract(+TIKTOK_DEFAULT_LAST_PUBLISHED_DAYS, 'day').unix(),
      },
    }

    console.log('conditions', conditions)
    const users = await prisma.tikTokUserInfo.findMany({
      where: conditions,
    })
    if (users.length === 0) return []
    metrics.set('after_db_filter_count', users.length)

    const kolInfos = await metrics.withPhase('get_tiktok_kol_info', () =>
      this.getTikTokKolInfo(users.map((user) => user.uniqueId)),
    )

    if (kolInfos.length === 0) return []
    metrics.set('final_kols_count', kolInfos.length)

    const results = kolInfos
      .map((kol) => ({
        kolId: kol.id,
        platform: kol.platform,
        platformId: kol.platformAccount || '',
        score:
          (allResults.find(
            (result: VisualSimilarityOutput) => result.username === kol.platformAccount,
          )?.score ?? 0) / 100,
      }))
      .filter((result) => result.score > 0 && result.platformId)

    return results
  }

  private async createKolInfoFormRapidAPI(
    userId: string | undefined,
    uniqueId: string,
  ): Promise<KolInfo | null> {
    try {
      if (!uniqueId) {
        throw new Error('没有提供 TikTok 用户 uniqueId')
      }
      const tiktokUser: TiktokUser | null = await TiktokApi.getInstance().getUserDetail({
        unique_id: uniqueId,
      })
      if (!tiktokUser || !tiktokUser.user) {
        console.error('没有找到 TikTok 用户信息或用户信息不完整:', uniqueId)
        return null
      }
      // 检查TikTokUserInfo是否存在
      const existingTikTokUser = await prisma.tikTokUserInfo.findUnique({
        where: { uniqueId: uniqueId },
      })

      // 增加一步处理粉丝数，双重处理，保险。
      if (tiktokUser.stats.followerCount && existingTikTokUser) {
        await prisma.tikTokUserInfo.update({
          where: { uniqueId: uniqueId },
          data: { followerCount: BigInt(tiktokUser.stats.followerCount) },
        })
      } else if (tiktokUser.stats.followerCount) {
        // 如果用户不存在，记录日志但不尝试更新
        console.warn(`TikTokUserInfo记录不存在，无法更新粉丝数: ${uniqueId}`)
      }

      // 首先尝试查找现有的 KOL 记录
      const existingKol = await prisma.kolInfo.findFirst({
        where: {
          platform: KolPlatform.TIKTOK,
          platformAccount: tiktokUser.user.uniqueId,
        },
      })

      const kolData = {
        title: tiktokUser.user.nickname,
        description: tiktokUser.user.signature,
        updatedAt: new Date(),
      }
      if (existingKol) {
        const emailFields: EmailFields = {
          email: undefined,
          emailSource: undefined,
          emailUpdatedAt: undefined,
          historyEmails: undefined,
        }
        const needUpdateEmail =
          tiktokUser.user.email && tiktokUser.user.email !== existingKol?.email
        if (needUpdateEmail) {
          emailFields.email = tiktokUser.user.email
          emailFields.emailSource = tiktokUser.user.emailSource
          emailFields.emailUpdatedAt = new Date()
          emailFields.historyEmails = [
            ...(existingKol.historyEmails as string[]),
            tiktokUser.user.email as string,
          ]
          EmailService.getInstance().addEmailAuditLog(
            userId,
            existingKol.id,
            emailFields.email,
            emailFields.emailSource,
          )
        }

        return await prisma.kolInfo.update({
          where: { id: existingKol.id },
          data: {
            ...kolData,
            ...emailFields,
            historyEmails: emailFields.historyEmails ?? existingKol.historyEmails,
          },
        })
      } else {
        return await prisma.kolInfo.create({
          data: {
            ...kolData,
            historyEmails: tiktokUser.user.email ? [tiktokUser.user.email] : [],
            platformAccount: tiktokUser.user.uniqueId,
            platform: KolPlatform.TIKTOK,
          },
        })
      }
    } catch (error) {
      console.error('Error in createKolInfoForTikTokUser:', error)
      Sentry.captureException(error)
      return null
    }
  }

  // 处理tiktok的用户数据
  private processUserData(
    data: TtUserDetailsAndVideos,
  ): Omit<TikTokUserInfo, 'createdAt' | 'updatedAt'> | null {
    if (!data || typeof data !== 'object') {
      console.error('Invalid user data:', data)
      return null
    }

    const { userId, uniqueId, nickname, avatar, followerCount, videos } = data

    if (!userId || !uniqueId) {
      console.error('Missing required fields in user data:', data)
      return null
    }

    const averagePlayCount = this.calculateAveragePlayCount(videos)
    const lastPublishedTime = this.calculateLastPublishedTime(videos)
    const region = this.processRegion(videos)
    const safeFollowerCount = followerCount ?? 0

    // 计算发布统计
    const publicationStats = PublicationStatsService.calculatePublicationStats(
      videos,
      KolPlatform.TIKTOK,
    )

    return {
      userId: userId,
      uniqueId: uniqueId,
      nickname: nickname || '',
      avatar: avatar || '',
      region: region,
      followerCount: BigInt(safeFollowerCount),
      lastPublishedTime: BigInt(lastPublishedTime || 0),
      haveCrawlered: true,
      averagePlayCount: BigInt(averagePlayCount || 0),
      videos: JSON.stringify(videos) as unknown as Prisma.JsonValue,
      publicationStats: publicationStats as unknown as Prisma.JsonValue,
    }
  }
  // 根据TtVideoBasicInfo处理region字段
  private processRegion(videos: TtVideoBasicInfo[]): string {
    if (!videos?.length) return ''
    const recentVideos = [...videos]
      .sort((a, b) => (b.create_time || 0) - (a.create_time || 0))
      .slice(0, 10)

    const regionCounts: Record<string, number> = recentVideos.reduce(
      (counts, video) => {
        const region = video.region
        if (region) {
          counts[region] = (counts[region] || 0) + 1
        }
        return counts
      },
      {} as Record<string, number>,
    )

    if (Object.keys(regionCounts).length === 0) return ''

    return Object.entries(regionCounts).sort((a, b) => b[1] - a[1])[0][0]
  }

  private calculateAveragePlayCount(videos: TtVideoBasicInfo[]): number {
    if (videos.length === 0) {
      return 0
    }
    const recentVideos = [...videos]
      .sort((a, b) => (b.create_time || 0) - (a.create_time || 0))
      .slice(0, 10)
    if (recentVideos.length <= 2) {
      const totalPlayCount = recentVideos.reduce((sum, video) => sum + video.play_count, 0)
      return Math.round(totalPlayCount / recentVideos.length)
    }
    const sortedByPlayCount = recentVideos.sort((a, b) => a.play_count - b.play_count)
    const filteredVideos = sortedByPlayCount.slice(1, -1)
    const totalPlayCount = filteredVideos.reduce((sum, video) => sum + video.play_count, 0)
    return Math.round(totalPlayCount / filteredVideos.length)
  }

  // 计算最后发布时间，存储的是秒级的时间戳
  private calculateLastPublishedTime(videos: TtVideoBasicInfo[]): number {
    if (videos.length === 0) {
      // 如果没有视频，返回100天前的时间戳
      return Math.floor(Date.now() / 1000) - 100 * 24 * 60 * 60
    }

    // 按创建时间降序排序视频
    const sortedVideos = videos.sort((a, b) => (b.create_time || 0) - (a.create_time || 0))

    // 返回最新视频的创建时间
    return sortedVideos[0].create_time || Math.floor(Date.now() / 1000) - 100 * 24 * 60 * 60
  }

  // 获取tiktok 的 kolInfo
  public async getTikTokKolInfo(uniqueIds: string[]): Promise<KolInfo[]> {
    if (uniqueIds.length === 0) {
      return []
    }
    const kolInfos = await prisma.kolInfo.findMany({
      where: {
        platform: KolPlatform.TIKTOK,
        platformAccount: { in: uniqueIds },
      },
    })
    return kolInfos
  }

  // 获取tiktok 的 tiktokUserInfo
  public async getTikTokUserInfoByUniqueIds(uniqueIds: string[]): Promise<TikTokUserInfo[]> {
    if (uniqueIds.length === 0) {
      return []
    }
    return await prisma.tikTokUserInfo.findMany({
      where: { uniqueId: { in: uniqueIds } },
      omit: {
        videos: false,
      },
    })
  }

  // 获取tiktok 的 tiktokUserInfo
  public async getTikTokUserInfoByUserIds(userIds: string[]): Promise<TikTokUserInfo[]> {
    if (userIds.length === 0) {
      return []
    }
    return await prisma.tikTokUserInfo.findMany({
      where: { userId: { in: userIds } },
      omit: {
        videos: false,
      },
    })
  }

  // 获取 TikTok 用户所在地区
  public async getTikTokUserRegion(uniqueId: string): Promise<string> {
    try {
      const api = TiktokApi.getInstance()
      const videos = await api.getUserVideos({ unique_id: uniqueId, count: 10, cursor: 0 })

      if (!videos) {
        return 'UNKNOWN'
      }

      const regionStats: Record<string, number> = {}
      videos.forEach((video) => {
        const region = video.region || 'UNKNOWN'
        regionStats[region] = (regionStats[region] || 0) + 1
      })

      // 返回出现次数最多的地区
      const sortedRegions = Object.entries(regionStats).sort(([, a], [, b]) => b - a)

      return sortedRegions[0]?.[0] || ' UNKNOWN'
    } catch (error) {
      console.error('get user region error:', error)
      return 'UNKNOWN'
    }
  }

  public async tiktokProcessHashTagBreakJob(
    taskParams: TtHashTagBreakTaskParams,
    taskId: string,
  ): Promise<TtHashTagBreakTaskResult> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('任务不存在')
    }
    console.log(`[tiktokProcessHashTagBreakJob] 开始处理标签 ${taskParams.tag}`)

    const hashTagInfo = await TiktokApi.getInstance().getHashTag(taskParams.tag)
    if (!hashTagInfo || !hashTagInfo.id) {
      throw new Error(`标签 ${taskParams.tag} 不存在或无法获取信息`)
    }
    const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
      platform: [KolPlatform.TIKTOK],
    })

    console.log(
      `[tiktokProcessHashTagBreakJob] 获取到 ${projectKolUniques.length} 该项目下已经评价过的用户`,
    )
    const options = {
      ratedUniqueIds: projectKolUniques || [],
    }

    const result = await getUniqueIdsFromHashTagForTtHashTagBreak(
      hashTagInfo.id,
      taskParams.cursor,
      taskParams.maxVideoCount || 100, // 每次任务获取最大的视频数
      options,
    )
    console.log(`[tiktokProcessHashTagBreakJob] 获取到 ${result.uniqueIds.length} 个用户ID`)

    const authors = await getAuthorsWithVideos(result.uniqueIds)
    console.log(`[tiktokProcessHashTagBreakJob] 成功获取 ${authors.length} 个用户详细信息`)

    await Promise.all([
      this.upsertTtUsers(authors),
      this.upsertKols(task.createdBy ?? undefined, authors),
    ])

    const uniqueIds = authors.map((author) => author.uniqueId)
    const response: TtHashTagBreakTaskResult = {
      taskId: taskId,
      tag: taskParams.tag,
      uniqueIds: uniqueIds,
      cursor: result.cursor,
      hasMore: result.hasMore,
      message: result.message,
      total: result.total,
      count: authors.length,
      progress: {
        total: hashTagInfo.user_count,
        current: result.cursor,
        videoCount: (taskParams.currentVideoCount ?? 0) + (taskParams.maxVideoCount ?? 100),
      },
    }

    console.log(`[tiktokProcessHashTagBreakJob] 处理完成，共处理 ${authors.length} 个用户`)
    return response
  }

  public async tiktokProcessSearchInputJob(
    taskParams: TtSearchInputBreakTaskParams,
    taskId: string,
  ): Promise<TtSearchInputBreakTaskResult> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('任务不存在')
    }
    console.log(`[tiktokProcessSearchInputJob] 开始处理搜索词 ${taskParams.searchInput}`)

    const { searchInput, cursor } = taskParams
    const result = await getTikTokSearchVideosForKeywordBreak(
      searchInput,
      cursor,
      taskParams.sortType ?? 0,
      taskParams.publishTimeType ?? 0,
      taskParams.maxVideoCount ?? 100,
    )
    console.log(`[tiktokProcessSearchInputJob] 获取到 ${result.uniqueIds.length} 个用户ID`)

    const authors = await getAuthorsWithVideos(result.uniqueIds)
    console.log(`[tiktokProcessSearchInputJob] 成功获取 ${authors.length} 个用户详细信息`)

    await Promise.all([
      TiktokService.getInstance().upsertTtUsers(authors),
      TiktokService.getInstance().upsertKols(task.createdBy ?? undefined, authors),
    ])

    const response: TtSearchInputBreakTaskResult = {
      taskId: taskId,
      searchInput: taskParams.searchInput,
      uniqueIds: authors.map((author) => author.uniqueId),
      cursor: result.cursor,
      hasMore: result.hasMore,
      totalAuthorCount: result.total,
      afterFilterAuthorCount: authors.length,
      videoCount: result.videoCount,
      progress: {
        videoCount: result.videoCount + (taskParams.currentVideoCount ?? 0),
      },
    }
    console.log(
      `[tiktokProcessSearchInputJob] 处理完成，共处理 ${result.videoCount} 个视频,共获取到 ${result.total} 个用户`,
    )
    return response
  }

  public async tiktokProcessFollowersSimilarJob(
    taskParams: TtFollowersSimilarTaskParams,
    taskId: string,
  ): Promise<TtFollowersSimilarTaskResult> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('任务不存在')
    }
    console.log(
      `[tiktokProcessFollowersSimilarJob] 开始处理任务 ${taskId}, 目标用户: ${taskParams.uniqueId}`,
    )

    try {
      // 获取通过粉丝列表获取相似用户
      const authors = await getSimilarUsers(taskParams.uniqueId, taskParams.excludeWords)
      console.log(`[tiktokProcessFollowersSimilarJob] 获取到 ${authors.length} 个相似用户`)

      // 并发执行持久化操作
      console.log(`[tiktokProcessFollowersSimilarJob] 开始持久化用户数据`)
      await Promise.all([
        this.upsertTtUsers(authors).then((ids) =>
          console.log(`[tiktokProcessFollowersSimilarJob] 完成TikTok用户信息更新: ${ids.length}个`),
        ),
        this.upsertKols(task.createdBy ?? undefined, authors).then((kols) =>
          console.log(`[tiktokProcessFollowersSimilarJob] 完成KOL信息更新: ${kols.length}个`),
        ),
      ])

      const uniqueIds = authors.map((author: TtUserDetailsAndVideos) => author.uniqueId)
      const response: TtFollowersSimilarTaskResult = {
        taskId: taskId,
        uniqueIds: uniqueIds,
        total: authors.length,
        count: authors.length,
      }

      console.log(
        `[tiktokProcessFollowersSimilarJob] 任务 ${taskId} 处理完成，共处理 ${authors.length} 个用户`,
      )
      return response
    } catch (error) {
      console.error(`[tiktokProcessFollowersSimilarJob] 任务 ${taskId} 处理失败:`, error)
      throw error
    }
  }
  // 返回的是similarChannelTask的result
  // meta字段在handleComplete中处理
  public async tiktokProcessFollowingListJob(
    taskParams: TtFollowingListTaskParams,
    taskId: string,
  ): Promise<TtFollowingListTaskResult> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('任务不存在')
    }
    const { userId, currentTime, currentCount, maxCount } = taskParams
    try {
      // 获取关注列表
      const { users, total, hasMore, time, followingCount } = await getFollowingUsers(
        userId,
        currentTime,
        maxCount,
      )
      console.log(`[tiktokProcessFollowingListJob] 获取到 ${users.length} 个关注用户`)

      await Promise.all([
        this.upsertTtUsers(users).then((ids) =>
          console.log(`[tiktokProcessFollowingListJob] 完成TikTok用户信息更新: ${ids.length}个`),
        ),
        this.upsertKols(task.createdBy ?? undefined, users).then((kols) =>
          console.log(`[tiktokProcessFollowingListJob] 完成KOL信息更新: ${kols.length}个`),
        ),
      ])

      const uniqueIds = users.map((user: TtUserDetailsAndVideos) => user.uniqueId)
      const response: TtFollowingListTaskResult = {
        taskId: taskId,
        uniqueIds: uniqueIds,
        total: users.length,
        following: followingCount,
        hasMore: hasMore,
        time: time,
        progress: {
          total: total,
          current: currentCount + maxCount,
          count: currentCount + users.length,
        },
      }
      console.log(
        `[tiktokProcessFollowingListJob] 任务 ${taskId} 处理完成，共处理 ${users.length} 个用户`,
      )
      return response
    } catch (error) {
      console.error(`[tiktokProcessFollowingListJob] 任务 ${taskId} 处理失败:`, error)
      throw error
    }
  }

  public async tiktokProcessBgmBreakJob(
    taskParams: TtBgmBreakTaskParams,
    taskId: string,
  ): Promise<TtBgmBreakTaskResult> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })
    if (!task) {
      throw new Error('任务不存在')
    }
    console.log(
      `[tiktokProcessBgmBreakJob] 开始处理任务 ${taskId}, 目标音乐: ${taskParams.musicUrl}`,
    )

    try {
      // 验证音乐是否存在
      const musicInfo = await TiktokApi.getInstance().getMusicInfo(taskParams.musicId)
      if (!musicInfo || !musicInfo.data.id || musicInfo.data.video_count == 0) {
        throw new Error(`音乐 ${taskParams.musicUrl} 不存在或音乐下没有视频`)
      }
      // 获取项目中已评价过的用户列表，用于去重
      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.TIKTOK],
      })
      console.log(
        `[tiktokProcessBgmBreakJob] 获取到 ${projectKolUniques.length} 该项目下已经评价过的用户`,
      )
      const options = {
        ratedUniqueIds: projectKolUniques || [],
      }
      const maxUniqueAuthors = 100 // 每次任务获取最大的用户数

      // 使用封装的方法获取唯一作者ID
      const result = await getUniqueIdsFromMusicForTtBgmBreak(
        taskParams.musicId,
        taskParams.cursor,
        maxUniqueAuthors,
        options,
      )

      console.log(`[tiktokProcessBgmBreakJob] 获取到 ${result.uniqueIds.length} 个用户ID`)

      // 获取作者详细信息
      const authors = await getAuthorsWithVideos(result.uniqueIds)
      console.log(`[tiktokProcessBgmBreakJob] 成功获取 ${authors.length} 个用户详细信息`)

      // 更新用户数据
      await Promise.all([
        this.upsertTtUsers(authors),
        this.upsertKols(task.createdBy ?? undefined, authors),
      ])

      const uniqueIds = authors.map((author) => author.uniqueId)
      const response: TtBgmBreakTaskResult = {
        taskId: taskId,
        musicUrl: taskParams.musicUrl,
        uniqueIds: uniqueIds,
        cursor: result.cursor,
        hasMore: result.hasMore,
        message: result.message,
      }
      console.log(
        `[tiktokProcessBgmBreakJob] 任务 ${taskId} 处理完成, 获取到 ${authors.length} 个唯一作者`,
      )
      return response
    } catch (error) {
      console.error(`[tiktokProcessBgmBreakJob] 处理音乐列表时出错: ${error}`)
      return {
        taskId,
        musicUrl: taskParams.musicUrl,
        uniqueIds: [],
        hasMore: false,
        cursor: taskParams.cursor,
      }
    }
  }

  public async tiktokProcessWebListJob(
    taskParams: TtWebListTaskParams,
    taskId: string,
  ): Promise<TtWebListTaskResult> {
    try {
      const task = await prisma.similarChannelTask.findUnique({
        where: { id: taskId },
      })
      if (!task) {
        throw new Error('任务不存在')
      }
      const urls = taskParams.urls || []
      if (urls.length === 0) {
        throw new Error('urls is empty')
      }

      const projectKolUniques = await ProjectKolService.getProjectKolUniques(taskParams.projectId, {
        platform: [KolPlatform.TIKTOK],
      })
      console.log(
        `[tiktokProcessWebListJob] 获取到 ${projectKolUniques.length} 该项目下已经评价过的用户`,
      )

      const usernames = await parseUrlUtils.extractTiktokUsernames(urls)

      // 过滤掉已经评价过的用户
      const filteredUsernames = usernames.filter(
        (username) => !projectKolUniques.includes(username),
      )

      if (filteredUsernames.length === 0) {
        throw new Error('无法从提供的URL中提取有效的TikTok用户名')
      }

      try {
        const authors = await getAuthorsWithVideos(filteredUsernames)
        console.log(`[tiktokProcessWebListJob] 成功获取 ${authors.length} 个用户详细信息`)

        await Promise.all([
          this.upsertTtUsers(authors),
          this.upsertKols(task.createdBy ?? undefined, authors),
        ])

        const uniqueIds = authors.map((author) => author.uniqueId)
        const response: TtWebListTaskResult = {
          taskId: taskId,
          uniqueIds: uniqueIds,
          message: 'success',
        }
        console.log(
          `[tiktokProcessWebListJob] 任务 ${taskId} 处理完成, 获取到 ${authors.length} 个唯一作者`,
        )
        return response
      } catch (error) {
        console.error(`[tiktokProcessWebListJob] 获取或更新作者信息失败: ${error}`)
        throw error
      }
    } catch (error: any) {
      console.error(`[tiktokProcessWebListJob] 处理失败: ${error}`)
      return {
        taskId,
        uniqueIds: [],
        message: `error: ${JSON.stringify(error) || '未知错误'}`,
      }
    }
  }

  /**
   * 获取历史任务的candidatesUsername
   */
  private async getPreviousTasksCandidatesUniqueIds(projectId: string): Promise<string[]> {
    const previousTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    if (!previousTasks.length) {
      return []
    }
    const allCandidatesUniqueIds: string[] = []

    for (const task of previousTasks) {
      if (task.candidate && Array.isArray(task.candidate)) {
        // 如果candidates是数组，直接处理
        (
          task.candidate as Array<{
            kolId: string
            platform: KolPlatform
            platformId: string
            score: number
          }>
        )
          .filter((candidate) => candidate.platform === KolPlatform.TIKTOK)
          .forEach((candidate) => {
            if (candidate.platformId) {
              allCandidatesUniqueIds.push(candidate.platformId)
            }
          })
      }
    }

    return allCandidatesUniqueIds
  }
}

export default TiktokService
