import { prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'

describe('kol tests', function () {
  describe('test get kol info', function () {
    it('should return specified kol info', async () => {
      const kol = await prisma.kolInfo.findFirst({
        where: {
          platform: 'INSTAGRAM',
          platformAccount: 'hannaukim',
        },
      })
      expect(kol).not.toBeUndefined()
      expect(kol!.platformAccount).eq('hannaukim')
    })
  })
})
