import { TaskType } from '@repo/database'
import { describe, expect, it } from 'vitest'
import { findCurrentTasksByProjectId } from './similar'
describe('should test about similars', () => {
  it('should find current project tasks', async () => {
    const projectId = 'cm4ii1knp000h10y7otd3lu5i'
    const tasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
    expect(tasks.length).eq(1)
  })
})
