// ttInfoService
import { Tik<PERSON><PERSON>om<PERSON>, TiktokCommentUser } from '@/api/@types/rapidapi/Tiktok'
import Tiktok<PERSON><PERSON> from '@/api/tiktok'
import {
  TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT,
  TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT,
  TIKTOK_MAX_COMMENT_COUNT,
} from '@/config/env'
import { getUserPortraitAnalysis } from '@/services/aiTools/userPortaitAnslysisWithoutReason'
import {
  AudienceAnalysisResult,
  CountryAnalysisResult,
  RegionAnalysisResult,
  UserPortraitLLMInput,
  UserPortraitLLMOutput,
  UserPortraitResult,
} from '@/types/audience'
import { TtVideoCommentsUsersAndRegionStatisticsResponse } from '@/types/response/ttInfo'
import { statisticsUserRegion } from '@/utils/country'
import Bluebird from 'bluebird'

const maxCommentCount = +TIKTOK_MAX_COMMENT_COUNT || 1000
const getAuthorRecentlyVideosCount = +TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT || 10
const getAuthorRecentlyVideosCommentsCount =
  +TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT || 1000
const maxAnalysisUsersCount = 100

/**
 * 获取视频评论用户数据并统计地区分布
 */
async function getVideoCommentsUsersWithStatistics(
  url: string,
): Promise<TtVideoCommentsUsersAndRegionStatisticsResponse> {
  const api = TiktokApi.getInstance()
  let cursor = 0
  let allCommentsUsers: TiktokCommentUser[] = []

  // 获取评论用户数据
  while (true) {
    const result = await api.getVideoComments({
      url,
      cursor,
      count: Math.min(50, maxCommentCount - allCommentsUsers.length),
    })

    if (!result?.comments?.length) {
      break
    }
    const commentUsers = result.comments.map((comment: TiktokComment) => comment.user)
    allCommentsUsers = [...allCommentsUsers, ...commentUsers]

    if (allCommentsUsers.length >= maxCommentCount || !result.hasMore) {
      break
    }

    cursor = result.cursor
  }

  const regionStats: Record<string, number> = {}
  const formattedUsers = allCommentsUsers.map((user) => {
    const region = user.region || '未知'
    regionStats[region] = (regionStats[region] || 0) + 1

    return {
      userId: user.id,
      uniqueId: user.unique_id,
      nickname: user.nickname,
      region: region,
    }
  })

  // 对地区统计进行排序
  const sortedRegionStats: Record<string, number> = Object.fromEntries(
    Object.entries(regionStats).sort(([, a], [, b]) => b - a),
  )

  // 修改从 URL 中提取视频 Id 的逻辑
  const videoId = url.match(/video\/(\d+)/)?.[1] || ''

  const response: TtVideoCommentsUsersAndRegionStatisticsResponse = {
    videoId,
    total: formattedUsers.length,
    region: sortedRegionStats,
    commentsUsers: formattedUsers,
  }
  return response
}

/**
 * 获取作者最近视频的评论用户
 */
async function getAuthorVideosCommentsUsers(
  uniqueId: string,
  pageSize: number = 50,
  maxCommentsCount: number = getAuthorRecentlyVideosCommentsCount,
  maxVideosCount: number = getAuthorRecentlyVideosCount,
): Promise<TiktokCommentUser[]> {
  try {
    const api = TiktokApi.getInstance()
    const MAX_COMMENTS = maxCommentsCount
    const CONCURRENCY = 50
    const MAX_VIDEOS = maxVideosCount
    const MAX_PAGES_PER_VIDEO = 5

    console.log(`[tt] 获取用户 ${uniqueId} 的视频`)
    const authorVideos = await api.getUserVideos({ unique_id: uniqueId, count: 30, cursor: 0 })

    if (!authorVideos?.length) {
      console.error(`[tt] 未找到用户 ${uniqueId} 的视频`)
      return []
    }

    const filteredVideos = authorVideos
      .filter((video) => !video.is_ad)
      .sort((a, b) => b.comment_count - a.comment_count)
      .slice(0, MAX_VIDEOS)

    console.log(`[tt] 将处理 ${filteredVideos.length} 个评论最多的视频`)

    const commentUserIdSet = new Set<string>()
    const commentUserMap = new Map<string, TiktokCommentUser>()

    async function processVideoComments(video: any): Promise<void> {
      if (commentUserIdSet.size >= MAX_COMMENTS) {
        return
      }

      let cursor = 0
      let pageCount = 0

      while (pageCount < MAX_PAGES_PER_VIDEO && commentUserIdSet.size < MAX_COMMENTS) {
        try {
          console.log(
            `[tt] 获取视频 ${video.video_id} 的第 ${pageCount + 1} 页评论${cursor ? '(分页)' : '(首页)'}`,
          )

          const result = await api.getVideoComments({
            url: video.video_id,
            cursor,
            count: Math.min(pageSize, MAX_COMMENTS - commentUserIdSet.size),
          })

          if (!result?.comments?.length) {
            console.log(`[tt] 视频 ${video.video_id} 没有更多评论`)
            break
          }

          console.log(
            `[tt] 视频 ${video.video_id} 第 ${pageCount + 1} 页获取到 ${result.comments.length} 条评论`,
          )

          for (const comment of result.comments) {
            if (comment.user && comment.user.id && !commentUserIdSet.has(comment.user.id)) {
              commentUserIdSet.add(comment.user.id)
              commentUserMap.set(comment.user.id, comment.user)
            }
          }

          if (
            result.hasMore &&
            pageCount < MAX_PAGES_PER_VIDEO - 1 &&
            commentUserIdSet.size < MAX_COMMENTS
          ) {
            cursor = result.cursor
            pageCount++
            console.log(`[tt] 视频 ${video.video_id} 将获取第 ${pageCount + 1} 页评论`)
          } else {
            if (!result.hasMore) {
              console.log(`[tt] 视频 ${video.video_id} 没有更多评论页`)
            } else {
              console.log(`[tt] 视频 ${video.video_id} 达到最大页数限制或已收集足够用户`)
            }
            break
          }

          if (pageCount % 2 === 0) {
            console.log(
              `[tt] 视频 ${video.video_id} 已处理 ${pageCount} 页评论，当前已收集 ${commentUserMap.size} 个用户`,
            )
          }
        } catch (error) {
          console.error(`[tt] 获取视频 ${video.video_id} 评论失败:`, error)
          break
        }
      }
    }

    console.log(`[tt] 开始并行处理 ${filteredVideos.length} 个视频的评论，并发数: ${CONCURRENCY}`)
    await Bluebird.map(filteredVideos, processVideoComments, { concurrency: CONCURRENCY })

    const commentUsers = Array.from(commentUserMap.values())
    console.log(`[tt] 成功获取 ${commentUsers.length} 个评论者的用户信息`)

    return commentUsers
  } catch (error) {
    console.error('[tt] 获取TikTok评论者用户信息失败:', error)
    return []
  }
}

/**
 * 统计评论用户的地区分布
 * @deprecated 使用 statisticsUserRegion 替代
 */
async function statisticsCommentsUsers(
  commentsUsers: TiktokCommentUser[],
): Promise<CountryAnalysisResult> {
  // 统计各地区数量
  const regionStats: Record<string, number> = {}
  commentsUsers.forEach((user) => {
    const region = user.region || '未知'
    regionStats[region] = (regionStats[region] || 0) + 1
  })

  const total = commentsUsers.length

  const statistics = Object.entries(regionStats)
    .map(([region, count]) => ({
      region,
      count,
      percentage: `${((count / total) * 100).toFixed(2)}%`,
    }))
    .sort((a, b) => b.count - a.count)

  const filteredStatistics = statistics.filter(
    (item) =>
      !(item.region.toLowerCase() === 'us' && parseFloat(item.percentage.replace('%', '')) < 10),
  )

  return {
    total,
    statistics: filteredStatistics,
  }
}

/**
 * 获取作者评论区用户的用户画像
 * @param commentsUsers 评论用户列表
 * @returns 用户画像
 */
async function getAuthorCommentsUsersPortrait(
  commentsUsers: TiktokCommentUser[],
): Promise<UserPortraitLLMOutput> {
  const users: UserPortraitLLMInput[] = commentsUsers.map((user) => {
    return {
      username: user.nickname,
      avatar: user.avatar,
      signature: user.signature || '',
    }
  })

  console.log(`[getAuthorCommentsUsersPortrait] 处理了 ${users.length} 个用户数据`)
  const result = await getUserPortraitAnalysis(users)
  return result
}

/**
 * 获取作者评论区用户的受众分析
 * @param uniqueId 作者ID
 * @returns 受众分析结果
 */
async function getAuthorAudienceAnalysis(uniqueId: string): Promise<AudienceAnalysisResult> {
  const userCommentUsers = await getAuthorVideosCommentsUsers(uniqueId)

  if (!userCommentUsers?.length) {
    throw new Error('未找到评论用户数据')
  }
  console.log(`[getAuthorAudienceAnalysis] 获取了 ${userCommentUsers.length} 个评论用户数据`)

  // 限制分析用户数量，避免请求过大
  const needAgeAndGenderAnalysisUsers = userCommentUsers.slice(0, maxAnalysisUsersCount)

  const [userPortraitResult, regionAnalysisResult] = await Promise.all([
    getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisUsers).catch((error) => {
      console.error('[getAuthorAudienceAnalysis] 用户画像分析失败:', error)
      return {
        gender: { male: 0, female: 0 },
        age: { under18: 0, age18to25: 0, age25to45: 0, above45: 0 },
      } as UserPortraitLLMOutput
    }),
    statisticsUserRegion(userCommentUsers).catch((error) => {
      console.error('[getAuthorAudienceAnalysis] 国家分析失败:', error)
      return {
        total: 0,
        statistics: [],
        developmentStatistics: [],
      } as RegionAnalysisResult
    }),
  ])

  // 验证返回的数据结构是否正确
  if (!userPortraitResult || typeof userPortraitResult !== 'object') {
    throw new Error('用户画像数据格式错误')
  }

  if (!regionAnalysisResult || typeof regionAnalysisResult !== 'object') {
    throw new Error('地区分析数据格式错误')
  }

  return {
    userPortraitResult: formatToPercentage(userPortraitResult),
    regionAnalysisResult,
  }
}

const formatToPercentage = (result: any): UserPortraitResult => {
  return {
    gender: {
      male: `${result.gender.male}%`,
      female: `${result.gender.female}%`,
    },
    age: {
      under18: `${result.age.under18}%`,
      age18to25: `${result.age.age18to25}%`,
      age25to45: `${result.age.age25to45}%`,
      above45: `${result.age.above45}%`,
    },
  }
}

/**
 * 获取单个视频的评论用户
 * @param videoId 视频ID
 * @param pageSize 每页评论数量
 * @param maxCommentsCount 最大评论数量
 * @returns 评论用户列表
 */
async function getVideosCommentsUsers(
  videoId: string,
  pageSize: number = 50,
  maxPages: number = 20,
  maxCommentsCount: number = maxCommentCount,
): Promise<TiktokCommentUser[]> {
  try {
    const api = TiktokApi.getInstance()

    console.log(`[tt] 获取视频 ${videoId} 的评论用户`)

    const commentUserIdSet = new Set<string>()
    const commentUserMap = new Map<string, TiktokCommentUser>()

    let cursor = 0
    let pageCount = 0

    while (pageCount < maxPages && commentUserIdSet.size < maxCommentsCount) {
      try {
        console.log(
          `[tt] 获取视频 ${videoId} 的第 ${pageCount + 1} 页评论${cursor ? '(分页)' : '(首页)'}`,
        )

        const result = await api.getVideoComments({
          url: videoId,
          cursor,
          count: Math.min(pageSize, maxCommentCount - commentUserIdSet.size),
        })

        if (!result?.comments?.length) {
          console.log(`[tt] 视频 ${videoId} 没有更多评论`)
          break
        }

        console.log(
          `[tt] 视频 ${videoId} 第 ${pageCount + 1} 页获取到 ${result.comments.length} 条评论`,
        )

        for (const comment of result.comments) {
          if (comment.user && comment.user.id && !commentUserIdSet.has(comment.user.id)) {
            commentUserIdSet.add(comment.user.id)
            commentUserMap.set(comment.user.id, comment.user)
          }
        }

        if (
          result.hasMore &&
          pageCount < maxPages - 1 &&
          commentUserIdSet.size < maxCommentsCount
        ) {
          cursor = result.cursor
          pageCount++
          console.log(`[tt] 视频 ${videoId} 将获取第 ${pageCount + 1} 页评论`)
        } else {
          if (!result.hasMore) {
            console.log(`[tt] 视频 ${videoId} 没有更多评论页`)
          } else {
            console.log(`[tt] 视频 ${videoId} 达到最大页数限制或已收集足够用户`)
          }
          break
        }
      } catch (error) {
        console.error(`[tt] 获取视频 ${videoId} 评论失败:`, error)
        break
      }
    }

    const commentUsers = Array.from(commentUserMap.values())
    console.log(`[tt] 成功获取 ${commentUsers.length} 个评论者的用户信息`)

    return commentUsers
  } catch (error) {
    console.error('[tt] 获取TikTok视频评论者用户信息失败:', error)
    return []
  }
}

async function getPostAudience(videoId: string): Promise<AudienceAnalysisResult> {
  try {
    const commentUsers = await getVideosCommentsUsers(videoId)

    if (!commentUsers?.length) {
      return {
        userPortraitResult: {},
        regionAnalysisResult: {},
      } as AudienceAnalysisResult
    }
    console.log(`[getPostAudience] 获取了 ${commentUsers.length} 个评论用户数据`)

    const needAgeAndGenderAnalysisUsers = commentUsers.slice(0, 300)

    const portraitPromise = getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisUsers)
    const regionPromise = statisticsUserRegion(commentUsers)
    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      portraitPromise,
      regionPromise,
    ])

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    } as AudienceAnalysisResult
  } catch (error) {
    console.error(`[getPostAudience] 视频 ${videoId} 受众分析失败:`, error)
    throw error
  }
}

export const TtInfoService = {
  getVideoCommentsUsersWithStatistics,
  getAuthorVideosCommentsUsers,
  statisticsCommentsUsers,
  getAuthorCommentsUsersPortrait,
  getAuthorAudienceAnalysis,
  getPostAudience,
  getVideosCommentsUsers,
}
