import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'

export class TokenService {
  private static instance: TokenService
  private readonly LATEST_TOKEN_PREFIX = `${REDIS_KEY_PREFIX}user_latest_token:`

  private constructor() {}

  public static getInstance(): TokenService {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService()
    }
    return TokenService.instance
  }

  async setLatestToken(userId: string, token: string): Promise<void> {
    const key = this.getTokenKey(userId)
    await redis.set(key, token)
  }

  async getToken(userId: string): Promise<string | null> {
    const key = this.getTokenKey(userId)
    return await redis.get(key)
  }

  async validateToken(userId: string, token: string): Promise<boolean> {
    const key = this.getTokenKey(userId)
    const latestToken = await redis.get(key)
    if (!latestToken) {
      await this.setLatestToken(userId, token)
      return true
    }

    if (latestToken !== token) {
      return false
    }

    return true
  }

  private getTokenKey(userId: string): string {
    return `${this.LATEST_TOKEN_PREFIX}${userId}`
  }
}
