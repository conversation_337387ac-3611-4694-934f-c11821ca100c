import { Tik<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TiktokFollowing } from '@/api/@types/rapidapi/Tiktok'
import InstagramApi from '@/api/instagram.ts'
import TiktokApi from '@/api/tiktok.ts'
import YoutubeApi from '@/api/youtube.ts'
import { throwError } from '@/common/errors/statusCodes'
import { twitter241Api } from '@/lib/twitter241Api'
import { twitterDescriptionAnalysisService } from '@/services/aiTools/twitterDescriptionAnalysis'
import { analyzeVisualSimilarityService } from '@/services/aiTools/visualSimilarity'
import { getUserAndVideos } from '@/services/worker/tiktokBreak.service'
import { SimilarUser, TtVideoBasicInfo } from '@/types/kol.ts'
import { createTaskRequest } from '@/types/request/similar.request.ts'
import { TtUserDetailsAndVideos } from '@/types/tiktokUsers'
import { extractEmail } from '@/utils/email'
import {
  KolPlatform,
  SimilarChannelTask,
  SimilarChannelTaskStatus,
  TaskType,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import { getProjectCandidatesWithPostsByTaskType } from './project'

export async function findTasksByProjectId(projectId: string) {
  // 首先查看这个项目是否存在
  const project = await prisma.project.findFirst({
    where: { id: projectId, deletedAt: null },
  })
  if (!project) {
    throw new Error('project not found')
  }
  // 根据 similarTask 查出来有多少 tasks
  const similarTasks = await prisma.similarChannelTask.findMany({
    where: {
      projectId: projectId,
      // status: { in: [SimilarChannelTaskStatus.COMPLETED, SimilarChannelTaskStatus.RESULT_READY] },
    },
    orderBy: {
      createdAt: 'asc',
    },
  })
  if ((similarTasks as SimilarChannelTask[]).length === 0) {
    return []
  }
  return similarTasks
}

export const findLatestByTaskType = async (
  projectId: string,
  taskType: TaskType,
): Promise<SimilarChannelTask | null> => {
  const tasks = await findTasksByProjectId(projectId)
  const tasksByType = tasks.filter((task) => task.type === taskType && task.isCompleted)

  if (tasksByType.length === 0) {
    return null
  }

  const latestTask = tasksByType.reduce((prev, current) =>
    prev.updatedAt > current.updatedAt ? prev : current,
  )

  if (latestTask.isTerminated) {
    return null
  }

  return latestTask
}

//找到最新的未终结的一批任务
export const findTasksByTaskType = async (
  projectId: string,
  taskType: TaskType,
): Promise<SimilarChannelTask[]> => {
  const tasks = await findTasksByProjectId(projectId)
  // 升序排序
  const tasksByType = tasks
    .filter((task) => task.type === taskType && task.isCompleted)
    .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())

  if (tasksByType.length === 0) {
    return []
  }

  let index = 0
  for (let i = 0; i < tasksByType.length; i++) {
    if (tasksByType[i].isTerminated) {
      index = i + 1
    }
  }
  // 降序排序
  return tasksByType.slice(index).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
}

export async function findCurrentTasksByProjectId(projectId: string, taskType: TaskType) {
  const tasks = await findTasksByProjectId(projectId)
  if (tasks.length === 0) {
    return []
  }
  let index = 0
  for (let i = 0; i < tasks.length; i++) {
    if (tasks[i].isTerminated) {
      index = i + 1
    }
  }
  return tasks.slice(index).filter((t) => t.type === taskType)
}

// 获取region uniqueIds
export async function getRegionUniqueIds(task: SimilarChannelTask[]): Promise<string[]> {
  if (task.length === 0) {
    throw new Error('task is empty')
  }
  const results = task.map((t) => t.result)
  const ttRegionUniqueIds: string[] = []
  results.forEach((result) => {
    const resultData = result as any
    if (resultData && resultData.uniqueIds) {
      ttRegionUniqueIds.push(...resultData.uniqueIds)
    }
  })
  console.log(`get ${ttRegionUniqueIds.length} region unique ids`)
  return ttRegionUniqueIds
}

/**
 * 终止指定项目中最新的特定类型任务
 * @param projectId - 项目ID
 * @param taskId - 当前任务ID（需要排除）
 * @param type - 任务类型
 * @param createdAt - 创建时间
 */
export async function terminateLatestTaskByType(
  projectId: string,
  taskId: string,
  type: TaskType,
  createdAt: Date,
): Promise<void> {
  try {
    const latestTask = await findLatestTaskToTerminate(projectId, taskId, type, createdAt)
    if (!latestTask) {
      return
    }
    await Promise.all(
      [
        terminateTask(latestTask.id),
        handleProjectCandidate(projectId, type),
        // type === TaskType.SIMILAR && updateProjectConfig(projectId),
      ].filter(Boolean),
    )

    console.log(`已终止任务: ${latestTask.id}, 类型: ${type}`)
  } catch (error) {
    console.error('终止任务失败:', error)
    // 静默处理错误，继续执行
  }
}

/**
 * 查找需要终止的最新任务
 */
async function findLatestTaskToTerminate(
  projectId: string,
  taskId: string,
  type: TaskType,
  createdAt: Date,
): Promise<SimilarChannelTask | null> {
  return prisma.similarChannelTask.findFirst({
    where: {
      projectId,
      type: type as TaskType,
      id: { not: taskId },
      createdAt: { lt: createdAt },
    },
    orderBy: { createdAt: 'desc' },
  })
}

/**
 * 终止指定任务
 */
async function terminateTask(taskId: string): Promise<void> {
  await prisma.similarChannelTask.update({
    where: { id: taskId },
    data: { isTerminated: true },
  })
}

/**
 * 处理项目候选数据
 */
async function handleProjectCandidate(projectId: string, type: TaskType): Promise<void> {
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId,
        type: type as TaskType,
      },
    },
  })

  if (projectCandidate) {
    const updateData = { candidates: {}, meta: {} }

    await prisma.projectCandidate.update({
      where: { id: projectCandidate.id },
      data: updateData,
    })
  }

  const project = await prisma.project.findUnique({
    where: { id: projectId },
    select: { candidates: true },
  })

  if (project?.candidates) {
    const currentCandidates = project.candidates as Record<string, any>

    if (currentCandidates[type]) {
      delete currentCandidates[type]

      await prisma.project.update({
        where: { id: projectId },
        data: {
          candidates: currentCandidates,
        },
      })
    }
  }
}

/**
 * 更新项目配置
 */
async function updateProjectConfig(projectId: string): Promise<void> {
  await prisma.project.update({
    where: { id: projectId },
    data: { config: {} },
  })
}

export async function getFollowingUsers(
  userId: string,
  time: number = 0,
  count?: number,
): Promise<{
  users: TtUserDetailsAndVideos[]
  total: number
  followingCount: number
  time: number
  hasMore: boolean
}> {
  const followingResponse = await TiktokApi.getInstance().getUserFollowing(
    userId,
    count || 200,
    time,
  )
  if (!followingResponse) {
    throw new Error('无法获取到该博主的关注列表')
  }
  console.log(`获取到 ${followingResponse.followings.length} 个关注用户`)

  // 把关注列表转换为基础的用户数组
  const basicUsers: SimilarUser[] = followingResponse.followings.map((following) => ({
    userId: following.id || '',
    uniqueId: following.unique_id || '',
    nickname: following.nickname || '',
    region: following.region || '',
    avatar: following.avatar || '',
    signature: following.signature || '',
    followerCount: following.follower_count || 0,
    insId: following.ins_id || '',
    youtubeChannelId: following.youtube_channel_id || '',
    twitterId: following.twitter_id || '',
    email: extractEmail(following.signature || '') || '',
    count: 0,
  }))

  // 并发处理每个用户的视频信息
  const enrichedUsers = await Bluebird.map(
    basicUsers,
    async (user) => {
      try {
        const videosResponse = await TiktokApi.getInstance().getUserVideos({
          user_id: user.userId,
          unique_id: user.uniqueId,
          count: 12,
          cursor: 0,
        })

        const videos = videosResponse || []
        return await processUserWithVideos(user, videos)
      } catch (error) {
        console.error(`处理用户 ${user.uniqueId} 的视频信息失败:`, error)
        return null
      }
    },
    { concurrency: 30 },
  )

  const validUsers = enrichedUsers.filter((user): user is TtUserDetailsAndVideos => user !== null)

  return {
    users: validUsers,
    total: followingResponse.total,
    followingCount: followingResponse.followings.length,
    time: followingResponse.time,
    hasMore: followingResponse.hasMore,
  }
}

export async function getSimilarUsers(
  uniqueId: string,
  excludeWords?: string[],
): Promise<TtUserDetailsAndVideos[]> {
  try {
    console.log(`[getSimilarUsers] 开始处理目标用户: ${uniqueId}`)

    const targetUser = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
    if (!targetUser || !targetUser.user.id) {
      throw new Error(`无法获取用户信息: ${uniqueId}`)
    }
    console.log(`[getSimilarUsers] 成功获取目标用户信息: ${targetUser.user.nickname}`)

    const initialFollowers = await getFollowers(targetUser.user.id, 200, 0)
    if (!initialFollowers.length) {
      throw new Error('无法获取到该博主的粉丝数据')
    }
    console.log(`[getSimilarUsers] 成功获取 ${initialFollowers.length} 个初始粉丝`)

    const userCountMap = new Map<string, number>()
    const allFollowings = new Map<string, SimilarUser>()
    let processedFollowers = 0
    let skippedFollowers = 0

    await Bluebird.map(
      initialFollowers,
      async (follower: TiktokFollower) => {
        try {
          const followingResponse = await TiktokApi.getInstance().getUserFollowing(follower.id, 200)
          if (!followingResponse?.followings) {
            skippedFollowers++
            return
          }

          // 初步筛选：只处理粉丝数小于50万的用户
          const validFollowings = followingResponse.followings.filter(
            (user) => (user.follower_count || 0) < 50_0000,
          )

          validFollowings.forEach((user: TiktokFollowing) => {
            if (!allFollowings.has(user.id)) {
              allFollowings.set(user.id, {
                userId: user.id || '',
                uniqueId: user.unique_id || '',
                nickname: user.nickname || '',
                avatar: user.avatar || '',
                signature: user.signature || '',
                followerCount: user.follower_count || 0,
                region: user.region || '',
                insId: user.ins_id || '',
                youtubeChannelId: user.youtube_channel_id || '',
                twitterId: user.twitter_id || '',
                email: extractEmail(user.signature || '') || '',
                count: 0,
              })
            }
            // 统计出现次数
            userCountMap.set(user.id, (userCountMap.get(user.id) || 0) + 1)
          })

          processedFollowers++
          if (processedFollowers % 10 === 0) {
            console.log(
              `[getSimilarUsers] 关注列表处理进度: ${processedFollowers}/${initialFollowers.length}, 已跳过: ${skippedFollowers}`,
            )
          }
        } catch (error) {
          console.error(`[getSimilarUsers] 获取用户 ${follower.unique_id} 的关注列表失败:`, error)
          skippedFollowers++
        }
      },
      { concurrency: 25 },
    )

    console.log(
      `[getSimilarUsers] 完成关注列表获取, 成功处理: ${processedFollowers}, 跳过: ${skippedFollowers}`,
    )

    // 不翻页，截取800个用户
    const sortedUsers = Array.from(allFollowings.values())
      .map((user) => ({
        ...user,
        count: userCountMap.get(user.userId) || 0,
      }))
      .sort((a, b) => {
        // 首先按出现次数排序，然后按粉丝数排序
        if (b.count !== a.count) return b.count - a.count
        return b.followerCount - a.followerCount
      })
      .slice(0, 800)

    console.log(`[getSimilarUsers] 初步筛选出 ${sortedUsers.length} 个候选用户`)

    const validUsers = await Bluebird.map(
      sortedUsers,
      async (user) => {
        try {
          const videosResponse = await TiktokApi.getInstance().getUserVideos({
            unique_id: user.uniqueId,
            count: 12,
            cursor: 0,
          })

          const videos = videosResponse || []

          const checkText = [user.signature, ...videos.map((video) => video.title || '')].join(' ')

          if (excludeWords && containsExcludedWords(checkText, excludeWords)) {
            console.log(`[getSimilarUsers] 用户 ${user.uniqueId} 因包含排除词被过滤`)
            return null
          }

          // 处理用户视频信息
          return await processUserWithVideos(user, videos)
        } catch (error) {
          console.error(`[getSimilarUsers] 处理用户 ${user.uniqueId} 失败:`, error)
          return null
        }
      },
      { concurrency: 25 },
    )

    // 7. 过滤掉无效用户并返回前200个
    const finalUsers = validUsers
      .filter((user): user is TtUserDetailsAndVideos => user !== null)
      .slice(0, 200)
    console.log(`[getSimilarUsers] 最终获取到 ${finalUsers.length} 个有效用户`)
    return finalUsers
  } catch (error) {
    console.error('[getSimilarUsers] 获取相似用户失败:', error)
    throw error
  }
}

// 获取用户的粉丝
async function getFollowers(
  userId: string,
  count: number = 200,
  cursor: number = 0,
): Promise<TiktokFollower[]> {
  try {
    const response = await TiktokApi.getInstance().getUserFollowers(userId, count, cursor)
    if (!response?.followers || response.followers.length === 0) {
      console.log('未获取到粉丝数据')
      return []
    }

    console.log(`成功获取 ${response.followers.length} 个粉丝`)
    return response.followers
  } catch (error) {
    console.error('获取粉丝列表失败:', error)
    return []
  }
}

// 处理用户视频信息
async function processUserWithVideos(
  user: SimilarUser,
  videos: TikTokVideo[],
): Promise<TtUserDetailsAndVideos> {
  if (!videos || videos.length === 0) {
    return {
      ...user,
      followingCount: 0,
      heartCount: 0,
      videoCount: 0,
      verified: false,
      privateAccount: false,
      instagramId: user.insId,
      twitterId: user.twitterId,
      youtubeChannelTitle: '',
      youtubeChannelId: user.youtubeChannelId,
      videos: [],
    }
  }

  // 转换视频数据
  const videoInfos: TtVideoBasicInfo[] = videos.map((video) => ({
    videoId: video.video_id || '',
    region: video.region || '',
    title: video.title || '',
    cover: video.cover || '',
    ai_dynamic_cover: video.ai_dynamic_cover || '',
    origin_cover: video.origin_cover || '',
    play: video.play || '',
    wmplay: video.wmplay || '',
    size: video.size || 0,
    play_count: video.play_count || 0,
    digg_count: video.digg_count || 0,
    comment_count: video.comment_count || 0,
    share_count: video.share_count || 0,
    download_count: video.download_count || 0,
    collect_count: video.collect_count || 0,
    create_time: video.create_time || 0,
    is_ad: video.is_ad || false,
  }))

  return {
    ...user,
    followingCount: 0,
    heartCount: 0,
    videoCount: videos.length,
    verified: false,
    privateAccount: false,
    instagramId: user.insId,
    twitterId: user.twitterId,
    youtubeChannelTitle: '',
    youtubeChannelId: user.youtubeChannelId,
    videos: videoInfos,
  }
}

/**
 * 检查字符串是否包含排除词列表中的任何词
 * @param text 要检查的字符串
 * @param excludeWords 排除词列表
 * @returns 如果包含任何排除词返回true，否则返回false
 */
export function containsExcludedWords(text: string, excludeWords?: string[]): boolean {
  // 如果没有排除词或文本为空，返回false
  if (!excludeWords?.length || !text) {
    return false
  }

  // 将文本转换为小写以进行不区分大小写的比较
  const lowerText = text.toLowerCase()

  // 检查是否包含任何排除词
  return excludeWords.some((word) => lowerText.includes(word.toLowerCase()))
}

export async function similarSearchForAllPlatforms(projectId: string) {
  if (!projectId.length) {
    throwError(400, 'project id is required!')
  }
  const latestTask = await prisma.similarChannelTask.findFirstOrThrow({
    where: {
      projectId: projectId,
      type: TaskType.SIMILAR,
      status: {
        in: [SimilarChannelTaskStatus.COMPLETED],
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
  const platform = (latestTask.params as createTaskRequest).platform as KolPlatform

  return getProjectCandidatesWithPostsByTaskType(projectId, TaskType.SIMILAR, platform)
}

export async function analysisUserKolDescription(
  id: string,
  handler: string,
  platform: KolPlatform,
  language: string,
) {
  let sourceUser = null

  switch (platform) {
    case KolPlatform.TIKTOK: {
      const userDetails = await getUserAndVideos(handler)

      if (!userDetails) {
        throw new Error('未找到TikTok用户')
      }

      sourceUser = {
        id: userDetails.userId,
        username: userDetails.uniqueId,
        fullname: userDetails.nickname,
        biography: userDetails.signature,
        url: userDetails.avatar,
        posts: userDetails.videos || [],
      }
      break
    }

    case KolPlatform.INSTAGRAM: {
      const user = await InstagramApi.getInstance().getUserWithPosts(handler)

      if (!user) {
        throw new Error('未找到Instagram用户')
      }

      sourceUser = {
        id: user.id,
        username: user.username,
        fullname: user.fullName,
        biography: user.biography,
        url: user.profilePicUrl,
        posts: user.posts || [],
      }
      break
    }

    case KolPlatform.YOUTUBE: {
      const channelId =
        id || (handler && (handler.includes('/') ? handler.split('/').pop() : handler))
      if (!channelId) {
        throw new Error('无效的YouTube频道ID或用户名')
      }

      const channel = await YoutubeApi.getInstance().getChannelWithVideos(channelId)

      if (!channel) {
        throw new Error('未找到YouTube频道')
      }

      sourceUser = {
        id: channel.channelId,
        username: channel.channelId,
        fullname: channel.title,
        biography: channel.description,
        url: channel.avatar?.[0]?.url || '',
        posts: channel.videos || [],
      }
      break
    }

    case KolPlatform.TWITTER: {
      const user = await twitter241Api.getUserByScreenName(handler)
      if (!user) {
        throw new Error('未找到Twitter用户')
      }

      if (!user.description || user.description.trim().length === 0) {
        return ''
      }

      const threeElements = await twitterDescriptionAnalysisService.generateThreeElements(
        user.description,
        language,
      )
      console.log('threeElements', threeElements)
      return threeElements.kolDescription
    }
    default:
      throw new Error('不支持的平台')
  }

  if (!sourceUser) {
    throw new Error('获取用户信息失败')
  }

  return await analyzeVisualSimilarityService.analysisUserKolDescription(sourceUser, language)
}

export async function checkRecentSimilarTask(params: createTaskRequest): Promise<boolean> {
  const twoMonthsAgo = new Date()
  twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2)

  const tasks = await prisma.similarChannelTask.findMany({
    where: {
      projectId: params.projectId,
      status: SimilarChannelTaskStatus.COMPLETED,
      type: TaskType.SIMILAR,
      createdAt: {
        gte: twoMonthsAgo,
      },
    },
  })

  const matchingTasks = tasks.filter((task) => {
    const taskParams = task.params as createTaskRequest
    if (taskParams.source !== params.source || taskParams.platform !== params.platform) {
      return false
    }
    // tt ytb 不涉及related

    // if (params.platform === KolPlatform.TIKTOK && params.ttMode !== undefined) {
    //   return Number(taskParams.ttMode) === Number(params.ttMode)
    // } else if (params.platform === KolPlatform.YOUTUBE && params.ytbMode !== undefined) {
    //   return Number(taskParams.ytbMode) === Number(params.ytbMode)
    // }
    return true
  })

  return matchingTasks.length > 0
}
