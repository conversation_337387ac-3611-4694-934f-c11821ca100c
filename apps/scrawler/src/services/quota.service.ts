import { PaginatedResponse, PaginationParams } from '@/types/pagination'
import {
  DailyQuotaResult,
  DateFilter,
  EnterpriseQuotaDailyUsageResult,
  EnterpriseQuotaDetailResult,
  EnterpriseQuotaTypeUsageStats,
  QuotaDetailResult,
  UserQuotaTypeUsageStats,
} from '@/types/quotaDetails'
import { PaginationService } from '@/utils/pagination'
import { QuotaType, prisma } from '@repo/database'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'
dayjs.extend(utc)
dayjs.extend(timezone)

// 提取的通用函数
// 获取配额使用统计
const getQuotaUsageStats = async (
  userId: string | null,
  enterpriseId: string | null,
  timeRange: number = 30,
): Promise<Record<string, number>> => {
  // 构建查询条件
  const conditions = []

  if (userId) {
    conditions.push(`"userId" = '${userId}'`)
  }

  if (enterpriseId) {
    conditions.push(`"enterpriseId" = '${enterpriseId}'`)
  }

  conditions.push(`usage != 0`)
  conditions.push(
    `"createdAt" >= (NOW() AT TIME ZONE 'Asia/Shanghai') - INTERVAL '${timeRange} days'`,
  )

  // 将条件组合成WHERE子句
  const whereClause = conditions.join(' AND ')

  // 使用模板字符串构建完整的SQL查询
  const query = `
    WITH all_quota_types AS (
      SELECT unnest(enum_range(NULL::"QuotaType")) AS type
    ),
    quota_usage AS (
      SELECT 
        type, 
        COALESCE(SUM(usage), 0) AS total_usage
      FROM 
        "QuotaLogs"
      WHERE 
        ${whereClause}
      GROUP BY 
        type
    )
    SELECT 
      t.type::text AS quota_type,
      COALESCE(u.total_usage, 0) AS total_usage
    FROM 
      all_quota_types t
    LEFT JOIN 
      quota_usage u ON t.type = u.type
    ORDER BY 
      total_usage DESC, quota_type ASC
  `

  // 执行查询
  const rawResults =
    await prisma.$queryRawUnsafe<Array<{ quota_type: string; total_usage: string }>>(query)

  return rawResults.reduce((acc: Record<string, number>, item) => {
    acc[item.quota_type] = Number(item.total_usage)
    return acc
  }, {})
}

// 获取失败任务记录
const getFailedQuotaLogs = async (
  userId: string | null,
  enterpriseId: string | null,
  timeRange: number = 30,
) => {
  const whereClause: any = {
    type: { in: [QuotaType.FAILED_SEARCH, QuotaType.FAILED_TASK] },
    taskId: { not: null },
    createdAt: { gte: dayjs().tz('Asia/Shanghai').subtract(timeRange, 'days').toDate() },
  }

  if (userId) {
    whereClause.userId = userId
  }

  if (enterpriseId) {
    whereClause.enterpriseId = enterpriseId
  }

  return prisma.quotaLog.findMany({
    where: whereClause,
    include: {
      task: {
        select: {
          type: true,
          reason: true,
        },
      },
    },
  })
}

// 处理失败任务并更新配额统计
const processFailedTasks = (
  failedQuotaLogs: Array<any>,
  quotaTypeUsageStats: Record<string, number>,
): Record<string, number> => {
  // 按taskId分组，确保每个taskId只处理一次
  const taskIdGroups = failedQuotaLogs.reduce(
    (acc: Record<string, Array<any>>, log: any) => {
      if (!log.taskId || !log.task) return acc

      if (!acc[log.taskId]) {
        acc[log.taskId] = []
      }
      acc[log.taskId].push(log)
      return acc
    },
    {} as Record<string, Array<any>>,
  )

  // 处理每个taskId组
  Object.values(taskIdGroups).forEach((logs) => {
    // 如果同一个taskId既有FAILED_SEARCH又有FAILED_TASK，优先使用FAILED_TASK
    const failedTaskLog = logs.find((log) => log.type === QuotaType.FAILED_TASK)
    const logToProcess = failedTaskLog || logs[0]

    if (!logToProcess.task) return

    let quotaType: string | null = null

    if (logToProcess.task.type === 'KEYWORD') {
      quotaType = 'AI_SEARCH'
    } else if (logToProcess.task.type === 'SIMILAR') {
      quotaType = 'SIMILAR_SEARCH'
    } else if (logToProcess.task.type === 'HASH_TAG_BREAK') {
      quotaType = 'TT_HASH_TAG_BREAK'
    } else if (logToProcess.task.type === 'SEARCH_INPUT_BREAK') {
      quotaType = 'TT_SEARCH_INPUT_BREAK'
    } else if (logToProcess.task.type === 'FOLLOWERS_SIMILAR') {
      quotaType = 'TT_FOLLOWERS_SIMILAR'
    } else if (logToProcess.task.type === 'FOLLOWING_LIST') {
      quotaType = 'TT_FOLLOWING_LIST'
    } else {
      return
    }

    if (quotaType && quotaTypeUsageStats[quotaType.toLowerCase()] !== undefined) {
      quotaTypeUsageStats[quotaType.toLowerCase()] += logToProcess.usage
    }
  })

  return quotaTypeUsageStats
}

// 过滤不需要的配额类型
const filterUnwantedQuotaTypes = (
  quotaTypeUsageStats: Record<string, number>,
): Record<string, number> => {
  const result = { ...quotaTypeUsageStats }
  Object.keys(result).forEach((key) => {
    if (
      key.toLowerCase().includes('failed_search') ||
      key.toLowerCase().includes('failed_task') ||
      key.toLowerCase().includes('admin') ||
      key.toLowerCase().includes('reset') ||
      key.toLowerCase().includes('expire') ||
      key.toLowerCase().includes('tt_search_keywords')
    ) {
      delete result[key]
    }
  })
  return result
}

// 模块组织
const QuotaService = {
  // 获取用户每日配额
  getDailyQuota: async (
    userId: string,
    dateFilter: DateFilter = {},
    pagination: PaginationParams = {},
  ): Promise<PaginatedResponse<DailyQuotaResult>> => {
    const { gte, lte } = dateFilter
    const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

    const user = await prisma.userInfo.findUnique({
      where: { userId },
      include: {
        membership: true,
      },
    })

    if (!user) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        userId: user.userId,
        type: {
          notIn: [QuotaType.RESET, QuotaType.ADMIN, QuotaType.EXPIRE],
        },
        usage: { not: 0 },
        createdAt: {
          ...(gte && { gte: gte }),
          ...(lte && { lte: lte }),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    if (!quotaLogs) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const [cardQueryByDayResults, dailyUsage] = await Promise.all([
      (async () => {
        const cardQueryLogs = quotaLogs.filter((log) => log.type === QuotaType.CARD_QUERY)
        const cardQueryByDay = cardQueryLogs.reduce((acc: Record<string, number>, log) => {
          const day = dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD')
          acc[day] = (acc[day] || 0) + log.usage
          return acc
        }, {})
        const cardQueryByDayResults = Object.entries(cardQueryByDay).map(([day, count]) => ({
          day,
          count,
        }))
        return cardQueryByDayResults
      })(),
      (async () => {
        return quotaLogs.reduce((acc: Map<string, number>, log) => {
          const date = dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD')
          acc.set(date, (acc.get(date) || 0) + log.usage)
          return acc
        }, new Map())
      })(),
    ])

    const allResults = Array.from(dailyUsage.entries())
      .map(([date, usage]) => ({
        date: new Date(date),
        userId: user.userId,
        email: user?.email || '',
        daily_usage: usage,
        card_query_usage: cardQueryByDayResults.find((item) => item.day === date)?.count || 0,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime())

    const total = allResults.length
    const totalPages = Math.ceil(total / pageSize)
    const paginatedResults = allResults.slice(skip, skip + pageSize)

    return {
      data: paginatedResults,
      total,
      page,
      pageSize,
      totalPages,
    }
  },

  // 获取用户配额详情
  getQuotaDetails: async (
    userId: string,
    dateFilter: DateFilter = {},
    pagination: PaginationParams = {},
  ): Promise<PaginatedResponse<QuotaDetailResult>> => {
    const { gte, lte } = dateFilter
    const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

    const user = await prisma.userInfo.findUnique({
      where: { userId },
      include: {
        membership: true,
      },
    })

    if (!user) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const total = await prisma.quotaLog.count({
      where: {
        userId: user.userId,
        createdAt: {
          ...(gte && { gte: gte }),
          ...(lte && { lte: lte }),
        },
        usage: { not: 0 },
      },
    })

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        userId: user.userId,
        createdAt: {
          ...(gte && { gte: new Date(gte) }),
          ...(lte && { lte: new Date(lte) }),
        },
        usage: { not: 0 },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        createdAt: true,
        usage: true,
        type: true,
        description: true,
      },
      skip,
      take: pageSize,
    })

    if (!quotaLogs) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const data = quotaLogs.map((log) => ({
      time: dayjs(log.createdAt).tz('Asia/Shanghai').toDate(),
      quota_cost: log.usage,
      quota_type: log.type,
      description: log.description,
      email: user?.email || '',
      userId: user.userId,
    }))

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  },

  // 获取企业每日配额
  getEnterpriseDailyQuota: async (
    enterpriseId: string,
    dateFilter: DateFilter = {},
    pagination: PaginationParams = {},
  ): Promise<PaginatedResponse<EnterpriseQuotaDailyUsageResult>> => {
    const { gte, lte } = dateFilter
    const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

    const enterprise = await prisma.enterprise.findUnique({
      where: { id: enterpriseId },
    })

    if (!enterprise) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        enterpriseId: enterpriseId,
        type: {
          notIn: [QuotaType.RESET, QuotaType.ADMIN, QuotaType.EXPIRE],
        },
        usage: { not: 0 },
        createdAt: {
          ...(gte && { gte: gte }),
          ...(lte && { lte: lte }),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    if (!quotaLogs) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const [cardQueryByDay, dailyUsage] = await Promise.all([
      (async () => {
        const cardQueryLogs = quotaLogs.filter((log) => log.type === QuotaType.CARD_QUERY)

        const cardQueryByDay = cardQueryLogs.reduce((acc: Record<string, number>, log) => {
          const day = dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD')
          acc[day] = (acc[day] || 0) + log.usage
          return acc
        }, {})

        return Object.entries(cardQueryByDay).map(([day, count]) => ({
          day,
          count,
        }))
      })(),
      (async () => {
        return quotaLogs.reduce((acc: Map<string, number>, log) => {
          const date = dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD')
          acc.set(date, (acc.get(date) || 0) + log.usage)
          return acc
        }, new Map())
      })(),
    ])
    const allResults = Array.from(dailyUsage.entries())
      .map(([date, usage]) => ({
        date: new Date(date),
        enterpriseName: enterprise.name,
        enterpriseId: enterpriseId,
        daily_usage: usage,
        card_query_usage: cardQueryByDay.find((item) => item.day === date)?.count || 0,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime())

    const total = allResults.length
    const totalPages = Math.ceil(total / pageSize)
    const paginatedResults = allResults.slice(skip, skip + pageSize)
    return {
      data: paginatedResults,
      total,
      page,
      pageSize,
      totalPages,
    }
  },

  // 获取企业配额详情
  getEnterpriseQuotaDetails: async (
    enterpriseId: string,
    dateFilter: DateFilter = {},
    pagination: PaginationParams = {},
  ): Promise<PaginatedResponse<EnterpriseQuotaDetailResult>> => {
    const { gte, lte } = dateFilter
    const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

    const enterprise = await prisma.enterprise.findUnique({
      where: { id: enterpriseId },
      include: {
        members: {
          include: {
            user: {
              select: {
                userId: true,
                email: true,
              },
            },
          },
        },
      },
    })

    if (!enterprise) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const total = await prisma.quotaLog.count({
      where: {
        enterpriseId: enterpriseId,
        createdAt: {
          ...(gte && { gte: gte }),
          ...(lte && { lte: lte }),
        },
        usage: { not: 0 },
      },
    })

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        enterpriseId: enterpriseId,
        createdAt: {
          ...(gte && { gte: gte }),
          ...(lte && { lte: lte }),
        },
        usage: { not: 0 },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: pageSize,
    })

    if (!quotaLogs) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      }
    }

    const data = quotaLogs.map((log) => ({
      time: dayjs(log.createdAt).tz('Asia/Shanghai').toDate(),
      quota_cost: log.usage,
      quota_type: log.type,
      description: log.description,
      userId: log.createdBy,
      createBy:
        enterprise.members.find((member) => member.userId === log.createdBy)?.user?.email ||
        log.createdBy ||
        enterprise.id ||
        '',
      enterpriseId: enterprise.id,
      enterpriseName: enterprise.name,
    }))

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  },

  // 获取用户配额信息
  getUserQuotaInfo: async (userId: string) => {
    const user = await prisma.userInfo.findUnique({
      where: { userId },
      include: {
        membership: true,
      },
    })

    if (!user || !user.membership) {
      throw new Error('User or membership not found')
    }

    return {
      accountQuota: user.membership.accountQuota,
      usedQuota: user.membership.usedQuota,
      remainingQuota: user.membership.accountQuota - user.membership.usedQuota,
      dailyUsage: user.membership.dailyUsage,
      type: user.membership.type,
      status: user.membership.status,
      effectiveAt: user.membership.effectiveAt,
      expireAt: user.membership.expireAt,
      timezone: user.membership.timezone,
    }
  },

  // 获取企业配额信息
  getEnterpriseQuotaInfo: async (enterpriseId: string) => {
    const enterprise = await prisma.enterprise.findUnique({
      where: { id: enterpriseId },
    })

    if (!enterprise) {
      throw new Error('Enterprise not found')
    }

    return {
      accountQuota: enterprise.accountQuota,
      usedQuota: enterprise.usedQuota,
      remainingQuota: enterprise.accountQuota - enterprise.usedQuota,
      dailyUsage: enterprise.dailyUsage,
      dailyLimit: enterprise.dailyLimit,
      memberUsageDailyLimit: enterprise.memberUsageDailyLimit,
      status: enterprise.status,
      effectiveAt: enterprise.effectiveAt,
      expireAt: enterprise.expireAt,
    }
  },

  // 导出用户配额日志
  exportUserQuotaLogs: async (userId: string, dateFilter: DateFilter = {}) => {
    const { gte, lte } = dateFilter

    const user = await prisma.userInfo.findUnique({
      where: { userId },
      select: {
        email: true,
      },
    })

    if (!user) {
      throw new Error('User not found')
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        userId,
        createdAt: {
          ...(gte && { gte: new Date(gte) }),
          ...(lte && { lte: new Date(lte) }),
        },
        usage: { not: 0 },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return quotaLogs.map((log) => ({
      time: dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
      type: log.type,
      quota: log.usage,
      description: log.description,
      email: user.email,
    }))
  },

  // 导出企业配额日志
  exportEnterpriseQuotaLogs: async (enterpriseId: string, dateFilter: DateFilter = {}) => {
    const { gte, lte } = dateFilter

    const enterprise = await prisma.enterprise.findUnique({
      where: { id: enterpriseId },
      include: {
        members: {
          include: {
            user: {
              select: {
                email: true,
              },
            },
          },
        },
      },
    })

    if (!enterprise) {
      throw new Error('Enterprise not found')
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        enterpriseId,
        createdAt: {
          ...(gte && { gte: new Date(gte) }),
          ...(lte && { lte: new Date(lte) }),
        },
        usage: { not: 0 },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return quotaLogs.map((log) => ({
      time: dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
      type: log.type,
      quota: log.usage,
      description: log.description,
      email:
        enterprise.members.find((member) => member.userId === log.createdBy)?.user?.email || '',
      enterpriseName: enterprise.name,
    }))
  },

  // 获取个人配额的用量统计
  getUserQuotaUsageStats: async (userId: string): Promise<UserQuotaTypeUsageStats> => {
    const user = await prisma.userInfo.findUnique({
      where: { userId },
      include: {
        membership: {
          include: {
            enterprise: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    })

    if (!user || !user.membership) {
      throw new Error('User or membership not found')
    }

    let enterpriseId = ''
    if (user.membership.enterprise) {
      enterpriseId = user.membership.enterprise.id
    }

    // 获取配额使用统计
    const quotaTypeUsageStats = await getQuotaUsageStats(userId, enterpriseId || null, 30)

    // 获取失败任务记录
    const failedQuotaLogs = await getFailedQuotaLogs(userId, enterpriseId || null, 30)

    // 处理失败任务
    let processedStats = processFailedTasks(failedQuotaLogs, quotaTypeUsageStats)

    // 过滤不需要的配额类型
    processedStats = filterUnwantedQuotaTypes(processedStats)

    return {
      userId: user.userId,
      membershipId: user.membership?.id || '',
      email: user.email || '',
      quotaTypeUsageStats: processedStats,
    }
  },

  getEnterpriseQuotaUsageStats: async (
    enterpriseId: string,
  ): Promise<EnterpriseQuotaTypeUsageStats> => {
    const enterprise = await prisma.enterprise.findUniqueOrThrow({
      where: { id: enterpriseId },
    })

    const [quotaTypeUsageStats, failedQuotaLogs] = await Promise.all([
      getQuotaUsageStats(null, enterpriseId, 30),
      getFailedQuotaLogs(null, enterpriseId, 30),
    ])

    // 处理失败任务
    let processedStats = processFailedTasks(failedQuotaLogs, quotaTypeUsageStats)

    // 过滤不需要的配额类型
    processedStats = filterUnwantedQuotaTypes(processedStats)

    // 查询过去一个月内在该企业有配额记录不为0的所有用户ID
    let userIds = await prisma.$queryRaw<Array<{ userId: string }>>`
      SELECT DISTINCT "userId" 
      FROM "QuotaLogs" 
      WHERE "enterpriseId" = ${enterpriseId}
      AND "usage" != 0
      AND "type" != 'admin'
      AND "createdAt" >= (NOW() AT TIME ZONE 'Asia/Shanghai') - INTERVAL '30 days'
    `
    // 去除userId中的admin
    userIds = userIds.filter((u) => u.userId !== 'admin')

    // 获取用户信息
    const userInfos = await prisma.userInfo.findMany({
      where: {
        userId: {
          in: userIds.map((u) => u.userId),
        },
      },
      include: {
        membership: true,
      },
    })

    // 为每个用户获取配额使用统计
    const memberUsageStats: UserQuotaTypeUsageStats[] = await Promise.all(
      userIds.map(async ({ userId }) => {
        const userInfo = userInfos.find((u) => u.userId === userId)

        // 如果找不到用户信息，返回基本信息
        if (!userInfo) {
          return {
            userId,
            membershipId: '',
            email: '',
            quotaTypeUsageStats: {},
          }
        }

        const [userQuotaTypeUsageStats, userFailedQuotaLogs] = await Promise.all([
          getQuotaUsageStats(userId, enterpriseId, 30),
          getFailedQuotaLogs(userId, enterpriseId, 30),
        ])

        // 处理用户失败任务
        let userProcessedStats = processFailedTasks(userFailedQuotaLogs, userQuotaTypeUsageStats)

        // 过滤不需要的配额类型
        userProcessedStats = filterUnwantedQuotaTypes(userProcessedStats)

        return {
          userId,
          membershipId: userInfo.membership?.id || '',
          email: userInfo.email || '',
          quotaTypeUsageStats: userProcessedStats,
        }
      }),
    )

    return {
      enterpriseId: enterprise.id,
      enterpriseName: enterprise.name,
      quotaTypeUsageStats: processedStats,
      memberUsageStats,
    }
  },
}

export default QuotaService
