import { prisma, TagAndNoteOpType } from '@repo/database'
import { kolRecordService } from './kolRecord'

class KolNoteService {
  // add
  async upsert(kolId: string, note: string, userId: string, enterpriseId: string | undefined) {
    const record = await kolRecordService.upsertKolRecord(kolId, userId, enterpriseId)
    const exist = await prisma.kolNote.findFirst({
      where: {
        userId,
        enterpriseId,
        kolId,
      },
    })
    // 更新kolRecord的updatedAt
    await prisma.kolRecord.update({
      where: {
        id: record.id,
      },
      data: {
        updatedAt: new Date(),
        updatedBy: userId,
      },
    })
    await prisma.kolRecordLog.create({
      data: {
        kolId,
        tagId: null,
        type: TagAndNoteOpType.MODIFY_NOTE,
        note: exist?.note ?? '',
        description: `modify note of kol ${kolId} to ${note}`,
        recordId: record.id,
        createdAt: new Date(),
        userId,
        enterpriseId,
      },
    })
    if (exist) {
      return prisma.kolNote.update({
        where: {
          id: exist.id,
        },
        data: {
          note: note,
        },
      })
    }
    return prisma.kolNote.create({
      data: {
        userId,
        enterpriseId,
        kolId,
        note,
        recordId: record.id,
        createdAt: new Date(),
      },
    })
  }

  // get
  async getNote(kolId: string, userId: string, enterpriseId: string | undefined) {
    const record = await prisma.kolNote.findFirst({
      where: {
        userId,
        enterpriseId,
        kolId,
      },
      select: {
        note: true,
      },
    })
    return record?.note ?? ''
  }
}

export const kolNoteService = new KolNoteService()
