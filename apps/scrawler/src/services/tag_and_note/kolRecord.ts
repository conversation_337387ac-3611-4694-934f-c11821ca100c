import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { prisma } from '@repo/database'

export enum OwnerType {
  USER = 'user',
  ENTERPRISE = 'enterprise',
}

class KolRecordService {
  async upsertKolRecord(kolId: string, userId: string, enterpriseId: string | undefined) {
    const kol = await prisma.kolInfo.findUnique({
      where: { id: kolId },
    })
    if (!kol) {
      throwError(StatusCodes.BAD_REQUEST, 'Kol not found')
    }
    return prisma.kolRecord.upsert({
      where: {
        ownerId_kolId: {
          kolId,
          ownerId: enterpriseId ? enterpriseId : userId,
        },
      },
      update: { updatedAt: new Date(), updatedBy: userId, deletedAt: null },
      create: {
        kolId,
        userId,
        enterpriseId,
        updatedBy: userId,
        ownerId: enterpriseId ? enterpriseId : userId,
        ownerType: enterpriseId ? OwnerType.ENTERPRISE : OwnerType.USER,
      },
    })
  }
}
export const kolRecordService = new KolRecordService()
