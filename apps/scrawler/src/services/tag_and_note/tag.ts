import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { MAX_TAG_COUNT } from '@/constants/kolTag'
import { SimpleKolTag } from '@/routes/schemas/tagAndNote'
import { cache } from '@/utils/cache'
import { CreatorTag, prisma, TagAndNoteOpType } from '@repo/database'

export const DefaultTags = [
  { name: 'Overpriced', color: '#FF7570' },
  { name: 'Low ROI', color: '#FF8119' },
  { name: 'High ROI', color: '#35BD4C' },
  { name: 'Campaign A', color: '#27B0E7' },
  { name: 'Campaign B', color: '#B790FA' },
]

class TagService {
  private render(tag: CreatorTag): SimpleKolTag {
    return {
      id: tag.id,
      name: tag.name,
      color: tag.color,
    }
  }
  async tryInitDefaultTags(userId: string, enterpriseId: string | undefined) {
    const tags = await prisma.creatorTag.findMany({
      where: {
        name: { in: DefaultTags.map((tag) => tag.name) },
        enterpriseId: enterpriseId ?? undefined,
        userId,
      },
    })

    if (tags.length === DefaultTags.length) {
      return
    }

    const newTags = DefaultTags.filter((tag) => !tags.some((t) => t.name === tag.name))
    console.log(`init tags for ${userId} ${enterpriseId}`, newTags)
    await prisma.creatorTag.createMany({
      data: newTags.map((tag) => ({
        name: tag.name,
        color: tag.color,
        userId,
        enterpriseId: enterpriseId,
        createdAt: new Date(),
      })),
    })
  }

  async getAllTags(userId: string, enterpriseId: string | undefined): Promise<SimpleKolTag[]> {
    console.log('getAllTags', userId, enterpriseId)
    const tags = await cache.wrap(getTagCacheKey(userId, enterpriseId), async () => {
      return prisma.creatorTag.findMany({
        where: enterpriseId
          ? {
              enterpriseId,
              deletedAt: null,
            }
          : {
              userId,
              enterpriseId: '',
              deletedAt: null,
            },
      })
    })
    return tags.map((tag) => this.render(tag))
  }

  async getAllTagsWithUseCount(
    userId: string,
    enterpriseId: string | undefined,
  ): Promise<(SimpleKolTag & { useCount: number; createdAt: Date })[]> {
    const tags = await prisma.creatorTag.findMany({
      where: enterpriseId
        ? {
            enterpriseId,
            deletedAt: null,
          }
        : {
            userId,
            enterpriseId: '',
            deletedAt: null,
          },
      include: {
        KolTag: {
          select: {
            id: true,
          },
          where: enterpriseId
            ? { enterpriseId }
            : {
                userId,
                enterpriseId: '',
              },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
    return tags.map((tag) => ({
      ...this.render(tag),
      useCount: tag.KolTag.length,
      createdAt: tag.createdAt,
    }))
  }
  // add
  async addTag(
    userId: string,
    enterpriseId: string | undefined,
    tagName: string,
    color: string,
  ): Promise<CreatorTag> {
    const exist = await prisma.creatorTag.findFirst({
      where: {
        name: tagName,
        userId,
        enterpriseId,
        deletedAt: null,
      },
    })
    if (exist) {
      throwError(StatusCodes.BAD_REQUEST, 'Tag Name Already Exists')
    }
    const count = await prisma.creatorTag.count({
      where: {
        userId: enterpriseId ? undefined : userId,
        enterpriseId,
        deletedAt: null,
      },
    })
    if (count >= MAX_TAG_COUNT) {
      throwError(StatusCodes.BAD_REQUEST, `Tag Exceeds Limit: ${MAX_TAG_COUNT}`)
    }
    await cache.del(getTagCacheKey(userId, enterpriseId))
    return prisma.creatorTag.create({
      data: {
        name: tagName,
        color,
        userId,
        enterpriseId: enterpriseId,
        createdAt: new Date(),
      },
    })
  }

  // delete
  async deleteTag(id: string, userId: string, enterpriseId: string | undefined): Promise<void> {
    const tag = await prisma.creatorTag.findUnique({
      where: { id },
    })
    if (!tag) {
      throwError(StatusCodes.NOT_FOUND, 'Tag Not Found')
    }
    await prisma.creatorTag.update({
      where: {
        id,
      },
      data: {
        deletedAt: new Date(),
      },
    })
    // remove all use
    const kolTags = await prisma.kolTag.findMany({
      where: {
        tagId: id,
      },
    })
    console.log(`remove all kol tags for ${id} ${tag.name}`, kolTags)
    await prisma.kolTag.deleteMany({
      where: { tagId: id },
    })
    await cache.del(getTagCacheKey(userId, enterpriseId))
    await prisma.kolRecordLog.create({
      data: {
        tagId: id,
        kolId: '',
        tagName: tag.name,
        type: TagAndNoteOpType.UPDATE_TAG_META,
        userId,
        enterpriseId: enterpriseId,
        description: `remove tag ${tag.name}`,
        createdAt: new Date(),
      },
    })
  }

  // update name and color
  async updateTag(
    id: string,
    name: string,
    color: string,
    userId: string,
    enterpriseId: string | undefined,
  ): Promise<void> {
    const exist = await prisma.creatorTag.findUnique({
      where: { id },
    })
    if (!exist) {
      throwError(StatusCodes.NOT_FOUND, 'Tag Not Found')
    }
    let description = ''
    if (exist.name !== name) {
      description += `update tag name from ${exist.name} to ${name}`
    }
    if (exist.color !== color) {
      description += `update tag color from ${exist.color} to ${color}`
    }
    await prisma.creatorTag.update({
      where: { id },
      data: { name, color },
    })
    await prisma.kolRecordLog.create({
      data: {
        tagId: id,
        kolId: '',
        tagName: exist.name,
        type: TagAndNoteOpType.UPDATE_TAG_META,
        userId,
        enterpriseId: enterpriseId,
        description,
        createdAt: new Date(),
      },
    })
  }
}

export const tagService = new TagService()

function getTagCacheKey(userId: string, enterpriseId: string | undefined) {
  return enterpriseId ? `tag:enterprise:${enterpriseId}` : `tag:user:${userId}`
}
