import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import { GetKolTagAndNoteRes, ListKolTagRes } from '@/routes/schemas/tagAndNote'
import Logger from '@/utils/logger'
import { getKolLink } from '@/utils/url'
import { TagAndNoteOpType, prisma } from '@repo/database'
import { kolRecordService } from './kolRecord'

class KolTagService {
  async list(
    page: number,
    pageSize: number,
    tags: string[] | undefined,
    userId: string,
    enterpriseId: string | undefined,
  ): Promise<ListKolTagRes> {
    // 构建查询条件
    const whereCondition = {
      ownerId: enterpriseId ? enterpriseId : userId,
      deletedAt: null,
      AND:
        tags && tags.length > 0
          ? tags.map((tagId) => ({
              tags: {
                some: {
                  tagId: tagId,
                },
              },
            }))
          : undefined,
    }

    // 获取总数
    const totalCount = await prisma.kolRecord.count({
      where: whereCondition,
    })

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / pageSize)

    // 获取分页数据
    const findRecords = await prisma.kolRecord.findMany({
      where: whereCondition,
      orderBy: { updatedAt: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    })

    const recordIds = findRecords.map((record) => record.id)
    const records = await prisma.kolRecord.findMany({
      where: {
        id: { in: recordIds },
        deletedAt: null,
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        notes: {
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                userId: true,
                email: true,
              },
            },
          },
        },
        kol: true,
        user: true,
      },
      orderBy: { updatedAt: 'desc' },
    })

    const data = records.map((record) => {
      Logger.info(`get tags:${JSON.stringify(record.tags)}`)
      return {
        kol: {
          id: record.kolId,
          nickname: record.kol?.title ?? record.kol?.platformAccount ?? '',
          avatar: record.kol?.avatar ?? '',
          platform: record.kol?.platform ?? ('YOUTUBE' as const),
          url: getKolLink(record.kol?.platform, record.kol?.platformAccount),
        },
        tags: record.tags
          .filter((tag) => tag.tag !== null)
          .map((tag) => ({
            id: tag.tagId,
            name: tag.tag.name,
            color: tag.tag.color,
          })),
        notes: record.notes.map((note) => ({
          note: note.note,
          user: {
            id: note.user.userId,
            email: note.user.email ?? '',
          },
        })),
        updatedBy: {
          id: record.updatedBy ?? '',
          email: record.user?.email ?? '',
        },
        updatedAt: record.updatedAt.toISOString(),
      }
    })

    return {
      data,
      meta: {
        currentPage: page,
        totalPages,
        totalCount,
        currentCount: data.length,
        pageSize,
      },
    }
  }

  // list
  async getKolTagAndNote(
    kolId: string,
    userId: string,
    enterpriseId: string | undefined,
  ): Promise<GetKolTagAndNoteRes> {
    const record = await prisma.kolRecord.findFirst({
      where: { kolId, ownerId: enterpriseId ? enterpriseId : userId, deletedAt: null },
      include: {
        tags: {
          include: {
            tag: true,
            user: true,
          },
        },
        notes: {
          where: {
            userId,
            enterpriseId,
          },
        },
      },
    })
    if (!record) {
      return {
        tags: [],
        note: '',
      }
    }
    return {
      tags: record.tags
        .filter((tag) => tag.tag !== null)
        .map((tag) => ({
          id: tag.tagId,
          name: tag.tag.name,
          color: tag.tag.color,
          createdAt: tag.createdAt.toISOString(),
          updatedBy: {
            id: tag.user.userId,
            email: tag.user.email ?? '',
          },
        })),
      note: record.notes[0]?.note ?? '',
    }
  }

  async updateKolTags(
    kolId: string,
    tagIds: string[],
    userId: string,
    enterpriseId: string | undefined,
  ) {
    const record = await kolRecordService.upsertKolRecord(kolId, userId, enterpriseId)
    const allTags = await prisma.creatorTag.findMany({
      where: {
        userId: enterpriseId ? undefined : userId,
        enterpriseId,
      },
    })
    const tags = allTags.filter((tag) => tagIds.includes(tag.id))
    if (tags.length !== tagIds.length) {
      throwError(StatusCodes.BAD_REQUEST, 'Tag Not Found')
    }

    await prisma.$transaction(async (tx) => {
      const existTags = await tx.kolTag.findMany({
        where: {
          kolId,
          userId: enterpriseId ? undefined : userId,
          enterpriseId,
        },
      })
      const newTags = tags.filter((tag) => !existTags.some((existTag) => existTag.tagId === tag.id))
      const existTagIds = existTags.map((tag) => tag.tagId)
      const newTagIds = tagIds.filter((tagId) => !existTagIds.includes(tagId))
      const deleteTagIds = existTagIds.filter((tagId) => !tagIds.includes(tagId))
      const [deleteCount, createCount, createLogCount, deleteLogCount] = await Promise.all([
        tx.kolTag.deleteMany({
          where: {
            tagId: { in: deleteTagIds },
            kolId,
            userId: enterpriseId ? undefined : userId,
            enterpriseId,
          },
        }),
        tx.kolTag.createMany({
          data: newTagIds.map((tagId) => ({
            tagId,
            kolId,
            userId,
            enterpriseId,
            recordId: record.id,
          })),
        }),
        tx.kolRecordLog.createMany({
          data: newTags.map((tag) => ({
            userId,
            enterpriseId,
            tagId: tag.id,
            kolId,
            tagName: tag.name,
            type: TagAndNoteOpType.ADD_TAG,
            createdAt: new Date(),
            description: `add tag ${tag.name} to kol ${kolId}`,
            recordId: record.id,
          })),
        }),
        tx.kolRecordLog.createMany({
          data: deleteTagIds.map((tagId) => ({
            userId,
            enterpriseId,
            tagId,
            kolId,
            tagName: allTags.find((tag) => tag.id === tagId)?.name ?? '',
            type: TagAndNoteOpType.DELETE_TAG,
            createdAt: new Date(),
            description: `remove tag ${allTags.find((tag) => tag.id === tagId)?.name ?? ''} from kol ${kolId}`,
            recordId: record.id,
          })),
        }),
        // update record time
        (async () => {
          if (newTags.length > 0 && deleteTagIds.length > 0) {
            await tx.kolRecord.update({
              where: { id: record.id },
              data: { updatedAt: new Date(), updatedBy: userId },
            })
          }
        })(),
      ])
      Logger.info(
        `update kol ${kolId} tags: deleteCount: ${deleteCount}, createCount: ${createCount}, createLogCount: ${createLogCount}, deleteLogCount: ${deleteLogCount}`,
      )
    })
    return tags
  }

  async addSingleTag(
    kolId: string,
    tagId: string,
    userId: string,
    enterpriseId: string | undefined,
  ) {
    const record = await kolRecordService.upsertKolRecord(kolId, userId, enterpriseId)

    const tag = await prisma.creatorTag.findFirst({
      where: {
        id: tagId,
        userId: enterpriseId ? undefined : userId,
        enterpriseId,
      },
    })

    if (!tag) {
      throwError(StatusCodes.BAD_REQUEST, 'Tag Not Found')
    }

    const existingTag = await prisma.kolTag.findFirst({
      where: {
        kolId,
        tagId,
        userId: enterpriseId ? undefined : userId,
        enterpriseId,
      },
    })

    if (existingTag) {
      throwError(StatusCodes.BAD_REQUEST, 'Tag already exists for this KOL')
    }

    await prisma.$transaction(async (tx) => {
      await tx.kolTag.create({
        data: {
          tagId,
          kolId,
          userId,
          enterpriseId,
          recordId: record.id,
        },
      })

      await tx.kolRecordLog.create({
        data: {
          userId,
          enterpriseId,
          tagId,
          kolId,
          tagName: tag.name,
          type: TagAndNoteOpType.ADD_TAG,
          createdAt: new Date(),
          description: `add tag ${tag.name} to kol ${kolId}`,
          recordId: record.id,
        },
      })

      await tx.kolRecord.update({
        where: { id: record.id },
        data: { updatedAt: new Date(), updatedBy: userId },
      })
    })

    return tag
  }

  async deleteSingleTag(
    kolId: string,
    tagId: string,
    userId: string,
    enterpriseId: string | undefined,
  ) {
    const existingTag = await prisma.kolTag.findFirst({
      where: {
        kolId,
        tagId,
        userId: enterpriseId ? undefined : userId,
        enterpriseId,
      },
      include: {
        tag: true,
      },
    })

    if (!existingTag) {
      throwError(StatusCodes.BAD_REQUEST, 'Tag not found for this KOL')
    }

    await prisma.$transaction(async (tx) => {
      await tx.kolTag.delete({
        where: {
          id: existingTag.id,
        },
      })

      await tx.kolRecordLog.create({
        data: {
          userId,
          enterpriseId,
          tagId,
          kolId,
          tagName: existingTag.tag.name,
          type: TagAndNoteOpType.DELETE_TAG,
          createdAt: new Date(),
          description: `remove tag ${existingTag.tag.name} from kol ${kolId}`,
          recordId: existingTag.recordId,
        },
      })

      await tx.kolRecord.update({
        where: { id: existingTag.recordId },
        data: { updatedAt: new Date(), updatedBy: userId },
      })
    })
  }

  async deleteKolRecord(kolId: string, userId: string, enterpriseId: string | undefined) {
    await prisma.$transaction(async (tx) => {
      const record = await tx.kolRecord.update({
        where: {
          ownerId_kolId: {
            kolId,
            ownerId: enterpriseId ? enterpriseId : userId,
          },
        },
        data: { deletedAt: new Date() },
      })
      await Promise.all([
        tx.kolTag.deleteMany({
          where: { recordId: record.id },
        }),
        tx.kolNote.deleteMany({
          where: { recordId: record.id },
        }),
        tx.kolRecordLog.deleteMany({
          where: { recordId: record.id },
        }),
      ])
    })
  }

  async exportKolTagsAndNotes(userId: string, enterpriseId: string | undefined) {
    // 构建查询条件
    const whereCondition = {
      ownerId: enterpriseId ? enterpriseId : userId,
      deletedAt: null,
    }

    // 获取所有数据（不分页）
    const records = await prisma.kolRecord.findMany({
      where: whereCondition,
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        notes: {
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                userId: true,
                email: true,
              },
            },
          },
        },
        kol: true,
      },
      orderBy: { updatedAt: 'desc' },
    })

    return records.map((record) => {
      const tagNames = record.tags
        .filter((tag) => tag.tag !== null)
        .map((tag) => tag.tag.name)
        .join(', ')

      const noteTexts = record.notes
        .map((note) => {
          const userEmail = note.user.email || ''
          const username = userEmail.split('@')[0] // 去掉域名部分
          return `${username}: ${note.note}`
        })
        .join('\n')

      return {
        id: record.id,
        platformAccount: record.kol?.title ?? 'Unknown KOL',
        platform: record.kol?.platform ?? '',
        link: getKolLink(record.kol?.platform, record.kol?.platformAccount),
        tags: tagNames,
        notes: noteTexts,
      }
    })
  }
}

export const kolTagService = new KolTagService()
