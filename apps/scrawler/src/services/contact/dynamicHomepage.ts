import RapidApiStatsCollector from '@/api/ApiCallStat'
import { getPiece } from '@/utils/url'
import { ofetch } from 'ofetch'
import { ContactItem, ExternalLink, ParseableExternalLink } from './contact'
import { newExternalLink } from './contact.utils'

enum BlockType {
  FEED = 'feed',
  HEADER = 'header',
  STYLE = 'style',
  SOCIAL_LINK_LIST = 'social_link_list',
  BUTTON_LIST = 'button_list',
}

interface LinkinbioPage {
  id: number
  created_time: number
  social_profiles: SocialProfile[]
  linkinbio_blocks: LinkinbioBlock[]
  account: Account
  entitlements: Entitlements
  plan_sku: string
}

interface SocialProfile {
  id: number
  nickname: string
  avatar_url: string
  name: string
  default_link: any
  linkinbio_prompt: string
  uid: string
  linkinbio_hero_image: boolean
  profile_type: string
  show_footer: boolean
  embedded_lib_show_header: boolean
  embedded_lib_layout: string
  embedded_lib_title: any
  embedded_lib_dark_mode: boolean
}

interface LinkinbioBlock {
  id: number
  block_data: BlockData
  block_type: BlockType
  index: number
  linkinbio_page_id: number
  connected_object_id: number | null
  connected_object_type: string | null
  linkinbio_attachments: LinkinbioAttachment[]
}

interface BlockData {
  enabled: boolean
  bio?: string
  display_name?: string
  button_shape?: string
  button_outline?: string
  page_background?: string
  page_text_color?: string
  page_font_family?: string
  button_background?: string
  button_box_shadow?: string
  button_text_color?: string
  page_background_type?: string
  social_link_icon_color?: string
  page_background_image_identifier?: string
  social_links?: SocialLink[]
  buttons?: Button[]
  button_groups?: any[]
}

interface LinkinbioAttachment {
  id: number
  identifier: string
  name: string
  record_id: number
  record_type: string
  data: any
  variants: Variant
}

interface Variant {
  thumb: Thumb
  original: Original
  thumb_md: ThumbMd
}

interface Thumb {
  identifier: string
  processed: boolean
  metadata: Metadata
  url: string
}

interface Original {
  identifier: string
  processed: boolean
  metadata: Metadata
  url: string
}

interface ThumbMd {
  identifier: string
  processed: boolean
  metadata: Metadata
  url: string
}

interface Metadata {
  width: number | null
  height: number | null
  file_size: number | null
  content_type: string
}

interface SocialLink {
  id: string
  url: string
  enabled: boolean
  platform: string
  created_at: string
}

interface Button {
  id: string
  url: string
  title: string
  enabled: boolean
  created_at: string
}

interface Account {
  best_describes: string
  main_profile_business_model: any
}

interface Entitlements {
  linkinbio_bannerless: boolean
  linkinbio_button_groups: number
  linkinbio_featured_banners: number
  linkinbio_featured_media_custom_media: number
  linkinbio_image_upload_page_background: boolean
  linkinbio_multi_item_banner_items: number
  linkinbio_seo: boolean
}
export class DynamicHomepageExternalLink extends ExternalLink implements ParseableExternalLink {
  async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (collector?.isExternalLinkExceeded()) {
      return []
    }
    if (this.rawLink.toLowerCase().includes('linkin.bio')) {
      const username = getPiece(this.rawLink, 1)
      const apiUrl = `https://api-prod.linkin.bio/api/v2/pages?nickname=${username}`
      const res = await ofetch(apiUrl)
      const data = res as { linkinbio_page: LinkinbioPage }
      if (data.linkinbio_page.linkinbio_blocks.length) {
        const block = data.linkinbio_page.linkinbio_blocks.find(
          (block) => block.block_type === BlockType.SOCIAL_LINK_LIST,
        )
        if (block && block.block_data.social_links) {
          const items: ContactItem[] = []
          for (let i = 0; i < block.block_data.social_links.length; i++) {
            const link = block.block_data.social_links[i]
            if (link.url) {
              const item = await newExternalLink(link.url, this.root, this.depth + 1)
              if ('parse' in item) {
                const parsed = await (item as ParseableExternalLink).parse(collector)
                if (parsed.length) {
                  items.push(...parsed)
                }
              }
            }
          }
          return items
        }
      }
      return []
    } else {
      return []
    }
  }
}
