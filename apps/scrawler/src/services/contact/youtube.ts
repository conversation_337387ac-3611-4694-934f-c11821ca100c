import RapidApiStatsCollector from '@/api/ApiCallStat'
import YoutubeApi from '@/api/youtube'
import Sentry from '@/infras/sentry'
import { getYoutubeChannelHandle, getYoutubeChannelId } from '@/utils/url'
import dayjs from 'dayjs'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  LinkType,
  ParseableExternalLink,
  extractPhoneNumber,
} from './contact'
import { MAX_LINK_DEPTH, appendFollowerCount } from './contact.utils'

export class YoutubeExternalLink extends ExternalLink implements ParseableExternalLink {
  public async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      if (collector?.isExternalLinkExceeded()) {
        Sentry.captureException(
          new Error(`[external-link]youtube ${this.rawLink} is exceeded, depth: ${this.depth}`),
        )
      }
      return []
    }
    const result = []

    try {
      const handle = getYoutubeChannelHandle(this.rawLink)
      let id: string | undefined | null = getYoutubeChannelId(this.rawLink)
      if (!id && handle) {
        id = await YoutubeApi.getInstance().getYoutubeChannelId(handle, collector)
      }
      if (!id) {
        throw new Error(`cannot find youtube handle for ${this.rawLink}`)
      }
      const channel = await YoutubeApi.getInstance().getChannel(id, collector)
      if (channel?.subscriberCount) {
        result.push({
          type: ContactType.SOCIAL,
          content: appendFollowerCount('YouTube', channel.subscriberCount),
          url: this.rawLink,
          depth: this.depth,
          updatedAt: dayjs().unix(),
          root: this.root,
          linkType: LinkType.YOUTUBE,
        } as ContactItem)
      }
      if (channel.email) {
        result.push({
          type: ContactType.EMAIL,
          content: channel?.email ?? '',
          url: this.rawLink,
          depth: this.depth,
          updatedAt: dayjs().unix(),
          root: this.root,
          linkType: LinkType.YOUTUBE,
          emailSource: channel?.emailSource ?? '',
        } as ContactItem)
      }
      const bio = channel.description
      const bioPhones = extractPhoneNumber(bio)
      if (bioPhones?.length) {
        result.push(
          ...bioPhones.map((phone) => {
            return {
              type: ContactType.PHONE,
              content: phone,
              url: this.rawLink,
              depth: this.depth,
              updatedAt: dayjs().unix(),
              root: this.root,
              linkType: LinkType.YOUTUBE,
            } as ContactItem
          }),
        )
      }
    } catch (err) {
      console.log(`failed to parse youtube channel: ${this.rawLink}`)
      Sentry.captureException(err)
    }
    return result
  }
}
