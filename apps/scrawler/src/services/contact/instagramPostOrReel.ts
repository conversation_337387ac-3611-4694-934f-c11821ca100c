import { IgPost } from '@/api/@types/rapidapi/Instagram'
import InstagramApi from '@/api/instagram'
import * as UrlService from '@/utils/url'
import { ExternalLink, PostExternalLink } from './contact'

export class InstagramPostOrReelExternalLink extends ExternalLink implements PostExternalLink {
  public async getPost(): Promise<IgPost | null> {
    let postId = null
    if (UrlService.isInstagramPost(this.rawLink)) {
      postId = UrlService.getInstagramPostId(this.rawLink)
    } else if (UrlService.isInstagramReel(this.rawLink)) {
      postId = UrlService.getInstagramReelId(this.rawLink)
    } else {
      return null
    }
    if (!postId) {
      return null
    }
    const post = await InstagramApi.getInstance().getPost(postId)
    if (!post) {
      return null
    }
    return post
  }
}
