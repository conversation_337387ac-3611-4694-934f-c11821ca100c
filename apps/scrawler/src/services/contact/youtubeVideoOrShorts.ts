import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import YoutubeApi from '@/api/youtube'
import * as UrlService from '@/utils/url'
import { ExternalLink, PostExternalLink } from './contact'

export class YoutubeVideoOrShortsExternalLink extends ExternalLink implements PostExternalLink {
  public async getPost(): Promise<VideoDetail | null> {
    let videoId = null
    if (UrlService.isYoutubeShorts(this.rawLink)) {
      videoId = UrlService.getYoutubeShortsId(this.rawLink)
    } else if (UrlService.isYoutubeVideo(this.rawLink)) {
      videoId = UrlService.getYoutubeVideoId(this.rawLink)
    } else {
      return null
    }
    if (!videoId) {
      return null
    }
    const video = await YoutubeApi.getInstance().getVideo(videoId)
    if (!video) {
      return null
    }
    return video
  }
}
