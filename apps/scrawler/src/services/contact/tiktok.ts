import RapidApiStatsCollector from '@/api/ApiCallStat'
import TiktokApi from '@/api/tiktok'
import Sentry from '@/infras/sentry'
import { getTiktokUniqueId } from '@/utils/url'
import dayjs from 'dayjs'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  extractPhoneNumber,
  LinkType,
  ParseableExternalLink,
} from './contact'
import { appendFollowerCount, MAX_LINK_DEPTH } from './contact.utils'

export class TiktokExternalLink extends ExternalLink implements ParseableExternalLink {
  public async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      if (collector?.isExternalLinkExceeded()) {
        Sentry.captureException(
          new Error(`[external-link]tiktok ${this.rawLink} is exceeded, depth: ${this.depth}`),
        )
      }
      return []
    }
    const result: ContactItem[] = []

    try {
      const uniqueId = getTiktokUniqueId(this.rawLink)
      if (uniqueId) {
        const ttUser = await TiktokApi.getInstance().getUserDetail(
          { unique_id: uniqueId },
          collector,
        )
        if (!ttUser) {
          return result
        }
        if (ttUser?.stats?.followerCount) {
          result.push({
            type: ContactType.SOCIAL,
            content: appendFollowerCount('TikTok', ttUser!.stats.followerCount!),
            url: this.rawLink,
            depth: this.depth,
            updatedAt: dayjs().unix(),
            root: this.root,
            linkType: LinkType.TIKTOK,
          } as ContactItem)
        }
        // extract emails
        if (ttUser?.user?.email) {
          result.push({
            type: ContactType.EMAIL,
            content: ttUser?.user?.email ?? '',
            url: this.rawLink,
            depth: this.depth,
            updatedAt: dayjs().unix(),
            root: this.root,
            linkType: LinkType.TIKTOK,
            emailSource: ttUser?.user?.emailSource ?? '',
          } as ContactItem)
        }
        // extract phone numbers
        const bio = ttUser?.user.signature
        const bioPhones = extractPhoneNumber(bio)
        if (bioPhones?.length) {
          result.push(
            ...bioPhones.map((phone) => {
              return {
                type: ContactType.PHONE,
                content: phone,
                url: this.rawLink,
                depth: this.depth,
                updatedAt: dayjs().unix(),
                root: this.root,
                linkType: LinkType.TIKTOK,
              } as ContactItem
            }),
          )
        }
      }
    } catch (err) {
      console.log(`failed to parse tiktok user: ${this.rawLink}`)
      Sentry.captureException(err)
    }
    return result
  }
}
