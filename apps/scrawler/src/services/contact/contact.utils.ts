import { ExternalLink, LinkType } from './contact'
import { HomepageExternalLink } from './homepage'
import { InstagramExternalLink } from './instagram'
import { InstagramPostOrReelExternalLink } from './instagramPostOrReel'
import { SnapchatExternalLink } from './snapchat'
import { TiktokExternalLink } from './tiktok'
import { TiktokVideoExternalLink } from './tiktokVideo'
import { UnknownExternalLink } from './unknown'
import { WhatsAppExternalLink } from './whatsapp'
import { YoutubeExternalLink } from './youtube'
import { YoutubeVideoOrShortsExternalLink } from './youtubeVideoOrShorts'

export const MAX_LINK_DEPTH = 3

export async function newExternalLink(url: string, root: string, depth = 0): Promise<ExternalLink> {
  const linkType = await ExternalLink.getLinkType(url)
  switch (linkType) {
    case LinkType.WHATSAPP:
      return new WhatsAppExternalLink(url, root, linkType, depth)
    case LinkType.SNAPCHAT:
      return new SnapchatExternalLink(url, root, linkType, depth)
    case LinkType.INSTAGRAM:
      return new InstagramExternalLink(url, root, linkType, depth)
    case LinkType.LINKTREE:
    case LinkType.HOMEPAGE:
      return new HomepageExternalLink(url, root, linkType, depth)
    case LinkType.YOUTUBE:
      return new YoutubeExternalLink(url, root, linkType, depth)
    case LinkType.TIKTOK:
      return new TiktokExternalLink(url, root, linkType, depth)
    case LinkType.YOUTUBE_SHORTS:
    case LinkType.YOUTUBE_VIDEO:
      return new YoutubeVideoOrShortsExternalLink(url, root, linkType, depth)
    case LinkType.TIKTOK_VIDEO:
      return new TiktokVideoExternalLink(url, root, linkType, depth)
    case LinkType.INSTAGRAM_POST:
    case LinkType.INSTAGRAM_REEL:
      return new InstagramPostOrReelExternalLink(url, root, linkType, depth)
    case LinkType.UNKNOWN:
    default:
      return new UnknownExternalLink(url, root, linkType, depth)
  }
}

export const appendFollowerCount = (raw: string, count: number) => {
  let printCount = String(count)
  const M = 1_000_000
  const K = 1_000
  if (count >= M) {
    printCount = String(Math.floor(count / M)) + 'M'
  } else if (count >= K) {
    printCount = String(Math.floor(count / K)) + 'K'
  }
  return `${printCount} Followers`
}
