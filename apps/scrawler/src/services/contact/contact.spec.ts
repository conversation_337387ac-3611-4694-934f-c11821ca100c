import { logTime } from '@/api/util'
import Bluebird from 'bluebird'
import dayjs from 'dayjs'
import { describe, it } from 'vitest'
import { extractPhoneNumber } from './contact'
import { appendFollowerCount, newExternalLink } from './contact.utils'
import { UnknownExternalLink } from './unknown'

describe('it should test all kinds of contact links', () => {
  it(
    'should test one link',
    async () => {
      const link = 'https://hoo.be/beckymaelee'
      const test = await newExternalLink(link, link)
      if ('parse' in test && typeof test.parse === 'function') {
        const result = await test.parse()
        console.log(result)
      } else {
        console.log('not a link')
      }
    },
    1000 * 1000,
  )

  it(
    'should test about extract link',
    async () => {
      const links = [
        'https://api.whatsapp.com/message/MT3CXOVD4WE6M1?autoload=1&app_absent=0https://iwtsp.com/97450253322',
        'https://www.snapchat.com/add/memeh4',
        'https://www.snapchat.com/add/thegamar15?locale=en_SA@calendar%3Dgregorianhttps://api.whatsapp.com/message/JTJUMPLR7H2IK1?autoload=1&app_absent=0https://www.snapchat.com/add/sa9lll?invite_id=RSw_MG_7&locale=en_SA@calendar%3Dgregorian&sid=ee0847a33d99468da031e078ea868034',
        'https://api.whatsapp.com/message/BVWH7J2J62LOG1?autoload=1&app_absent=0',
        'https://api.whatsapp.com/send/?phone=+97466557666&text&type=phone_number&app_absent=0',
        'https://www.snapchat.com/add/zah.bah?invite_id=DFI8uxy1&locale=en_AE&sid=63b4dd36596a489c9c3eebe82bf8b547',
        'https://api.whatsapp.com/send?phone=96893658495',
        'https://reach.link/lamis-alqahtani',
        'https://www.snapchat.com/add/shood2008?invite_id=3DhdkRI7&locale=ar_QA@numbers%3Dlatn&xp_id=1&sid=d345ead7a4534106bb62890b935a7f56',
        'https://www.snapchat.com/add/model.rinad?locale=en_SA@calendar%3Dgregorian&sid=7fb0fae507274e18ae54b45e78640530',
        'https://www.snapchat.com/add/lxcv_23',
        'https://www.snapchat.com/add/alqahtani_203',
        'https://linkjar.co/Haneen.Alzahrani',
        'https://linktr.ee/itsilf9',
        'https://www.snapchat.com/add/t.om86?locale=ar_SA@calendar%3Dgregorianhttps://www.snapchat.com/add/qxqx1010?invite_id=grx8QKsI&locale=ar_SA@calendar%3Dgregorian&sid=ac3c27f2e6cf44dd95032433bcccab42',
        'https://www.snapchat.com/add/fao.119?invite_id=Z5gfhtxu&locale=en_SA@calendar%3Dgregorian&xp_id=1&sid=2121a22c79864f0a8d9fa3c787b60051https://reach.link/anfalalsairafi',
        'https://reach.link/ayah-abdullah',
        'https://reach.link/shaikhaaet1',
        'https://www.snapchat.com/add/shrq.1https://www.snapchat.com/add/s.k104',
        'https://wsend.co/96894940453',
        'https://www.snapchat.com/add/kay6na?invite_id=IDUBwFxk&locale=en_AE&sid=80fd07cfd5404be4b451ea1ff23b642f',
        'https://www.snapchat.com/add/aoosh-2001?invite_id=MJzHxiSb&xp_id=1&sid=5f141df81b824edbb589c8b20c8f16f4',
        'https://www.snapchat.com/add/z.almarri?locale=en_AE',
      ]
      const startTime = dayjs().unix()
      let successCount = 0
      let failCount = 0
      // const  link = ''
      await Bluebird.map(
        links,
        async (link) => {
          const linkStruct = await newExternalLink(link, link)
          if (linkStruct instanceof UnknownExternalLink) {
            console.log(`${link} is unknown.`)
            failCount += 1
            return
          }
          const results = await logTime(linkStruct.parse(), '[sj:link]parse ' + link)
          if (results.length) {
            successCount += 1
          } else {
            failCount += 1
          }
          console.log(`------------ result ----------\n
        get result for ${link}: ${JSON.stringify(results)}\n
      ---------------------------------------`)
        },
        { concurrency: 10 },
      )
      console.log(`total time: ${dayjs().unix() - startTime} s`)
      console.log(`success ${successCount}, failed ${failCount}`)
      // console.log(JSON.stringify(results))
    },
    20 * 60 * 1000,
  )
})

describe('all in one test cases, find out how many contact will be found.', () => {
  it('should extract number', async () => {
    const tests = [
      'Model•                                                               \n •ترخيص موثوق ✅\n\nللتواصل وتنسيق الاعمال•\nواتساب فقط : +966537681416',
      '+97 1234456789',
      '+                ',
    ]
    for (let i = 0; i < tests.length; i++) {
      console.log(tests[i])
      const phones = await extractPhoneNumber(tests[i])
      console.log(phones)
    }
  })
})

describe('it should test some common cases', () => {
  it('should test follower count', async () => {
    const numbers = [100, 2000, 1_200_000, 1_000_000_000]
    numbers.forEach((n) => {
      console.log(appendFollowerCount('', n))
    })
  })
})
