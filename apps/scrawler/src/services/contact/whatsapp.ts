import RapidApiStatsCollector from '@/api/ApiCallStat'
import Sentry from '@/infras/sentry'
import Logger from '@/utils/logger'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  extractPhoneNumber,
  LinkType,
  ParseableExternalLink,
} from './contact'
import { MAX_LINK_DEPTH } from './contact.utils'
import { HomepageExternalLink } from './homepage'

export class WhatsAppExternalLink extends ExternalLink implements ParseableExternalLink {
  public type: LinkType = LinkType.WHATSAPP

  public async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      return []
    }
    const phoneNumber = extractPhoneNumber(this.rawLink)
    const results: ContactItem[] = []
    if (phoneNumber?.length) {
      phoneNumber.forEach((number) => {
        results.push({
          type: ContactType.PHONE,
          content: number,
          depth: this.depth,
          url: this.rawLink,
          root: this.root,
          linkType: LinkType.WHATSAPP,
        } as ContactItem)
      })
    }
    try {
      const pageResult = await new HomepageExternalLink(
        this.rawLink,
        this.root,
        this.type,
        this.depth,
      ).parse(collector)
      if (pageResult.length) {
        Sentry.captureEvent({
          message: 'whatsapp parse page',
          level: 'info',
          extra: {
            url: this.rawLink,
            count: pageResult.length,
          },
          tags: {
            success: true,
          },
        })
        results.push(...pageResult)
      }
    } catch (err) {
      Logger.error(`whatsapp parse error: ${err}`)
      Sentry.captureEvent({
        message: 'whatsapp parse page',
        level: 'info',
        extra: {
          url: this.rawLink,
          reason: err instanceof Error ? err.message : 'unknown',
        },
        tags: {
          success: false,
        },
      })
    }
    return results
  }
}
