import RapidApiStatsCollector from '@/api/ApiCallStat'
import InstagramApi from '@/api/instagram'
import Sentry from '@/infras/sentry'
import { getInstagramUsername } from '@/utils/url'
import dayjs from 'dayjs'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  extractPhoneNumber,
  LinkType,
  ParseableExternalLink,
} from './contact'
import { appendFollowerCount, MAX_LINK_DEPTH } from './contact.utils'

export class InstagramExternalLink extends ExternalLink implements ParseableExternalLink {
  public async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      if (collector?.isExternalLinkExceeded()) {
        Sentry.captureException(
          new Error(`[external-link]instagram ${this.rawLink} is exceeded, depth: ${this.depth}`),
        )
      }
      return []
    }
    const result = []

    try {
      const username = getInstagramUsername(this.rawLink)
      if (!username) {
        throw new Error(`cannot parse username from ${this.rawLink}`)
      }
      const user = await InstagramApi.getInstance().getUser(username, collector)
      if (!user) {
        return []
      }
      if (user?.followerCount) {
        result.push({
          type: ContactType.SOCIAL,
          content: appendFollowerCount('Instagram', user.followerCount),
          url: this.rawLink,
          depth: this.depth,
          updatedAt: dayjs().unix(),
          root: this.root,
          linkType: LinkType.INSTAGRAM,
          platformAccount: user.username,
        } as ContactItem)
      }
      if (user?.email) {
        result.push({
          type: ContactType.EMAIL,
          content: user.email,
          url: this.rawLink,
          depth: this.depth,
          updatedAt: dayjs().unix(),
          root: this.root,
          linkType: LinkType.INSTAGRAM,
          emailSource: user.emailSource,
          platformAccount: user.username,
        } as ContactItem)
      }
      const bio = user?.biography
      const bioPhones = extractPhoneNumber(bio)
      if (bioPhones?.length) {
        result.push(
          ...bioPhones.map((phone) => {
            return {
              type: ContactType.PHONE,
              content: phone,
              url: this.rawLink,
              depth: this.depth,
              updatedAt: dayjs().unix(),
              root: this.root,
              linkType: LinkType.INSTAGRAM,
              platformAccount: user.username,
            } as ContactItem
          }),
        )
      }
    } catch (err) {
      console.log(`failed to parse tiktok user: ${this.rawLink}`)
      Sentry.captureException(err)
    }
    return result
  }
}
