import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import { parse } from 'node-html-parser'
import { ofetch } from 'ofetch'
import { ContactItem, ContactType, ExternalLink, extractPhoneNumber, LinkType } from './contact'
import { MAX_LINK_DEPTH } from './contact.utils'

export class SnapchatExternalLink extends ExternalLink {
  public async parse(): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH) {
      return []
    }
    try {
      const raw = await ofetch(this.rawLink)
      const pageData = parse(raw)
      const bios = pageData
        .querySelectorAll('*')
        .filter((i) => i.classNames.toLowerCase().includes('header_mobileuserdescription__'))
        .map((i) => i.rawText)
      const results: ContactItem[] = []
      if (bios?.length) {
        bios.forEach((bio) => {
          const email = extractEmail(bio)
          const number = extractPhoneNumber(bio)
          if (email?.length) {
            results.push({
              type: ContactType.EMAIL,
              content: email,
              depth: this.depth,
              url: this.rawLink,
              root: this.root,
              linkType: LinkType.SNAPCHAT,
              emailSource: EmailSourceType.LINK_INFO,
            })
          }
          if (number?.length) {
            results.push(
              ...number.map((i) => {
                return {
                  type: ContactType.PHONE,
                  content: i,
                  depth: this.depth,
                  url: this.rawLink,
                  root: this.root,
                  linkType: LinkType.SNAPCHAT,
                }
              }),
            )
          }
        })
      }
      return results
    } catch (err) {
      console.log(`failed to parse snapchat link: ${this.rawLink}`)
      Sentry.captureException(err)
      return []
    }
  }
}
