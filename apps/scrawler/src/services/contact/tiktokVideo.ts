import { TiktokVideoDetail } from '@/api/@types/rapidapi/Tiktok'
import TiktokApi from '@/api/tiktok'
import * as UrlService from '@/utils/url'
import { ExternalLink, PostExternalLink } from './contact'

export class TiktokVideoExternalLink extends ExternalLink implements PostExternalLink {
  async getPost(): Promise<TiktokVideoDetail | null> {
    let videoId = null
    if (UrlService.isTiktokVideo(this.rawLink)) {
      videoId = UrlService.getTiktokVideoid(this.rawLink)
    }
    if (!videoId) {
      return null
    }
    const video = await TiktokApi.getInstance().getVideoDetail(videoId)
    if (!video) {
      return null
    }
    return video
  }
}
