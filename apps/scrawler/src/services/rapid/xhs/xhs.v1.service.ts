import { retryUtil } from '@/utils/retry'
import assert from 'assert'
import { xhsV1Api } from '../../../lib/xhs.v1'
import { XhsCommonResponse, XhsNoteDetailResponse } from '../../../types/xhs/rapid/xhs.v1'

interface CoreNoteInfo {
  id: string
  title: string
  desc: string
  createTime: Date
  type: string
  region: string

  // 作者信息
  authorId: string
  authorName: string
  authorNickname: string
  authorRedId: string
  authorAvatar: string
  authorFollowers?: number

  // 统计信息
  likes: number
  comments: number
  shares: number
  collects: number
  views: number

  // 图片信息
  images: {
    url: string
    width: number
    height: number
  }[]

  // 分享信息
  shareInfo: {
    link: string
    title: string
    image: string
  }
}

/**
 * 获取小红书笔记原始响应
 */
async function getNoteRawDetail(noteId: string) {
  assert(noteId, new Error('noteId is required'))
  return await xhsV1Api.getNoteDetail(noteId)
}

/**
 * 获取核心笔记信息
 */
async function getNoteCoreDetail(noteId: string): Promise<CoreNoteInfo> {
  assert(noteId, new Error('noteId is required'))
  return retryUtil.retry(
    async () => {
      const response = await xhsV1Api.getNoteDetail(noteId)
      if (response.code !== 0 || !response?.data) {
        throw new Error('获取小红书笔记详情失败')
      }
      return extractNoteCoreInfo(response)
    },
    3,
    1000,
    2,
  )
}

/**
 * 将小红书短链接转换为带有笔记ID的完整链接
 */
async function convertShortLinkToNoteId(shortLink: string): Promise<string> {
  assert(shortLink, new Error('shortLink is required'))
  const response = await fetch(shortLink, {
    method: 'HEAD',
    redirect: 'follow',
  })
  return decodeURIComponent(response.url)
}

function extractNoteCoreInfo(response: XhsCommonResponse<XhsNoteDetailResponse>): CoreNoteInfo {
  const note = response.data.data[0].note_list[0]
  const user = note.user

  return {
    id: note.id ?? '',
    title: note.title ?? '',
    desc: note.desc ?? '',
    createTime: new Date(note.time * 1000),
    type: note.model_type ?? '',
    region: note.ip_location ?? '',

    // 作者信息
    authorId: user.id ?? '',
    authorName: user.name ?? '',
    authorNickname: user.nickname ?? '',
    authorRedId: user.red_id ?? '',
    authorAvatar: user.image ?? '',

    // 统计信息
    likes: note.liked_count ?? 0,
    comments: note.comments_count ?? 0,
    shares: note.shared_count ?? 0,
    collects: note.collected_count ?? 0,
    views: note.view_count ?? 0,

    // 图片信息
    images:
      note.images_list?.map((img: { url: string; width: number; height: number }) => ({
        url: img.url ?? '',
        width: img.width ?? 0,
        height: img.height ?? 0,
      })) ?? [],

    // 分享信息
    shareInfo: {
      link: note.share_info?.link ?? '',
      title: note.share_info?.title ?? '',
      image: note.share_info?.image ?? '',
    },
  }
}

const xhsV1Service = {
  getNoteCoreDetail,
  getNoteRawDetail,
  convertShortLinkToNoteId,
}

export default xhsV1Service
