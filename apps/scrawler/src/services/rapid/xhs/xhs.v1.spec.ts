import { describe, it } from 'vitest'
import xhsV1Service from './xhs.v1.service'

describe('xhs', () => {
  it(
    'should get note detail',
    async () => {
      const noteId = '67fe1752000000001c0076821'
      try {
        const noteDetail = await xhsV1Service.getNoteCoreDetail(noteId)
        console.log(JSON.stringify(noteDetail, null, 2))
      } catch (err: unknown) {
        console.error((err as Error).message)
      }
    },
    1000 * 60 * 60,
  )

  it('should convert xhs short link to note id', async () => {
    const shortlink = 'http://xhslink.com/a/XwB2DeJ71bOab'
    try {
      const noteId = await xhsV1Service.convertShortLinkToNoteId(shortlink)
      console.log(noteId)
    } catch (err: unknown) {
      console.error(String(err))
    }
  })
})
