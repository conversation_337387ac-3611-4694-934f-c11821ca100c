import { retryUtil } from '@/utils/retry'
import assert from 'assert'
import { douyinV1Api } from '../../../lib/douyin.v1'
import {
  DouyinCommonResponse,
  DouyinVideoDetailResponse,
} from '../../../types/douyin/rapid/douyin.v1'

interface CoreVideoInfo {
  id: string
  title: string
  createTime: Date
  mediaType: string // 2: 图文 4:视频
  region: string

  // 作者信息
  authorId: string
  authorSecUid: string // 安全用户ID，重要，用于构建稳定的用户主页链接
  authorUniqueId: string // 抖音号
  authorName: string
  authorSignature?: string
  authorAvatar?: string
  authorFollowers?: number

  // 统计信息
  likes: number
  comments: number
  shares: number
  plays: number
  collects: number
  admire: number
  play_url: string
  coverUrl: string
  duration: number
}

/**
 * 获取抖音视频原始响应
 */
async function getVideoRawDetail(videoId: string) {
  assert(videoId, new Error('videoId is required'))
  return await douyinV1Api.getVideoDetail(videoId)
}

/**
 * 获取核心视频信息
 */
async function getVideoCoreDetail(videoId: string): Promise<CoreVideoInfo> {
  assert(videoId, new Error('videoId is required'))
  return retryUtil.retry(
    async () => {
      const response = await douyinV1Api.getVideoDetail(videoId)
      if (response.code !== 0 || !response?.data?.aweme_detail)
        throw new Error('获取抖音视频详情失败')

      return extractVideoCoreInfo(response)
    },
    3,
    1000,
    2,
  )
}

function extractVideoCoreInfo(
  response: DouyinCommonResponse<DouyinVideoDetailResponse>,
): CoreVideoInfo {
  const { aweme_detail } = response.data

  return {
    id: aweme_detail.aweme_id ?? '',
    title: aweme_detail.desc ?? '',
    createTime: new Date(aweme_detail.create_time * 1000),
    mediaType: aweme_detail.media_type ?? '',
    region: aweme_detail.region ?? '',

    // 作者信息
    authorId: aweme_detail.author.uid ?? '',
    authorSecUid: aweme_detail.author.sec_uid ?? '',
    authorUniqueId: aweme_detail.author.unique_id ?? '',
    authorName: aweme_detail.author.nickname ?? '',
    authorSignature: aweme_detail.author.signature ?? '',
    authorAvatar: aweme_detail.author.avatar_thumb.url_list[0] ?? '',
    authorFollowers: aweme_detail.author.follower_count ?? 0,

    // 统计信息
    likes: aweme_detail.statistics.digg_count ?? 0,
    comments: aweme_detail.statistics.comment_count ?? 0,
    shares: aweme_detail.statistics.share_count ?? 0,
    plays: aweme_detail.statistics.play_count ?? 0,
    collects: aweme_detail.statistics.collect_count ?? 0,
    admire: aweme_detail.statistics.admire_count ?? 0,

    // 视频信息
    play_url: aweme_detail.video.play_addr.url_list[0] ?? '',
    coverUrl: aweme_detail.video.cover.url_list[0] ?? '',
    duration: (aweme_detail.duration || aweme_detail.video.duration) / 1000, // 秒
  }
}

function buildVideoUrls(videoId: string): {
  webUrl: string
  mobileUrl: string
} {
  return {
    webUrl: `https://www.douyin.com/video/${videoId}`,
    mobileUrl: `https://www.iesdouyin.com/share/video/${videoId}`,
  }
}

function buildNoteUrls(noteId: string): {
  webUrl: string
  mobileUrl: string
} {
  return {
    webUrl: `https://www.douyin.com/note/${noteId}`,
    mobileUrl: `https://www.iesdouyin.com/share/note/${noteId}`,
  }
}

function buildUserUrls(secUid: string): {
  webUrl: string
  mobileUrl: string
} {
  return {
    webUrl: `https://www.douyin.com/user/${secUid}`,
    mobileUrl: `https://www.iesdouyin.com/share/user/${secUid}`,
  }
}

const douyinV1Service = {
  getVideoCoreDetail,
  getVideoRawDetail,
  extractVideoCoreInfo,
  buildVideoUrls,
  buildNoteUrls,
  buildUserUrls,
}

export default douyinV1Service
