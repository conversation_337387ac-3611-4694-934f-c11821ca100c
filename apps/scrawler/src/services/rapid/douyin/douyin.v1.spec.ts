import { describe, it } from 'vitest'
import douyinV1Service from './douyin.v1.service'

describe('douyin', () => {
  it(
    'should get video detail',
    async () => {
      const videoId = '749501818221058793110'
      try {
        const videoDetail = await douyinV1Service.getVideoCoreDetail(videoId)
      } catch (err: unknown) {
        console.error((err as Error).message)
      }
    },
    1000 * 60 * 60,
  )
})
