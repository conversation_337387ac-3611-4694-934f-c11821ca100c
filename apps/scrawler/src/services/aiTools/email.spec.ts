import { generateEmailContent, generateEmailEvaluation } from '@/services/aiTools/email.ts'
import { EmailPrompt, EmailResult } from '@/types/email.ts'
import { beforeAll, describe, expect, it } from 'vitest'

describe('should improve email by AI', () => {
  const goodEmailContent =
    "Hi Katy💜,\n\nI hope you’re having a wonderful day! I’ve been following your inspiring content for quite some time, and I must say, your ability to motivate and uplift your audience is truly remarkable. Your recent videos on personal growth and wellness resonate deeply with me, and I believe they align beautifully with the mission of our brand.\n\nI’m reaching out on behalf of the Otter Life team, creators of a delightful health tracking app that embodies the spirit of inspiration and self-improvement that you promote. Otter Life is designed to help users cultivate healthy habits, monitor stress levels, and track various health metrics, all while enjoying the company of a charming pet otter on their devices. You can check it out here: [Otter Life App](https://apps.apple.com/vn/app/otterlife-track-health-habit/id6475028592), and we’re proud to have received the App Store Editor's Choice award!\n\nI believe that a collaboration between us could inspire many more individuals to take charge of their health and well-being. Your unique expertise and connection with your audience would bring a wonderful perspective to our app, and I’d love to discuss how we can work together to spread this message further.\n\nIf you’re interested, could you please share your audience demographic data and collaboration rates? I’m eager to explore this opportunity with you and am more than happy to accommodate your schedule for a chat!\n\nLooking forward to your reply! 😊\n\nWarm regards,\nMonica from Otter Life Team"
  const badEmailContent =
    'Hello, \n\nI want to collaborate with you! Can you please give us a chance to make a advertisement on your account? we will pay for that! looking for your reply! \n\nOtterlife team.'
  const emailPrompt = {
    emailTemplatePrompt: {
      subject: 'Collaboration Opportunity with Otter Life App 💜',
      text: badEmailContent,
    },
    kolPrompt: {
      kolTitle: 'hannaukim',
      kolDescription: 'Chasing awareness\n',
      kolEmail: '<EMAIL>',
      kolHistoryEmails: [],
      platformAccount: 'hannaukim',
      infoLastUpdated: '2024-11-11',
      projectTitle: 'Default Project',
      projectDescription:
        'This is a project for searching Kols to use Apple watch and iPhone to improve health and work life balance.',
    },
  } as EmailPrompt
  let result: EmailResult
  beforeAll(async () => {
    result = await generateEmailContent(emailPrompt)
  })
  it(
    'should return better email',
    async () => {
      expect(result.emailSubject).toBeTruthy()
      expect(result.emailBody).toBeTruthy()
      console.log(result.emailSubject)
      console.log(result.emailBody)
    },
    { timeout: 20 * 1000 },
  )

  it(
    'should evaluate emails before and after AI improvement.',
    async () => {
      const rawEmail = {
        subject: emailPrompt.emailTemplatePrompt.subject,
        content: emailPrompt.emailTemplatePrompt.text,
      }
      const rawEmailEvaluate = await generateEmailEvaluation(rawEmail)
      const betterEmail = { subject: result.emailSubject, content: result.emailBody }
      const betterEmailEvaluate = await generateEmailEvaluation(betterEmail)
      console.log('origin email: ')
      console.log(JSON.stringify(rawEmail))
      console.log(`origin email evaluation: `)
      console.log(JSON.stringify(rawEmailEvaluate))
      console.log('better email: ')
      console.log(JSON.stringify(betterEmail))
      console.log(`better email evaluation: `)
      console.log(JSON.stringify(betterEmailEvaluate))
    },
    { timeout: 60 * 1000 },
  )
})
