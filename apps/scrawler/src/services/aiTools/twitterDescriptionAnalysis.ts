import { chatCompletion } from '@/api/openai'
import { ChatCompletionMessageParam } from 'openai/resources'

interface TwitterDescriptionAnalysisParams {
  description: string
  banList: string[]
  allowList: string[]
  kolDescription: string
}

interface TwitterDescriptionAnalysisResult {
  score: number
  reason: string
}

class TwitterDescriptionAnalysisService {
  private static instance: TwitterDescriptionAnalysisService

  static getInstance(): TwitterDescriptionAnalysisService {
    if (!TwitterDescriptionAnalysisService.instance) {
      TwitterDescriptionAnalysisService.instance = new TwitterDescriptionAnalysisService()
    }
    return TwitterDescriptionAnalysisService.instance
  }

  /**
   * 分析Twitter用户描述并给出评分
   * @param params 分析参数
   * @returns 评分结果 (0 或 100)
   */
  async analyzeDescription(
    params: TwitterDescriptionAnalysisParams,
  ): Promise<TwitterDescriptionAnalysisResult> {
    try {
      const { description, banList, allowList, kolDescription } = params

      // 如果描述为空，返回0分
      if (!description || description.trim().length === 0) {
        return {
          score: 0,
          reason: '用户描述为空',
        }
      }

      const prompt = this.buildPrompt(description, banList, allowList, kolDescription)
      const messages: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content:
            'You are an AI assistant that analyzes Twitter user descriptions to determine if they match specific criteria. You must return a JSON response with score (0 or 100) and reason fields.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ]

      const response = await chatCompletion({
        model: 'gemini-2.0-flash',
        messages,
        response_format: { type: 'json_object' },
        temperature: 0.3,
        max_tokens: 500,
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('OpenAI返回内容为空')
      }

      const result = JSON.parse(content) as TwitterDescriptionAnalysisResult

      // 确保分数只能是0或100
      result.score = result.score >= 50 ? 100 : 0

      console.log(
        `[TwitterDescriptionAnalysis] 用户描述分析完成，得分: ${result.score}, 原因: ${result.reason}`,
      )
      return result
    } catch (error) {
      console.error('[TwitterDescriptionAnalysis] 分析用户描述时出错:', error)
      return {
        score: 0,
        reason: '分析过程出错',
      }
    }
  }

  /**
   * 批量分析Twitter用户描述
   * @param users 用户列表
   * @param banList 屏蔽词列表
   * @param allowList 允许词列表
   * @param kolDescription KOL描述
   * @returns 高分用户列表（score = 100）
   */
  async batchAnalyzeDescriptions(
    users: Array<{ screen_name: string; description: string }>,
    banList: string[],
    allowList: string[],
    kolDescription: string,
  ): Promise<Array<{ screen_name: string; score: number; reason: string }>> {
    const results = await Promise.all(
      users.map(async (user) => {
        const analysisResult = await this.analyzeDescription({
          description: user.description,
          banList,
          allowList,
          kolDescription,
        })

        return {
          screen_name: user.screen_name,
          score: analysisResult.score,
          reason: analysisResult.reason,
        }
      }),
    )

    // 只返回得分为100的用户
    const highScoreUsers = results.filter((result) => result.score === 100)

    console.log(
      `[TwitterDescriptionAnalysis] 批量分析完成，总用户数: ${users.length}，高分用户数: ${highScoreUsers.length}`,
    )

    return highScoreUsers
  }
  /**
   * 根据源博主描述生成三要素
   * @param sourceDescription 源博主描述
   * @param language 语言（可选）
   * @returns 生成的三要素
   */
  async generateThreeElements(
    sourceDescription: string,
    language?: string,
  ): Promise<{
    banList: string[]
    allowList: string[]
    kolDescription: string
  }> {
    try {
      const systemPrompt = language
        ? `你是一个专业的社交媒体博主垂类分析专家。请分析提供的博主描述信息，并确定该博主的垂类/内容方向。

请根据博主的内容特点，提供以下信息：
1. 博主的垂类/内容方向描述（kolDescription）
2. 与该垂类相关的关键词列表（allowList）
3. 与该垂类不相关的禁用词列表（banList）

【重要提示】
分析要基于博主的描述内容。描述需要具体且准确，关键词和禁用词应当有针对性。
确定博主的垂类/内容方向时不能出现具体的人名、城市、国家、品牌、公司、产品等，应该使用通用的内容类别和风格描述。
所有返回的内容（kolDescription、allowList、banList）必须使用${language}语言。

【非常重要】你必须严格按照下面的JSON格式返回结果，特别注意所有属性名必须使用双引号：
{
  "kolDescription": "用简洁准确的1-2句话描述该博主的核心垂类/内容方向",
  "allowList": ["关键词1", "关键词2", "关键词3"...],
  "banList": ["禁用词1", "禁用词2", "禁用词3"...]
}`
        : `你是一个专业的社交媒体博主垂类分析专家。请分析提供的博主描述信息，并确定该博主的垂类/内容方向。

请根据博主的内容特点，提供以下信息：
1. 博主的垂类/内容方向描述（kolDescription）
2. 与该垂类相关的关键词列表（allowList）
3. 与该垂类不相关的禁用词列表（banList）

【重要提示】
分析要基于博主的描述内容。描述需要具体且准确，关键词和禁用词应当有针对性。
确定博主的垂类/内容方向时不能出现具体的人名、城市、国家、品牌、公司、产品等，应该使用通用的内容类别和风格描述。

【非常重要】你必须严格按照下面的JSON格式返回结果，特别注意所有属性名必须使用双引号：
{
  "kolDescription": "用简洁准确的1-2句话描述该博主的核心垂类/内容方向",
  "allowList": ["关键词1", "关键词2", "关键词3"...],
  "banList": ["禁用词1", "禁用词2", "禁用词3"...]
}`

      const userPrompt = `根据以下源博主的描述，生成三个要素用于筛选相似的KOL：

源博主描述：
"${sourceDescription}"

请生成：
1. banList（屏蔽词列表）：应该排除的关键词，比如竞争对手、不相关领域等
2. allowList（允许词列表）：应该包含的关键词，比如专业领域、技术栈、行业术语等
3. kolDescription（目标KOL描述）：一句话描述要寻找的KOL类型

${language ? `重要：请确保返回的banList、allowList和kolDescription中的内容都使用${language}语言。` : ''}

请返回JSON格式的结果：
{
  "banList": ["词1", "词2", ...],
  "allowList": ["词1", "词2", ...],
  "kolDescription": "目标KOL的描述"
}`

      const messages: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: userPrompt,
        },
      ]

      const response = await chatCompletion({
        model: 'gemini-2.0-flash',
        messages,
        response_format: { type: 'json_object' },
        temperature: 0.5,
        max_tokens: 800,
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new Error('AI生成三要素时返回内容为空')
      }

      const result = JSON.parse(content) as {
        banList: string[]
        allowList: string[]
        kolDescription: string
      }

      console.log('[TwitterDescriptionAnalysis] AI生成三要素完成:', result)
      return result
    } catch (error) {
      console.error('[TwitterDescriptionAnalysis] 生成三要素时出错:', error)
      // 返回默认值
      return {
        banList: [],
        allowList: [],
        kolDescription: '',
      }
    }
  }

  private buildPrompt(
    description: string,
    banList: string[],
    allowList: string[],
    kolDescription: string,
  ): string {
    return `请分析以下Twitter用户的个人简介，判断是否符合目标KOL的特征。

用户简介：
"${description}"

评分规则：
1. 屏蔽词列表（如果简介中包含任何屏蔽词，直接给0分）：
${banList.map((word) => `- ${word}`).join('\n')}

2. 允许词列表（简介中包含这些词是加分项）：
${allowList.map((word) => `- ${word}`).join('\n')}

3. 目标KOL描述（简介应该与此描述相关）：
"${kolDescription}"

评分标准：
- 如果简介包含任何屏蔽词，给0分
- 如果简介与目标KOL描述高度相关，且包含允许词，给100分
- 如果简介与目标KOL描述不相关，给0分
- 只能给0分或100分，不能给其他分数

请返回JSON格式的结果：
{
  "score": 0或100,
  "reason": "评分理由"
}
`
  }
}

export const twitterDescriptionAnalysisService = TwitterDescriptionAnalysisService.getInstance()
