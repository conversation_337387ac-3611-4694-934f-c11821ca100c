import { openai } from '@/api/openai'
import {
  EMAIL_EVALUATION_PROMPT,
  EMAIL_PERSONALIZATION_BASIC_PROMPT,
  FEATURE_ENABLE_POLISH_EMAIL,
} from '@/config/env.ts'
import { EvaluateEmailUserPrompt } from '@/services/aiTools/prompt/email.prompt'
import { EmailEvaluateResult, EmailPrompt, EmailResult } from '@/types/email'

export const generateEmailContent = async (emailPrompt: EmailPrompt): Promise<EmailResult> => {
  if (!FEATURE_ENABLE_POLISH_EMAIL) {
    return {
      emailSubject: emailPrompt.emailTemplatePrompt.subject,
      emailBody: emailPrompt.emailTemplatePrompt.text,
    } as EmailResult
  }
  // 解构 emailPrompt 以获取各个部分
  const { kolPrompt, emailTemplatePrompt } = emailPrompt
  // Important:
  //   - Do not include placeholders like "[Your Name]", "[Your Position]", or similar placeholders. The email should not mention sender details, only focus on the content related to the recipient and the project.
  const systemPrompt = EMAIL_PERSONALIZATION_BASIC_PROMPT
  const userPrompt = `
    KOL Information:
    Title: ${kolPrompt.kolTitle}
    Description: ${kolPrompt.kolDescription}
    Email: ${kolPrompt.kolEmail}
    Platform Account: ${kolPrompt.platformAccount}
    
    Email Template:
    Subject: ${emailTemplatePrompt.subject}
    Text: ${emailTemplatePrompt.text}

    Project Information:
    Title: ${kolPrompt.projectTitle}
    Description: ${kolPrompt.projectDescription}

    Output the email subject and body in English in JSON format, and the email body has an HTML format, like this:
    {
      "emailSubject": "<subject>",
      "emailBody": "<body>"
    }
  `

  const messages = [
    {
      role: 'system' as const,
      content: systemPrompt,
    },
    {
      role: 'user' as const,
      content: userPrompt,
    },
  ]

  // todo 这里的token数量可以交给使用者来控制。如:200-1000字转换为token数
  const result = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages,
    max_tokens: 2000,
    response_format: {
      type: 'json_object',
    },
  })

  // 解析生成的 JSON 内容
  const obj = JSON.parse(result.choices[0].message.content!)

  // 替换占位符并返回结果
  return {
    emailSubject: obj?.emailSubject,
    emailBody: obj?.emailBody,
  } as EmailResult
}

export const generateEmailEvaluation = async (record: { subject: string; content: string }) => {
  const systemPrompt = EMAIL_EVALUATION_PROMPT
  const userText = EvaluateEmailUserPrompt(record.subject, record.content)
  const messages = [
    {
      role: 'system' as const,
      content: systemPrompt,
    },
    {
      role: 'user' as const,
      content: userText,
    },
  ]
  const result = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages,
    max_tokens: 2000,
    response_format: {
      type: 'json_object',
    },
  })
  const obj = JSON.parse(result.choices[0].message.content!)
  return obj as EmailEvaluateResult
}
