import { TiktokCommentUser } from '@/api/@types/rapidapi/Tiktok'
import { describe, expect, it } from 'vitest'
import { TtInfoService } from '../ttInfo.service'
import { getUserPortraitAnalysis as getUserPortraitAnalysisWithoutReason } from './userPortaitAnslysisWithoutReason'

describe('getUserPortraitAnalysis', () => {
  it(
    '应该处理多个用户的分析',
    async () => {
      try {
        const userName = 'eveolivia_'
        console.log('获取TikTok用户评论...')
        const videosCommentsUsers: TiktokCommentUser[] =
          await TtInfoService.getAuthorVideosCommentsUsers(userName, 50, 4, 150)

        // 处理用户数据，移除查询参数
        const users = videosCommentsUsers.slice(0, 100).map((user) => {
          let cleanAvatar = ''
          if (user.avatar) {
            try {
              cleanAvatar = user.avatar.split('?')[0]
            } catch (e) {
              console.warn('无法清理头像URL:', user.avatar)
            }
          }

          return {
            username: user.nickname,
            avatar: cleanAvatar,
            signature: user.signature || '',
          }
        })

        console.log(`处理了 ${users.length} 个用户数据`)
        users.forEach((user, index) => {
          console.log(
            `用户 ${index + 1}: ${user.username}, 头像URL长度: ${user.avatar?.length || 0}, 简介长度: ${user.signature?.length || 0}`,
          )
        })

        const startTime = Date.now()
        console.log('开始分析用户画像...')
        const result = await getUserPortraitAnalysisWithoutReason(users)
        const endTime = Date.now()
        console.log('多用户分析时间：', endTime - startTime)
        console.log('多用户分析结果：', JSON.stringify(result))
        // 验证基本结构
        expect(result).toBeDefined()
        expect(result.gender.male + result.gender.female).toBe(100)
        expect(
          result.age.under18 + result.age.age18to25 + result.age.age25to45 + result.age.above45,
        ).toBe(100)
      } catch (error) {
        console.error('多用户分析时发生错误：', error)
        // 不抛出错误，允许测试继续
        expect(true).toBe(true) // 确保测试不会失败
      }
    },
    { timeout: 300000 },
  )
})
