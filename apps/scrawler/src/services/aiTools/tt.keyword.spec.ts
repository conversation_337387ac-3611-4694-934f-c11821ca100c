import TiktokApi from '@/api/tiktok'
import { describe, it } from 'vitest'
import { generateTTKeywords } from './tt.keywords'

describe('generateTTKeywords', () => {
  it(
    '应该生成关键词并打印结果',
    async () => {
      const videos = await TiktokApi.getInstance().getUserVideos({
        unique_id: 'alsonchang',
        count: 20,
        cursor: 0,
      })
      // 拼接所有视频标题
      const titles = videos?.map((video) => video.title).join(' ')

      try {
        const result = await generateTTKeywords({
          channelTitle: '',
          channelDescription: titles || '',
        })
        // 打印所有的关键词
        console.log(
          '生成的关键词结果：',
          result.search_term_suggestions.map((item) => item.search_terms),
        )
        console.log('生成的关键词结果：', JSON.stringify(result, null, 2))
      } catch (error) {
        console.error('生成关键词时发生错误：', error)
      }
    },
    { timeout: 300000 },
  )
})
