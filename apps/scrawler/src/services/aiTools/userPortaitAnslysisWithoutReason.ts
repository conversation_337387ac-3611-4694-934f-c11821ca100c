import { chatCompletion } from '@/api/openai'
import { COMMENTS_PORTAIT_LLM_NAME } from '@/config/env'
import Sentry from '@/infras/sentry'
import { UserPortraitLLMInput, UserPortraitLLMOutput } from '@/types/audience'
import { aiUtils } from '@/utils/ai'
import ImageUtil from '@/utils/images'
import { JsonParseUtil } from '@/utils/jsonParse'
import Bluebird from 'bluebird'
import { ChatCompletionContentPart } from 'openai/resources/index.mjs'

/**
 * 基于博主视频下评论者的简介/昵称/images 来分析博主的受众画像。
 * 获取评论者的简介/昵称/images
 *  */
// 系统提示词
export const UserPortraitSystemPrompt = (): string => {
  return `你是一位专业的社交媒体用户数据分析师，擅长通过多维度信息推断用户特征。现在你需要根据提供的用户头像、用户名和个人签名数据，分析用户群体的性别和年龄分布。
  
  ## 分析方法指南：
  
  ### 性别判断要点：
  - 头像特征：面部轮廓、妆容、发型、服饰风格等
  - 用户名特点：带有明显性别倾向的词汇、符号、昵称风格
  - 签名内容：表达方式、话题偏好、情感风格、用词习惯
  - 常见模式：女性用户可能更多使用情感化表达、表情符号、自拍头像；男性可能更多使用简洁语句、游戏/科技相关内容
  
  ### 年龄判断要点：
  - 头像风格：不同年龄段流行的自拍方式、滤镜使用、形象展示
  - 用户名特征：使用数字（可能暗示出生年份）、流行文化引用、网络流行语
  - 签名内容：生活状态提示（学生、职场、家庭）、社会话题偏好、表达成熟度
  - 语言特点：Z世代流行语、90后常用表达、中年人表达习惯等
  
  ### 复合分析原则：
  1. 交叉验证：当某一指标无法确定时，通过其他维度的信息进行补充判断
  2. 权重分配：头像>签名>用户名（一般情况下的可信度排序）
  3. 模式识别：将用户与已知的典型用户群体特征进行匹配
  4. 不确定性处理：明确标记置信度低的判断，避免过度推断
  
  ### 分析流程：
  1. 首先逐个分析所有可用信息，为每个用户建立初步判断
  2. 识别出高置信度的判断和低置信度的判断
  3. 汇总统计各类别数量，计算百分比
  4. 对于难以判断的用户，优先归入特征最相似的主流类别
  
  ## 重要规则：
  1. 即使信息有限，也必须做出判断，不能返回空结果或零值
  2. 性别分布总和必须为100%，年龄分布总和必须为100%
  3. 如果无法确定，请基于有限信息做出最合理的推测
  
  ## 输出要求，非常重要【必须严格按照以下JSON格式输出最终分析结果，不要输出分析过程或其他额外内容】：
  {
    "gender": {
      "male": 数字,
      "female": 数字
    },
    "age": {
      "under18": 数字,
      "age18to25": 数字,
      "age25to45": 数字,
      "above45": 数字
    }
  }
  
  所有数值应为整数百分比，性别分布总和必须为100，年龄分布总和必须为100。`
}

// 用户提示词
export const UserPortraitUserPrompt = async (
  users: UserPortraitLLMInput[],
): Promise<ChatCompletionContentPart[]> => {
  const userPrompts: ChatCompletionContentPart[] = [
    await aiUtils.text('请分析以下评论区用户的头像、昵称和个人简介，判断观众的性别和年龄分布：'),
  ]

  for (let i = 0; i < users.length; i++) {
    const user = users[i]
    userPrompts.push(
      await aiUtils.text(
        `用户 ${i + 1}：\n昵称：${user.username}\n个人简介：${user.signature || '无'}`,
      ),
    )

    if (user.avatar) {
      try {
        const thumbnail = await ImageUtil.fetchImageAsBase64(user.avatar)
        userPrompts.push({
          type: 'image_url',
          image_url: {
            url: `data:${thumbnail.inlineData.mimeType};base64,${thumbnail.inlineData.data}`,
          },
        })
      } catch (error) {
        console.warn(
          `获取图片失败: ${user.avatar}, 错误: ${error instanceof Error ? error.message : String(error)}`,
        )
      }
    }
  }
  return userPrompts
}

// 解析用户画像结果
const parsePortraitResult = (result: string): UserPortraitLLMOutput => {
  try {
    const parsedResult = JsonParseUtil.parseJsonRobustly<UserPortraitLLMOutput>(result)

    if (!parsedResult.gender || !parsedResult.age) {
      throw new Error('Invalid result format: missing gender or age')
    }

    return {
      gender: {
        male: parsedResult.gender.male || 0,
        female: parsedResult.gender.female || 0,
      },
      age: {
        under18: parsedResult.age.under18 || 0,
        age18to25: parsedResult.age.age18to25 || 0,
        age25to45: parsedResult.age.age25to45 || 0,
        above45: parsedResult.age.above45 || 0,
      },
    }
  } catch (error) {
    Sentry.captureException(error)
    console.error('Failed to parse portrait result:', error)
    throw new Error('Failed to parse portrait result')
  }
}

// 标准化结果，确保百分比正确
function normalizePortraitResult(
  parsedResult: UserPortraitLLMOutput,
  userCount: number,
): UserPortraitLLMOutput {
  // 标准化性别分布
  const genderSum = parsedResult.gender.male + parsedResult.gender.female
  if (genderSum !== 100) {
    if (genderSum > 0) {
      parsedResult.gender.male = Math.round((parsedResult.gender.male / genderSum) * 100)
      parsedResult.gender.female = 100 - parsedResult.gender.male
    } else {
      parsedResult.gender.male = userCount === 1 ? 100 : 50
      parsedResult.gender.female = userCount === 1 ? 0 : 50
    }
  }

  // 标准化年龄分布
  const ageSum =
    parsedResult.age.under18 +
    parsedResult.age.age18to25 +
    parsedResult.age.age25to45 +
    parsedResult.age.above45

  if (ageSum !== 100) {
    if (ageSum > 0) {
      const factor = 100 / ageSum
      parsedResult.age.under18 = Math.round(parsedResult.age.under18 * factor)
      parsedResult.age.age18to25 = Math.round(parsedResult.age.age18to25 * factor)
      parsedResult.age.age25to45 = Math.round(parsedResult.age.age25to45 * factor)
      parsedResult.age.above45 =
        100 - (parsedResult.age.under18 + parsedResult.age.age18to25 + parsedResult.age.age25to45)
    } else {
      parsedResult.age.under18 = 10
      parsedResult.age.age18to25 = 60
      parsedResult.age.age25to45 = 25
      parsedResult.age.above45 = 5
    }
  }

  return parsedResult
}

export const getUserPortraitAnalysis = async (
  users: UserPortraitLLMInput[],
): Promise<UserPortraitLLMOutput> => {
  try {
    if (users.length === 0) {
      throw new Error('not found audience users')
    }

    if (users.length <= 20) {
      return await processUserBatch(users)
    }

    const batchSize = 20
    const batches: UserPortraitLLMInput[][] = []

    for (let i = 0; i < users.length; i += batchSize) {
      batches.push(users.slice(i, i + batchSize))
    }

    console.log(`[getUserPortraitAnalysis] 处理${users.length}个用户，分成${batches.length}批`)

    const concurrencyLimit = 50

    const batchResults = await Bluebird.map(
      batches,
      async (batch) => {
        try {
          return await processUserBatch(batch)
        } catch (error) {
          console.error(`批次处理失败:`, error)
          return null
        }
      },
      { concurrency: concurrencyLimit },
    )

    console.log(`[getUserPortraitAnalysis] 所有批次处理完成`)

    const successfulResults = batchResults.filter(
      (result): result is UserPortraitLLMOutput => result !== null,
    )

    if (successfulResults.length === 0) {
      throw new Error('所有批次处理都失败了')
    }

    if (successfulResults.length === 1) {
      return successfulResults[0]
    }
    console.log(
      `[getUserPortraitAnalysis] 共 ${batches.length} 批次，成功了 ${successfulResults.length} 个批次`,
    )
    return mergePortraitResults(successfulResults)
  } catch (error) {
    Sentry.captureException(error)
    console.error('Failed to get user portrait analysis:', error)
    throw new Error('Failed to get user portrait analysis')
  }
}

async function processUserBatch(users: UserPortraitLLMInput[]): Promise<UserPortraitLLMOutput> {
  try {
    const [systemPrompt, userPrompt] = await Promise.all([
      UserPortraitSystemPrompt(),
      UserPortraitUserPrompt(users),
    ])
    console.log(
      `[processUserBatch]使用模型 ${COMMENTS_PORTAIT_LLM_NAME} 处理 ${users.length} 个用户`,
    )

    const response = await chatCompletion({
      model: COMMENTS_PORTAIT_LLM_NAME,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      temperature: 0.2,
      response_format: { type: 'json_object' },
      order: ['aihubmix', 'openRouterApi'],
    })

    const result = response.choices[0]?.message?.content || ''
    let parsedResult = parsePortraitResult(result)
    parsedResult = normalizePortraitResult(parsedResult, users.length)
    return parsedResult
  } catch (error) {
    console.error(`批次处理失败:`, error)
    throw error
  }
}

// 合并多个分析结果
function mergePortraitResults(results: UserPortraitLLMOutput[]): UserPortraitLLMOutput {
  // 初始化合并结果
  const merged: UserPortraitLLMOutput = {
    gender: { male: 0, female: 0 },
    age: { under18: 0, age18to25: 0, age25to45: 0, above45: 0 },
  }

  const totalWeight = results.length

  for (const result of results) {
    merged.gender.male += result.gender.male / totalWeight
    merged.gender.female += result.gender.female / totalWeight

    merged.age.under18 += result.age.under18 / totalWeight
    merged.age.age18to25 += result.age.age18to25 / totalWeight
    merged.age.age25to45 += result.age.age25to45 / totalWeight
    merged.age.above45 += result.age.above45 / totalWeight
  }

  merged.gender.male = Math.round(merged.gender.male)
  merged.gender.female = 100 - merged.gender.male

  merged.age.under18 = Math.round(merged.age.under18)
  merged.age.age18to25 = Math.round(merged.age.age18to25)
  merged.age.age25to45 = Math.round(merged.age.age25to45)
  merged.age.above45 = 100 - (merged.age.under18 + merged.age.age18to25 + merged.age.age25to45)

  return merged
}
