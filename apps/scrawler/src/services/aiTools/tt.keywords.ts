import { openai } from '@/api/openai'
import { COMMENTS_PORTAIT_LLM_NAME, OPENAI_MODEL } from '@/config/env'
import { TTKeywordPrompt, TTKeywordResult } from '@/types/ttKeywordPrompt'
import { aiUtils } from '@/utils/ai'
import { ChatCompletionContentPart } from 'openai/resources/index.mjs'

export const generateTTKeywords = async (
  ttKeywordPrompt: TTKeywordPrompt,
): Promise<TTKeywordResult> => {
  const { channelTitle, channelDescription, kolDescription } = ttKeywordPrompt

  const systemPrompt = `
    Assume you are an outstanding TikTok expert, proficient in content analysis and search optimization. Your task is to assist me in finding other channels similar to a given channel and meeting specific user requirements. Please follow these steps:

    1. Carefully analyze the TikTok channel title and content description I provide.
    2. Consider the user's specific requirements about what kind of creators they want to find.
    3. Summarize the main content direction, style characteristics, and target audience of the channel.
    4. Identify and list the key tags used in the original channel.
    5. Based on your analysis and user requirements, generate 10 sets of high-quality search terms or phrases that should help find similar TikTok channels matching these requirements.
    6. Each set of search terms should combine:
       - Content-related words from the original channel
       - One or more tags from the original channel
       - Keywords that reflect the user's specific requirements
    7. The search terms should be in the same language as the original channel title.
    8. Avoid using generic or trending tags such as 'foryou', 'foryoupage', 'viral', 'trending', 'viralvideo', or any tag starting with 'fyp'. Focus on content-specific and niche-relevant tags instead.

    Channel Information:
    Title: ${channelTitle}
    Description: ${channelDescription}
    ${kolDescription ? `Ideal Creator Description: ${kolDescription}` : ''}

    Please output your analysis and suggestions using the following JSON format:
    
      "search_term_suggestions": [
        {
          "search_terms": "",
          "content_words": "",
          "used_tags": [],
          "relevance_explanation": "",
          "expected_results": ""
        },
        // ... repeat 10 times ...
      ]

    Note: Please ensure that your analysis is accurate and comprehensive, and that the search terms are specifically targeted to find creators that match both the original channel style AND the user's specific requirements. The generated keywords should help find creators who not only create similar content but also meet the user's expectations.
  `

  const messages = [
    {
      role: 'system' as const,
      content: systemPrompt,
    },
    {
      role: 'user' as const,
      content: `Generate keyword analysis and search suggestions based on the provided channel details.`,
    },
  ]

  const result = await openai.chat.completions.create({
    model: OPENAI_MODEL,
    messages,
    max_tokens: 2000,
    response_format: {
      type: 'json_object',
    },
  })

  const obj = JSON.parse(result.choices[0].message.content!)

  return obj as TTKeywordResult
}

export const generateTagsFromVideoCovers = async (
  videos: Array<{ cover: string; title: string }>,
): Promise<{ tags: string[] }> => {
  const systemPrompt = `你是一位专业的TikTok内容分析专家。你的任务是分析创作者的视频封面和标题，生成最相关的内容标签。

请遵循以下规则：
1. 综合分析视频封面的视觉元素和视频标题，推断视频的主题和内容类型
2. 生成的标签应该具体且相关，避免过于宽泛
3. 不要使用普通的热门标签（如：trending, viral, fyp等）
4. 标签应该使用目标地区的主要语言
5. 一共生成10个最符合视频内容的标签，确保这些标签能准确反映视频主题
6. 标签应该有一定的搜索量，但也要足够精准

输出格式要求（必须使用JSON格式）：
{
  "tags": ["标签1", "标签2", ... "标签10"]
}
`

  const userPrompt: ChatCompletionContentPart[] = [
    await aiUtils.text(
      `我会发给你一个tiktok创作者的视频封面和标题，你需要根据这些信息生成10个最符合视频内容的tag。你只需要返回JSON格式的标签即可:\n`,
    ),
  ]

  videos.forEach(async (video, index) => {
    userPrompt.push(
      await aiUtils.text(`视频 ${index + 1}:\n标题: ${video.title}\n封面图片:`),
      await aiUtils.image(video.cover, 'auto', true),
    )
  })

  try {
    const response = await openai.chat.completions.create({
      model: COMMENTS_PORTAIT_LLM_NAME,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      temperature: 0.3,
      response_format: { type: 'json_object' },
    })

    const result = JSON.parse(response.choices[0]?.message?.content || '{}')
    return {
      tags: Array.isArray(result.tags) ? result.tags.slice(0, 10) : [],
    }
  } catch (error) {
    console.error('生成标签时出错:', error)
    return { tags: [] }
  }
}
