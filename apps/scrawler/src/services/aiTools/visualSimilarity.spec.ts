import InstagramApi from '@/lib/instagramRapidApi.v3'
import { describe, it } from 'vitest'
import { analyzeVisualSimilarityService } from './visualSimilarity'

describe('should test vertical analysis', () => {
  it('should test vertical analysis', async () => {
    const userRequest = {
      username: 'arm<PERSON><PERSON><PERSON>',
    }
    const currentUser = await InstagramApi.getInstance().getUserWithPosts(userRequest)
    if (!currentUser) {
      throw new Error('current user not found')
    }
    const result = await analyzeVisualSimilarityService.analysisUserVeticalSimilarity({
      id: currentUser.id,
      username: currentUser.username,
      fullname: currentUser.fullName || '',
      biography: currentUser.biography || '',
      url: currentUser.profilePicUrl || '',
      posts: currentUser.posts || [],
    })
    console.log(result)
  })
}, 100000)
