import InstagramApi from '@/api/instagram'
import TiktokApi from '@/api/tiktok'
import YoutubeApi from '@/api/youtube'
import { getInstagramUsername, getTiktokUniqueId } from '@/utils/url'
import { KolInfo, KolPlatform, prisma } from '@repo/database'
import Blue<PERSON> from 'bluebird'
import fs from 'fs'
import path from 'path'
import { describe, it } from 'vitest'
import { LinkType } from './contact/contact'
import { newExternalLink } from './contact/contact.utils'

// const PATH = '/Users/<USER>/youtube-channels.txt'
const PATH = '/Users/<USER>/kol-without-email.txt'
const OUTPUT_PATH = '/Users/<USER>/nano-email-results'

describe('get email by nano', () => {
  it(
    'should test how many channel with nano email has link',
    async () => {
      const channelIds = fs.readFileSync(PATH, 'utf-8').split('\n').filter(Boolean)

      let totalChannels = 0
      let channelsWithLinks = 0
      let channelsWithValidLinks = 0
      await Bluebird.map(
        channelIds,
        async (channelId) => {
          try {
            const channel = await YoutubeApi.getInstance().getChannel(channelId)
            if (channel) {
              totalChannels++
              const links = channel.links ?? []

              if (links.length > 0) {
                console.log(`Channel ${channelId} has ${links.length} links`)
                channelsWithLinks++
                // 写入数据库
                try {
                  const result = await prisma.kolInfo.update({
                    where: {
                      platform_platformAccount: {
                        platform: KolPlatform.YOUTUBE,
                        platformAccount: channelId,
                      },
                    },
                    data: {
                      links: links.map((i) => i.link),
                    },
                  })
                  if (result?.links?.length) {
                    console.log(`updated links`)
                  }
                } catch (error) {
                  console.error(`Error writing channel ${channelId} to database:`, error)
                }
                if (
                  links.some(
                    (link) =>
                      link.link.includes('tiktok.com') || link.link.includes('instagram.com'),
                  )
                ) {
                  channelsWithValidLinks++
                }
              } else {
                console.log(`Channel ${channelId} has no links`)
              }
            } else {
              console.log(`Channel ${channelId} not found`)
            }
          } catch (error) {
            console.error(`Error fetching channel ${channelId}:`, error)
          }
        },
        { concurrency: 20 },
      )

      const linkRate = (channelsWithLinks / totalChannels) * 100
      const validLinkRate = (channelsWithValidLinks / totalChannels) * 100
      console.log(`Total channels: ${totalChannels}`)
      console.log(`Channels with links: ${channelsWithLinks}`)
      console.log(`Link rate: ${linkRate.toFixed(2)}%`)
      console.log(`Channels with valid links: ${channelsWithValidLinks}`)
      console.log(`Valid link rate: ${validLinkRate.toFixed(2)}%`)
    },

    10 * 60 * 1000,
  )

  it(
    'should try to get email by cross-platform',
    async () => {
      const channelIds = fs.readFileSync(PATH, 'utf-8').split('\n').filter(Boolean)
      // channelIds = Array.from(new Set(channelIds)).slice(0, 100)
      const sourceChannel = await prisma.kolInfo.findMany({
        where: {
          platform: KolPlatform.YOUTUBE,
          platformAccount: {
            in: channelIds,
          },
        },
      })

      const fetchEmailCrossPlatform = async (kol: KolInfo) => {
        let message = `------------------Source: ${kol.platformAccount} - ${JSON.stringify(kol.links)}\n`
        const links = kol.links ?? []
        const tiktokLink = links.find((i) => i.includes('tiktok.com'))
        const instagramLink = links.find((i) => i.includes('instagram.com'))
        let hasEmail = false
        const emails = []
        if (tiktokLink) {
          const result = await fetchEmailFromTiktok(tiktokLink)
          message += message
          if (result.email) {
            message += `found email from tiktok: ${result.email}\n`
            hasEmail = true
            emails.push(result.email)
          }
        }
        if (instagramLink) {
          const result = await fetchEmailFromInstagram(instagramLink)
          message += result.message
          if (result.email) {
            message += `found email from instagram: ${result.email}\n`
            hasEmail = true
            emails.push(result.email)
          }
        }
        if (hasEmail) {
          message += `found email from tiktok or instagram: ${emails.join(',')}\n`
          if (kol.email && emails.includes(kol.email)) {
            message += `email are the same\n`
          } else {
            message += `email are not the same, current ${kol.email} is not in ${emails.join(',')}\n`
          }
          if (kol.platformAccount && emails.length == 1) {
            await prisma.kolInfo.update({
              where: {
                platform_platformAccount: {
                  platform: KolPlatform.YOUTUBE,
                  platformAccount: kol.platformAccount ?? '',
                },
              },
              data: {
                email: emails[0],
              },
            })
          }
        } else {
          message += `not found email from tiktok or instagram\n`
        }

        console.log(message + '\n----------------------------\n')
        return {
          channelId: kol.platformAccount,
          hasEmail,
          message,
          emails,
        }
      }
      const result = await Bluebird.map(sourceChannel, fetchEmailCrossPlatform, {
        concurrency: 10,
      })
      console.log(`Total channels: ${sourceChannel.length}`)
      console.log(`Channels with email: ${result.filter((i) => i.hasEmail).length}`)
      console.log(
        `Channels without email: ${sourceChannel.length - result.filter((i) => i.hasEmail).length}`,
      )
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      if (!fs.existsSync(OUTPUT_PATH)) {
        fs.mkdirSync(OUTPUT_PATH)
      }
      const noEmailResults = result.filter((r) => !r.hasEmail)
      const noEmailOutputFile = path.join(OUTPUT_PATH, `no-email-results-${timestamp}.txt`)
      const noEmailContent = noEmailResults.map((r) => r.channelId).join('\n')
      fs.writeFileSync(noEmailOutputFile, noEmailContent)
      console.log(`没有邮箱的结果已保存到文件: ${noEmailOutputFile}`)
      const outputFile = path.join(OUTPUT_PATH, `email-result-messages-${timestamp}.txt`)
      const outputContent = result.map((r) => r.message).join('\n')
      fs.writeFileSync(outputFile, outputContent)
      console.log(`结果已保存到文件: ${outputFile}`)
    },
    60 * 60 * 1000,
  )
})

async function fetchEmailFromTiktok(link: string): Promise<{ message: string; email: string }> {
  let message = ''
  const url = await newExternalLink(link, link)
  if (url.type === LinkType.TIKTOK) {
    const id = getTiktokUniqueId(link)
    if (id) {
      const user = await prisma.kolInfo.findUnique({
        where: {
          platform_platformAccount: {
            platform: KolPlatform.TIKTOK,
            platformAccount: id,
          },
        },
        select: {
          email: true,
        },
      })
      if (user) {
        return {
          message: 'find tiktok user in the database success\n',
          email: user.email ?? '',
        }
      } else {
        const user = await TiktokApi.getInstance().getUserDetail({ unique_id: id })
        if (user) {
          return {
            message: 'find tiktok user from api success\n',
            email: user.user.email ?? '',
          }
        }
      }
    } else {
      message = `Not a tiktok link: ${link}\n`
    }
  }
  return {
    message,
    email: '\n',
  }
}

async function fetchEmailFromInstagram(link: string): Promise<{ message: string; email: string }> {
  let message = ''
  const url = await newExternalLink(link, link)
  if (url.type === LinkType.INSTAGRAM) {
    const id = getInstagramUsername(link)
    if (id) {
      const user = await prisma.kolInfo.findUnique({
        where: {
          platform_platformAccount: {
            platform: KolPlatform.INSTAGRAM,
            platformAccount: id,
          },
        },
        select: {
          email: true,
        },
      })
      if (user) {
        return {
          message: 'find instagram user in the database success\n',
          email: user.email ?? '',
        }
      } else {
        const user = await InstagramApi.getInstance().getUser(id)
        if (user) {
          return {
            message: 'find instagram user from api success\n',
            email: user.email ?? '',
          }
        }
      }
    } else {
      message = `Not a instagram link: ${link}\n`
    }
  }
  return {
    message,
    email: '\n',
  }
}
