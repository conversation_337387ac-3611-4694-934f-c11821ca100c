import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'
import Sentry from '@/infras/sentry'
import { getApiPrice } from '@/types/apiCode'
import { prisma } from '@repo/database'

class ApiQuotaService {
  async upsertDailyQuotaCount(quota: number, code: string) {
    const quotaKey = `${REDIS_KEY_PREFIX}api-quota:${code}:${new Date().toISOString().split('T')[0]}`
    const exist = await redis.get(quotaKey)
    if (!exist) {
      await Promise.all([
        redis.set(quotaKey, quota, 'EX', 2592000), // 30 days
        this.upsertYesterdayQuotaCount(code, quota),
      ])
    }
  }

  // 传入的是 usedQuota
  private async upsertYesterdayQuotaCount(code: string, currentCount: number) {
    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1))
      .toISOString()
      .split('T')[0]
    const quotaKey = `${REDIS_KEY_PREFIX}api-quota:${code}:${yesterday}`
    const yesterdayQuota = await redis.get(quotaKey)
    let used = 0
    if (yesterdayQuota) {
      used = currentCount - Number(yesterdayQuota)
    } else {
      Sentry.captureException(new Error(`${code} yesterday quota count not found`))
    }
    await prisma.apiDailyUsage.upsert({
      where: {
        apiCode_date: {
          apiCode: code,
          date: yesterday,
        },
      },
      update: {
        usageQuota: used,
        cost: this.calculateCost(code, used),
      },
      create: {
        apiCode: code,
        date: yesterday,
        usageQuota: used,
        cost: this.calculateCost(code, used),
      },
    })
  }

  private calculateCost(code: string, quota: number) {
    return quota * getApiPrice(code)
  }
}

export default new ApiQuotaService()
