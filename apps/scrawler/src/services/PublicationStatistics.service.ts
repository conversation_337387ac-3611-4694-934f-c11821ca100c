import { PostType } from '@/api/@types/rapidapi/Instagram'
import { YoutubeType } from '@/api/@types/rapidapi/Youtube'
import InstagramApi from '@/api/instagram'
import Tiktok<PERSON>pi from '@/api/tiktok'
import YoutubeApi from '@/api/youtube'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { PaginationParams } from '@/types/pagination'
import {
  BatchProcessConfig,
  EngagementMetrics,
  GetPostUrlsResponse,
  UpdatePublicationDataParams,
  UpdateVideoDataRequestParams,
  UpdateVideoDataResult,
  UpdateVideoDataStats,
} from '@/types/publicationStatistics'
import { extractEmail } from '@/utils/email'
import Logger from '@/utils/logger'
import { PaginationService } from '@/utils/pagination'
import { retryUtil } from '@/utils/retry'
import { KolPlatform, PublicationStatisticsSheetData, prisma } from '@repo/database'
import assert from 'assert'
import Bluebird from 'bluebird'
import KolService from './kol'
import douyinV1Service from './rapid/douyin/douyin.v1.service'
import xhsV1Service from './rapid/xhs/xhs.v1.service'
import TiktokService from './tiktok'

const DEFAULT_BATCH_CONFIG: BatchProcessConfig = {
  batchSize: 100,
  batchDelay: 200,
  concurrency: 50,
}

function chunkArray<T>(array: T[], size: number): T[][] {
  return array.reduce(
    (acc, _, i) => (i % size ? acc : [...acc, array.slice(i, i + size)]),
    [] as T[][],
  )
}

function calculateEngagementMetrics(metrics: EngagementMetrics) {
  const { views, likes, comments, favorites } = metrics
  const totalEngagement = likes + comments + favorites
  const engagementRate = views > 0 ? totalEngagement / views : 0
  const cpm = views > 0 ? 0 : 0

  return {
    totalEngagement,
    engagementRate,
    cpm,
  }
}

const cleanInputData = <T extends string | null | undefined>(arr: T[] = []): T[] =>
  arr.filter((item): item is NonNullable<T> => !!item && item.trim() !== '') as T[]

// 更新投放的视频数据
async function updateVideoData(
  userId: string | undefined,
  {
    spreadsheetId,
    tiktok,
    youtube,
    instagram,
    douyin,
    xhs,
    tagIds,
    batchConfig = DEFAULT_BATCH_CONFIG,
  }: UpdateVideoDataRequestParams & {
    batchConfig?: BatchProcessConfig
  },
): Promise<UpdateVideoDataResult> {
  const tiktokApi = TiktokApi.getInstance()
  const youtubeApi = YoutubeApi.getInstance()
  const instagramApi = InstagramRapidApiV3.getInstance()

  const platformData = {
    tiktok: {
      ids: cleanInputData([...(tiktok?.videosIds || [])]),
      urls: cleanInputData(tiktok?.urls || []),
    },
    youtube: {
      ids: cleanInputData([...(youtube?.videosIds || []), ...(youtube?.shortsIds || [])]),
      urls: cleanInputData(youtube?.urls || []),
    },
    instagram: {
      ids: cleanInputData([...(instagram?.postsIds || []), ...(instagram?.reelsIds || [])]),
      urls: cleanInputData(instagram?.urls || []),
    },
    douyin: {
      ids: cleanInputData([...(douyin?.videosIds || []), ...(douyin?.noteIds || [])]),
      urls: cleanInputData(douyin?.urls || []),
    },
    xhs: {
      ids: cleanInputData([...(xhs?.noteIds || [])]),
      urls: cleanInputData(xhs?.urls || []),
    },
  }

  const stats = {
    total:
      platformData.tiktok.urls.length +
      platformData.youtube.ids.length +
      platformData.instagram.ids.length +
      platformData.douyin.ids.length +
      platformData.xhs.ids.length,
    success: 0,
    failed: 0,
    created: 0,
    updated: 0,
    failedVideoIds: [] as string[],
    failedVideoUrls: [] as string[],
  }

  console.log(`开始处理数据，总数：${stats.total}`)
  console.log('批处理配置:', {
    批次大小: batchConfig.batchSize,
    延迟时间: `${batchConfig.batchDelay}ms`,
    并发数: batchConfig.concurrency,
  })

  try {
    const [tiktokResults, youtubeResults, instagramResults, douyinResults, xhsResults] =
      await Promise.all([
        processTiktokBatches(userId, platformData.tiktok.urls, tiktokApi, stats, batchConfig),
        processYoutubeBatches(
          userId,
          platformData.youtube.ids,
          youtube?.shortsIds || [],
          platformData.youtube.urls,
          youtubeApi,
          stats,
          batchConfig,
        ),
        processInstagramBatches(
          userId,
          platformData.instagram.ids,
          instagram?.reelsIds || [],
          platformData.instagram.urls,
          instagramApi,
          stats,
          batchConfig,
        ),
        processDouyinBatches(platformData.douyin.ids, platformData.douyin.urls, stats, batchConfig),
        processXhsBatches(platformData.xhs.ids, platformData.xhs.urls, stats, batchConfig),
      ])

    const validResults = [
      ...tiktokResults,
      ...youtubeResults,
      ...instagramResults,
      ...douyinResults,
      ...xhsResults,
    ].filter((result): result is NonNullable<typeof result> => result !== null)

    stats.success = validResults.length

    console.log('开始更新数据库...')
    const updatedData = await processDbBatches(
      validResults,
      spreadsheetId,
      stats,
      batchConfig,
      tagIds,
    )

    console.log('处理完成，统计信息:', {
      总数: stats.total,
      成功: stats.success,
      失败: stats.failed,
      新建: stats.created,
      更新: stats.updated,
    })

    return {
      data: updatedData.filter((item): item is NonNullable<typeof item> => item !== null),
      stats,
    }
  } catch (error) {
    console.error('更新视频数据失败:', error)
    throw error
  }
}

// tiktok track
async function processTiktokBatches(
  userId: string | undefined,
  urls: string[],
  tiktokApi: TiktokApi,
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
): Promise<any[]> {
  const batches = chunkArray(urls, config.batchSize)
  const results: any[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理TikTok第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (url) => {
        try {
          const detail = await retryUtil.retryWithValidator(
            () => tiktokApi.getVideoDetail(url),
            (result) => !!result, // 结果不为空或undefined时验证通过
            3,
            1000,
            2,
            (result, error, retryCount, nextDelay) => {
              if (error) {
                console.warn(
                  `TikTok视频(${url})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                  error,
                )
              } else if (!result) {
                console.warn(
                  `TikTok视频(${url})返回空结果，将在${nextDelay}ms后第${retryCount}次重试`,
                )
              }
            },
          )
          if (!detail) {
            stats.failed++
            stats.failedVideoUrls.push(url)
            return null
          }

          const [userInfo, kol] = await Promise.all([
            retryUtil.retryWithValidator(
              () => tiktokApi.getUserDetail({ user_id: detail.author.id }),
              (result) => !!result && !!result.user, // 结果不为空且包含user信息时验证通过
              3,
              1000,
              2,
              (result, error, retryCount, nextDelay) => {
                if (error) {
                  console.warn(
                    `TikTok用户(${detail.author.id})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                    error,
                  )
                } else if (!result || !result.user) {
                  console.warn(
                    `TikTok用户(${detail.author.id})返回无效结果，将在${nextDelay}ms后第${retryCount}次重试`,
                  )
                }
              },
            ),
            KolService.getInstance().findOrCreateKol(
              userId,
              detail.author.unique_id,
              detail.author.id,
              KolPlatform.TIKTOK,
            ),
          ])
          const followers = userInfo?.stats?.followerCount || 0
          const metrics = calculateEngagementMetrics({
            views: detail.play_count,
            likes: detail.digg_count,
            comments: detail.comment_count,
            favorites: detail.collect_count,
          })

          if (!detail.region) {
            detail.region = await TiktokService.getInstance().getTikTokUserRegion(
              detail.author.unique_id,
            )
          }

          return {
            videoId: detail.id,
            authorUniqueId: detail.author.unique_id,
            authorUserId: detail.author.id,
            nickName: userInfo?.user?.nickname,
            postType: 'TIKTOK_POST',
            region: detail.region,
            contactInformation:
              userInfo?.user?.bio_email || extractEmail(userInfo?.user?.signature) || '',
            publishDate: new Date(detail.create_time * 1000),
            influencer: `@${detail.author.unique_id}`,
            postLink: `https://www.tiktok.com/@${detail.author.unique_id}/video/${detail.id}`,
            postThumbnailUrl: detail.cover || '',
            ...(detail.author.avatar && { authorAvatar: detail.author.avatar }),
            ...(followers > 0 && { followers }),
            ...(detail.play_count > 0 && { views: detail.play_count }),
            ...(detail.digg_count > 0 && { likes: detail.digg_count }),
            ...(detail.comment_count > 0 && { comments: detail.comment_count }),
            ...(detail.collect_count > 0 && { favorites: detail.collect_count }),
            ...(detail.share_count > 0 && { shares: detail.share_count }),
            ...metrics,
            kolId: kol?.id ?? '',
            platform: KolPlatform.TIKTOK,
          }
        } catch (error) {
          console.error(`处理TikTok视频 ${url} 失败:`, error)
          stats.failed++
          stats.failedVideoUrls.push(url)
          return null
        }
      },
      { concurrency: config.concurrency },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `TikTok第 ${i + 1} 批处理完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      console.log(`等待 ${config.batchDelay}ms 后处理下一批...`)
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

// youtube track
async function processYoutubeBatches(
  userId: string | undefined,
  videoIds: string[],
  shorts: string[],
  urls: string[],
  youtubeApi: YoutubeApi,
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
): Promise<any[]> {
  const batches = chunkArray(videoIds, config.batchSize)
  const results: any[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理YouTube第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (videoId) => {
        try {
          const videoDetail = await retryUtil.retryWithValidator(
            () => youtubeApi.getVideo(videoId),
            (result) => !!result, // 结果不为空或undefined时验证通过
            3,
            1000,
            2,
            (result, error, retryCount, nextDelay) => {
              if (error) {
                console.warn(
                  `YouTube视频(${videoId})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                  error,
                )
              } else if (!result) {
                console.warn(
                  `YouTube视频(${videoId})返回空结果，将在${nextDelay}ms后第${retryCount}次重试`,
                )
              }
            },
          )
          if (!videoDetail) {
            stats.failed++
            stats.failedVideoIds.push(videoId)
            const url = urls.find((url) => url.includes(videoId))
            if (url) {
              stats.failedVideoUrls.push(url)
            }
            return null
          }

          const isShort = shorts?.includes(videoId)
          const channelInfo = await retryUtil.retryWithValidator(
            () => youtubeApi.getChannel(videoDetail.channelId),
            (result) => !!result && !!result.channelId, // 结果不为空且包含channelId时验证通过
            3,
            1000,
            2,
            (result, error, retryCount, nextDelay) => {
              if (error) {
                console.warn(
                  `YouTube频道(${videoDetail.channelId})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                  error,
                )
              } else if (!result || !result.channelId) {
                console.warn(
                  `YouTube频道(${videoDetail.channelId})返回无效结果，将在${nextDelay}ms后第${retryCount}次重试`,
                )
              }
            },
          )
          const kol = await KolService.getInstance().findOrCreateKol(
            userId,
            channelInfo.channelHandle,
            channelInfo.channelId,
            KolPlatform.YOUTUBE,
          )

          const parseCount = (value: any): number => {
            if (typeof value === 'number') return value
            if (typeof value === 'string') return parseInt(value.replace(/,/g, '')) || 0
            return 0
          }

          const views = parseCount(videoDetail.viewCount)
          const likes = parseCount(videoDetail.likeCount)
          const comments = parseCount(videoDetail.commentCount)

          const metrics = calculateEngagementMetrics({
            views,
            likes,
            comments,
            favorites: 0,
          })

          return {
            videoId: videoDetail.videoId,
            authorUniqueId: channelInfo?.channelHandle || videoDetail.channelId,
            authorUserId: videoDetail.channelId,
            nickName: videoDetail.channelTitle,
            postType:
              (videoDetail?.type as YoutubeType) === 'shorts' ? 'YOUTUBE_SHORTS' : 'YOUTUBE_VIDEO',
            region: channelInfo?.country || '',
            contactInformation: extractEmail(channelInfo?.description) || channelInfo?.email || '',
            publishDate: new Date(videoDetail.publishedDate || Date.now()),
            influencer: `${channelInfo?.channelHandle || videoDetail.channelTitle}`,
            postLink: isShort
              ? `https://www.youtube.com/shorts/${videoDetail.videoId}`
              : `https://www.youtube.com/watch?v=${videoDetail.videoId}`,
            postThumbnailUrl:
              `https://i.ytimg.com/vi/${videoDetail.videoId}/hqdefault.jpg` ||
              videoDetail.thumbnail.filter((item) => item.width === 1280 && item.height === 720)[0]
                ?.url ||
              videoDetail.thumbnail[0]?.url,
            ...(channelInfo?.avatar?.[0]?.url && { authorAvatar: channelInfo.avatar[0].url }),
            ...(channelInfo?.subscriberCount && { followers: channelInfo.subscriberCount }),
            ...(views > 0 && { views }),
            ...(likes > 0 && { likes }),
            ...(comments > 0 && { comments }),
            ...metrics,
            kolId: kol?.id ?? '',
            platform: KolPlatform.YOUTUBE,
          }
        } catch (error) {
          console.error(`处理YouTube视频 ${videoId} 失败:`, error)
          stats.failed++
          stats.failedVideoIds.push(videoId)
          const url = urls.find((url) => url.includes(videoId))
          if (url) {
            stats.failedVideoUrls.push(url)
          }
          return null
        }
      },
      { concurrency: config.concurrency },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `YouTube第 ${i + 1} 批处理完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      console.log(`等待 ${config.batchDelay}ms 后处理下一批...`)
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

// ins track
async function processInstagramBatches(
  userId: string | undefined,
  postIds: string[],
  reels: string[],
  urls: string[],
  instagramApi: InstagramRapidApiV3,
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
): Promise<any[]> {
  const batches = chunkArray(postIds, config.batchSize)
  const results: any[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理Instagram第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (postId) => {
        try {
          const postDetail = await retryUtil.retryWithValidator(
            () => instagramApi.getPost(postId),
            (result) => !!result && !!result.id, // 结果不为空且包含id时验证通过
            3,
            1000,
            2,
            (result, error, retryCount, nextDelay) => {
              if (error) {
                console.warn(
                  `Instagram帖子(${postId})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                  error,
                )
              } else if (!result || !result.id) {
                console.warn(
                  `Instagram帖子(${postId})返回无效结果，将在${nextDelay}ms后第${retryCount}次重试`,
                )
              }
            },
          )

          if (!postDetail) {
            stats.failed++
            stats.failedVideoIds.push(postId)
            const url = urls.find((url) => url.includes(postId))
            if (url) {
              stats.failedVideoUrls.push(url)
            }
            return null
          }

          const userInfo = await retryUtil.retryWithValidator(
            () => instagramApi.getUser({ username: postDetail.username || '' }),
            (result) => !!result && !!result.username, // 结果不为空且包含username时验证通过
            3,
            1000,
            2,
            (result, error, retryCount, nextDelay) => {
              if (error) {
                console.warn(
                  `Instagram用户(${postDetail.username})请求失败，将在${nextDelay}ms后第${retryCount}次重试`,
                  error,
                )
              } else if (!result || !result.username) {
                console.warn(
                  `Instagram用户(${postDetail.username})返回无效结果，将在${nextDelay}ms后第${retryCount}次重试`,
                )
              }
            },
          )
          if (!userInfo) {
            console.warn(`无法获取Instagram用户信息: ${postDetail.username}`)
          }

          const kol = await KolService.getInstance().findOrCreateKol(
            userId,
            userInfo?.username || '',
            userInfo?.id || '',
            KolPlatform.INSTAGRAM,
          )

          const isReel = reels?.includes(postId)
          const metrics = calculateEngagementMetrics({
            views: isReel
              ? Number(postDetail.play_count) || 0
              : Number(postDetail.like_count) * 15 || 0,
            likes: Number(postDetail.like_count) || 0,
            comments: Number(postDetail.comment_count) || 0,
            favorites: 0,
          })

          return {
            videoId: postDetail.id,
            authorUniqueId: userInfo?.username || postDetail.username || '',
            authorUserId: userInfo?.id || postDetail.userId || '',
            nickName: userInfo?.fullName || '',
            postType:
              (postDetail?.type as PostType) === 'reel' ? 'INSTAGRAM_REEL' : 'INSTAGRAM_POST',
            region: userInfo?.region || '',
            contactInformation:
              extractEmail(userInfo?.biography) ||
              userInfo?.biographyEmail ||
              userInfo?.email ||
              '',
            publishDate: new Date(Number(postDetail.created_at) * 1000),
            influencer: `@${userInfo?.username || postDetail.username || ''}`,
            postLink: isReel
              ? `https://www.instagram.com/reel/${postDetail.id}`
              : `https://www.instagram.com/p/${postDetail.id}`,
            postThumbnailUrl: postDetail.thumbnail_url || '',
            ...(userInfo?.profilePicUrl && { authorAvatar: userInfo.profilePicUrl }),
            ...(userInfo?.followerCount && { followers: Number(userInfo.followerCount) }),
            ...{
              views:
                postDetail?.type === 'reel'
                  ? Number(postDetail.play_count)
                  : Number(postDetail.like_count) * 15,
            },
            ...(postDetail.like_count > 0 && { likes: Number(postDetail.like_count) }),
            ...(postDetail.comment_count > 0 && { comments: Number(postDetail.comment_count) }),
            ...metrics,
            kolId: kol?.id ?? '',
            platform: KolPlatform.INSTAGRAM,
          }
        } catch (error) {
          console.error(`处理Instagram帖子 ${postId} 失败:`, error)
          stats.failed++
          stats.failedVideoIds.push(postId)
          const url = urls.find((url) => url.includes(postId))
          if (url) {
            stats.failedVideoUrls.push(url)
          }
          return null
        }
      },
      { concurrency: config.concurrency },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `Instagram第 ${i + 1} 批处理完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      console.log(`等待 ${config.batchDelay}ms 后处理下一批...`)
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

// douyin track
async function processDouyinBatches(
  videoIds: string[],
  urls: string[],
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
): Promise<any[]> {
  // 输入数据在updateVideoData中已经被过滤，此处不再需要过滤
  const batches = chunkArray(videoIds, config.batchSize)
  const results: any[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理Douyin第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (videoId) => {
        try {
          const videoDetail = await douyinV1Service.getVideoCoreDetail(videoId)

          if (!videoDetail || videoDetail.id === '') {
            stats.failed++
            stats.failedVideoIds.push(videoId)
            const url = urls.find((url) => url.includes(videoId))
            if (url) {
              stats.failedVideoUrls.push(url)
            }
            return null
          }

          // 计算互动指标
          const douyinMetrics = calculateEngagementMetrics({
            views: videoDetail.plays,
            likes: videoDetail.likes,
            comments: videoDetail.comments,
            favorites: videoDetail.collects || 0,
          })

          return {
            videoId: videoDetail.id,
            authorUniqueId: videoDetail.authorUniqueId,
            authorUserId: videoDetail.authorSecUid || videoDetail.authorId,
            nickName: videoDetail.authorName,
            postType: videoDetail.mediaType == '2' ? 'DOUYIN_NOTE' : 'DOUYIN_VIDEO',
            region: videoDetail.region || '',
            contactInformation: extractEmail(videoDetail.authorSignature) || '',
            publishDate: videoDetail.createTime,
            influencer: videoDetail.authorName,
            postLink:
              videoDetail.mediaType == '2'
                ? douyinV1Service.buildNoteUrls(videoId).webUrl
                : douyinV1Service.buildVideoUrls(videoId).webUrl || '',
            postThumbnailUrl: videoDetail.coverUrl || '',
            authorAvatar: videoDetail.authorAvatar || '',
            followers: videoDetail.authorFollowers || 0,
            views: videoDetail.plays,
            likes: videoDetail.likes,
            comments: videoDetail.comments,
            shares: videoDetail.shares,
            favorites: videoDetail.collects || 0,
            totalEngagement:
              videoDetail.likes +
              videoDetail.comments +
              (videoDetail.shares || 0) +
              (videoDetail.collects || 0),
            engagementRate: douyinMetrics.engagementRate,
            platform: KolPlatform.DOUYIN,
          }
        } catch (error) {
          console.error(`处理抖音视频 ${videoId} 失败:`, error)
          stats.failed++
          stats.failedVideoIds.push(videoId)
          const url = urls.find((url) => url.includes(videoId))
          if (url) {
            stats.failedVideoUrls.push(url)
          }
          return null
        }
      },
      { concurrency: 10 },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `抖音第 ${i + 1} 批处理完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      console.log(`等待 ${config.batchDelay}ms 后处理下一批...`)
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

// xhs track
async function processXhsBatches(
  noteIds: string[],
  urls: string[],
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
): Promise<any[]> {
  const batches = chunkArray(noteIds, config.batchSize)
  const results: any[] = []
  const idUrlMap = new Map<string, string>()
  noteIds.forEach((id, index) => {
    if (urls[index]) {
      idUrlMap.set(id, urls[index])
    }
  })

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理小红书第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (noteId) => {
        try {
          const noteDetail = await xhsV1Service.getNoteCoreDetail(noteId)

          if (!noteDetail || noteDetail.id === '') {
            stats.failed++
            stats.failedVideoIds.push(noteId)
            const url = idUrlMap.get(noteId) || urls.find((url) => url.includes(noteId))
            if (url) {
              stats.failedVideoUrls.push(url)
            }
            return null
          }

          // 计算互动指标
          const xhsMetrics = calculateEngagementMetrics({
            views: noteDetail.views,
            likes: noteDetail.likes,
            comments: noteDetail.comments,
            favorites: noteDetail.collects,
          })

          return {
            videoId: noteDetail.id,
            authorUniqueId: noteDetail.authorRedId || noteDetail.authorId,
            authorUserId: noteDetail.authorId,
            nickName: noteDetail.authorNickname || noteDetail.authorName,
            postType: noteDetail.type == 'note' ? 'XHS_NOTE' : 'XHS_VIDEO',
            region: noteDetail.region || '',
            contactInformation: noteDetail.authorRedId, // 小红书号
            publishDate: noteDetail.createTime,
            influencer: noteDetail.authorNickname || noteDetail.authorName,
            postLink:
              idUrlMap.get(noteId) ||
              urls.find((url) => url.includes(noteId)) ||
              noteDetail.shareInfo.link,
            postThumbnailUrl: noteDetail.shareInfo.image || noteDetail.images[0]?.url,
            authorAvatar: noteDetail.authorAvatar || '',
            followers: noteDetail.authorFollowers || 0,
            views: noteDetail.views,
            likes: noteDetail.likes,
            comments: noteDetail.comments,
            shares: noteDetail.shares,
            favorites: noteDetail.collects,
            totalEngagement:
              noteDetail.likes + noteDetail.comments + noteDetail.shares + noteDetail.collects,
            engagementRate: xhsMetrics.engagementRate,
            platform: KolPlatform.XHS,
          }
        } catch (error) {
          console.error(`处理小红书笔记 ${noteId} 失败:`, error)
          stats.failed++
          stats.failedVideoIds.push(noteId)
          const url = idUrlMap.get(noteId) || urls.find((url) => url.includes(noteId))
          if (url) {
            stats.failedVideoUrls.push(url)
          }
          return null
        }
      },
      { concurrency: 3 },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `小红书第 ${i + 1} 批处理完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      console.log(`等待 ${config.batchDelay}ms 后处理下一批...`)
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

// batch insert data to db
async function processDbBatches(
  data: any[],
  spreadsheetId: string,
  stats: UpdateVideoDataStats,
  config: BatchProcessConfig,
  tagIds?: string[],
): Promise<any[]> {
  const batches = chunkArray(data, config.batchSize)
  const results: any[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`处理数据库更新第 ${i + 1}/${batches.length} 批，共 ${batch.length} 条`)

    const batchResults = await Bluebird.map(
      batch,
      async (item) => {
        try {
          const existingData = await prisma.publicationStatisticsSheetData.findFirst({
            where: { videoId: item.videoId, spreadsheetId },
          })

          let result = null

          if (existingData) {
            stats.updated++
            const updateData = Object.entries(item).reduce(
              (acc, [key, value]) => {
                if (key === 'followers') {
                  if (typeof value === 'number' && value > 0) {
                    acc[key] = { set: value }
                  }
                } else if (key === 'kolId' && !existingData.kolId) {
                  acc[key] = value
                } else if (key === 'platform' && !existingData.platform) {
                  acc[key] = value
                } else if (typeof value === 'number') {
                  if (value > 0) acc[key] = { set: value }
                } else if (typeof value === 'string') {
                  if (value.trim() !== '') acc[key] = { set: value }
                } else if (value instanceof Date) {
                  acc[key] = { set: value }
                } else if (key === 'postThumbnailUrl') {
                  if (typeof value === 'string' && value.trim() !== '') {
                    acc[key] = { set: value }
                  }
                }
                return acc
              },
              {} as Record<string, any>,
            )

            if (!existingData.kolId) {
              updateData['kolId'] = item.kolId
            }
            if (!existingData.platform) {
              updateData['platform'] = item.platform
            }

            result = await prisma.publicationStatisticsSheetData.update({
              where: { id: existingData.id },
              data: updateData,
            })
          } else {
            stats.created++
            result = await prisma.publicationStatisticsSheetData.create({
              data: {
                ...item,
                spreadsheetId,
              },
            })
          }

          if (tagIds && tagIds.length > 0 && result) {
            const existingTags = await prisma.tag.findMany({
              where: {
                id: {
                  in: tagIds,
                },
              },
              select: {
                id: true,
              },
            })

            const existingTagIds = new Set(existingTags.map((tag) => tag.id))
            const validTagIds = tagIds.filter((tagId) => existingTagIds.has(tagId))

            if (validTagIds.length > 0) {
              const existingRelations = await prisma.publicationTag.findMany({
                where: {
                  publicationId: result.id,
                  tagId: {
                    in: validTagIds,
                  },
                },
                select: {
                  tagId: true,
                },
              })

              const existingRelationTagIds = new Set(existingRelations.map((rel) => rel.tagId))

              const tagIdsToCreate = validTagIds.filter((id) => !existingRelationTagIds.has(id))

              if (tagIdsToCreate.length > 0) {
                await prisma.publicationTag.createMany({
                  data: tagIdsToCreate.map((tagId) => ({
                    publicationId: result.id,
                    tagId,
                  })),
                  skipDuplicates: true,
                })
              }
            }
          }

          return result
        } catch (error) {
          console.error(`数据库更新失败:`, error)
          stats.failed++
          return null
        }
      },
      { concurrency: config.concurrency },
    )

    results.push(...batchResults)

    const successCount = batchResults.filter((r) => r !== null).length
    console.log(
      `数据库第 ${i + 1} 批更新完成: 成功 ${successCount}，失败 ${batch.length - successCount}`,
    )

    if (i < batches.length - 1) {
      await new Promise((resolve) => setTimeout(resolve, config.batchDelay))
    }
  }

  return results
}

async function getUserPostLinks(
  userId: string,
  type: 'all' | number,
): Promise<GetPostUrlsResponse> {
  const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
    where: {
      userId,
    },
  })
  if (!userGoogleSheet) {
    return {
      spreadsheetId: '',
      userId: userId,
      links: [],
    }
  }
  const spreadSheetId = userGoogleSheet.spreadsheetId

  const postUrls = await prisma.publicationStatisticsSheetData.findMany({
    where: {
      spreadsheetId: spreadSheetId,
    },
    take: type === 'all' ? undefined : type,
    orderBy: {
      publishDate: 'desc',
    },
  })

  return {
    spreadsheetId: spreadSheetId,
    userId: userId,
    links: postUrls.map((item) => item.postLink || '').filter((item) => item !== ''),
  }
}

async function getUserPostData(
  userId: string,
  pagination: PaginationParams = {},
): Promise<{ data: PublicationStatisticsSheetData[]; total: number }> {
  const { page, pageSize, skip } = PaginationService.handlePagination(pagination)
  const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
    where: { userId },
  })

  if (!userGoogleSheet) return { data: [], total: 0 }

  const [total, userPostData] = await Promise.all([
    prisma.publicationStatisticsSheetData.count({
      where: {
        spreadsheetId: userGoogleSheet.spreadsheetId,
      },
    }),
    prisma.publicationStatisticsSheetData.findMany({
      where: {
        spreadsheetId: userGoogleSheet.spreadsheetId,
      },
      include: {
        tags: true,
      },
      orderBy: {
        publishDate: 'desc',
      },
      skip,
      take: pageSize,
    }),
  ])

  return { data: userPostData, total }
}

async function deleteUserPostData(userId: string, publicationId: string) {
  let result = null
  try {
    await prisma.$transaction(async (tx) => {
      await tx.publicationTag.deleteMany({
        where: {
          publicationId: publicationId,
        },
      })

      result = await tx.publicationStatisticsSheetData.delete({
        where: { id: publicationId },
        include: {
          tags: true,
        },
      })
    })
    return result
  } catch (error) {
    console.error('删除投放视频数据失败:', error)
    throw error
  }
}

async function batchDeleteUserPostData(userId: string, publicationIds: string[]) {
  try {
    const userGoogleSheet = await prisma.userGoogleSheet.findUniqueOrThrow({
      where: { userId },
      select: { spreadsheetId: true },
    })

    Logger.info(`[batchDeleteUserPostData] userGoogleSheet: ${userGoogleSheet.spreadsheetId}`)

    const publications = await prisma.publicationStatisticsSheetData.findMany({
      where: {
        id: { in: publicationIds },
        spreadsheetId: userGoogleSheet.spreadsheetId,
      },
      select: { id: true },
    })

    if (!publications?.length) {
      throw new Error('no publications found with provided ids')
    }

    Logger.info(`[batchDeleteUserPostData] publications: ${publications.length}`)
    const validIds = publications.map((p) => p.id)
    const invalidIds = publicationIds.filter((id) => !validIds.includes(id))

    if (invalidIds.length > 0) {
      throw new Error(`以下ID无效或不属于当前用户: ${invalidIds.join(', ')}`)
    }

    const result = await prisma.$transaction(async (tx) => {
      await tx.publicationTag.deleteMany({
        where: {
          publicationId: { in: validIds },
        },
      })

      const deleteResult = await tx.publicationStatisticsSheetData.deleteMany({
        where: {
          id: { in: validIds },
        },
      })

      return deleteResult
    })

    return {
      deletedCount: result.count,
      deletedIds: validIds,
    }
  } catch (error) {
    console.error('批量删除投放视频数据失败:', error)
    throw error
  }
}

async function trackExsitingUserPostData(userId: string, publicationId: string) {
  const publication = await prisma.publicationStatisticsSheetData.findUnique({
    where: { id: publicationId },
    include: {
      tags: true,
    },
  })
  if (!publication) {
    throw new Error('the publication is not found')
  }
  const tiktokApi = TiktokApi.getInstance()
  const youtubeApi = YoutubeApi.getInstance()
  const instagramApi = InstagramApi.getInstance()

  const { platform, videoId, postLink, authorUniqueId, postThumbnailUrl } = publication

  try {
    let updatedData = null

    switch (platform) {
      case KolPlatform.TIKTOK:
        if (!postLink) throw new Error('TikTok post link is required')
        const tiktokDetail = await tiktokApi.getVideoDetail(postLink)
        if (!tiktokDetail) throw new Error('Failed to fetch TikTok video detail')

        const [tiktokUserInfo, tiktokKol] = await Promise.all([
          tiktokApi.getUserDetail({ user_id: tiktokDetail.author.id }),
          KolService.getInstance().findOrCreateKol(
            userId,
            tiktokDetail.author.unique_id,
            tiktokDetail.author.id,
            KolPlatform.TIKTOK,
          ),
        ])

        const tiktokMetrics = calculateEngagementMetrics({
          views: tiktokDetail.play_count,
          likes: tiktokDetail.digg_count,
          comments: tiktokDetail.comment_count,
          favorites: tiktokDetail.collect_count,
        })

        updatedData = {
          ...(tiktokUserInfo?.stats?.followerCount && {
            followers: { set: tiktokUserInfo.stats.followerCount },
          }),
          ...(tiktokDetail.play_count > 0 && { views: { set: tiktokDetail.play_count } }),
          ...(tiktokDetail.digg_count > 0 && { likes: { set: tiktokDetail.digg_count } }),
          ...(tiktokDetail.comment_count > 0 && { comments: { set: tiktokDetail.comment_count } }),
          ...(tiktokDetail.collect_count > 0 && { favorites: { set: tiktokDetail.collect_count } }),
          ...(tiktokDetail.share_count > 0 && { shares: { set: tiktokDetail.share_count } }),
          ...(tiktokMetrics.totalEngagement > 0 && {
            totalEngagement: { set: tiktokMetrics.totalEngagement },
          }),
          ...(tiktokMetrics.engagementRate > 0 && {
            engagementRate: { set: tiktokMetrics.engagementRate },
          }),
          ...(tiktokKol?.id && { kolId: tiktokKol.id }),
          ...(!postThumbnailUrl && {
            postThumbnailUrl: { set: tiktokDetail.cover },
          }),
        }
        break

      case KolPlatform.YOUTUBE:
        if (!videoId) throw new Error('YouTube video ID is required')
        const youtubeDetail = await youtubeApi.getVideo(videoId)
        if (!youtubeDetail) throw new Error('Failed to fetch YouTube video detail')

        const channelInfo = await youtubeApi.getChannel(youtubeDetail.channelId)
        const youtubeKol = await KolService.getInstance().findOrCreateKol(
          userId,
          channelInfo.channelHandle,
          channelInfo.channelId,
          KolPlatform.YOUTUBE,
        )

        const parseCount = (value: any): number => {
          if (typeof value === 'number') return value
          if (typeof value === 'string') return parseInt(value.replace(/,/g, '')) || 0
          return 0
        }

        const youtubeViews = parseCount(youtubeDetail.viewCount)
        const youtubeLikes = parseCount(youtubeDetail.likeCount)
        const youtubeComments = parseCount(youtubeDetail.commentCount)

        const youtubeMetrics = calculateEngagementMetrics({
          views: youtubeViews,
          likes: youtubeLikes,
          comments: youtubeComments,
          favorites: 0,
        })

        updatedData = {
          ...(channelInfo?.subscriberCount && { followers: { set: channelInfo.subscriberCount } }),
          ...(youtubeViews > 0 && { views: { set: youtubeViews } }),
          ...(youtubeLikes > 0 && { likes: { set: youtubeLikes } }),
          ...(youtubeComments > 0 && { comments: { set: youtubeComments } }),
          ...(youtubeMetrics.totalEngagement > 0 && {
            totalEngagement: { set: youtubeMetrics.totalEngagement },
          }),
          ...(youtubeMetrics.engagementRate > 0 && {
            engagementRate: { set: youtubeMetrics.engagementRate },
          }),
          ...(youtubeKol?.id && { kolId: youtubeKol.id }),
          ...(!postThumbnailUrl && {
            postThumbnailUrl: {
              set:
                `https://i.ytimg.com/vi/${youtubeDetail.videoId}/hqdefault.jpg` ||
                youtubeDetail.thumbnail.filter(
                  (item) => item.width === 1280 && item.height === 720,
                )[0]?.url ||
                youtubeDetail.thumbnail[0]?.url,
            },
          }),
        }
        break

      case KolPlatform.INSTAGRAM:
        if (!videoId) throw new Error('Instagram post ID is required')
        const instagramDetail = await instagramApi.getPost(videoId)
        if (!instagramDetail) throw new Error('Failed to fetch Instagram post detail')

        const instagramUserInfo = await instagramApi.getUser(instagramDetail.username || '')
        const instagramKol = await KolService.getInstance().findOrCreateKol(
          userId,
          instagramUserInfo?.username || '',
          instagramUserInfo?.id || '',
          KolPlatform.INSTAGRAM,
        )

        const isReel = publication.postType === 'INSTAGRAM_REEL'
        const instagramMetrics = calculateEngagementMetrics({
          views: isReel ? instagramDetail.play_count || 0 : instagramDetail.like_count * 15 || 0,
          likes: instagramDetail.like_count || 0,
          comments: instagramDetail.comment_count || 0,
          favorites: 0,
        })

        updatedData = {
          ...(instagramUserInfo?.followerCount && {
            followers: { set: instagramUserInfo.followerCount },
          }),
          ...{
            views: {
              set: isReel ? instagramDetail.play_count || 0 : instagramDetail.like_count * 15 || 0,
            },
          },
          ...(instagramDetail.like_count > 0 && { likes: { set: instagramDetail.like_count } }),
          ...(instagramDetail.comment_count > 0 && {
            comments: { set: instagramDetail.comment_count },
          }),
          ...(instagramMetrics.totalEngagement > 0 && {
            totalEngagement: { set: instagramMetrics.totalEngagement },
          }),
          ...(instagramMetrics.engagementRate > 0 && {
            engagementRate: { set: instagramMetrics.engagementRate },
          }),
          ...(instagramKol?.id && { kolId: instagramKol.id }),
          postThumbnailUrl: { set: instagramDetail.thumbnail_url || '' },
        }
        break
      case KolPlatform.DOUYIN:
        assert(videoId, new Error('Douyin video ID is required'))
        let douyinDetail = null
        try {
          douyinDetail = await douyinV1Service.getVideoCoreDetail(videoId)
        } catch (error) {
          console.error(`获取抖音视频数据失败:`, error)
          throw error
        }

        updatedData = {
          ...(douyinDetail.authorFollowers && {
            followers: { set: douyinDetail.authorFollowers },
          }),
          ...(douyinDetail.plays > 0 && { views: { set: douyinDetail.plays } }),
          ...(douyinDetail.likes > 0 && { likes: { set: douyinDetail.likes } }),
          ...(douyinDetail.comments > 0 && { comments: { set: douyinDetail.comments } }),
          ...(douyinDetail.collects &&
            douyinDetail.collects > 0 && {
              favorites: { set: douyinDetail.collects },
            }),
          ...(douyinDetail.shares > 0 && { shares: { set: douyinDetail.shares } }),
          ...(!postThumbnailUrl && {
            postThumbnailUrl: { set: douyinDetail.coverUrl || '' },
          }),
          updatedAt: new Date(),
        }
        break
      case KolPlatform.XHS:
        assert(videoId, new Error('XHS video ID is required'))
        let xhsDetail = null
        try {
          xhsDetail = await xhsV1Service.getNoteCoreDetail(videoId)
        } catch (error) {
          console.error(`获取小红书视频数据失败:`, error)
          throw error
        }

        updatedData = {
          ...(xhsDetail.views > 0 && { views: { set: xhsDetail.views } }),
          ...(xhsDetail.likes > 0 && { likes: { set: xhsDetail.likes } }),
          ...(xhsDetail.comments > 0 && { comments: { set: xhsDetail.comments } }),
          ...(xhsDetail.collects &&
            xhsDetail.collects > 0 && {
              favorites: { set: xhsDetail.collects },
            }),
          ...(xhsDetail.shares > 0 && { shares: { set: xhsDetail.shares } }),
          ...(!postThumbnailUrl && {
            postThumbnailUrl: { set: xhsDetail.shareInfo.image || xhsDetail.images[0]?.url || '' },
          }),
          updatedAt: new Date(),
        }
        break
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }

    if (updatedData) {
      const result = await prisma.publicationStatisticsSheetData.update({
        where: { id: publicationId },
        data: {
          ...updatedData,
          updatedAt: new Date(),
        },
      })
      return result
    }

    throw new Error('No data to update')
  } catch (error) {
    console.error(`更新 ${platform} 数据失败:`, error)
    throw error
  }
}

async function updatePublicationData(
  userId: string,
  publicationId: string,
  data: UpdatePublicationDataParams,
) {
  const result = await prisma.publicationStatisticsSheetData.update({
    where: { id: publicationId },
    data,
  })
  return result
}

async function updatePublicationRecentData(
  userId: string,
  pagination: PaginationParams,
): Promise<UpdateVideoDataResult> {
  const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
    where: { userId },
  })

  if (!userGoogleSheet) {
    throw new Error('the userGoogleSheet is not found')
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

  const result = await prisma.publicationStatisticsSheetData.findMany({
    where: { spreadsheetId: userGoogleSheet.spreadsheetId },
    orderBy: { publishDate: 'desc' },
    skip,
    take: pageSize,
  })

  if (result.length === 0) {
    return {
      data: [],
      stats: {
        total: 0,
        success: 0,
        failed: 0,
        created: 0,
        updated: 0,
        failedVideoIds: [],
        failedVideoUrls: [],
      },
    }
  }
  const postRequestParams: UpdateVideoDataRequestParams = {
    spreadsheetId: userGoogleSheet.spreadsheetId,
    tiktok: {
      videosIds: result
        .filter((item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST')
        .map((item) => item.videoId)
        .filter((item) => item !== null) as string[],
      urls: result
        .filter((item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST')
        .map((item) => item.postLink)
        .filter((item) => item !== null) as string[],
    },
    youtube: {
      videosIds: result
        .filter(
          (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
        )
        .map((item) => item.videoId)
        .filter((item) => item !== null) as string[],
      shortsIds: result
        .filter(
          (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
        )
        .map((item) => item.videoId)
        .filter((item) => item !== null) as string[],
      urls: result
        .filter((item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_POST')
        .map((item) => item.postLink)
        .filter((item) => item !== null) as string[],
    },
    instagram: {
      reelsIds: result
        .filter(
          (item) => item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_REEL',
        )
        .map((item) => item.videoId)
        .filter((item) => item !== null) as string[],
      postsIds: result
        .filter(
          (item) => item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_POST',
        )
        .map((item) => item.videoId)
        .filter((item) => item !== null) as string[],
      urls: result
        .filter((item) => item.platform === KolPlatform.INSTAGRAM)
        .map((item) => item.postLink)
        .filter((item) => item !== null) as string[],
    },
  }

  const updateVideoDataResult = await updateVideoData(userId, postRequestParams)
  return updateVideoDataResult
}

async function getPublicationImage(publicationId: string) {
  const publication = await prisma.publicationStatisticsSheetData.findUnique({
    where: { id: publicationId },
    select: { postThumbnailUrl: true },
  })

  if (!publication || !publication.postThumbnailUrl) {
    throw new Error('images not found')
  }

  return { imageUrl: publication.postThumbnailUrl }
}

export const publicationService = {
  getUserPostData,
  updateVideoData,
  getUserPostLinks,
  calculateEngagementMetrics,
  deleteUserPostData,
  batchDeleteUserPostData,
  trackExsitingUserPostData,
  updatePublicationData,
  updatePublicationRecentData,
  getPublicationImage,
}
