import { prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'
import { clearList, getStatistic, processContent, processLinks, uploadList } from './excludeList'

const tiktokContent =
  'https://www.tiktok.com/@hancute.04\n \
https://www.tiktok.com/@undauntingexploration/video/7379159572117818631'
const youtubeContent =
  'https://www.youtube.com/channel/UCx1m6AboILQKMNXbsLjjI4Q/videos\n \
        https://www.youtube.com/watch?v=t4SFb29JEzY'
const instagramContent =
  'https://www.instagram.com/kkiez.kollektiv\n \
        https://www.instagram.com/reel/DFHxi7CS5Z4/?utm_source=ig_web_copy_link&igsh=MzRlODBiNWFlZA=='
const unsupportContent =
  '\n \
          https://whatsapp.com/'

describe('should test about procedure about exclude list', () => {
  it('should add some links for user', async () => {
    const fullContent = tiktokContent + '\n'
    // + youtubeContent + '\n'
    // + instagramContent + '\n'
    //  + unsupportContent
    const user = await prisma.userInfo.findFirstOrThrow({
      where: {
        email: '<EMAIL>',
      },
    })
    expect(user?.userId).toBeDefined()
    const results = await uploadList(fullContent, user.userId)
    console.log(results)
  })

  it('should get count for user', async () => {
    const user = await prisma.userInfo.findFirstOrThrow({
      where: {
        email: '<EMAIL>',
      },
    })
    expect(user?.userId).toBeDefined()
    const result = await getStatistic(user.userId)
    console.log(result.count)
  })

  it('should clear the list for user', async () => {
    const user = await prisma.userInfo.findFirstOrThrow({
      where: {
        email: '<EMAIL>',
      },
    })
    expect(user?.userId).toBeDefined()
    const result = await clearList(user!.userId)
    console.log(result)
  })
})

describe('should test all about exclude list feature', () => {
  describe('should test about extract links', () => {
    it('should import all tiktok links', async () => {
      const links = processContent(tiktokContent)
      const results = await processLinks(links)
      console.log(results)
    })

    it('should import all youtube links', async () => {
      const links = processContent(youtubeContent)
      const results = await processLinks(links)
      console.log(results)
    })

    it('should import all instagram links', async () => {
      const links = processContent(instagramContent)
      const results = await processLinks(links)
      console.log(results)
    })

    it('should failed for all unsupported links', async () => {
      const links = processContent(unsupportContent)
      const results = await processLinks(links)
      console.log(results)
    })
  })

  describe('should test about list expansion', () => {
    const allRecords = [...tiktokList, ...youtubeList, ...insList, ...unknownList]
    allRecords.forEach((v, idx) => {
      v.lineNumber = idx + 1
    })
  })
})

const tiktokList = [
  {
    content: 'https://www.tiktok.com/@hancute.04',
    lineNumber: 1,
    linkType: 'tiktok',
    platform: 'TIKTOK',
    platformAccount: 'hancute.04',
    kolId: 'cm6bvk6am0001mcaljnboejwg',
  },
  {
    content: '         https://www.tiktok.com/@undauntingexploration/video/7379159572117818631',
    lineNumber: 2,
    linkType: 'tiktok_video',
    postId: '7379159572117818631',
    platform: 'TIKTOK',
    platformAccount: 'undauntingexploration',
    kolId: 'cm6bvjmcr0001zxpdd7r6hy7b',
  },
]
const youtubeList = [
  {
    content: 'https://www.youtube.com/channel/UCx1m6AboILQKMNXbsLjjI4Q/videos',
    lineNumber: 1,
    linkType: 'youtube',
    platform: 'YOUTUBE',
    platformAccount: 'UCx1m6AboILQKMNXbsLjjI4Q',
    kolId: 'cm6byxphv000143aov0v0yte2',
  },
  {
    content: 'https://www.youtube.com/watch?v=t4SFb29JEzY',
    lineNumber: 2,
    linkType: 'youtube_video',
    postId: 't4SFb29JEzY',
    platform: 'YOUTUBE',
    platformAccount: 'UCzr30osBdTmuFUS8IfXtXmg',
    kolId: 'cm6byxpnm000343aoj4gfnd2r',
  },
]

const insList = [
  {
    content: 'https://www.instagram.com/kkiez.kollektiv',
    lineNumber: 1,
    linkType: 'instagram',
    platform: 'INSTAGRAM',
    platformAccount: 'kkiez.kollektiv',
    kolId: 'cm6brqgqb000w111kre19eixo',
  },
  {
    content:
      'https://www.instagram.com/reel/DFHxi7CS5Z4/?utm_source=ig_web_copy_link&igsh=MzRlODBiNWFlZA==',
    lineNumber: 2,
    linkType: 'instagram_reel',
    postId: 'DFHxi7CS5Z4',
    platform: 'INSTAGRAM',
    platformAccount: 'illustrator.blog_artist',
    kolId: 'cm6brqgqa0005111ksfhfpp1v',
  },
]

const unknownList = [
  {
    content: '',
    lineNumber: 1,
    linkType: 'unknown',
    platform: undefined,
    platformAccount: undefined,
    postId: undefined,
    result: 'failed',
    message: 'unknown link',
  },
  {
    content: 'https://whatsapp.com/',
    lineNumber: 2,
    linkType: 'unknown',
    platform: undefined,
    platformAccount: undefined,
    postId: undefined,
    result: 'failed',
    message: 'unknown link',
  },
]
