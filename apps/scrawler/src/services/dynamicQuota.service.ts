// 动态计算配额
import { QuotaCost } from '@/enums/QuotaCost'
import { InsMode } from '@/enums/TaskMode'
import { SimilarTaskRequest } from '@/types/request/similar.request'
import { KolPlatform } from '@repo/database'
import { FastifyRequest } from 'fastify'

function calculateSimilarSearchQuota(req: FastifyRequest): number {
  const { platform, taskRound, insMode } = req.body as SimilarTaskRequest
  let quota = QuotaCost.SIMILAR_SEARCH
  if (platform === KolPlatform.INSTAGRAM) {
    if (insMode === InsMode.AI_VISUAL) {
      quota = QuotaCost.SIMILAR_SEARCH * Number(taskRound || 1)
    }
  }
  return quota
}

/**
 * 计算 hashTag 搜索配额,按照用户需要的视频数量扣费
 * @param req
 * @returns
 */
function calculateHashTagSearchQuota(req: FastifyRequest): number {
  let { maxVideoCount } = req.body as any
  if (!maxVideoCount) {
    maxVideoCount = 100
    // @ts-ignore
    req.body.maxVideoCount = maxVideoCount
  }
  return Math.ceil(maxVideoCount / 10)
}

function calculateInputSearchQuota(req: FastifyRequest): number {
  let { maxVideoCount } = req.body as any
  if (!maxVideoCount) {
    maxVideoCount = 100
    // @ts-ignore
    req.body.maxVideoCount = maxVideoCount
  }
  return Math.ceil(maxVideoCount / 10)
}

function calculateFollowingBreakQuota(req: FastifyRequest): number {
  let { maxCount } = req.body as any
  if (!maxCount) {
    maxCount = 100
    // @ts-ignore
    req.body.maxCount = maxCount
  }
  return Math.ceil(maxCount / 10)
}

function calculateTaggedBreakQuota(req: FastifyRequest): number {
  let { maxVideoCount } = req.body as any
  if (!maxVideoCount) {
    maxVideoCount = 100
    // @ts-ignore
    req.body.maxVideoCount = maxVideoCount
  }
  return Math.ceil(maxVideoCount / 10)
}

// 用户理解的次数，后台检验配额*10s
function calculateLongCrawlerQuota(req: FastifyRequest): number {
  let { numberOfRuns } = req.body as any

  if (!numberOfRuns) {
    numberOfRuns = 10
    // @ts-ignore
    req.body.numberOfRuns = numberOfRuns
  }
  return numberOfRuns * 10
}

// 计算增加的长爬虫配额
function calculateAddLongCrawlerQuota(req: FastifyRequest): number {
  const { params } = req.body as any
  const { addNumberOfRuns } = params || {}
  if (!addNumberOfRuns) {
    return 0
  }
  return addNumberOfRuns * 10
}

function calculatePublicationStatisticsUpdateBatch(req: FastifyRequest): number {
  const { publicationIds } = req.body as any
  if (!publicationIds) {
    return 0
  }

  return new Set(publicationIds).size
}

export const DynamicQuotaService = {
  calculateSimilarSearchQuota,
  calculateHashTagSearchQuota,
  calculateInputSearchQuota,
  calculateFollowingBreakQuota,
  calculateTaggedBreakQuota,
  calculateLongCrawlerQuota,
  calculateAddLongCrawlerQuota,
  calculatePublicationStatisticsUpdateBatch,
}
