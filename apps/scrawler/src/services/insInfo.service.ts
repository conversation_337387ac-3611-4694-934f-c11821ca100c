import { IgLike, IgUser } from '@/api/@types/rapidapi/Instagram'
import { downloadJSON } from '@/api/aliyun'
import Sentry from '@/infras/sentry'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { getUserPortraitAnalysis } from '@/services/aiTools/userPortaitAnslysisWithoutReason'
import {
  AudienceAnalysisResult,
  UserPortraitLLMInput,
  UserPortraitLLMOutput,
  UserPortraitResult,
} from '@/types/audience'
import { statisticsUserRegion } from '@/utils/country'
import { KolPlatform, prisma } from '@repo/database'
import * as turf from '@turf/turf'
import Bluebird from 'bluebird'
import { Feature, Point } from 'geojson'

/**
 * 获取Instagram用户评论者的用户信息
 * @param usernameOrId Instagram用户名或ID
 * @returns 评论者的用户对象数组
 */
async function getCommentUsers(usernameOrId: string): Promise<IgUser[]> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const MAX_COMMENTS = 200
    const CONCURRENCY = 50
    const MAX_CONTENTS = 10
    const MAX_PAGES_PER_CONTENT = 5

    // 1. 获取用户的posts和reels
    console.log(`[ins] 获取用户 ${usernameOrId} 的posts和reels`)
    const [postsResponse, reelsResponse] = await Promise.all([
      instagramApi.getUserPosts({ username: usernameOrId }),
      instagramApi.getReels({ username: usernameOrId }),
    ])
    const posts = postsResponse.posts || []
    const reels = reelsResponse.reels || []

    const allContents = [
      ...posts.map((post) => ({
        id: post.id,
        code: post.code,
        created_at: post.created_at,
        type: 'post',
        comment_count: post.comment_count,
        is_pinned: post.is_pinned,
      })),
      ...reels.map((reel) => ({
        id: reel.id,
        code: reel.code,
        created_at: reel.created_at,
        type: 'reel',
        comment_count: reel.comment_count,
        is_pinned: reel.is_pinned,
      })),
    ]
      .filter((content) => !content.is_pinned) // 过滤掉置顶的帖子
      .sort((a, b) => b.created_at - a.created_at)
      .slice(0, MAX_CONTENTS)

    //判断总评论数是否大于MAX_COMMENTS
    const totalCommentCount = allContents.reduce((sum, content) => sum + content.comment_count, 0)
    console.log('totalCommentCount', totalCommentCount)
    if (totalCommentCount < MAX_COMMENTS * 2) {
      throw new Error(`[ins] 评论数小于${MAX_COMMENTS * 2}，无法进行评论者分析`)
    }
    console.log(`[ins] 将处理 ${allContents.length} 个最新内容的评论`)

    const commentUserIdSet = new Set<string>()
    const commentUserMap = new Map<string, IgUser>()

    async function processContentComments(content: {
      id: string
      code: string
      type: string
    }): Promise<void> {
      if (commentUserIdSet.size >= MAX_COMMENTS) {
        return
      }

      let paginationToken: string | undefined
      let pageCount = 0

      while (pageCount < MAX_PAGES_PER_CONTENT && commentUserIdSet.size < MAX_COMMENTS) {
        try {
          const comments = await instagramApi.getPostComments(content.code, {
            pagination_token: paginationToken,
          })
          const newUserIds: string[] = []
          comments.comments.forEach((comment) => {
            if (
              comment.user_id &&
              !commentUserIdSet.has(comment.user_id) &&
              comment.is_private === false
            ) {
              commentUserIdSet.add(comment.user_id)
              newUserIds.push(comment.user_id)
            }
          })
          if (newUserIds.length > 0) {
            const batchSize = 2
            for (let i = 0; i < newUserIds.length; i += batchSize) {
              const batch = newUserIds.slice(i, i + batchSize)

              const userResults = await Promise.allSettled(
                batch.map(async (userId) => {
                  try {
                    return { userId, user: await instagramApi.getUser({ id: userId }) }
                  } catch (error) {
                    return { userId, error }
                  }
                }),
              )

              userResults.forEach((result) => {
                if (result.status === 'fulfilled' && result.value.user) {
                  commentUserMap.set(result.value.userId, result.value.user)
                }
              })

              if (commentUserMap.size >= MAX_COMMENTS) break
            }
          }

          if (comments.pagination_token && pageCount < MAX_PAGES_PER_CONTENT - 1) {
            console.log(
              `[ins] ${content.type} ${content.code} 存在下一页，pagination_token: ${comments.pagination_token.substring(0, 10)}...`,
            )
            paginationToken = comments.pagination_token
            pageCount++
          } else {
            console.log(`[ins] ${content.type} ${content.code} 没有更多页或达到页数限制`)
            break
          }

          if (pageCount % 2 === 0) {
            console.log(
              `[ins] ${content.type} ${content.code} 已处理 ${pageCount} 页评论，当前已收集 ${commentUserMap.size} 个用户`,
            )
          }
        } catch (error) {
          console.error(`[ins] 获取${content.type} ${content.code} 评论失败:`, error)
          break
        }
      }
    }
    console.log(`[ins] 开始并行处理 ${allContents.length} 个内容的评论，并发数: ${CONCURRENCY}`)
    await Bluebird.map(allContents, processContentComments, { concurrency: CONCURRENCY })
    const commentUsers = Array.from(commentUserMap.values())
    console.log(`[ins] 成功获取 ${commentUsers.length} 个评论者的用户信息`)

    return commentUsers
  } catch (error) {
    console.error('[ins getCommentUsers] 获取评论用户数据失败:', error)
    throw error
  }
}

/**
 * 获取Instagram用户的粉丝列表(包含详细信息)
 * @param usernameOrId Instagram用户名或ID
 * @returns 粉丝用户对象数组
 */
async function getFollowers(usernameOrId: string): Promise<IgUser[]> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const MAX_PAGES = 20
    const MAX_PUBLIC_USERS = 600
    const CONCURRENCY = 50

    console.log(`[ins] 开始获取用户 ${usernameOrId} 的粉丝列表`)

    const publicFollowerIds: string[] = []
    let paginationToken: string | undefined = undefined
    let pageCount = 0

    while (pageCount < MAX_PAGES) {
      console.log(`[ins] 获取用户 ${usernameOrId} 的粉丝列表第 ${pageCount + 1} 页数据`)

      const followersResponse = await instagramApi.getFollowers(
        { username: usernameOrId },
        {
          pagination_token: paginationToken,
        },
      )

      // 过滤掉私密账号
      const publicFollowers = followersResponse.followers.filter((follower) => !follower.is_private)

      console.log(
        `[ins] 第 ${pageCount + 1} 页共 ${followersResponse.followers.length} 个粉丝，其中非私密账号 ${publicFollowers.length} 个`,
      )

      publicFollowers.forEach((follower) => {
        publicFollowerIds.push(follower.id)
      })

      if (publicFollowerIds.length >= MAX_PUBLIC_USERS) {
        console.log(`[ins] 已达到最大非私密用户数量 ${MAX_PUBLIC_USERS}，停止获取更多数据`)
        publicFollowerIds.splice(MAX_PUBLIC_USERS)
        break
      }

      paginationToken = followersResponse.pagination_token
      if (!paginationToken) {
        console.log(`[ins] 粉丝列表没有更多页，共获取 ${pageCount + 1} 页`)
        break
      }

      pageCount++
    }

    console.log(`[ins] 共获取 ${publicFollowerIds.length} 个非私密粉丝账号，开始获取详细信息`)

    // 使用Bluebird并发获取用户详细信息
    const followerUsers = await Bluebird.map(
      publicFollowerIds,
      async (userId) => {
        try {
          console.log(`[ins] 正在获取粉丝 ${userId} 的详细信息`)
          const user = await instagramApi.getUser({ id: userId })
          return user
        } catch (error) {
          console.error(`[ins] 获取粉丝 ${userId} 详细信息失败:`, error)
          return null
        }
      },
      { concurrency: CONCURRENCY },
    )

    const validFollowerUsers = followerUsers.filter((user): user is IgUser => user !== null)

    console.log(`[ins] 成功获取 ${validFollowerUsers.length} 个粉丝用户的详细信息`)

    return validFollowerUsers
  } catch (error) {
    console.error('[ins] 获取Instagram粉丝失败:', error)
    Sentry.captureException(error)
    return []
  }
}

/**
 * 获取Instagram用户帖子的点赞用户列表
 * @param usernameOrId Instagram用户名或ID
 * @returns 顺序获取点赞用户详细信息列表
 */
async function getLikesBySequential(usernameOrId: string): Promise<IgUser[]> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const MAX_CONTENTS = 10
    const MAX_LIKES = 100
    const CONCURRENCY = 10

    // 1. 获取用户的posts和reels
    console.log(`[ins] 获取用户 ${usernameOrId} 的posts和reels`)
    const [postsResponse, reelsResponse] = await Promise.all([
      instagramApi.getUserPosts({ username: usernameOrId }),
      instagramApi.getReels({ username: usernameOrId }),
    ])
    const posts = postsResponse.posts || []
    const reels = reelsResponse.reels || []

    const allContents = [
      ...posts.map((post) => ({
        id: post.id,
        code: post.code,
        created_at: post.created_at,
        type: 'post',
        like_count: post.like_count,
        is_pinned: post.is_pinned,
      })),
      ...reels.map((reel) => ({
        id: reel.id,
        code: reel.code,
        created_at: reel.created_at,
        type: 'reel',
        like_count: reel.like_count,
        is_pinned: reel.is_pinned,
      })),
    ]
      .filter((content) => !content.is_pinned) // 过滤掉置顶的帖子
      .sort((a, b) => b.created_at - a.created_at)
      .slice(0, MAX_CONTENTS)

    console.log(`[ins] 将处理 ${allContents.length} 个最新内容的点赞`)

    const likeUsernameSet = new Set<string>()

    for (const content of allContents) {
      if (likeUsernameSet.size >= MAX_LIKES) {
        console.log(`[ins] 已达到目标点赞用户数量 ${MAX_LIKES}，停止获取更多数据`)
        break
      }

      try {
        console.log(`[ins] 正在处理 ${content.type} ${content.code} 的点赞`)
        const likes = await instagramApi.getLikes(content.code)

        for (const like of likes.likes) {
          if (!likeUsernameSet.has(like.username) && !like.is_private) {
            likeUsernameSet.add(like.username)

            if (likeUsernameSet.size >= MAX_LIKES) {
              console.log(`[ins] 已达到目标点赞用户数量 ${MAX_LIKES}，停止获取更多数据`)
              break
            }
          }
        }

        console.log(
          `[ins] ${content.type} ${content.code} 处理完成，当前已收集 ${likeUsernameSet.size} 个用户名`,
        )
      } catch (error) {
        console.error(`[ins] 获取${content.type} ${content.code} 点赞失败:`, error)
        continue
      }
    }

    console.log(`[ins] 开始获取 ${likeUsernameSet.size} 个点赞用户的详细信息`)

    const likeUsers = await Bluebird.map(
      Array.from(likeUsernameSet),
      async (username) => {
        try {
          console.log(`[ins] 正在获取点赞用户 ${username} 的详细信息`)
          const user = await instagramApi.getUser({ username })
          return user
        } catch (error) {
          console.error(`[ins] 获取点赞用户 ${username} 详细信息失败:`, error)
          return null
        }
      },
      { concurrency: CONCURRENCY },
    )

    const validLikeUsers = likeUsers.filter((user): user is IgUser => user !== null)
    console.log(`[ins] 成功获取 ${validLikeUsers.length} 个点赞用户的详细信息`)

    return validLikeUsers
  } catch (error) {
    console.error('[ins getLikes] 获取点赞用户数据失败:', error)
    throw error
  }
}

/**
 * 获取Instagram用户帖子的点赞用户列表
 * @param usernameOrId Instagram用户名或ID
 * @returns 并发获取点赞用户详细信息列表
 */
async function getLikesByConcurrent(usernameOrId: string): Promise<IgUser[]> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const MAX_CONTENTS = 10
    const FINAL_USERS = 300
    const CONCURRENCY = 10

    // 1. 获取用户的posts和reels
    console.log(`[ins] 获取用户 ${usernameOrId} 的posts和reels`)
    const [postsResponse, reelsResponse] = await Promise.all([
      instagramApi.getUserPosts({ username: usernameOrId }),
      instagramApi.getReels({ username: usernameOrId }),
    ])
    const posts = postsResponse.posts || []
    const reels = reelsResponse.reels || []

    const allContents = [
      ...posts.map((post) => ({
        id: post.id,
        code: post.code,
        created_at: post.created_at,
        type: 'post',
        like_count: post.like_count,
        is_pinned: post.is_pinned,
      })),
      ...reels.map((reel) => ({
        id: reel.id,
        code: reel.code,
        created_at: reel.created_at,
        type: 'reel',
        like_count: reel.like_count,
        is_pinned: reel.is_pinned,
      })),
    ]
      .filter((content) => !content.is_pinned)
      .sort((a, b) => b.created_at - a.created_at)
      .slice(0, MAX_CONTENTS)

    console.log(`[ins] 将并发处理 ${allContents.length} 个最新内容的点赞`)

    // 并发获取所有内容的点赞
    const likeUsernameSet = new Set<string>()
    await Bluebird.map(
      allContents,
      async (content) => {
        try {
          console.log(`[ins] 正在处理 ${content.type} ${content.code} 的点赞`)
          const likes = await instagramApi.getLikes(content.code)

          for (const like of likes.likes) {
            if (!likeUsernameSet.has(like.username) && !like.is_private) {
              likeUsernameSet.add(like.username)
            }
          }

          console.log(
            `[ins] ${content.type} ${content.code} 处理完成，当前已收集 ${likeUsernameSet.size} 个用户名`,
          )
        } catch (error) {
          console.error(`[ins] 获取${content.type} ${content.code} 点赞失败:`, error)
        }
      },
      { concurrency: CONCURRENCY },
    )

    // 随机选择300个用户
    const allUsernames = Array.from(likeUsernameSet)
    const selectedUsernames = allUsernames.sort(() => Math.random() - 0.5).slice(0, FINAL_USERS)

    console.log(
      `[ins] 从 ${likeUsernameSet.size} 个用户中随机选择 ${FINAL_USERS} 个用户获取详细信息`,
    )

    // 获取选中用户的详细信息
    const likeUsers = await Bluebird.map(
      selectedUsernames,
      async (username) => {
        try {
          console.log(`[ins] 正在获取点赞用户 ${username} 的详细信息`)
          const user = await instagramApi.getUser({ username })
          return user
        } catch (error) {
          console.error(`[ins] 获取点赞用户 ${username} 详细信息失败:`, error)
          return null
        }
      },
      { concurrency: CONCURRENCY },
    )

    const validLikeUsers = likeUsers.filter((user): user is IgUser => user !== null)
    console.log(`[ins] 成功获取 ${validLikeUsers.length} 个点赞用户的详细信息`)

    return validLikeUsers
  } catch (error) {
    console.error('[ins getLikes] 获取点赞用户数据失败:', error)
    throw error
  }
}

/**
 * 随机获取600个点赞用户
 * @param usernameOrId
 * @returns 返回完整的IgLike对象和postId
 */
async function getLikesUserNameByRandom(
  usernameOrId: string,
): Promise<Array<IgLike & { postId: string }>> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const MAX_CONTENTS = 10 // 减少处理的帖子数量
    const FINAL_USERS = 600
    const CONCURRENCY = 10 // 减少并发数以避免API限制
    const TARGET_USERNAME_COUNT = 900

    console.log(`[ins] 获取用户 ${usernameOrId} 的posts`)
    const postsResponse = await instagramApi.getUserPosts({ username: usernameOrId })
    const posts = postsResponse.posts || []

    const allContents = posts
      .filter((post) => !post.is_pinned)
      .sort((a, b) => b.like_count - a.like_count)
      .slice(0, MAX_CONTENTS)
      .map((post) => ({
        id: post.id,
        code: post.code,
        created_at: post.created_at,
        type: 'post',
        like_count: post.like_count,
        is_pinned: post.is_pinned,
      }))

    console.log(`[ins] 将并发处理 ${allContents.length} 个最新内容的点赞`)

    const likesMap = new Map<string, IgLike & { postId: string }>()
    let hasEnoughUsernames = false

    // 处理点赞的Promise
    await Bluebird.map(
      allContents,
      async (content) => {
        if (hasEnoughUsernames) return

        try {
          console.log(`[ins] 正在处理 ${content.type} ${content.code} 的点赞`)

          const likes = await instagramApi.getLikes(content.code)
          for (const like of likes.likes.slice(0, 500)) {
            if (hasEnoughUsernames) break
            if (!likesMap.has(like.username) && !like.is_private) {
              likesMap.set(like.username, { ...like, postId: content.code })
              if (likesMap.size >= TARGET_USERNAME_COUNT) {
                hasEnoughUsernames = true
                console.log(`[ins] 已收集到 ${TARGET_USERNAME_COUNT} 个用户，停止获取更多数据`)
                break
              }
            }
          }

          console.log(
            `[ins] ${content.type} ${content.code} 处理完成，当前已收集 ${likesMap.size} 个用户`,
          )
        } catch (error) {
          console.error(`[ins] 获取${content.type} ${content.code} 点赞失败:`, error)
        }
      },
      { concurrency: CONCURRENCY },
    )

    const allLikes = Array.from(likesMap.values())
    const selectedLikes = allLikes.sort(() => Math.random() - 0.5).slice(0, FINAL_USERS)

    console.log(`[ins] 从 ${likesMap.size} 个用户中随机选择 ${FINAL_USERS} 个用户获取详细信息`)

    return selectedLikes
  } catch (error) {
    console.error('[ins getLikesAndUserCountry] 获取点赞用户数据失败:', error)
    return [] // 如果连用户名都没有收集到，返回空数组
  }
}

// 加载世界地图数据并查找国家代码
let worldGeoJSONCache: any = null

export async function loadWorldGeoJSON(): Promise<any> {
  try {
    try {
      console.log('[ins] 开始从OSS加载世界地图GeoJSON数据')
      const ossData = await downloadJSON('json/countries.geojson')
      worldGeoJSONCache = ossData
      console.log('[ins] OSS加载成功')
      return ossData
    } catch (error) {
      console.error('[ins] OSS加载失败:', error)
      console.log('[ins] 开始从GitHub加载世界地图GeoJSON数据')
      const response = await fetch(
        'https://raw.githubusercontent.com/datasets/geo-countries/master/data/countries.geojson',
      )
      const githubData = await response.json()
      worldGeoJSONCache = githubData
      console.log('[ins] GitHub加载成功')
      return githubData
    }
  } catch (error) {
    console.error('[ins] 加载世界地图数据完全失败:', error)
    throw new Error('无法加载世界地图数据，请检查网络连接和文件权限')
  }
}

export function findCountryCodeSync(point: Feature<Point>, worldGeoJSON: any): string | null {
  try {
    const coordinates = point.geometry.coordinates
    if (!coordinates || coordinates.length < 2) {
      console.error('无效的点坐标:', coordinates)
      return null
    }

    const lng = coordinates[0]
    const lat = coordinates[1]
    if (isNaN(lng) || isNaN(lat)) {
      console.error('坐标包含非数字值:', { lng, lat })
      return null
    }

    if (Math.abs(lat) > 90 || Math.abs(lng) > 180) {
      console.error('坐标超出有效范围:', { lng, lat })
      return null
    }

    if (!worldGeoJSON || !worldGeoJSON.features || !Array.isArray(worldGeoJSON.features)) {
      console.error('无效的世界地图数据')
      return null
    }

    const validFeatures = worldGeoJSON.features.filter((feature: any) => {
      return (
        feature &&
        feature.geometry &&
        (feature.geometry.type === 'Polygon' || feature.geometry.type === 'MultiPolygon') &&
        feature.properties &&
        feature.properties['ISO3166-1-Alpha-2'] !== '-99' &&
        feature.properties['ISO3166-1-Alpha-3'] !== '-99'
      )
    })

    if (validFeatures.length === 0) {
      console.error('世界地图数据中没有有效的多边形')
      return null
    }

    console.log(`[findCountryCodeSync] 正在查找坐标 [${lng}, ${lat}] 所在国家...`)

    for (const feature of validFeatures) {
      try {
        // 先检查坐标是否在国家的边界框内
        if (feature.bbox) {
          const [minX, minY, maxX, maxY] = feature.bbox
          // 边界框外不用再检查详细多边形以提高效率
          if (lng < minX || lng > maxX || lat < minY || lat > maxY) {
            continue
          }
        }

        if (turf.booleanPointInPolygon(point, feature)) {
          // 支持多种可能的国家代码属性名格式
          const props = feature.properties
          // 二字母国家代码
          let countryCode =
            props.ISO_A2 ||
            props.iso_a2 ||
            props['ISO3166-1-Alpha-2'] ||
            props.ISO3166_1_Alpha_2 ||
            // 三字母国家代码
            props.ISO_A3 ||
            props.iso_a3 ||
            props['ISO3166-1-Alpha-3'] ||
            props.ISO3166_1_Alpha_3 ||
            // 名称作为最后的兜底
            props.NAME ||
            props.name

          // 再次确认不返回"-99"值
          if (countryCode === '-99') {
            console.log(`[findCountryCodeSync] 坐标 [${lng}, ${lat}] 找到的国家代码为-99，跳过`)
            continue
          }

          // 特殊处理台湾地区代码
          if (countryCode === 'CN-TW' || countryCode === 'TWN') {
            countryCode = 'TW'
            console.log(`[findCountryCodeSync] 将台湾地区代码转换为 TW`)
          }

          console.log(
            `[findCountryCodeSync] 坐标 [${lng}, ${lat}] 找到国家: ${countryCode}, 使用属性: ${JSON.stringify(props)}`,
          )
          return countryCode
        }
      } catch (featureError) {
        console.warn(`[findCountryCodeSync] 检查特征时出错:`, featureError)
        // 继续检查下一个特征
        continue
      }
    }

    // 如果没找到，尝试近似匹配
    console.log(`[findCountryCodeSync] 坐标 [${lng}, ${lat}] 没有找到确切匹配，尝试近似匹配...`)

    // 查找最近的国家
    let minDistance = Infinity
    let closestCountry = null

    for (const feature of validFeatures) {
      try {
        // 计算点到多边形边界的最短距离
        const distance = turf.distance(point, turf.nearestPoint(point, feature.geometry))
        if (distance < minDistance) {
          minDistance = distance
          const props = feature.properties
          // 支持多种可能的国家代码属性名格式
          let potentialCountry =
            props.ISO_A2 ||
            props.iso_a2 ||
            props['ISO3166-1-Alpha-2'] ||
            props.ISO3166_1_Alpha_2 ||
            props.ISO_A3 ||
            props.iso_a3 ||
            props['ISO3166-1-Alpha-3'] ||
            props.ISO3166_1_Alpha_3 ||
            props.NAME ||
            props.name

          // 特殊处理台湾地区代码
          if (potentialCountry === 'CN-TW' || potentialCountry === 'TWN') {
            potentialCountry = 'TW'
            console.log(`[findCountryCodeSync] 将台湾地区代码转换为 TW`)
          }

          // 确保不使用"-99"值
          if (potentialCountry !== '-99') {
            closestCountry = potentialCountry
          }
        }
      } catch (distanceError) {
        // 忽略距离计算错误
        continue
      }
    }

    // 如果最近的国家在50km以内，认为是该国家
    if (closestCountry && minDistance < 0.5) {
      // 大约50km
      console.log(
        `[findCountryCodeSync] 坐标 [${lng}, ${lat}] 近似匹配到国家: ${closestCountry}，距离: ${minDistance.toFixed(2)}度`,
      )
      return closestCountry
    }

    console.log(`[findCountryCodeSync] 坐标 [${lng}, ${lat}] 未找到所在国家`)
    return null
  } catch (error) {
    console.error('查找国家代码失败:', error)
    return null
  }
}

// 收集用户位置信息的函数
async function collectUserGeoData(username: string): Promise<{
  username: string
  points: Array<{
    city: string
    lat: number
    lng: number
  }>
}> {
  if (!username) {
    return { username, points: [] }
  }

  try {
    const response = await InstagramRapidApiV3.getInstance().getUserPosts({ username: username })
    const IgPosts = response.posts || []
    if (!IgPosts.length) {
      console.log(`[ins] 用户 ${username} 没有帖子`)
      return { username, points: [] }
    }

    const postsWithLocation = IgPosts.filter(
      (post) =>
        post.location?.lat &&
        post.location?.lng &&
        post.location.lat !== 0 &&
        post.location.lng !== 0,
    )

    if (!postsWithLocation.length) {
      console.log(`[ins] 用户 ${username} 没有带位置的帖子`)
      return { username, points: [] }
    }
    const postsToProcess = postsWithLocation.sort((a, b) => b.created_at - a.created_at)
    const points = postsToProcess
      .filter(
        (post) =>
          post.location &&
          typeof post.location.lat === 'number' &&
          typeof post.location.lng === 'number',
      )
      .map((post) => ({
        lat: post.location!.lat as number,
        lng: post.location!.lng as number,
        city: post.location!.name as string,
      }))

    return { username, points }
  } catch (error) {
    console.error(`[ins] 获取用户 ${username} 位置失败:`, error)
    return { username, points: [] }
  }
}

async function getLikesUserCountry(
  users: { username: string; postId: string }[],
): Promise<{ username: string; country: string; city: string; postId: string }[]> {
  if (!users?.length) {
    return []
  }
  console.log(`[ins] 开始获取 ${users.length} 个用户的国家信息`)
  const MAX_CONCURRENCY = 30

  try {
    // 使用缓存的世界地图数据，如果没有则重新加载
    const worldGeoJSON = worldGeoJSONCache || (await loadWorldGeoJSON())

    console.log(`[ins] 开始并行收集用户地理数据，并发数: ${MAX_CONCURRENCY}`)
    const usersGeoData = await Bluebird.map(
      users,
      async (user) => {
        try {
          console.log(`[ins] 开始获取用户 ${user.username} 的地理数据`)
          const geoData = await collectUserGeoData(user.username)
          return { ...geoData, postId: user.postId }
        } catch (error) {
          console.error(`[ins] 获取用户 ${user.username} 的地理数据失败:`, error)
          return { username: user.username, points: [], postId: user.postId }
        }
      },
      { concurrency: MAX_CONCURRENCY },
    )

    console.log(`[ins] 已收集 ${usersGeoData.length} 个用户的地理数据，开始批量解析国家信息`)

    const results = await Bluebird.map(
      usersGeoData,
      async (userData) => {
        if (!userData.points.length) {
          return { username: userData.username, country: '', city: '', postId: userData.postId }
        }

        for (const point of userData.points) {
          const turfPoint = turf.point([point.lng, point.lat])
          const countryCode = findCountryCodeSync(turfPoint, worldGeoJSON)
          if (countryCode) {
            return {
              username: userData.username,
              country: countryCode,
              city: point.city || '',
              postId: userData.postId,
            }
          }
        }

        return { username: userData.username, country: '', city: '', postId: userData.postId }
      },
      { concurrency: MAX_CONCURRENCY },
    )

    const validResults = results.filter((item) => item.country)
    console.log(`[ins] 成功获取到 ${validResults.length}/${users.length} 个用户的国家信息`)

    return results
  } catch (error) {
    console.error(`[ins] 获取用户国家信息时发生错误:`, error)
    return users.map((user) => ({
      username: user.username,
      country: '',
      city: '',
      postId: user.postId,
    }))
  }
}

/**
 * 获取Instagram评论者的用户画像
 * @param commentUsers 评论者的用户对象数组
 * @returns 用户画像结果
 */
async function getAuthorCommentsUsersPortrait(
  commentUsers: IgUser[],
): Promise<UserPortraitLLMOutput> {
  const users: UserPortraitLLMInput[] = commentUsers.map((user) => {
    return {
      username: user.username,
      avatar: user.profilePicUrl || '',
      signature: user.biography || '',
    }
  })

  console.log(`[ins getAuthorCommentsUsersPortrait] 处理了 ${users.length} 个用户数据`)
  const result = await getUserPortraitAnalysis(users)
  return result
}

/**
 * 获取Instagram评论者的用户画像
 * @param commentUsers 评论者的用户对象数组
 * @returns 用户画像结果
 */
async function getAuthorLikesUsersPortrait(
  likesUsers: Array<IgLike & { postId: string }>,
): Promise<UserPortraitLLMOutput> {
  const users: UserPortraitLLMInput[] = likesUsers.map((user) => {
    return {
      username: user.full_name,
      avatar: user.profile_pic_url || '',
      signature: user.full_name || '',
    }
  })
  console.log(`[ins getAuthorLikesUsersPortrait] 处理了 ${users.length} 个用户数据`)
  const result = await getUserPortraitAnalysis(users)
  return result
}

/**
 * 保存受众分析用户数据到数据库
 * @param usernameOrId 被分析的Instagram用户ID
 * @param users 受众分析用户数据
 * @param taskId 任务ID
 */
async function saveAudienceAnalysisUsers(
  usernameOrId: string,
  users: { username: string; country: string; city: string; postId: string }[],
  taskId: string,
): Promise<void> {
  try {
    if (!users.length) {
      console.log(`[ins saveAudienceAnalysisUsers] 没有用户数据需要保存`)
      return
    }

    console.log(`[ins saveAudienceAnalysisUsers] 开始保存 ${users.length} 条用户数据到数据库`)
    const result = await prisma.$transaction(
      async (tx) => {
        return await tx.audienceAnalysis.createMany({
          data: users.map((user) => ({
            usernameOrId: user.username,
            platform: KolPlatform.INSTAGRAM,
            city: user.city || '',
            region: user.country,
            postId: user.postId,
            taskId,
            location: user,
          })),
          skipDuplicates: true,
        })
      },
      { timeout: 10_000 },
    )

    console.log(`[ins saveAudienceAnalysisUsers] 成功保存 ${result.count} 条用户数据到数据库`)
  } catch (error) {
    console.error(`[ins saveAudienceAnalysisUsers] 保存用户数据失败:`, error)
    Sentry.captureException(error)
  }
}

/**
 * 获取Instagram用户评论者的受众分析
 * @param usernameOrId Instagram用户名或ID
 * @returns 受众分析结果
 */
async function getUserAudienceAnalysis(
  usernameOrId: string,
  taskId?: string,
): Promise<AudienceAnalysisResult> {
  try {
    // get likes users
    const selectedUsers = await getLikesUserNameByRandom(usernameOrId)

    if (!selectedUsers?.length) {
      throw new Error('没有获取到足够数量的用户名')
    }
    // user to get country
    const userToGetCountry = selectedUsers.map((user) => ({
      username: user.username,
      postId: user.postId,
    }))

    // get country
    const userCountries = await getLikesUserCountry(userToGetCountry)

    const userWithCountry = userCountries.filter((user) => user.username && user.country)

    if (!userWithCountry.length) {
      throw new Error('没有获取到足够数量的用户名')
    }

    console.log(
      `[ins googleMap users] 从 ${userToGetCountry.length} 个用户中获取到 ${userWithCountry.length} 个国家信息`,
    )

    const portraitPromise = getAuthorLikesUsersPortrait(selectedUsers.slice(0, 300))
    const regionPromise = statisticsUserRegion(userWithCountry)

    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      portraitPromise,
      regionPromise,
    ])

    // 保存受众分析数据到数据库
    await saveAudienceAnalysisUsers(usernameOrId, userWithCountry, taskId!)

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    } as AudienceAnalysisResult
  } catch (error) {
    try {
      console.log(
        `[ins getUserAudienceAnalysis] 点赞获取受众分析失败${JSON.stringify(error)}，开始获取粉丝列表`,
      )
      const followers = await getFollowers(usernameOrId)
      const portraitPromise = getAuthorCommentsUsersPortrait(followers.slice(0, 100))
      const regionPromise = statisticsUserRegion(followers)

      const [userPortraitResult, regionAnalysisResult] = await Promise.all([
        portraitPromise,
        regionPromise,
      ])

      return {
        userPortraitResult: formatToPercentage(userPortraitResult),
        regionAnalysisResult,
      } as AudienceAnalysisResult
    } catch (error) {
      console.error('[ins getUserAudienceAnalysis] 获取用户数据失败:', error)
      throw error
    }
  }
}

const formatToPercentage = (result: any): UserPortraitResult => {
  return {
    gender: {
      male: `${result.gender.male}%`,
      female: `${result.gender.female}%`,
    },
    age: {
      under18: `${result.age.under18}%`,
      age18to25: `${result.age.age18to25}%`,
      age25to45: `${result.age.age25to45}%`,
      above45: `${result.age.above45}%`,
    },
  }
}

/**
 * 获取单个Instagram视频/帖子的点赞用户
 * @param videoCode Instagram视频/帖子代码
 * @returns 点赞用户列表
 */
async function getVideoLikesUsers(
  videoCode: string,
  maxUsers: number = 600,
): Promise<Array<IgLike & { postId: string }>> {
  try {
    const instagramApi = InstagramRapidApiV3.getInstance()
    console.log(`[ins] 获取视频/帖子 ${videoCode} 的点赞用户`)

    const likes = await instagramApi.getLikes(videoCode)

    if (!likes?.likes?.length) {
      console.log(`[ins] 视频/帖子 ${videoCode} 没有点赞数据`)
      return []
    }

    console.log(`[ins] 视频/帖子 ${videoCode} 获取到 ${likes.likes.length} 个点赞用户`)

    // 过滤私密账号并限制数量
    const filteredLikes = likes.likes
      .filter((like) => !like.is_private)
      .slice(0, maxUsers)
      .map((like) => ({
        ...like,
        postId: videoCode,
      }))

    console.log(`[ins] 视频/帖子 ${videoCode} 过滤后剩余 ${filteredLikes.length} 个非私密点赞用户`)

    return filteredLikes
  } catch (error) {
    console.error(`[ins] 获取视频/帖子 ${videoCode} 的点赞用户失败:`, error)
    return []
  }
}

/**
 * 获取Instagram单个视频/帖子的受众分析
 * @param videoCode Instagram视频/帖子代码
 * @param taskId 可选的任务ID，用于保存分析结果
 * @returns 受众分析结果
 */
async function getPostAudience(videoCode: string): Promise<AudienceAnalysisResult> {
  try {
    const likesUsers = await getVideoLikesUsers(videoCode)

    // 如果未收集到点赞用户数据，则返回空结果
    if (!likesUsers?.length) {
      return {
        userPortraitResult: {},
        regionAnalysisResult: {},
      } as AudienceAnalysisResult
    }
    console.log(`[ins getPostAudience] 获取了 ${likesUsers.length} 个点赞用户数据`)

    // 获取用户国家信息
    const userToGetCountry = likesUsers.map((user) => ({
      username: user.username,
      postId: user.postId,
    }))

    const userCountries = await getLikesUserCountry(userToGetCountry)
    const userWithCountry = userCountries.filter((user) => user.username && user.country)

    console.log(
      `[ins getPostAudience] 从 ${userToGetCountry.length} 个用户中获取到 ${userWithCountry.length} 个国家信息`,
    )

    // 并行执行分析任务，不设置超时
    const portraitPromise = getAuthorLikesUsersPortrait(likesUsers.slice(0, 300))
    const regionPromise = statisticsUserRegion(userWithCountry)

    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      portraitPromise,
      regionPromise,
    ])

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    } as AudienceAnalysisResult
  } catch (error) {
    console.error(`[ins getPostAudience] 视频/帖子 ${videoCode} 受众分析失败:`, error)
    throw error
  }
}

export const InsInfoService = {
  getCommentUsers,
  getUserAudienceAnalysis,
  getFollowers,
  getLikesUserCountry,
  loadWorldGeoJSON,
  findCountryCodeSync,
  saveAudienceAnalysisUsers,
  getPostAudience,
  getVideoLikesUsers,
}
