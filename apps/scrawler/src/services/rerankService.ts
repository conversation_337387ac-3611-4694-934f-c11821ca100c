import { JINA_API_KEY } from '@/config/env'
/**
 * Jina Rerank 结果接口
 */
interface RerankResult {
  index: number
  relevance_score: number
}

/**
 * Jina Rerank API 响应接口
 */
interface JinaRerankResponse {
  model: string
  usage: {
    total_tokens: number
  }
  results: RerankResult[]
}

/**
 * 重排序结果接口
 */
export interface RerankResultItem {
  bloggerId: string
  score: number
}

async function rerankInstagramResults(
  documentsMap: Map<string, string>,
  query: string,
  topN: number = 70,
): Promise<RerankResultItem[]> {
  if (!documentsMap || documentsMap.size === 0) {
    console.warn('[rerankInstagramResults] 没有有效的文档')
    return []
  }

  console.log(`[rerankInstagramResults] 文档数量: ${documentsMap.size}`)

  // 过滤出非空文档
  const validEntries: Array<[string, string]> = []
  const bloggerIds: string[] = []
  const documents: string[] = []

  for (const [bloggerId, content] of documentsMap) {
    if (content && content.trim() !== '') {
      validEntries.push([bloggerId, content])
      bloggerIds.push(bloggerId)
      documents.push(content)
    }
  }

  console.log(`[rerankInstagramResults] 有效文档数量: ${validEntries.length}`)

  if (!validEntries.length) {
    console.warn('[rerankInstagramResults] 没有有效的文档内容')
    return []
  }

  try {
    // 调用 Jina 官方 Rerank API
    const response = await fetch('https://api.jina.ai/v1/rerank', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${JINA_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'jina-reranker-v2-base-multilingual',
        query: query,
        documents: documents,
        top_n: Math.min(topN, documents.length),
        return_documents: false,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Jina Rerank API 错误: ${response.status} - ${errorText}`)
    }

    const data: JinaRerankResponse = await response.json()

    if (!data.results || !Array.isArray(data.results)) {
      throw new Error('Jina Rerank API 返回格式错误')
    }

    const rerankedResults: RerankResultItem[] = data.results.map((rerankResult) => {
      const bloggerId = bloggerIds[rerankResult.index]
      return {
        bloggerId: bloggerId,
        score: rerankResult.relevance_score,
      }
    })

    if (rerankedResults.length > 0) {
      for (let i = 0; i < Math.min(5, rerankedResults.length); i++) {
        console.log(
          `  ${i + 1}. ${rerankedResults[i].bloggerId}: ${rerankedResults[i].score.toFixed(4)}`,
        )
      }
    }

    return rerankedResults
  } catch (error) {
    console.error('[rerankInstagramResults] 重排序失败:', error)
    return []
  }
}

async function rerankInstagram(
  documentsMap: Map<string, string>,
  query: string,
  topN: number = 70,
): Promise<RerankResultItem[]> {
  return rerankInstagramResults(documentsMap, query, topN)
}

export const RerankService = {
  rerankInstagramResults,
  rerankInstagram,
}
