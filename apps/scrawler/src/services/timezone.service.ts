import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'

dayjs.extend(utc)
dayjs.extend(timezone)

export class TimezoneService {
  // 将时间转换为指定时区对应的 UTC 时间
  static convertToUserTimezone(date: Date, userTimezone: string): Date {
    const targetTime = dayjs.tz(date, userTimezone)
    const offset = targetTime.utcOffset() * 60 * 1000
    return new Date(date.getTime() - offset)
  }

  // 获取用户时区的当前时间
  static getCurrentUserTime(userTimezone: string): Date {
    return dayjs().tz(userTimezone).toDate()
  }

  // 判断在用户时区是否是新的一天
  static isNewDay(lastResetTime: Date, userTimezone: string): boolean {
    const lastResetInUserTz = dayjs.utc(lastResetTime).tz(userTimezone)
    const nowInUserTz = dayjs().tz(userTimezone)
    return lastResetInUserTz.date() !== nowInUserTz.date()
  }

  // 获取用户时区的今天开始和结束时间（返回 UTC 时间用于存储）
  static getUserDayRange(userTimezone: string): { start: Date; end: Date } {
    // 1. 获取用户时区的当前时间
    const nowInUserTz = dayjs().tz(userTimezone)
    // 2. 获取用户时区的今天开始和结束时间
    const startOfDay = nowInUserTz.startOf('day')
    const endOfDay = nowInUserTz.endOf('day')

    // 3. 转换为 UTC 时间存储
    // 例如：东八区 2024-01-27 00:00:00 -> UTC 2024-01-26 16:00:00
    return {
      start: startOfDay.utc().toDate(), // 自动转换为 UTC 时间
      end: endOfDay.utc().toDate(), // 自动转换为 UTC 时间
    }
  }

  // 格式化显示用户时区的时间
  static formatToUserTimezone(
    utcDate: Date,
    userTimezone: string,
    format = 'YYYY-MM-DD HH:mm:ss',
  ): string {
    return dayjs(utcDate).tz(userTimezone).format(format)
  }

  static convertBetweenTimezones(date: Date, fromTimezone: string, toTimezone: string): Date {
    // 1. 将 UTC 时间转换到源时区的具体时间点
    const dateInFromTz = dayjs.utc(date).tz(fromTimezone)
    const hours = dateInFromTz.hour()
    const minutes = dateInFromTz.minute()
    const seconds = dateInFromTz.second()

    // 2. 在新时区创建相同的时间点
    const dateInToTz = dayjs()
      .tz(toTimezone)
      .year(dateInFromTz.year())
      .month(dateInFromTz.month())
      .date(dateInFromTz.date())
      .hour(hours)
      .minute(minutes)
      .second(seconds)

    // 3. 转换为 UTC 时间存储
    return dateInToTz.utc().toDate()
  }
}
