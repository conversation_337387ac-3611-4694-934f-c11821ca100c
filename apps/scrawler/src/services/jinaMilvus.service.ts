import { EASYKOL_JINA_V4_MILVUS_COLLECTION_NAME } from '@/config/env'
import { milvusClient } from '@/infras/milvus'
import Sentry from '@/infras/sentry'
import { KolPlatform, TaskType } from '@repo/database'
import {
  CollectionData,
  ConsistencyLevelEnum,
  DataType,
  FieldType,
  SparseFloatVector,
} from '@zilliz/milvus2-sdk-node'
import { Mutex } from 'async-mutex'

const client = milvusClient()
const collectionName = EASYKOL_JINA_V4_MILVUS_COLLECTION_NAME
const mutex = new Mutex()

// 定义 Jina V4 增强数据结构（2048维）
export interface JinaV4MilvusData {
  // 主键和基本信息
  id?: string // milvus 生成
  userId: string // 唯一不可变
  account: string // 唯一可变
  platform: string // 平台
  nickname?: string // 昵称

  // 用户统计数据
  followerCount?: number
  averagePlayCount?: number
  averageLikeCount?: number // 新增：平均点赞量（Instagram专用）
  lastPublishedTime?: number

  // 地理信息
  region?: string

  // 内容数据
  signature?: string
  videoTexts?: string

  // 邮箱
  email?: string

  // 向量数据
  denseVector: number[] // 2048维 Jina V4 向量
  sparseVector?: SparseFloatVector // 稀疏向量

  meta?: Record<string, any>

  // 预留字段
  reserved1?: string
  reserved2?: string
  reserved3?: string

  createdAt?: number
  updatedAt?: number
}

/**
 * 创建 Jina V4 集合
 */
async function createJinaV4Collection() {
  const fields: FieldType[] = [
    // 主键和基本信息
    {
      name: 'id',
      data_type: DataType.VarChar,
      is_primary_key: true,
      max_length: 100,
      autoID: true,
      description: '主键',
    },
    {
      name: 'userId',
      data_type: DataType.VarChar,
      max_length: 100,
      nullable: false,
      description: '用户ID',
    },
    {
      name: 'account',
      data_type: DataType.VarChar,
      max_length: 255,
      nullable: false,
      description: '账号',
    },
    {
      name: 'platform',
      data_type: DataType.VarChar,
      max_length: 50,
      nullable: false,
      description: '平台',
    },
    {
      name: 'nickname',
      data_type: DataType.VarChar,
      max_length: 255,
      nullable: true,
      description: '昵称',
    },

    // 用户统计数据
    { name: 'followerCount', data_type: DataType.Int64, nullable: true, description: '粉丝数' },
    {
      name: 'averagePlayCount',
      data_type: DataType.Int64,
      nullable: true,
      description: '平均播放量',
    },
    {
      name: 'averageLikeCount',
      data_type: DataType.Int64,
      nullable: true,
      description: '平均点赞量（Instagram专用）',
    },
    {
      name: 'lastPublishedTime',
      data_type: DataType.Int64,
      nullable: true,
      description: '最后发布时间',
    },

    // 地理信息
    {
      name: 'region',
      data_type: DataType.VarChar,
      max_length: 100,
      nullable: true,
      description: '地区',
    },

    // 内容数据
    {
      name: 'signature',
      data_type: DataType.VarChar,
      max_length: 10000,
      description: '签名',
      nullable: true,
    },
    {
      name: 'videoTexts',
      data_type: DataType.VarChar,
      max_length: 50000,
      description: '视频拼接后的文本',
      nullable: true,
    },

    // email
    {
      name: 'email',
      data_type: DataType.VarChar,
      max_length: 255,
      description: '邮箱',
      nullable: true,
    },

    // 向量数据 - Jina V4 是 2048 维
    {
      name: 'dense_vector',
      data_type: DataType.FloatVector,
      type_params: { dim: '2048' }, // Jina V4 维度
      description: '稠密向量',
    },
    { name: 'sparse_vector', data_type: DataType.SparseFloatVector, description: '稀疏向量' },

    // 元数据
    { name: 'meta', data_type: DataType.JSON, nullable: true, description: '元数据' },

    // 预留字段
    {
      name: 'reserved1',
      data_type: DataType.VarChar,
      max_length: 10000,
      description: '预留字段1',
      nullable: true,
    },
    {
      name: 'reserved2',
      data_type: DataType.VarChar,
      max_length: 10000,
      description: '预留字段2',
      nullable: true,
    },
    {
      name: 'reserved3',
      data_type: DataType.VarChar,
      max_length: 10000,
      description: '预留字段3',
      nullable: true,
    },

    // 时间戳
    { name: 'createdAt', data_type: DataType.Int64, description: '创建时间' },
    { name: 'updatedAt', data_type: DataType.Int64, description: '更新时间' },
  ]

  await client.createCollection({
    collection_name: collectionName,
    description: 'Instagram data with Jina V4 embeddings (2048d)',
    fields,
  })
}

/**
 * 检查集合是否存在
 */
export async function checkJinaV4CollectionExists(): Promise<boolean> {
  const collections = await client.showCollections()
  return collections.data.some((collection: CollectionData) => collection.name === collectionName)
}

/**
 * 创建索引
 */
async function createJinaV4Index() {
  // 为稠密向量创建索引（2048维）
  const denseIndexParams = {
    metric_type: 'COSINE',
    index_type: 'HNSW',
    params: JSON.stringify({ M: 32, efConstruction: 500 }),
  }

  await client.createIndex({
    index_name: 'idx_dense_vector',
    collection_name: collectionName,
    field_name: 'dense_vector',
    extra_params: denseIndexParams,
  })

  // 为稀疏向量创建索引
  const sparseIndexParams = {
    metric_type: 'IP',
    index_type: 'SPARSE_INVERTED_INDEX',
  }

  await client.createIndex({
    index_name: 'idx_sparse_vector',
    collection_name: collectionName,
    field_name: 'sparse_vector',
    extra_params: sparseIndexParams,
  })

  // 为常用过滤字段创建索引
  const scalarFields = [
    'userId',
    'account',
    'email',
    'platform',
    'region',
    'followerCount',
    'lastPublishedTime',
    'averagePlayCount',
    'averageLikeCount',
  ]

  for (const field of scalarFields) {
    await client.createIndex({
      index_name: `idx_${field}`,
      collection_name: collectionName,
      field_name: field,
      index_type: 'INVERTED',
    })
  }
}

/**
 * 加载集合
 */
async function loadJinaV4Collection() {
  await client.loadCollectionSync({
    collection_name: collectionName,
  })
}

/**
 * 初始化 Jina V4 集合
 */
export async function initJinaV4Collection() {
  await mutex.runExclusive(async () => {
    const exists = await checkJinaV4CollectionExists()
    if (!exists) {
      console.log(`[initJinaV4Collection] 创建集合 ${collectionName}`)
      await createJinaV4Collection()
      console.log(`[initJinaV4Collection] 创建索引`)
      await createJinaV4Index()
      console.log(`[initJinaV4Collection] 加载集合`)
      await loadJinaV4Collection()
      console.log(`[initJinaV4Collection] 集合初始化完成`)
    } else {
      console.log(`[initJinaV4Collection] 集合 ${collectionName} 已存在`)
      // 确保集合已加载
      await loadJinaV4Collection()
    }
  })
}

/**
 * 批量插入数据（带删除）
 */
export async function bulkInsertJinaV4WithDelete(data: JinaV4MilvusData[]) {
  if (data.length === 0) {
    console.warn('[bulkInsertJinaV4WithDelete] 没有数据需要插入')
    return { insertResult: { succ_index: [] }, deleteResult: { delete_cnt: 0 } }
  }

  try {
    // 1. 先删除已存在的数据
    const accounts = data.map((item) => item.account)
    const platform = data[0].platform // 假设同一批次都是同一平台

    const deleteExpr = `account in ${JSON.stringify(accounts)} and platform == "${platform}"`
    const deleteResult = await client.deleteEntities({
      collection_name: collectionName,
      expr: deleteExpr,
    })

    console.log(`[bulkInsertJinaV4WithDelete] 删除 ${deleteResult.delete_cnt} 条已存在的数据`)

    // 2. 准备插入数据
    const insertData = data.map((item) => ({
      userId: item.userId,
      account: item.account,
      nickname: item.nickname || '',
      platform: item.platform,
      region: item.region || '',
      followerCount: item.followerCount || 0,
      averagePlayCount: item.averagePlayCount || 0,
      averageLikeCount: item.averageLikeCount || 0,
      lastPublishedTime: item.lastPublishedTime || 0,
      signature: item.signature || '',
      videoTexts: item.videoTexts || '',
      email: item.email || '',
      dense_vector: item.denseVector,
      sparse_vector: item.sparseVector || { indices: [], values: [] },
      meta: JSON.stringify(item.meta || {}),
      reserved1: item.reserved1 || '',
      reserved2: item.reserved2 || '',
      reserved3: item.reserved3 || '',
      createdAt: item.createdAt || Math.floor(Date.now() / 1000),
      updatedAt: item.updatedAt || Math.floor(Date.now() / 1000),
    }))

    // 3. 执行插入
    const insertResult = await client.insert({
      collection_name: collectionName,
      data: insertData,
    })

    console.log(`[bulkInsertJinaV4WithDelete] 成功插入 ${insertResult.succ_index.length} 条数据`)

    return { insertResult, deleteResult }
  } catch (error) {
    console.error('[bulkInsertJinaV4WithDelete] 操作失败:', error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * Instagram 专用搜索函数（使用 Jina V4）
 */
export async function advancedFilterSearchForInstagramJinaV4(
  embeddings: number[][],
  filters: {
    taskType: TaskType
    platform?: string
    regions?: string[]
    minFollowers?: number
    maxFollowers?: number
    minAveragePlayCount?: number
    maxAveragePlayCount?: number
    minAverageLikeCount?: number
    maxAverageLikeCount?: number
    lastPublishedAfter?: number
    userIds?: string[]
    uniqueIds?: string[]
    ratedIds?: string[]
  },
): Promise<
  Array<{
    userId: string
    account: string
    nickname?: string
    videoTexts?: string
    meta?: any
    score: number
  }>
> {
  console.log(`[advancedFilterSearchForInstagramJinaV4] 开始高级过滤搜索`)

  if (!embeddings || embeddings.length === 0) {
    console.warn(`[advancedFilterSearchForInstagramJinaV4] 输入的embeddings为空，返回空结果`)
    return []
  }

  try {
    // 构建过滤表达式
    const filterExpressions: string[] = []

    // 平台过滤（Instagram）
    filterExpressions.push(`platform == "${KolPlatform.INSTAGRAM}"`)

    // 地区过滤
    if (filters.regions && filters.regions.length > 0) {
      const regionExpr = filters.regions.map((r) => `region == "${r}"`).join(' or ')
      filterExpressions.push(`(${regionExpr})`)
    }

    // 粉丝数过滤
    if (filters.minFollowers !== undefined) {
      filterExpressions.push(`followerCount >= ${filters.minFollowers}`)
    }
    if (filters.maxFollowers !== undefined) {
      filterExpressions.push(`followerCount <= ${filters.maxFollowers}`)
    }

    // 平均点赞数过滤（Instagram 特有）
    if (filters.minAverageLikeCount !== undefined) {
      filterExpressions.push(`averageLikeCount >= ${filters.minAverageLikeCount}`)
    }
    if (filters.maxAverageLikeCount !== undefined) {
      filterExpressions.push(`averageLikeCount <= ${filters.maxAverageLikeCount}`)
    }

    // 最后发布时间过滤
    if (filters.lastPublishedAfter !== undefined) {
      filterExpressions.push(`lastPublishedTime >= ${filters.lastPublishedAfter}`)
    }

    // 排除已评分的用户
    if (filters.ratedIds && filters.ratedIds.length > 0) {
      const ratedIdsStr = JSON.stringify(filters.ratedIds)
      filterExpressions.push(`account not in ${ratedIdsStr}`)
    }

    const filterExpr = filterExpressions.join(' and ')
    console.log(`[advancedFilterSearchForInstagramJinaV4] 过滤表达式: ${filterExpr}`)

    // 执行搜索
    const searchResults = await client.search({
      metric_type: 'COSINE',
      consistency_level: ConsistencyLevelEnum.Bounded,
      vectors: embeddings,
      anns_field: 'dense_vector',
      timeout: 30_000,
      collection_name: collectionName,
      expr: filterExpr,
      topk: 50,
      output_fields: ['userId', 'account', 'nickname', 'videoTexts', 'meta'],
      limit: 50,
      params: {
        ef: 1500,
      },
    })

    if (searchResults.status.code !== 0) {
      throw new Error(
        `搜索失败: code=${searchResults.status.error_code}, reason=${searchResults.status.reason}`,
      )
    }

    if (!searchResults.results || !Array.isArray(searchResults.results)) {
      console.warn(`[advancedFilterSearchForInstagramJinaV4] 搜索结果为空或格式错误`)
      return []
    }

    const results = searchResults.results.map((item) => ({
      userId: item.userId,
      account: item.account,
      nickname: item.nickname,
      videoTexts: item.videoTexts,
      meta: item.meta ? JSON.parse(item.meta) : undefined,
      score: item.score,
    }))
    const uniqueResults = Array.from(
      new Map(results.map((item) => [item.account, item])).values(),
    ).sort((a, b) => b.score - a.score)
    // 打印前10个结果的分数
    console.log(
      `[advancedFilterSearchForInstagramJinaV4] 前10个结果的分数:`,
      uniqueResults.slice(0, 10).map((item) => ({
        account: item.account,
        score: item.score,
      })),
    )

    console.log(
      `[advancedFilterSearchForInstagramJinaV4] 搜索完成，返回 ${uniqueResults.length} 个结果`,
    )
    return uniqueResults
  } catch (error) {
    console.error(`[advancedFilterSearchForInstagramJinaV4] 搜索出错:`, error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 批量获取用户的meta字段数据
 * @param uniqueIds 用户账号数组
 * @param platform 平台
 * @returns Promise<Map<string, any>> 账号到meta数据的映射
 */
export async function batchGetMetaData(
  uniqueIds: string[],
  platform: KolPlatform,
): Promise<Map<string, any>> {
  if (!uniqueIds || uniqueIds.length === 0) {
    console.warn('[batchGetMetaData] 没有提供有效的uniqueIds，返回空Map')
    return new Map()
  }

  console.log(`[batchGetMetaData] 开始批量获取 ${uniqueIds.length} 个用户的meta数据`)

  try {
    const metaDataMap = new Map<string, any>()
    const batchSize = 100

    for (let startIdx = 0; startIdx < uniqueIds.length; startIdx += batchSize) {
      const endIdx = Math.min(startIdx + batchSize, uniqueIds.length)
      const batchIds = uniqueIds.slice(startIdx, endIdx)

      const idsString = JSON.stringify(batchIds).replace('[', '').replace(']', '')
      const filter = `account in [${idsString}] and platform == "${platform}"`

      const queryResult = await client.query({
        collection_name: collectionName,
        filter: filter,
        output_fields: ['account', 'meta'],
        limit: batchIds.length,
        timeout: 60_000,
      })

      if (queryResult.status.code !== 0) {
        console.error(
          `[batchGetMetaData] 批量查询失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
        )
        continue
      }

      if (queryResult.data && queryResult.data.length > 0) {
        for (const item of queryResult.data) {
          if (item.account && item.meta) {
            try {
              // meta 字段是 JSON 字符串，需要解析
              const parsedMeta = typeof item.meta === 'string' ? JSON.parse(item.meta) : item.meta
              metaDataMap.set(item.account, parsedMeta)
            } catch (error) {
              console.warn(`[batchGetMetaData] 解析用户 ${item.account} 的 meta 数据失败:`, error)
              // 如果解析失败，存储原始值
              metaDataMap.set(item.account, item.meta)
            }
          }
        }
      }
    }

    console.log(
      `[batchGetMetaData] 成功获取 ${metaDataMap.size}/${uniqueIds.length} 个用户的meta数据`,
    )

    // 添加调试日志，检查解析后的数据结构
    if (metaDataMap.size > 0) {
      const firstEntry = metaDataMap.entries().next().value
      if (firstEntry) {
        console.log('[batchGetMetaData] 第一个meta数据示例:', {
          account: firstEntry[0],
          metaType: typeof firstEntry[1],
          hasData: !!firstEntry[1],
          hasDimensions: !!firstEntry[1]?.dimensions,
          dimensionsKeys: firstEntry[1]?.dimensions ? Object.keys(firstEntry[1].dimensions) : [],
        })
      }
    }

    return metaDataMap
  } catch (error) {
    console.error('[batchGetMetaData] 批量获取meta数据失败:', error)
    throw error
  }
}

/**
 * 通过 uniqueId 获取用户的稠密向量
 * @param uniqueId 用户的唯一标识
 * @returns 稠密向量
 */
export async function getDenseVectorsByUniqueId(
  uniqueId: string,
  platform: KolPlatform,
): Promise<number[]> {
  try {
    console.log(`[getVectorsByUniqueId] 开始查询用户 ${uniqueId} 的向量数据`)

    const queryResult = await client.query({
      collection_name: collectionName,
      filter: `account == "${uniqueId.replace(/"/g, '\\"')}" and platform == "${platform}"`,
      output_fields: ['dense_vector'],
      limit: 1,
      timeout: 30_000,
    })

    if (queryResult.status.code !== 0) {
      console.warn(
        `[getVectorsByUniqueId] 查询用户向量失败: code=${queryResult.status.error_code}, reason=${queryResult.status.reason}`,
      )
      return []
    }
    if (!queryResult.data || queryResult.data.length === 0) {
      console.warn(`[getVectorsByUniqueId] 未找到用户 ${uniqueId} 的向量数据`)
      return []
    }
    const result = queryResult.data[0]
    const denseVector = result.dense_vector

    if (!denseVector) {
      console.warn(`[getVectorsByUniqueId] 用户 ${uniqueId} 的向量数据不完整`)
      return []
    }
    return denseVector
  } catch (error) {
    console.error(`[getVectorsByUniqueId] 获取用户 ${uniqueId} 的向量数据失败:`, error)
    return []
  }
}

export const JinaV4MilvusService = {
  initJinaV4Collection,
  bulkInsertJinaV4WithDelete,
  advancedFilterSearchForInstagramJinaV4,
  batchGetMetaData,
  getDenseVectorsByUniqueId,
}
