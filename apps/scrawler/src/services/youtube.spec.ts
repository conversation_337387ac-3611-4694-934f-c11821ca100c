import YoutubeService from '@/services/youtube.ts'
import { prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'

describe('youtube service tests', () => {
  describe('youtube search', () => {
    it('should create kol info by rapid api', async () => {
      const name = '@mkbhd'
      const kol = await YoutubeService.getInstance().createKolByRapidApi(name)
      expect(kol.platformAccount).eq('@mkbhd')
    })

    it('should update errors', async () => {
      const id = 'cm3woe9o20007pas54tg28xgb'
      await prisma.similarChannelTask.update({
        where: {
          id: id,
        },
        data: {
          errors: 'what',
        },
      })

      const task = await prisma.similarChannelTask.findUnique({
        where: {
          id: id,
        },
      })
      expect(task)
      console.log(JSON.parse(JSON.stringify(task.errors)))
      expect(task.errors).eq('what')
    })
  })
})
