import { Channel } from '@/api/@types/rapidapi/Youtube'
import YoutubeApi from '@/api/youtube'
import Sentry from '@/infras/sentry'
import YoutubeRapidApi from '@/lib/youtubeRapidApi'
import { getUserPortraitAnalysis } from '@/services/aiTools/userPortaitAnslysisWithoutReason'
import {
  AudienceAnalysisResult,
  FakeRadarData,
  RegionAnalysisResult,
  UserPortraitLLMInput,
  UserPortraitLLMOutput,
  UserPortraitResult,
} from '@/types/audience'
import { statisticsUserRegion } from '@/utils/country'
import Bluebird from 'bluebird'
/**
 * 获取频道评论者的Channel信息
 * @param channelId YouTube频道ID
 * @returns 评论者的Channel对象数组和视频评论映射
 */
async function getCommentChannels(channelId: string): Promise<{
  channels: Channel[]
  videoCommentsMap: Record<string, string[]>
  videosCount: number // 视频ID -> 评论者channelId数组（不去重）
}> {
  try {
    const youtubeApi = YoutubeRapidApi.getInstance()
    const MAX_COMMENTS = 1500
    const CONCURRENCY = 50
    const MAX_VIDEOS_IDS = 10
    const MAX_PAGES_PER_VIDEO = 30

    console.log(`[ytb] 获取频道 ${channelId} 的视频`)
    const [shorts, videos] = await Promise.all([
      youtubeApi.getChannelShorts({ channelId }),
      youtubeApi.getChannelVideos(channelId),
    ])
    const allVideosIds = [...shorts, ...videos]
      .sort((a, b) => {
        const dateA = a.publishedDate || a.publishedTimeText
        const dateB = b.publishedDate || b.publishedTimeText
        return dateB.localeCompare(dateA)
      })
      .map((video) => video.videoId)

    const videoIds = Array.from(new Set(allVideosIds)).slice(0, MAX_VIDEOS_IDS)
    console.log(`[ytb] 将处理 ${videoIds.length} 个最新视频的评论`)

    const commentChannelMap = new Map<string, Channel>()
    const videoCommentsMap: Record<string, string[]> = {} // 视频ID -> 评论者channelId数组

    async function processVideoComments(videoId: string): Promise<void> {
      if (commentChannelMap.size >= MAX_COMMENTS) {
        return
      }

      let continuationToken: string | undefined
      let pageCount = 0
      // 初始化视频评论映射
      if (!videoCommentsMap[videoId]) {
        videoCommentsMap[videoId] = []
      }

      while (pageCount < MAX_PAGES_PER_VIDEO && commentChannelMap.size < MAX_COMMENTS) {
        try {
          const comments = await YoutubeApi.getInstance().getVideoComments(videoId, {
            sort_by: 'newest',
            token: continuationToken,
          })

          const uniqueChannelIds = new Set<string>()
          // 记录该视频下的所有评论者ID（不去重）
          comments.data.forEach((comment) => {
            const authorId = comment.authorChannelId
            videoCommentsMap[videoId].push(authorId)
            uniqueChannelIds.add(authorId)
          })

          const newChannelIds = [...uniqueChannelIds].filter((id) => !commentChannelMap.has(id))

          if (newChannelIds.length > 0) {
            const batchSize = 20
            for (let i = 0; i < newChannelIds.length; i += batchSize) {
              const batch = newChannelIds.slice(i, i + batchSize)

              const channelResults = await Promise.allSettled(
                batch.map(async (channelId) => {
                  try {
                    return { channelId, channel: await youtubeApi.getChannel(channelId) }
                  } catch (error) {
                    return { channelId, error }
                  }
                }),
              )

              channelResults.forEach((result) => {
                if (result.status === 'fulfilled' && result.value.channel) {
                  commentChannelMap.set(result.value.channelId, result.value.channel)
                }
              })

              if (commentChannelMap.size >= MAX_COMMENTS) break
            }
          }

          if (
            comments.continuation &&
            comments.continuation.trim() !== '' &&
            pageCount < MAX_PAGES_PER_VIDEO - 1
          ) {
            continuationToken = comments.continuation
            console.log(
              `[ytb] 视频 ${videoId} 存在下一页，continuationToken: ${continuationToken.substring(0, 10)}...`,
            )
            pageCount++
          } else {
            console.log(`[ytb] 视频 ${videoId} 没有更多页或达到页数限制`)
            break
          }

          if (pageCount % 3 === 0) {
            console.log(
              `[ytb] 视频 ${videoId} 已处理 ${pageCount} 页评论，当前已收集 ${commentChannelMap.size} 个频道，该视频评论数: ${videoCommentsMap[videoId].length}`,
            )
          }
        } catch (error) {
          console.error(`[ytb] 获取视频 ${videoId} 评论失败:`, error)
          break
        }
      }
    }

    console.log(`[ytb] 开始并行处理 ${videoIds.length} 个视频的评论，并发数: ${CONCURRENCY}`)
    await Bluebird.map(videoIds, processVideoComments, { concurrency: CONCURRENCY })

    const commentChannels = Array.from(commentChannelMap.values())
    console.log(`[ytb] 成功获取 ${commentChannels.length} 个评论者的频道信息`)

    return {
      channels: commentChannels,
      videoCommentsMap,
      videosCount: videoIds.length,
    }
  } catch (error) {
    console.error('[ytb] 获取评论者频道信息失败:', error)
    Sentry.captureException(error)
    return {
      channels: [],
      videoCommentsMap: {},
      videosCount: 0,
    }
  }
}

/**
 * 获取频道评论者的用户画像
 * @param commentChannels 评论者的Channel对象数组
 * @returns 用户画像结果
 */
async function getAuthorCommentsUsersPortrait(
  commentsChannels: Channel[],
): Promise<UserPortraitLLMOutput> {
  const channels: UserPortraitLLMInput[] = commentsChannels.map((channel) => {
    return {
      username: channel.title,
      avatar: channel.avatar[0].url,
      signature: channel.description || '',
    }
  })

  console.log(`[getAuthorCommentsUsersPortrait] 处理了 ${channels.length} 个用户数据`)
  const result = await getUserPortraitAnalysis(channels)
  return result
}

/**
 * 计算假雷达数据
 * @param videoCommentsMap 视频ID与评论者channelId的映射
 * @param videosCount 视频总数
 * @param commentChannels 评论者频道信息
 * @returns 假雷达数据
 */
async function getFakeRadarData(
  videoCommentsMap: Record<string, string[]>,
  videosCount: number,
  commentChannels: Channel[],
): Promise<FakeRadarData> {
  try {
    const totalCommentCount = Object.values(videoCommentsMap).reduce(
      (sum, comments) => sum + comments.length,
      0,
    )
    const totalUserCount = commentChannels.length
    // 保留俩位小数
    const avgCommentUserCount = Number((totalCommentCount / totalUserCount).toFixed(2))
    const channelMap = new Map<string, Channel>()
    commentChannels.forEach((channel) => {
      channelMap.set(channel.channelId, channel)
    })

    const usersWithoutCountry = commentChannels.filter((channel) => !channel.country)
    const userWithoutCountryCount = usersWithoutCountry.length
    const userWithoutCountryRate = Number(
      ((userWithoutCountryCount / totalUserCount) * 100).toFixed(2),
    )

    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

    // 计算疑似假粉丝数量 - 使用joinedDate判断（小于1年）
    const suspectedFakeUsers = commentChannels.filter((channel) => {
      if (!channel.joinedDate) return false

      try {
        // joinedDate 为 ISO格式: "2016-06-02"
        const joinDate = new Date(channel.joinedDate)
        return joinDate > oneYearAgo
      } catch (error) {
        console.error(`[getFakeRadarData] 解析joinedDate失败: ${channel.joinedDate}`, error)
        return false
      }
    })

    const suspectedFakeCount = suspectedFakeUsers.length
    const suspectedFakeRate = Number(((suspectedFakeCount / totalUserCount) * 100).toFixed(2))

    return {
      totalCommentCount,
      totalUserCount,
      avgCommentUserCount,
      videoCount: videosCount,
      userWithoutCountryCount,
      userWithoutCountryRate: `${userWithoutCountryRate}%`,
      suspectedFakeCount,
      suspectedFakeRate: `${suspectedFakeRate}%`,
    }
  } catch (error) {
    console.error('[getFakeRadarData] 计算假雷达数据失败:', error)
    return {
      totalCommentCount: 0,
      totalUserCount: 0,
      avgCommentUserCount: 0,
      videoCount: 0,
      userWithoutCountryCount: 0,
      userWithoutCountryRate: 0,
      suspectedFakeCount: 0,
      suspectedFakeRate: 0,
    }
  }
}

/**
 * 获取频道评论者的受众分析
 * @param channelId YouTube频道ID
 * @returns 受众分析结果
 */
async function getChannelAudienceAnalysis(channelId: string): Promise<AudienceAnalysisResult> {
  const {
    channels: commentChannels,
    videoCommentsMap,
    videosCount,
  } = await getCommentChannels(channelId)

  if (!commentChannels?.length) {
    throw new Error('未找到评论用户数据')
  }
  console.log(`[getChannelAudienceAnalysis] 获取了 ${commentChannels.length} 个评论用户数据`)

  const maxAnalysisChannelsCount = 100
  const needAgeAndGenderAnalysisChannels = commentChannels.slice(0, maxAnalysisChannelsCount)

  const [userPortraitResult, regionAnalysisResult, fakeRadarData] = await Promise.all([
    getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisChannels).catch((error) => {
      console.error('[getChannelAudienceAnalysis] 用户画像分析失败:', error)
      return {
        gender: { male: 0, female: 0 },
        age: { under18: 0, age18to25: 0, age25to45: 0, above45: 0 },
      } as UserPortraitLLMOutput
    }),
    statisticsUserRegion(commentChannels).catch((error) => {
      console.error('[getChannelAudienceAnalysis] 国家分析失败:', error)
      return {
        total: 0,
        statistics: [],
        developmentStatistics: [],
      } as RegionAnalysisResult
    }),
    getFakeRadarData(videoCommentsMap, videosCount, commentChannels).catch((error) => {
      console.error('[getChannelAudienceAnalysis] 假粉分析失败:', error)
      return {
        totalCommentCount: 0,
        totalUserCount: 0,
        avgCommentUserCount: 0,
        videoCount: 0,
        userWithoutCountryCount: 0,
        userWithoutCountryRate: '0%',
        suspectedFakeCount: 0,
        suspectedFakeRate: '0%',
      } as FakeRadarData
    }),
  ])

  if (!userPortraitResult || typeof userPortraitResult !== 'object') {
    throw new Error('用户画像数据格式错误')
  }

  if (!regionAnalysisResult || typeof regionAnalysisResult !== 'object') {
    throw new Error('地区分析数据格式错误')
  }

  if (!fakeRadarData || typeof fakeRadarData !== 'object') {
    throw new Error('假粉分析数据格式错误')
  }

  return {
    userPortraitResult: formatToPercentage(userPortraitResult),
    regionAnalysisResult,
    fakeRadarData,
  }
}

const formatToPercentage = (result: any): UserPortraitResult => {
  return {
    gender: {
      male: `${result.gender.male}%`,
      female: `${result.gender.female}%`,
    },
    age: {
      under18: `${result.age.under18}%`,
      age18to25: `${result.age.age18to25}%`,
      age25to45: `${result.age.age25to45}%`,
      above45: `${result.age.above45}%`,
    },
  }
}

/**
 * 获取单个YouTube视频的评论用户
 * @param videoId YouTube视频ID
 * @param maxUsers 最大获取用户数量，默认1000
 * @param pages 最大获取页数，默认50
 * @returns 评论者的Channel对象数组
 */
async function getVideoCommentsUsers(
  videoId: string,
  maxUsers: number = 1000,
  pages: number = 50,
): Promise<Channel[]> {
  try {
    const youtubeApi = YoutubeRapidApi.getInstance()

    console.log(`[ytb] 获取视频 ${videoId} 的评论用户，最大页数: ${pages}，最大用户数: ${maxUsers}`)

    const commentChannelMap = new Map<string, Channel>()
    let continuationToken: string | undefined
    let pageCount = 0

    while (pageCount < pages && commentChannelMap.size < maxUsers) {
      try {
        const comments = await YoutubeApi.getInstance().getVideoComments(videoId, {
          sort_by: 'newest',
          token: continuationToken,
        })

        if (!comments?.data?.length) {
          console.log(`[ytb] 视频 ${videoId} 没有更多评论`)
          break
        }

        const uniqueChannelIds = new Set<string>()
        comments.data.forEach((comment) => {
          const authorId = comment.authorChannelId
          if (authorId) {
            uniqueChannelIds.add(authorId)
          }
        })

        const newChannelIds = [...uniqueChannelIds].filter((id) => !commentChannelMap.has(id))

        if (newChannelIds.length > 0) {
          const batchSize = 20
          for (let i = 0; i < newChannelIds.length; i += batchSize) {
            const batch = newChannelIds.slice(i, i + batchSize)

            const channelResults = await Promise.allSettled(
              batch.map(async (channelId) => {
                try {
                  return { channelId, channel: await youtubeApi.getChannel(channelId) }
                } catch (error) {
                  return { channelId, error }
                }
              }),
            )

            channelResults.forEach((result) => {
              if (result.status === 'fulfilled' && result.value.channel) {
                commentChannelMap.set(result.value.channelId, result.value.channel)
              }
            })

            if (commentChannelMap.size >= maxUsers) break
          }
        }

        if (comments.continuation && comments.continuation.trim() !== '' && pageCount < pages - 1) {
          continuationToken = comments.continuation
          console.log(
            `[ytb] 视频 ${videoId} 存在下一页，continuationToken: ${continuationToken.substring(0, 10)}...`,
          )
          pageCount++
        } else {
          console.log(`[ytb] 视频 ${videoId} 没有更多页或达到页数限制`)
          break
        }

        if (pageCount % 3 === 0) {
          console.log(
            `[ytb] 视频 ${videoId} 已处理 ${pageCount} 页评论，当前已收集 ${commentChannelMap.size} 个频道`,
          )
        }
      } catch (error) {
        console.error(`[ytb] 获取视频 ${videoId} 评论失败:`, error)
        break
      }
    }

    const commentChannels = Array.from(commentChannelMap.values())
    console.log(`[ytb] 成功获取 ${commentChannels.length} 个评论者的频道信息`)

    return commentChannels
  } catch (error) {
    console.error('[ytb] 获取视频评论者频道信息失败:', error)
    Sentry.captureException(error)
    return []
  }
}

/**
 * 获取单个YouTube视频的受众分析
 * @param videoId YouTube视频ID
 * @param maxUsers 最大获取用户数量，默认1000
 * @param pages 最大获取页数，默认50
 * @returns 受众分析结果
 */
async function getPostAudience(
  videoId: string,
  maxUsers: number = 1000,
  pages: number = 50,
): Promise<AudienceAnalysisResult> {
  try {
    const commentChannels = await getVideoCommentsUsers(videoId, maxUsers, pages)

    // 如果未收集到评论用户数据，则返回空结果
    if (!commentChannels?.length) {
      return {
        userPortraitResult: {},
        regionAnalysisResult: {},
      } as AudienceAnalysisResult
    }

    console.log(`[getVideoAudienceAnalysis] 获取了 ${commentChannels.length} 个评论用户数据`)

    // 用户画像分析最多使用300个用户
    const needAgeAndGenderAnalysisChannels = commentChannels.slice(0, 300)

    const portraitPromise = getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisChannels)
    const regionPromise = statisticsUserRegion(commentChannels)

    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      portraitPromise,
      regionPromise,
    ])

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    } as AudienceAnalysisResult
  } catch (error) {
    console.error(`[getVideoAudienceAnalysis] 视频 ${videoId} 受众分析失败:`, error)
    throw error
  }
}

export const YtbInfoService = {
  getCommentChannels,
  getChannelAudienceAnalysis,
  getVideoCommentsUsers,
  getPostAudience,
}
