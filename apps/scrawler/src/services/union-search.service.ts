import TiktokApi from '@/api/tiktok'
import Sentry from '@/infras/sentry'
import { prisma } from '@repo/database'
import Bluebird from 'bluebird'

/**
 * 获取tiktok用户粉丝数
 * @param uniqueIds 用户唯一ID
 * @returns 粉丝数统计结果
 */
export async function getTikTokUserFollowers(uniqueIds: string[]) {
  const api = new TiktokApi()
  const stats = {
    success: 0,
    failed: 0,
    failedIds: [] as string[],
    zeroFollowers: 0,
  }

  const processUser = async (uniqueId: string): Promise<boolean> => {
    try {
      const userInfo = await api.getUserDetail({ unique_id: uniqueId })
      if (!userInfo?.user?.id) {
        throw new Error('Invalid user info')
      }

      // 粉丝数为0直接计入失败
      if (!userInfo.stats.followerCount) {
        console.warn(`用户 ${uniqueId} 粉丝数为0`)
        stats.zeroFollowers++
        return false
      }

      console.log(`get ${uniqueId} followerCount: ${userInfo.stats.followerCount}`)
      await prisma.tikTokUserInfo.update({
        where: { userId: userInfo.user.id },
        data: {
          followerCount: BigInt(userInfo.stats.followerCount),
        },
      })
      return true
    } catch (error) {
      return false
    }
  }

  await Bluebird.map(
    uniqueIds,
    async (uniqueId) => {
      const success = await processUser(uniqueId)
      if (success) {
        stats.success++
      } else {
        stats.failed++
        stats.failedIds.push(uniqueId)
        if (!stats.zeroFollowers) {
          // 只有非零粉丝数的失败才记录到Sentry
          Sentry.captureException(`Failed to get follower count for ${uniqueId}`)
        }
        console.error(`获取用户 ${uniqueId} 粉丝数失败`)
      }
    },
    { concurrency: 25 },
  )

  // 记录统计结果
  console.log(`获取粉丝数统计:
    总数: ${uniqueIds.length}
    成功: ${stats.success}
    失败: ${stats.failed} (其中粉丝数为0: ${stats.zeroFollowers})
    失败的uniqueIds: ${stats.failedIds.join(', ')}`)

  return stats
}

/**
 * 根据userIds 获取tiktok用户粉丝数
 * @param userIds 用户ID数组
 * @returns 粉丝数统计结果
 */
export async function getTikTokUserFollowersByUserIds(userIds: string[]) {
  const api = new TiktokApi()
  const stats = {
    success: 0,
    failed: 0,
    failedIds: [] as string[],
    zeroFollowers: 0,
    notFound: 0,
  }

  const processUser = async (userId: string): Promise<boolean> => {
    try {
      // 先检查用户是否存在
      const existingUser = await prisma.tikTokUserInfo.findUnique({
        where: { userId },
      })

      if (!existingUser) {
        console.warn(`用户 ${userId} 在数据库中不存在，跳过`)
        stats.notFound++
        return false
      }

      const userInfo = await api.getUserDetail({ user_id: userId })
      if (!userInfo?.user?.id) {
        throw new Error('Invalid user info')
      }

      // 粉丝数为0直接计入失败
      if (!userInfo.stats.followerCount) {
        console.warn(`用户 ${userId} 粉丝数为0`)
        stats.zeroFollowers++
        return false
      }

      console.log(`更新用户 ${userId} 粉丝数: ${userInfo.stats.followerCount}`)
      await prisma.tikTokUserInfo.update({
        where: { userId: userInfo.user.id },
        data: {
          followerCount: BigInt(userInfo.stats.followerCount),
        },
      })
      return true
    } catch (error) {
      console.error(`处理用户 ${userId} 时出错:`, error)
      return false
    }
  }

  await Bluebird.map(
    userIds,
    async (userId) => {
      const success = await processUser(userId)
      if (success) {
        stats.success++
      } else {
        if (!stats.notFound) {
          stats.failed++
          stats.failedIds.push(userId)
          if (!stats.zeroFollowers) {
            // 只有非零粉丝数且非不存在的失败才记录到Sentry
            Sentry.captureException(`Failed to get follower count for ${userId}`)
          }
          console.error(`获取用户 ${userId} 粉丝数失败`)
        }
      }
    },
    {
      concurrency: 25,
    },
  )

  // 记录统计结果
  console.log(`获取粉丝数统计:
    总数: ${userIds.length}
    成功: ${stats.success}
    失败: ${stats.failed} (其中粉丝数为0: ${stats.zeroFollowers})
    不存在: ${stats.notFound}
    失败的userIds: ${stats.failedIds.join(', ')}`)

  return stats
}
