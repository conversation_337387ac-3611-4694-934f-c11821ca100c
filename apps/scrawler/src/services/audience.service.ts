import { SheetData, exportToExcel } from '@/utils/excel'
import { AudienceAnalysis } from '@repo/database'

/**
 * 导出受众分析数据为Excel
 * @param audienceAnalysis 受众分析数据数组
 * @param source 数据来源名称
 * @returns Excel文件的Buffer
 */
export async function exportAudienceAnalysisExcel(
  audienceAnalysis: AudienceAnalysis[],
  source: string,
) {
  const uniquePostIds = Array.from(
    new Set(audienceAnalysis.map((item) => item.postId).filter(Boolean)),
  )

  const postIdToCodeMap = new Map<string, number>()
  uniquePostIds.forEach((postId, index) => {
    if (postId) {
      postIdToCodeMap.set(postId, index + 1)
    }
  })

  const formattedData = audienceAnalysis.map((item) => {
    const username = item.usernameOrId
    const userUrl = username ? `https://www.instagram.com/${username}/` : ''

    const postId = item.postId || ''
    const postCode = postId ? `post${postIdToCodeMap.get(postId) || ''}` : ''

    const postUrl = postId ? `https://www.instagram.com/p/${postId}/` : ''

    return {
      username,
      userUrl,
      postId: postCode, // 使用生成的code替代原始postId
      postUrl,
      country: item.region || '',
      city: item.city || '',
    }
  })

  const columns = [
    { header: 'Instagram Liker', key: 'username', width: 20, hyperlink: 'userUrl' },
    { header: 'Liked Post', key: 'postId', width: 20, hyperlink: 'postUrl' },
    { header: 'Country Info', key: 'country', width: 15 },
    { header: "Liker's Homepage Post Location", key: 'city', width: 25 },
  ]

  const commonStyle = {
    headerStyle: {
      font: { bold: true, size: 12 },
    },
    rowStyle: {},
  }

  const sheetData: SheetData = {
    name: `@${source} DATA`,
    data: formattedData,
    columns,
    styles: commonStyle,
  }

  return await exportToExcel([sheetData])
}

export const AudienceService = {
  exportAudienceAnalysisExcel,
}
