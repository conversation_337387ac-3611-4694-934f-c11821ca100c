import { FREE_QUOTA_STRATEGY } from '@/types/memberShip'
import { MemberStatus, MemberType, prisma } from '@repo/database'
import { User } from '@supabase/supabase-js'
import { tagService } from './tag_and_note/tag'
import assert from 'assert'
import { TimezoneService } from './timezone.service'

export const userHasInited = async (userId: string) => {
  const info = await prisma.userInfo.findFirst({
    where: {
      userId: userId,
    },
  })
  return !!info
}

export const initUser = async (user: User) => {
  try {
    await prisma.$transaction(
      async (tx) => {
        await tx.userInfo.upsert({
          where: {
            userId: user.id,
          },
          create: {
            userId: user.id,
            email: user.email,
            avatar: user.user_metadata.avatar_url,
            phoneNumber: user.phone,
            supabase: user as any,
          },
          update: {},
        })

        const existingProject = await tx.projectMembership.findFirst({
          where: {
            userId: user.id,
            role: 'OWNER',
          },
        })

        if (!existingProject) {
          const project = await tx.project.create({
            data: {
              title: 'Default Project',
              description: '',
            },
          })

          await tx.projectMembership.create({
            data: {
              userId: user.id,
              projectId: project.id,
              role: 'OWNER',
            },
          })
        }
        // 初始化默认的 tags
        await tagService.tryInitDefaultTags(user.id, undefined)
      },
      { timeout: 5_000 },
    )
  } catch (error) {
    if ((error as any).code === 'P2002') {
      console.log('User already exists, ignoring duplicate creation')
      return
    }
    throw error
  }
}

export const userMembershipHasInited = async (userId: string) => {
  const membership = await prisma.userMembership.findFirst({
    where: { userId: userId },
  })
  return !!membership
}

export const initUserMembership = async (user: User) => {
  try {
    const userTimezone = 'Asia/Shanghai'
    const { start: effectiveAt, end: expireAt } = TimezoneService.getUserDayRange(userTimezone)

    await prisma.userMembership.upsert({
      where: {
        userId: user.id,
      },
      create: {
        userId: user.id,
        timezone: userTimezone,
        effectiveAt: effectiveAt,
        expireAt: expireAt,
        type: MemberType.FREE,
        status: MemberStatus.ACTIVE,
        accountQuota: FREE_QUOTA_STRATEGY.FIRST_DAY,
        usedQuota: 0,
      },
      update: {},
    })
  } catch (error) {
    if ((error as any).code === 'P2002') {
      console.log('UserMembership already exists, ignoring duplicate creation')
      return
    }
    throw error
  }
}

/**
 *
 * 获取一个用户额度的扣除主体，可能是个人，可能是企业
 * @param userId 用户 ID
 * @returns 会员信息或者为空
 */
export const getUserMembershipAndEnterprise = async (userId: string) => {
  const membership = await prisma.userMembership.findUnique({
    where: {
      userId: userId,
    },
    include: {
      enterprise: true,
    },
  })
  return membership
}

// 获取用户信息
export const getUserByUserId = async (userId: string) => {
  assert(userId, 'userId is required')
  const user = await prisma.userInfo.findUniqueOrThrow({
    where: {
      userId: userId,
    },
  })
  return user
}
