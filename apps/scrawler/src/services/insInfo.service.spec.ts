import { describe, it } from 'vitest'
import InstagramRapidApiV3 from '../lib/instagramRapidApi.v3'
describe('should test insInfoService', () => {
  it('should test getCommentUsers function', async () => {
    const instagramApi = InstagramRapidApiV3.getInstance()
    const code = `DFK3nBQOskI`
    const paginationToken =
      'GU1bBDYTExtafVNDXThQA18BQBJZLWkGSx5ZLUNHAVkASggkGh0GJwU0FQNcElYBUhR9IBMgGW06NwExFgQaHlE4VDZlGwZfHRQaMBE8Oj0fCxBtXhhGHVEEHiwLXj5XLEIeKloHX1McUQMcHz0HPwQkLScRB0QKBB8QSisuVyUvawELEiQdOC8vCwITCDwuAAUAMgATMCUNTTNRX1c9UGIAPR4iLSAANCYNHT8BPi1HCCUFFx0mARRNN0UZBD8XHX0rQz0RNQIhHSUoERMIFihPOW4rRwAYJxw1ETEqJRUHSRJaNkYwGiwXCRgkLSIwHQ81Ri5BDV8HFDIAERYgGVErLBEvFTtSJ0EibyBDJBoxHCU7IhswCAMfSFAqfQpWHVwHRh1ABU8ERH1KCVVSfR1HOgk4Sh1ACA'
    const comments = await instagramApi.getPostComments(code, {
      pagination_token: paginationToken,
    })
    console.log(comments)
  })
})
