import { describe, it } from 'vitest'
import { queryByFilter } from './milvus.service'

describe('it should test query by filter for milvus', () => {
  it('should get user quota usage stats', async () => {
    const filter = `nickname == ''`
    const collectionName = 'easykol_milvus_prod'
    const limit = 100
    const offset = 0
    const outputFields = [
      'account',
      'platform',
      'nickname',
      'email',
      'region',
      'signature',
      'videoTexts',
      'followerCount',
      'averagePlayCount',
      'lastPublishedTime',
    ]

    const stats = await queryByFilter(collectionName, filter, outputFields, limit, offset)
    // stats.forEach((item) => {
    //   console.log(item)
    // })
    console.log(`[queryByFilter] 查询结果数量: ${stats.length}`)
  })
})
