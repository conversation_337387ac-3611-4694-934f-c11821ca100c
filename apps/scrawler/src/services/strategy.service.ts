import { CreateStrategyPayload } from '@/types/strategy'
import { EasykolTrackStrategy, prisma } from '@repo/database'
import assert from 'assert'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'

dayjs.extend(utc)
dayjs.extend(timezone)

function calculateNextRunAt(
  lastScheduledRun: Date,
  intervalHours: number,
  userTimezone: string = 'UTC',
): Date {
  const nowUtc = dayjs.utc()
  let nextRunLocal = dayjs(lastScheduledRun).tz(userTimezone).add(intervalHours, 'hour')

  while (nextRunLocal.isBefore(nowUtc)) {
    nextRunLocal = nextRunLocal.add(intervalHours, 'hour')
  }
  return nextRunLocal.toDate()
}

export async function findDueStrategies(currentTime: Date): Promise<EasykolTrackStrategy[]> {
  return prisma.easykolTrackStrategy.findMany({
    where: {
      isActive: true,
      nextRunAt: {
        lte: currentTime,
      },
    },
    orderBy: {
      nextRunAt: 'asc',
    },
  })
}

export async function recordRunSituation(strategyId: string): Promise<void> {
  await prisma.$transaction(
    async (tx) => {
      const strategy = await tx.easykolTrackStrategy.findUniqueOrThrow({
        where: { id: strategyId },
      })

      const newRemainingRuns = Math.max(0, strategy.remainingRuns - 1)
      const newIsActive = newRemainingRuns > 0

      const userTimezone = 'Asia/Shanghai'
      const newNextRunAt = calculateNextRunAt(
        strategy.nextRunAt,
        strategy.intervalHours,
        userTimezone,
      )

      await tx.easykolTrackStrategy.update({
        where: { id: strategyId },
        data: {
          remainingRuns: newRemainingRuns,
          isActive: newIsActive,
          nextRunAt: newNextRunAt,
        },
      })
    },
    { maxWait: 10_000, timeout: 10_000 },
  )
}

export async function createStrategy(
  userId: string,
  payload: CreateStrategyPayload,
): Promise<EasykolTrackStrategy> {
  assert(userId, '需要用户 ID')
  assert(payload.videoIds && payload.videoIds.length > 0, '需要至少一个视频 ID')

  const now = new Date()
  const userTimezone = 'Asia/Shanghai'

  return prisma.$transaction(
    async (tx) => {
      const template = await tx.userTrackStrategyTemplate.findUnique({
        where: { userId },
      })

      if (template && template.isActive === false) {
        throw new Error('策略模板已禁用，无法创建新策略。请先启用策略模板。')
      }

      const totalRuns = template?.totalRuns ?? 7
      const intervalHours = template?.intervalHours ?? 24
      const isActive = template?.isActive ?? true
      const name = template?.name ?? ' '

      const firstRunAt = calculateNextRunAt(now, intervalHours, userTimezone)

      const userGoogleSheet = await tx.userGoogleSheet.findFirst({
        where: {
          userId: userId,
        },
      })

      assert(userGoogleSheet, new Error('用户未配置 Google Sheet'))

      const publications = await tx.publicationStatisticsSheetData.findMany({
        where: {
          videoId: {
            in: payload.videoIds,
          },
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
      })

      if (publications.length !== payload.videoIds.length) {
        const foundVideoIds = publications.map((p) => p.videoId).filter(Boolean)
        const missingVideoIds = payload.videoIds.filter((id) => !foundVideoIds.includes(id))
        console.log(`部分视频数据未找到，将被跳过: ${missingVideoIds.join(', ')}`)
      }

      const newStrategy = await tx.easykolTrackStrategy.create({
        data: {
          userId: userId,
          name: name,
          totalRuns: totalRuns,
          remainingRuns: totalRuns,
          intervalHours: intervalHours,
          isActive: isActive,
          nextRunAt: firstRunAt,
        },
      })

      if (publications.length > 0) {
        await tx.publicationStatisticsSheetData.updateMany({
          where: {
            id: {
              in: publications.map((p) => p.id),
            },
          },
          data: {
            easykolTrackStrategyId: newStrategy.id,
          },
        })
      }
      return newStrategy
    },
    { maxWait: 10_000, timeout: 10_000 },
  )
}

export async function getScheduleById(
  userId: string,
  strategyId: string,
): Promise<EasykolTrackStrategy | null> {
  assert(userId, '需要用户 ID')
  assert(strategyId, '需要策略 ID')
  return prisma.easykolTrackStrategy.findFirst({
    where: { id: strategyId, userId: userId },
  })
}

const strategyService = {
  createStrategy,
  getScheduleById,
  recordRunSituation,
  findDueStrategies,
}

export default strategyService
