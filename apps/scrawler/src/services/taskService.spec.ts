import { describe, expect, it } from 'vitest'
import TaskService from './task'

describe('should test about task service ', () => {
  it('should get ongoing project similar tasks authors', async () => {
    const projectId = 'cm4dyokjn0079vb4vk65t8vzd'
    const uniqueIds =
      await TaskService.getInstance().getOngoingProjectSimilarTasksAuthors(projectId)
    console.log(uniqueIds)
    console.log(uniqueIds.length)
    expect(uniqueIds.length).gt(1)
  })
})
