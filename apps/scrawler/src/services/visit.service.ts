import { REDIS_KEY_PREFIX } from '@/config/env'
import { redis } from '@/infras/redis'
import { OverseasPlatforms } from '@/types/kol'
import { KolPlatform } from '@repo/database'

const INFO_CARD_EXPIRE_DAYS = 7
const INFO_CARD_EXPIRE_SECONDS = INFO_CARD_EXPIRE_DAYS * 24 * 60 * 60
const INFO_CARD_TRANSACTION_PREFIX = 'infoCard'
const platformPrefix = {
  [KolPlatform.YOUTUBE]: 'y',
  [KolPlatform.INSTAGRAM]: 'i',
  [KolPlatform.TIKTOK]: 't',
  [KolPlatform.TWITTER]: 'x',
} as const

// Key generation utilities
const generateVisitKey = (userId: string, date: string): string =>
  `${REDIS_KEY_PREFIX}${INFO_CARD_TRANSACTION_PREFIX}:${date}:${userId}`

const getPlatformVisitId = (platform: OverseasPlatforms, account: string): string =>
  `${platformPrefix[platform]}:${account}`

// 使用utc时间
const getTodayString = () => new Date().toISOString().split('T')[0]

const getDateString = (daysAgo: number) => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toISOString().split('T')[0]
}

export const checkAndRecordVisit = async (
  userId: string,
  platform: OverseasPlatforms,
  account: string,
): Promise<boolean> => {
  const key = generateVisitKey(userId, getTodayString())
  const visitId = getPlatformVisitId(platform, account)

  try {
    const result = await redis.eval(
      `local key = KEYS[1]
       local member = ARGV[1]
       local expire = ARGV[2]
       local added = redis.call('SADD', key, member)
       if added == 1 and redis.call('TTL', key) == -1 then
         redis.call('EXPIRE', key, expire)
       end
       return added`,
      1,
      key,
      visitId,
      INFO_CARD_EXPIRE_SECONDS.toString(),
    )
    return result === 1
  } catch (error) {
    console.error('Redis operation failed:', error)
    return false
  }
}

export const hasVisitedInLastWeek = async (
  userId: string,
  platform: OverseasPlatforms,
  account: string,
): Promise<boolean> => {
  const visitId = getPlatformVisitId(platform, account)
  const pipeline = redis.pipeline()

  for (let i = 0; i < INFO_CARD_EXPIRE_DAYS; i++) {
    const dateKey = generateVisitKey(userId, getDateString(i))
    pipeline.sismember(dateKey, visitId)
  }

  try {
    const results = await pipeline.exec()
    return results?.some(([err, visited]) => !err && visited === 1) ?? false
  } catch (error) {
    console.error('Redis pipeline failed:', error)
    return false
  }
}
