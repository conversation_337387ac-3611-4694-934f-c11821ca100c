import InstagramApi from '@/api/instagram.ts'
import YoutubeApi from '@/api/youtube.ts'
import Sentry from '@/infras/sentry'
import { InsInfoService } from '@/services/insInfo.service'
import TiktokService from '@/services/tiktok.ts'
import { EmailSourceType } from '@/types/email'
import { kolRegionInfo } from '@/types/kol'
import { KolInfo, KolPlatform, prisma } from '@repo/database'
import EmailService from './email'

export const findKolByIdentifier = async (
  identifier: { id?: string; handler?: string },
  platform: KolPlatform,
): Promise<KolInfo | null> => {
  const { handler } = identifier
  let { id } = identifier

  if (!id && !handler) {
    throw new Error('Either id or handler must be provided')
  }

  switch (platform) {
    case KolPlatform.YOUTUBE:
      if (!id && handler) {
        id = await YoutubeApi.getInstance().getYoutubeChannelId(handler)
      }
      if (id) {
        const kolById = await prisma.kolInfo.findFirst({
          where: {
            platformAccount: id,
          },
        })
        if (kolById) return kolById
      }
      return null

    case KolPlatform.INSTAGRAM:
    case KolPlatform.TIKTOK:
      // Instagram 和 TikTok 只通过 handler 查找
      if (!handler) {
        throw new Error(`${platform} platform requires handler`)
      }
      return prisma.kolInfo.findFirst({
        where: {
          OR: [
            { platformAccount: handler.toLowerCase(), platform },
            { platformAccount: `@${handler.toLowerCase()}`, platform },
          ],
        },
      })

    default:
      throw new Error(`Unsupported platform: ${platform}`)
  }
}

/**
 * 根据标识符查找kol详情信息,对应三张表的信息
 * @param identifier 标识符
 * @param platform 平台
 * @returns
 */
export const getKolRegionInfoByIdentifier = async (
  identifier: { id?: string; handler?: string },
  platform: KolPlatform,
): Promise<kolRegionInfo> => {
  const { handler } = identifier
  let { id } = identifier
  const kolRegionInfo: kolRegionInfo = {
    userId: id || '',
    username: handler || '',
    platform: platform,
    region: '',
  }

  if (!id && !handler) {
    return kolRegionInfo
  }

  switch (platform) {
    case KolPlatform.YOUTUBE:
      if (!id && handler) {
        id = await YoutubeApi.getInstance().getYoutubeChannelId(handler)
      }
      if (id) {
        const youtubeChannel = await prisma.youTubeChannel.findFirst({
          where: {
            channelId: id,
          },
        })

        if (youtubeChannel) {
          console.log(`get youtube region for ${handler}`)
          kolRegionInfo.region = youtubeChannel.country || kolRegionInfo.region
          kolRegionInfo.userId = youtubeChannel.channelId || kolRegionInfo.userId
          kolRegionInfo.username = youtubeChannel.channelHandle || kolRegionInfo.username
        }
      }
      break

    case KolPlatform.INSTAGRAM: {
      if (handler) {
        const instagramUserInfo = await prisma.instagramUserInfo.findFirst({
          where: {
            OR: [{ username: handler.toLowerCase() }, { username: `@${handler.toLowerCase()}` }],
          },
        })
        if (instagramUserInfo) {
          console.log(`get instagram region for ${handler}`)
          kolRegionInfo.region = instagramUserInfo.region || kolRegionInfo.region
          kolRegionInfo.userId = instagramUserInfo.id || kolRegionInfo.userId
          kolRegionInfo.username = instagramUserInfo.username || kolRegionInfo.username
        }
      }
      break
    }

    case KolPlatform.TIKTOK:
      if (handler) {
        const tikTokUserInfo = await prisma.tikTokUserInfo.findFirst({
          where: {
            OR: [{ uniqueId: handler.toLowerCase() }, { uniqueId: `@${handler.toLowerCase()}` }],
          },
          omit: {
            videos: false,
          },
        })

        if (tikTokUserInfo) {
          console.log(`get tiktok region for ${handler}`)
          kolRegionInfo.region = tikTokUserInfo.region || kolRegionInfo.region
          kolRegionInfo.userId = tikTokUserInfo.userId || kolRegionInfo.userId
          kolRegionInfo.username = tikTokUserInfo.uniqueId || kolRegionInfo.username
        }
      }
      break

    default:
      console.log('Unsupported platform:', platform)
      break
  }

  // 当region不存在时，尝试从API获取并更新数据库
  if (!kolRegionInfo.region) {
    try {
      switch (platform) {
        case KolPlatform.YOUTUBE:
          if (id) {
            const channel = await YoutubeApi.getInstance().getChannel(id)
            if (channel && channel.country) {
              console.log(`complete the youtube region for ${id}`)
              kolRegionInfo.region = channel.country
              await prisma.youTubeChannel.updateMany({
                where: { channelId: id },
                data: { country: channel.country },
              })
            }
          }
          break

        case KolPlatform.INSTAGRAM:
          if (handler) {
            let regionFound = false
            try {
              const remoteUser = await InstagramApi.getInstance().getUser(handler)
              if (remoteUser?.region) {
                console.log(`complete the instagram region for ${handler}`)
                kolRegionInfo.region = remoteUser.region
                await prisma.instagramUserInfo.updateMany({
                  where: {
                    OR: [
                      { username: handler.toLowerCase() },
                      { username: `@${handler.toLowerCase()}` },
                    ],
                  },
                  data: { region: remoteUser.region },
                })
                regionFound = true
              }
            } catch (error) {
              console.error(`通过API获取Instagram用户 ${handler} 信息失败:`, error)
            }

            if (!regionFound) {
              try {
                const userWithCountry = await InsInfoService.getLikesUserCountry([
                  { username: handler, postId: '' },
                ])
                const country = userWithCountry[0]?.country
                if (country) {
                  kolRegionInfo.region = country
                  await prisma.instagramUserInfo.updateMany({
                    where: {
                      OR: [
                        { username: handler.toLowerCase() },
                        { username: `@${handler.toLowerCase()}` },
                      ],
                    },
                    data: { region: country },
                  })
                }
              } catch (secondError) {
                console.error(`通过帖子位置获取Instagram用户 ${handler} 国家信息失败:`, secondError)
              }
            }
          }
          break

        case KolPlatform.TIKTOK:
          if (handler) {
            console.log(`complete the tiktok region for ${handler}`)
            const region = await TiktokService.getInstance().getTikTokUserRegion(handler)
            if (region) {
              kolRegionInfo.region = region
              await prisma.tikTokUserInfo.updateMany({
                where: {
                  OR: [
                    { uniqueId: handler.toLowerCase() },
                    { uniqueId: `@${handler.toLowerCase()}` },
                  ],
                },
                data: { region },
              })
            }
          }
          break
      }
    } catch (error) {
      console.error(`获取 ${platform} 用户 ${handler || id} 的region信息失败:`, error)
      Sentry.captureException(error)
    }
  }

  return kolRegionInfo
}

export const addEmailForKol = async (
  userId: string | undefined,
  kolId: string,
  email: string,
  source?: EmailSourceType | undefined | null,
) => {
  // email audit log
  EmailService.getInstance().addEmailAuditLog(userId, kolId, email, source ?? undefined)
  return prisma.kolInfo.update({
    where: {
      id: kolId,
    },
    data: {
      email,
      // add email to history arr
      historyEmails: {
        push: email,
      },
      emailSource: source ?? '',
      emailUpdatedAt: new Date(),
    },
  })
}

export const createYtbKolByIdOrName = async (
  userId: string | undefined,
  idOrName: string,
): Promise<KolInfo | null> => {
  if (!idOrName) {
    throw new Error('Channel ID or Name is required')
  }
  const channelInfo = await YoutubeApi.getInstance().getChannelWithVideos(idOrName)
  if (!channelInfo) {
    return null
  }

  const updates = {
    title: channelInfo.title,
    description: channelInfo.description,
    platformAccount: channelInfo.channelId,
    platform: KolPlatform.YOUTUBE,
    links: channelInfo.links
      ? channelInfo.links.map((i) => (i.link.startsWith('https://') ? i.link : `https://${i.link}`))
      : [],
    updatedAt: new Date(),
  }

  const exist = await prisma.kolInfo.findUnique({
    where: {
      platform_platformAccount: {
        platformAccount: channelInfo.channelId,
        platform: KolPlatform.YOUTUBE,
      },
    },
  })

  let kol: KolInfo | null = exist
  if (exist) {
    const emailFields: EmailFields = {
      email: undefined,
      emailSource: undefined,
      emailUpdatedAt: undefined,
      historyEmails: undefined,
    }
    const needUpdateEmail = channelInfo.email && exist.email !== channelInfo.email
    if (needUpdateEmail) {
      emailFields.email = channelInfo.email as string
      emailFields.emailSource = channelInfo.emailSource as EmailSourceType
      emailFields.emailUpdatedAt = new Date()
      emailFields.historyEmails = [
        ...new Set([...exist.historyEmails, channelInfo.email as string]),
      ] as string[]
      // email audit log
      await EmailService.getInstance().addEmailAuditLog(
        userId,
        exist.id,
        channelInfo.email as string,
        channelInfo.emailSource as EmailSourceType,
      )
    }
    kol = await prisma.kolInfo.update({
      where: { id: exist.id },
      data: {
        ...updates,
        ...emailFields,
        historyEmails: emailFields.historyEmails ?? exist.historyEmails,
      },
    })
  } else {
    kol = await prisma.kolInfo.create({
      data: {
        ...updates,
        platformAccount: channelInfo.channelId,
        platform: KolPlatform.YOUTUBE,
        email: channelInfo.email,
        emailSource: channelInfo.emailSource as EmailSourceType,
        historyEmails: [],
      },
    })
  }

  return kol as KolInfo
}

export type EmailFields = {
  email: string | undefined | null
  emailSource: EmailSourceType | undefined | null
  emailUpdatedAt: Date | undefined | null
  historyEmails: string[] | undefined | null
}
