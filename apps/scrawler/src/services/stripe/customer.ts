import { prisma } from '@repo/database'
import { stripe } from './client'

// create or get stripe customer
export async function getOrCreateCustomer(userId: string, email: string): Promise<string> {
  try {
    return await prisma.$transaction(
      async (tx) => {
        let customerRecord = await tx.stripeCustomer.findUnique({
          where: { userId },
        })

        if (customerRecord) {
          return customerRecord.stripeCustomerId
        }

        // create new stripe customer
        const customer = await stripe.customers.create({
          email: email,
          metadata: {
            userId: userId,
          },
        })

        // store customer info to database
        customerRecord = await tx.stripeCustomer.create({
          data: {
            userId: userId,
            stripeCustomerId: customer.id,
          },
        })

        return customer.id
      },
      { timeout: 10_000 },
    )
  } catch (error) {
    console.error('create or get stripe customer failed:', {
      userId,
      email,
      error: error instanceof Error ? error.message : error,
    })
    throw error
  }
}
