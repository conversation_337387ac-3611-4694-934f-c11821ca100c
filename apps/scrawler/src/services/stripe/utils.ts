import { STRIPE_BASIC_PLAN_PRICE_ID, STRIPE_PRO_PLAN_PRICE_ID } from '@/config/stripe'
import { SubscriptionPlanType } from '@repo/database'
// 30 days in milliseconds
export const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000

// map stripe subscription status to prisma status
export function mapStripeStatusToPrismaStatus(
  status: string,
):
  | 'ACTIVE'
  | 'INACTIVE'
  | 'PAST_DUE'
  | 'CANCELED'
  | 'TRIALING'
  | 'INCOMPLETE'
  | 'INCOMPLETE_EXPIRED'
  | 'UNPAID'
  | 'PAUSED' {
  const statusMap: Record<
    string,
    | 'ACTIVE'
    | 'INACTIVE'
    | 'PAST_DUE'
    | 'CANCELED'
    | 'TRIALING'
    | 'INCOMPLETE'
    | 'INCOMPLETE_EXPIRED'
    | 'UNPAID'
    | 'PAUSED'
  > = {
    active: 'ACTIVE',
    past_due: 'PAST_DUE',
    canceled: 'CANCELED',
    unpaid: 'UNPAID',
    trialing: 'TRIALING',
    incomplete: 'INCOMPLETE',
    incomplete_expired: 'INCOMPLETE_EXPIRED',
    paused: 'PAUSED',
  }

  return statusMap[status] || 'INACTIVE'
}

// 根据价格ID确定套餐类型
export function getPlanTypeByPriceId(priceId: string): SubscriptionPlanType {
  if (priceId === STRIPE_BASIC_PLAN_PRICE_ID) return SubscriptionPlanType.BASIC
  if (priceId === STRIPE_PRO_PLAN_PRICE_ID) return SubscriptionPlanType.PRO
  throw new Error(`Unknown price ID: ${priceId}`)
}
// 根据套餐类型确定金额
export function getPlanAmountByType(planType: SubscriptionPlanType): string {
  return planType === SubscriptionPlanType.BASIC ? '300' : '600'
}

// 根据套餐类型确定配额数量
export function getPlanQuotaByType(planType: SubscriptionPlanType): number {
  return planType === SubscriptionPlanType.BASIC ? 500 : 1500
}
