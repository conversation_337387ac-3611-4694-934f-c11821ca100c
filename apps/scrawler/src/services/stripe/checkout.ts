import {
  STRIPE_BASIC_PLAN_PRICE_ID,
  STRIPE_PRO_PLAN_PRICE_ID,
  SUBSCRIPTION_CANCEL_URL,
  SUBSCRIPTION_SUCCESS_URL,
} from '@/config/stripe'
import { Stripe } from 'stripe'
import { stripe } from './client'
import { getOr<PERSON>reateCustomer } from './customer'

// get checkout session
export async function retrieveCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
  return stripe.checkout.sessions.retrieve(sessionId)
}

// create checkout session
export async function createCheckoutSession(
  userId: string,
  email: string,
  planType: 'BASIC' | 'PRO',
) {
  const customerId = await getOrCreateCustomer(userId, email)
  const priceId = planType === 'BASIC' ? STRIPE_BASIC_PLAN_PRICE_ID : STRIPE_PRO_PLAN_PRICE_ID

  const session = await stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${SUBSCRIPTION_SUCCESS_URL}?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${SUBSCRIPTION_CANCEL_URL}`,
    metadata: {
      userId: userId,
      planType: planType,
    },
  })

  return session
}
