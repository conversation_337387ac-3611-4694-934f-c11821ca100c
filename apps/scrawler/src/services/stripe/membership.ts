import { EASYKOL_WARNING_CHANNEL } from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'
import {
  CardSubscriptionStatus,
  MemberStatus,
  MemberType,
  QuotaType,
  SubscriptionPlanType,
  prisma,
} from '@repo/database'
import assert from 'assert'
import { getPlanAmountByType } from './utils'
/**
 * 更新用户会员配额和信息卡会员
 * @param userId 用户ID
 * @param planType 订阅计划类型
 * @param source 触发来源
 * @param periodStart 订阅周期开始时间
 * @param periodEnd 订阅周期结束时间
 * @returns 操作是否成功
 */
export async function updateUserMembershipForSubscription(
  userId: string,
  planType: SubscriptionPlanType,
  source: 'checkout_completed' | 'invoice_payment_succeeded',
  periodStart: Date,
  periodEnd: Date,
): Promise<boolean> {
  const quotaAmount = planType === 'BASIC' ? 500 : 1500
  const subscriptionEvent = source === 'checkout_completed' ? '订阅' : '续费'

  // 验证订阅周期时间
  assert(periodEnd, '订阅周期结束时间不能为空')
  assert(periodStart, '订阅周期开始时间不能为空')

  try {
    // 使用局部事务处理数据库操作
    await prisma.$transaction(
      async (tx) => {
        // 获取用户会员信息
        const membership = await tx.userMembership.findUniqueOrThrow({
          where: { userId },
        })

        // 企业用户跳过处理
        if (membership.type === MemberType.ENTERPRISE && membership.enterpriseId) {
          console.log(`用户 ${userId} 是企业用户，跳过个人订阅计划处理`)
          return
        }

        // 更新会员信息
        await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            type: MemberType.PAID,
            accountQuota: { set: quotaAmount },
            usedQuota: { set: 0 },
            cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
            cardSubscriptionEffectiveAt: periodStart,
            cardSubscriptionExpireAt: periodEnd,
            effectiveAt: periodStart,
            expireAt: periodEnd,
            status: MemberStatus.ACTIVE,
          },
        })

        // 记录配额日志
        await tx.quotaLog.create({
          data: {
            userId,
            membershipId: membership.id,
            usage: quotaAmount,
            type: QuotaType.ADMIN,
            description: `EasyKOL ${planType} ${subscriptionEvent},配额: ${quotaAmount},会员有效期:${periodStart}至${periodEnd}`,
            createdBy: 'system',
            metadata: {
              source,
              planType,
              subscriptionEvent,
              eventTime: new Date().toISOString(),
              periodStart: periodStart.toISOString(),
              periodEnd: periodEnd.toISOString(),
            },
          },
        })
      },
      { timeout: 10_000 },
    )

    // 事务外异步发送 Slack 通知
    await sendSlackPaymentNotification(userId, planType, subscriptionEvent, periodStart).catch(
      (error) => {
        console.error('发送 Slack 付费通知失败:', error)
      },
    )

    return true
  } catch (error) {
    console.error(
      `更新用户会员失败: 用户=${userId}, 计划=${planType}, 原因=${error instanceof Error ? error.message : String(error)}`,
    )
    return false
  }
}

/**
 * 发送 Slack 付费通知
 */
async function sendSlackPaymentNotification(
  userId: string,
  planType: SubscriptionPlanType,
  subscriptionEvent: string,
  paymentTime: Date,
): Promise<void> {
  try {
    // 获取用户信息
    const user = await prisma.userInfo.findUniqueOrThrow({
      where: { userId },
      select: { email: true },
    })

    // 构建支付通知数据
    const paymentData = {
      email: user.email || '未知邮箱',
      plan: planType,
      amount: `¥${getPlanAmountByType(planType)}`,
      subscriptionType: subscriptionEvent,
      paymentTime: paymentTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      }),
    }

    // 发送 Slack 通知
    await SlackClient.getInstance().sendPaymentNotification(
      EASYKOL_WARNING_CHANNEL,
      paymentData,
      `🎉 用户${user.email} ${subscriptionEvent}成功`,
      { notify_users: true },
    )

    console.log(`用户付费 Slack 通知发送成功: ${user.email}`)
  } catch (error) {
    console.error(
      `发送 Slack 付费通知失败: ${error instanceof Error ? error.message : String(error)}`,
    )
  }
}
