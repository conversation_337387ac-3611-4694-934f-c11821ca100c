import { EASYKOL_STRIPE_PROTAL_RETURN_URL } from '@/config/stripe'
import { prisma, StripeSubscription, SubscriptionStatus } from '@repo/database'
import assert from 'assert'
import { Stripe } from 'stripe'
import { stripe } from './client'

// get subscription info
export async function getSubscription(subscriptionId: string) {
  assert(subscriptionId, new Error('subscriptionId is required'))
  return stripe.subscriptions.retrieve(subscriptionId)
}

// cancel subscription at period end
export async function cancelSubscription(subscriptionId: string) {
  assert(subscriptionId, new Error('subscriptionId is required'))
  return stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  })
}

// immediately cancel subscription,dont refund
export async function immediatelyCancelSubscription(subscriptionId: string) {
  assert(subscriptionId, new Error('subscriptionId is required'))
  return stripe.subscriptions.cancel(subscriptionId, {
    prorate: false, // dont refund
  })
}

// create customer portal session (for managing subscription)
export async function createPortalSession(customerId: string) {
  assert(customerId, new Error('customerId is required'))
  const portalOptions: Stripe.BillingPortal.SessionCreateParams = {
    customer: customerId,
  }

  const portalReturnUrl = EASYKOL_STRIPE_PROTAL_RETURN_URL || ''
  if (portalReturnUrl) {
    portalOptions.return_url = portalReturnUrl
  }

  return stripe.billingPortal.sessions.create(portalOptions)
}

export async function findUserActiveSubscription(
  userId: string,
): Promise<StripeSubscription | null> {
  if (!userId) {
    console.error('findUserActiveSubscription called with no userId')
    return null
  }
  return prisma.stripeSubscription.findFirst({
    where: {
      userId,
      status: { in: [SubscriptionStatus.PAST_DUE, SubscriptionStatus.ACTIVE] },
    },
    orderBy: { createdAt: 'desc' },
  })
}
