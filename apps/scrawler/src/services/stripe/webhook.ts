import { SubscriptionPlanType, SubscriptionStatus, TransitionStatus, prisma } from '@repo/database'
import { Stripe } from 'stripe'
import { stripe } from './client'
import { updateUserMembershipForSubscription } from './membership'
import { immediatelyCancelSubscription } from './subscription'
import { getPlanTypeByPriceId, mapStripeStatusToPrismaStatus } from './utils'
// handle stripe webhook event
export async function handleWebhookEvent(event: Stripe.Event) {
  try {
    console.log(
      `-----------------------------------Stripe Event: ${event.type} [${event.id}]-----------------------------------`,
    )

    switch (event.type) {
      case 'checkout.session.completed':
        console.log(`Processing ${event.type}...`)
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        console.log(`Processed ${event.type} [${event.id}]`)
        break

      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        console.log(`Processing ${event.type}...`)
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        console.log(`Processed ${event.type} [${event.id}]`)
        break

      case 'invoice.payment_succeeded':
        console.log(`Processing ${event.type}...`)
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        console.log(`Processed ${event.type} [${event.id}]`)
        break

      default:
        console.log(`Unhandled Stripe Event: ${event.type} [${event.id}]`)
        break
    }
  } catch (error) {
    console.error(
      `Error processing Stripe Event ${event.type} [${event.id}]: ${error instanceof Error ? error.message : String(error)}`,
    )
  }
}

// handle checkout session completed event
async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  if (session.subscription && session.metadata?.userId) {
    try {
      console.log(`处理结账完成事件，用户ID: ${session.metadata.userId}, 会话ID: ${session.id}`)
      const userId = session.metadata.userId
      const planType = session.metadata.planType as SubscriptionPlanType

      const subscriptionId =
        typeof session.subscription === 'string' ? session.subscription : session.subscription.id

      const subscription = await stripe.subscriptions.retrieve(subscriptionId)

      const subscriptionItem = subscription.items.data[0]
      const startTimestamp = subscriptionItem.current_period_start
      const endTimestamp = subscriptionItem.current_period_end
      const canceledTimestamp = subscription.canceled_at || null

      const periodStart = new Date(startTimestamp * 1000)
      const periodEnd = new Date(endTimestamp * 1000)
      const canceledAt = canceledTimestamp ? new Date(canceledTimestamp * 1000) : null
      const priceId = subscriptionItem.price.id

      console.log(`创建新订阅记录，订阅ID: ${subscriptionId}, 状态: ${subscription.status}`)

      const previousSubscriptions = await prisma.stripeSubscription.findMany({
        where: {
          userId,
          stripeSubscriptionId: {
            not: subscriptionId,
          },
          status: {
            in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PAST_DUE],
          },
        },
      })

      if (previousSubscriptions && previousSubscriptions.length > 0) {
        console.log(`发现 ${previousSubscriptions.length} 个之前的活跃订阅，先取消这些订阅`)

        await Promise.all(
          previousSubscriptions.map(async (prevSub) => {
            try {
              await immediatelyCancelSubscription(prevSub.stripeSubscriptionId)
              console.log(`成功取消之前的订阅: ${prevSub.stripeSubscriptionId}`)
            } catch (error) {
              console.error(`取消之前的订阅失败: ${prevSub.stripeSubscriptionId}`, error)
            }
          }),
        )
      }

      await prisma.stripeSubscription.create({
        data: {
          userId,
          stripeSubscriptionId: subscriptionId,
          status: mapStripeStatusToPrismaStatus(subscription.status),
          planType: planType,
          priceId: priceId,
          currentPeriodStart: periodStart,
          currentPeriodEnd: periodEnd,
          cancelAtPeriodEnd: subscription.cancel_at_period_end,
          canceledAt: canceledAt,
        },
      })

      await updateUserMembershipForSubscription(
        userId,
        planType,
        'checkout_completed',
        periodStart,
        periodEnd,
      )
      console.log(`结账完成事件处理完成，用户ID: ${userId}`)
    } catch (error) {
      console.error('处理结账完成事件失败:', error)
      throw error
    }
  }
}

// handle subscription updated event
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    const customerId = subscription.customer
    if (!customerId) {
      console.error('订阅没有关联的客户ID')
      return
    }

    const customerIdStr = typeof customerId === 'string' ? customerId : customerId.id
    console.log(`处理订阅更新，客户ID: ${customerIdStr}, 订阅ID: ${subscription.id}`)

    await prisma.$transaction(
      async (tx) => {
        const customerRecord = await tx.stripeCustomer.findUnique({
          where: { stripeCustomerId: customerIdStr },
        })

        if (!customerRecord) {
          console.log(`未找到客户记录，客户ID: ${customerIdStr}`)
          return
        }

        const subscriptionItem = subscription.items.data[0]
        const startTimestamp = subscriptionItem.current_period_start
        const endTimestamp = subscriptionItem.current_period_end
        const canceledTimestamp = subscription.canceled_at || null

        const periodStart = new Date(startTimestamp * 1000)
        const periodEnd = new Date(endTimestamp * 1000)
        const canceledAt = canceledTimestamp ? new Date(canceledTimestamp * 1000) : null

        const priceId = subscriptionItem.price.id
        const newPlanType = getPlanTypeByPriceId(priceId)

        await tx.stripeSubscription.update({
          where: { stripeSubscriptionId: subscription.id },
          data: {
            status: mapStripeStatusToPrismaStatus(subscription.status),
            planType: newPlanType,
            priceId: priceId,
            currentPeriodStart: periodStart,
            currentPeriodEnd: periodEnd,
            cancelAtPeriodEnd: subscription.cancel_at_period_end || false,
            canceledAt: canceledAt,
            transitionStatus: TransitionStatus.NORMAL,
          },
        })
      },
      { timeout: 10_000 },
    )
  } catch (error) {
    console.error('处理订阅更新失败:', error)
    // 即使处理失败，也要将transitionStatus重置为NORMAL，防止前端一直轮询
    try {
      if (subscription && subscription.id) {
        await prisma.stripeSubscription.updateMany({
          where: {
            stripeSubscriptionId: subscription.id,
            transitionStatus: {
              in: [TransitionStatus.CANCELED_IN_PROGRESS, TransitionStatus.RESTORING],
            },
          },
          data: {
            transitionStatus: TransitionStatus.NORMAL,
          },
        })
        console.log(
          `已重置订阅 ${subscription.id} 的transitionStatus为NORMAL（处理失败后的兜底措施）`,
        )
      }
    } catch (resetError) {
      console.error(`重置订阅状态失败:`, resetError)
    }
    throw error
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log(`处理账单支付成功事件，发票ID: ${invoice.id}`)
  console.log(`账单原因: ${invoice.billing_reason}`)
  try {
    if (!invoice.customer) {
      console.error('发票没有关联的客户ID')
      return
    }
    const customerId = typeof invoice.customer === 'string' ? invoice.customer : invoice.customer.id

    const customerRecord = await prisma.stripeCustomer.findUnique({
      where: { stripeCustomerId: customerId },
    })

    if (!customerRecord) {
      console.log(`找不到客户记录，客户ID: ${customerId}，跳过处理`)
      return
    }

    console.log(`找到客户记录，用户ID: ${customerRecord.userId}`)

    const isFirstPayment = invoice.billing_reason === 'subscription_create'
    const isRenewal = invoice.billing_reason === 'subscription_cycle'
    const isUpgrade = invoice.billing_reason === 'subscription_update'

    console.log(
      `发票类型: ${isFirstPayment ? '首次订阅' : isRenewal ? '续费' : isUpgrade ? '升级/降级' : '其他'}`,
    )

    switch (true) {
      case isFirstPayment:
        // 首次订阅
        console.log('首次订阅，跳过处理')
        break

      case isRenewal: {
        let subscriptionId: string | undefined = undefined
        // 1. 从invoice.parent.subscription_details.subscription中获取
        if (
          invoice.parent &&
          invoice.parent.subscription_details &&
          invoice.parent.subscription_details.subscription
        ) {
          subscriptionId = invoice.parent.subscription_details.subscription as string
          console.log(`从invoice.parent.subscription_details获取到订阅ID: ${subscriptionId}`)
        }
        // 2. 从invoice.lines.data中获取
        else if (invoice.lines && invoice.lines.data && invoice.lines.data.length > 0) {
          const lineItem = invoice.lines.data[0]
          if (
            lineItem &&
            lineItem.parent &&
            lineItem.parent.subscription_item_details &&
            lineItem.parent.subscription_item_details.subscription
          ) {
            subscriptionId = lineItem.parent.subscription_item_details.subscription as string
            console.log(
              `从invoice.lines.data[0].parent.subscription_item_details获取到订阅ID: ${subscriptionId}`,
            )
          }
        }
        // 3. 从数据库记录中获取
        else {
          const subscription = await prisma.stripeSubscription.findFirst({
            where: {
              userId: customerRecord.userId,
              status: {
                in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PAST_DUE],
              },
            },
            orderBy: { currentPeriodStart: 'desc' },
          })
          subscriptionId = subscription?.stripeSubscriptionId
          console.log(`从数据库记录获取到订阅ID: ${subscriptionId}`)
        }

        if (!subscriptionId) {
          console.error(`无法获取有效的订阅ID，用户ID: ${customerRecord.userId}，跳过处理`)
          return
        }

        console.log(`最终获取到的订阅ID: ${subscriptionId}，开始从Stripe获取最新数据`)
        // 使用获取到的订阅ID从Stripe获取最新订阅信息
        const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId)

        const subscriptionItem = stripeSubscription.items.data[0]
        const priceId = subscriptionItem.price.id
        const planType = getPlanTypeByPriceId(priceId)

        // 获取订阅周期信息
        const startTimestamp = subscriptionItem.current_period_start
        const endTimestamp = subscriptionItem.current_period_end
        const periodStart = new Date(startTimestamp * 1000)
        const periodEnd = new Date(endTimestamp * 1000)

        console.log(`从Stripe获取的最新订阅信息，价格ID: ${priceId}，确定套餐类型: ${planType}`)
        console.log(
          `订阅续费，更新会员配额和信息卡，用户ID: ${customerRecord.userId}, 周期: ${periodStart.toISOString()} - ${periodEnd.toISOString()}`,
        )
        await updateUserMembershipForSubscription(
          customerRecord.userId,
          planType,
          'invoice_payment_succeeded',
          periodStart,
          periodEnd,
        )
        break
      }
      case isUpgrade:
        // 升级降级
        console.log('升级降级，跳过处理')
        break

      default:
        console.log('其他情况，跳过处理')
        break
    }

    console.log(`账单支付成功事件处理完成，发票ID: ${invoice.id}`)
  } catch (error) {
    console.error(
      `处理账单支付成功事件失败: ${error instanceof Error ? error.message : String(error)}`,
    )
    console.error(`错误详情: ${JSON.stringify(error)}`)
    throw error
  }
}
