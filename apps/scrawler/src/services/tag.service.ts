import { PaginationParams } from '@/types/pagination'
import { CreateTagParams, GetTagsResponse } from '@/types/tag'
import { PaginationService } from '@/utils/pagination'
import { prisma } from '@repo/database'

const MAX_LABELS_PER_USER = 24
const MAX_LABEL_NAME_LENGTH = 30

export const tagService = {
  async createTag(userId: string, params: CreateTagParams) {
    const userLabelsCount = await prisma.tag.count({
      where: { createdBy: userId },
    })
    if (userLabelsCount >= MAX_LABELS_PER_USER) {
      throw new Error('已达到标签数量上限')
    }

    if (params.name.length > MAX_LABEL_NAME_LENGTH) {
      throw new Error('标签名称不能超过30个字符')
    }

    const existingLabel = await prisma.tag.findUnique({
      where: { name: params.name },
    })
    if (existingLabel) {
      throw new Error('标签名称已存在')
    }

    return await prisma.tag.create({
      data: {
        name: params.name,
        color: params.color,
        createdBy: userId,
      },
    })
  },

  async deleteTag(userId: string, tagId: string) {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('标签不存在或无权限删除')
    }

    return await prisma.$transaction(
      async (tx) => {
        await tx.publicationTag.deleteMany({
          where: { tagId: tagId },
        })

        return await tx.tag.delete({
          where: { id: tagId },
        })
      },
      {
        timeout: 10000,
      },
    )
  },

  async getTags(userId: string, pagination: PaginationParams = {}): Promise<GetTagsResponse> {
    const { page, pageSize, skip } = PaginationService.handlePagination(pagination)

    const [total, items] = await Promise.all([
      prisma.tag.count({
        where: { createdBy: userId },
      }),
      prisma.tag.findMany({
        where: { createdBy: userId },
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
    ])

    return {
      items: items.map((item) => ({
        id: item.id,
        tagId: item.id,
        name: item.name,
        color: item.color,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      })),
      total,
    }
  },
}
