import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { ChannelVideo } from '@/api/@types/rapidapi/Youtube'
import { TtVideoBasicInfo } from '@/types/kol'
import { getUnixTimestamp, parsePublishedTime } from '@/utils/date'
import { KolPlatform } from '@repo/database'

export interface PublicationStats {
  lastPublishedTime?: number
  weeklyPostCount: number
  monthlyPostCount: number
  calculatedAt: number
}

export type VideoPost = TtVideoBasicInfo | IgPost | ChannelVideo

export class PublicationStatsService {
  /**
   * 计算发布统计信息
   * @param videos 视频/帖子数组
   * @param platform 平台类型
   * @returns 发布统计信息
   */
  static calculatePublicationStats(videos: VideoPost[], platform: KolPlatform): PublicationStats {
    if (!videos || !Array.isArray(videos) || videos.length === 0) {
      return {
        weeklyPostCount: 0,
        monthlyPostCount: 0,
        calculatedAt: Date.now(),
      }
    }

    const now = Date.now()
    const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000
    const oneMonthAgo = now - 30 * 24 * 60 * 60 * 1000

    let lastPublishedTime: number | undefined
    let weeklyCount = 0
    let monthlyCount = 0

    for (const video of videos) {
      const publishTime = this.getPublishTime(video, platform)
      if (!publishTime) continue

      const publishTimeMs = publishTime * 1000 // 转换为毫秒

      // 更新最近发布时间
      if (!lastPublishedTime || publishTimeMs > lastPublishedTime) {
        lastPublishedTime = publishTimeMs
      }

      // 计算周发布数
      if (publishTimeMs >= oneWeekAgo) {
        weeklyCount++
      }

      // 计算月发布数
      if (publishTimeMs >= oneMonthAgo) {
        monthlyCount++
      }
    }

    return {
      lastPublishedTime,
      weeklyPostCount: weeklyCount,
      monthlyPostCount: monthlyCount,
      calculatedAt: now,
    }
  }

  /**
   * 根据平台获取发布时间
   * @param video 视频/帖子对象
   * @param platform 平台类型
   * @returns 发布时间戳（秒）
   */
  private static getPublishTime(video: VideoPost, platform: KolPlatform): number | null {
    switch (platform) {
      case KolPlatform.TIKTOK:
        video = video as TtVideoBasicInfo
        return video.create_time || null
      case KolPlatform.YOUTUBE:
        // YouTube videos have publishedAt in ISO format
        video = video as ChannelVideo
        if (video.publishedAt) {
          // publishedAt is already in ISO format like "2025-06-06T00:00:00Z"
          return getUnixTimestamp(video.publishedAt)
        } else if (video.publishedDate) {
          // Fallback to publishedDate if publishedAt is not available
          const isoDate = parsePublishedTime(video.publishedDate)
          return getUnixTimestamp(isoDate)
        }
        return null
      case KolPlatform.INSTAGRAM:
        video = video as IgPost
        return video.created_at || null
      default:
        return null
    }
  }

  /**
   * 检查统计数据是否需要更新（超过24小时）
   * @param stats 当前统计数据
   * @returns 是否需要更新
   */
  static shouldUpdateStats(stats: PublicationStats | null | undefined): boolean {
    if (!stats || typeof stats !== 'object') return true

    const calculatedAt = stats.calculatedAt
    if (!calculatedAt) return true

    const now = Date.now()
    const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000

    return calculatedAt < twentyFourHoursAgo
  }

  /**
   * 格式化显示用的发布统计数据
   * @param stats 统计数据
   * @returns 格式化后的数据
   */
  static formatStatsForDisplay(stats: PublicationStats): {
    lastPublishedTime: string
    weeklyPostCount: number
    monthlyPostCount: number
  } {
    const lastPublishedTime = stats.lastPublishedTime
      ? new Date(stats.lastPublishedTime).toISOString().split('T')[0]
      : 'N/A'

    return {
      lastPublishedTime,
      weeklyPostCount: Math.round(stats.weeklyPostCount || 0),
      monthlyPostCount: Math.round(stats.monthlyPostCount || 0),
    }
  }
}
