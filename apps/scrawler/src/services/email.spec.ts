import { getChannelEmailFromNano } from '@/api/nano.ts'
import EmailService from '@/services/email.ts'
import { extractEmail } from '@/utils/email'
import { $Enums, prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'
import KolPlatform = $Enums.KolPlatform

describe('should find email for kol', () => {
  it('should extract email', () => {
    const source =
      '✨ Over 30 | Chic, practical looks\n💪 Athletic wear + lifestyle inspo\n🧒 Mom of 3 | AZ girl \n📩 Email <EMAIL>\n👇🏻 Shop my looks'
    const emails = extractEmail(source)
    console.log(emails)
  })
  describe('should find email for youtuber', () => {
    it(
      'should get email from nano',
      async () => {
        const kol = await prisma.kolInfo.findFirst({
          where: {
            platformAccount: 'mohamed_selva',
            platform: 'TIKTOK',
          },
        })
        expect(kol).toBeDefined()
        const email = await EmailService.getInstance().getKolEmail(kol)
        expect(email.email).toBeTypeOf('string')
      },
      { timeout: 1000 * 60 },
    )

    it('should get email by nano', async () => {
      const channelId = 'UCLGe0PxyRFWmXVGJKq_gGvw'
      const email = await getChannelEmailFromNano(KolPlatform.YOUTUBE, channelId)
      expect(email.email).eq('<EMAIL>')
    })
  })
})
