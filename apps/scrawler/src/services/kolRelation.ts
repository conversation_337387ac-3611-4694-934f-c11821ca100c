import { KolRelationInfo } from '@/types/response/kol'
import { prisma, ProjectKolAttitude, UserMembership } from '@repo/database'
import TimeAgo from 'javascript-time-ago'
import en from 'javascript-time-ago/locale/en'
import { getUserMembershipAndEnterprise } from './user'
TimeAgo.addLocale(en)
const relativeTime = new TimeAgo('en-US')

const getKolRelationForUser = async (kolId: string, userId: string): Promise<KolRelationInfo[]> => {
  const membership = await getUserMembershipAndEnterprise(userId)
  if (!membership) {
    return []
  }
  return (
    await Promise.all([
      (async () => {
        const likes = await getKolLikeRelation(kolId, membership)
        return likes
          .map((i) => {
            return {
              title: 'LIKED',
              properties: [
                {
                  key: 'Date',
                  value: relativeTime.format(i.createdAt).replace(' ago', ''),
                  type: 'text',
                  index: 0,
                },
                {
                  key: 'From',
                  value: (i.user.email ?? '').split('@')[0],
                  type: 'text',
                  index: 1,
                },
              ],
              timestamp: Math.floor(i.createdAt.valueOf() / 1000),
            }
          })
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, 3) as KolRelationInfo[]
      })(),
      (async () => {
        const contacts = await getKolContactRelation(kolId, membership)
        return contacts
          .map((i) => {
            return {
              title: 'CONTACTED',
              properties: [
                {
                  key: 'Date',
                  value: relativeTime.format(i.createdAt).replace(' ago', ''),
                  type: 'text',
                  index: 0,
                },
                {
                  key: 'From',
                  value: (i.user.email ?? '').split('@')[0],
                  type: 'text',
                  index: 1,
                },
              ],
              timestamp: Math.floor(i.createdAt.valueOf() / 1000),
            }
          })
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, 3) as KolRelationInfo[]
      })(),
      (async () => {
        const trackedRecords = await getKolPostedRelation(kolId, membership)
        return trackedRecords
          .map((i) => {
            const time = i.publishDate || i.createdAt
            const properties = [
              {
                key: 'Date',
                value: relativeTime.format(time).replace(' ago', ''),
                type: 'text',
                index: 0,
              },
              {
                key: 'From',
                value: (i.userGoogleSheet.user.email ?? '').split('@')[0],
                type: 'text',
                index: 1,
              },
              {
                key: 'Link',
                value: i.postLink,
                type: 'link',
                index: 3,
                href: i.postLink,
              },
            ]
            if (i.totalCost && i.totalCost > 0) {
              properties.push({
                key: 'Cost',
                value: '$' + (i.totalCost ?? ''),
                type: 'text',
                index: 2,
              })
            }
            return {
              title: 'POSTED',
              properties: properties,
              timestamp: Math.floor(time.valueOf() / 1000),
            }
          })
          .sort((a, b) => b.timestamp - a.timestamp) as KolRelationInfo[]
      })(),
    ])
  )
    .flat()
    .sort((a, b) => b.timestamp - a.timestamp)
}

const getKolLikeRelation = async (kolId: string, membership: UserMembership) => {
  const isEnterprise = membership?.type == 'ENTERPRISE' && !!membership.enterpriseId?.length
  if (isEnterprise) {
    return prisma.projectKol.findMany({
      where: {
        user: {
          membership: {
            enterpriseId: membership.enterpriseId,
          },
        },
        kolId: kolId,
        attitude: {
          in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.SUPERLIKE],
        },
      },
      include: {
        user: true,
      },
    })
  } else {
    return prisma.projectKol.findMany({
      where: {
        rateBy: membership.userId,
        kolId: kolId,
        attitude: {
          in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.SUPERLIKE],
        },
      },
      include: {
        user: true,
      },
    })
  }
}

const getKolContactRelation = async (kolId: string, membership: UserMembership) => {
  const isEnterprise = membership?.type == 'ENTERPRISE' && !!membership.enterpriseId?.length
  if (isEnterprise) {
    return prisma.kolRelationRecord.findMany({
      where: {
        enterpriseId: membership.enterpriseId!,
        kolId: kolId,
      },
      include: {
        user: true,
      },
    })
  } else {
    return prisma.kolRelationRecord.findMany({
      where: {
        userId: membership.userId,
        kolId: kolId,
      },
      include: {
        user: true,
      },
    })
  }
}

const getKolPostedRelation = async (kolId: string, membership: UserMembership) => {
  const isEnterprise = membership.type == 'ENTERPRISE' && !!membership.enterpriseId?.length
  return await prisma.publicationStatisticsSheetData.findMany({
    where: {
      userGoogleSheet: {
        user: {
          userId: isEnterprise ? undefined : membership.userId,
          membership: {
            enterprise: {
              id: isEnterprise ? membership.enterpriseId! : undefined,
            },
          },
        },
      },
      kolId: kolId,
    },
    include: {
      userGoogleSheet: {
        include: {
          user: true,
        },
      },
    },
  })
}

export { getKolRelationForUser }
