import { Static, Type } from '@sinclair/typebox'

export const EmailAuditLogReqSchame = Type.Object({
  kolId: Type.Optional(Type.String({ description: '博主ID' })),
  query: Type.Optional(Type.String({ description: '博主账号' })),
  platform: Type.Optional(Type.String({ description: '平台' })),
  page: Type.Optional(Type.Number({ description: '页码', default: 1 })),
  pageSize: Type.Optional(Type.Number({ description: '每页条数', default: 10 })),
})

export type EmailAuditLogReq = Static<typeof EmailAuditLogReqSchame>
