import { prisma, SystemNotifyPosition } from '@repo/database'
import { randomUUID } from 'crypto'

export interface SystemNotifyStruct {
  content: string
  valid: boolean
  position?: SystemNotifyPosition
}
const SystemNotifyService = {
  getSystemNotifies: async () => {
    const notifies = await prisma.systemNotify.findMany()
    return notifies
  },

  createSystemNotify: async (notify: SystemNotifyStruct) => {
    const id = randomUUID()
    await prisma.systemNotify.create({
      data: {
        ...notify,
        id: id.toString(),
      },
    })
  },

  updateSystemNotify: async (id: string, notify: SystemNotifyStruct) => {
    if (!id.length) {
      throw new Error('id is required')
    }
    await prisma.systemNotify.update({
      where: { id: id },
      data: {
        content: notify.content,
        valid: notify.valid,
        position: notify.position,
        updatedAt: new Date(),
      },
    })
  },

  deleteSystemNotify: async (id: string) => {
    await prisma.systemNotify.delete({
      where: { id },
    })
  },
}

export default SystemNotifyService
