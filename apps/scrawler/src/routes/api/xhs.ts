import { handleUnknownError } from '@/common/errorHandler'
import { serverErrorResponse, successResponse } from '@/common/response/response'
import { withAuth } from '@/middlewares/auth'
import xhsV1Service from '@/services/rapid/xhs/xhs.v1.service'
import assert from 'assert'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/convert-short-link', withAuth(), async (request, reply) => {
    const { shortLink } = request.query as { shortLink: string }
    assert(shortLink && shortLink.trim() !== '', new Error('shortLink is required'))

    try {
      const convertedLink = await xhsV1Service.convertShortLinkToNoteId(shortLink)
      return reply.status(200).send(successResponse(convertedLink))
    } catch (error: unknown) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
}

export default router
