import { DEBUG_TOKEN, REDIS_KEY_PREFIX } from '@/config/env.ts'
import { redis } from '@/infras/redis.ts'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/userToken', async (req, res) => {
    const { token, email } = (req as any).query
    if (token !== DEBUG_TOKEN) {
      return res.status(401).send('wrong token')
    }
    if (!email) {
      return res.status(400).send('email is not provided')
    }
    return res.status(200).send(await redis.hget(`${REDIS_KEY_PREFIX}users`, email))
  })
}

export default router
