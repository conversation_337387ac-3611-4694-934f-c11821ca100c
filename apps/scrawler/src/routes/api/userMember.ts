import { handleUnknownError } from '@/common/errorHandler'
import { notFoundResponse, serverErrorResponse, successResponse } from '@/common/response/response'
import { isAdmin, isAllowedEmail, withAuth } from '@/middlewares/auth'
import { MembershipService } from '@/services/membership.service'
import { UpdateMembershipParams } from '@/types/memberShip'
import { MemberType, prisma } from '@repo/database'
import dayjs from 'dayjs'
import 'dayjs/plugin/timezone.js'
import 'dayjs/plugin/utc.js'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post('/admin', withAuth(isAdmin), async (request, reply) => {
    const { emails, params, description } = request.body as {
      emails: string[]
      params: UpdateMembershipParams
      description?: string
    }

    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return reply.status(400).send(notFoundResponse('emails array is required'))
    }

    console.info('Received update request:', {
      emails,
      params: JSON.stringify(params),
      description,
    })

    const users = await prisma.userInfo.findMany({
      where: {
        email: {
          in: emails,
        },
      },
    })

    const validUsers = users.filter((user) => user.email !== null)
    const foundEmails = new Set(validUsers.map((user) => user.email as string))

    const notFoundEmails = emails.filter((email) => !foundEmails.has(email))

    if (notFoundEmails.length > 0) {
      return reply
        .status(400)
        .send(notFoundResponse(`以下用户不存在: ${notFoundEmails.join(', ')}`))
    }

    if (params.accountQuota) {
      params.accountQuota = Number(params.accountQuota)
      if (isNaN(params.accountQuota)) {
        return reply.status(400).send(notFoundResponse('Invalid accountQuota format'))
      }
    }

    if (params.effectiveAt) {
      const effectiveDate = new Date(params.effectiveAt)
      if (isNaN(effectiveDate.getTime())) {
        return reply.status(400).send(notFoundResponse('Invalid effectiveAt date format'))
      }
      params.effectiveAt = effectiveDate
    }

    if (params.expireAt) {
      const expireDate = new Date(params.expireAt)
      if (isNaN(expireDate.getTime())) {
        return reply.status(400).send(notFoundResponse('Invalid expireAt date format'))
      }
      params.expireAt = expireDate
    }

    if (params.effectiveAt && params.expireAt && params.effectiveAt > params.expireAt) {
      return reply.status(400).send(notFoundResponse('effectiveAt must be less than expireAt'))
    }
    try {
      const results = await Promise.allSettled(
        Array.from(foundEmails).map((email) =>
          MembershipService.getInstance().updateMembership(
            email,
            params,
            description,
            (request as any).user?.id || 'admin',
          ),
        ),
      )

      const successResults = results.filter(
        (r): r is PromiseFulfilledResult<any> => r.status === 'fulfilled',
      )
      const failedResults = results.filter(
        (r): r is PromiseRejectedResult => r.status === 'rejected',
      )

      return reply.status(200).send(
        successResponse({
          success: successResults.map((r) => r.value),
          failed: failedResults.map((r) => ({
            error: r.reason instanceof Error ? r.reason.message : 'Unknown error',
          })),
        }),
      )
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/emails', withAuth(isAdmin), async (request, reply) => {
    try {
      const emails = await MembershipService.getInstance().getEmails()
      return reply.status(200).send(successResponse(emails))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get<{
    Querystring: {
      type?: 'FREE' | 'PAID'
    }
  }>('/members', async (request, reply) => {
    try {
      const { type = 'PAID' } = request.query
      const users = await MembershipService.getInstance().getMembershipUsers(
        MemberType[type as keyof typeof MemberType],
      )
      return reply.send(successResponse(users))
    } catch (error) {
      console.error('Failed to fetch members:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  /**
   * 更新用户会员信息，开通会员
   */
  fastify.post('/members-info', async (request, reply) => {
    try {
      const { emails } = request.body as { emails: string[] }

      if (!emails || !Array.isArray(emails) || emails.length === 0) {
        return reply.status(400).send(notFoundResponse('emails array is required'))
      }

      // 查询所有匹配邮箱的用户信息
      const userInfos = await prisma.userInfo.findMany({
        where: {
          email: {
            in: emails.filter((email) => email && typeof email === 'string'),
          },
        },
        select: {
          userId: true,
          email: true,
          avatar: true,
          createdAt: true,
          membership: {
            select: {
              type: true,
              status: true,
              effectiveAt: true,
              expireAt: true,
              accountQuota: true,
              usedQuota: true,
              createdAt: true,
              updatedAt: true,
              timezone: true,
              cardSubscriptionStatus: true,
              cardSubscriptionEffectiveAt: true,
              cardSubscriptionExpireAt: true,
              enterprise: true,
            },
          },
        },
      })

      // 统计未找到的邮箱
      const foundEmails = new Set(userInfos.map((user) => user.email?.toLowerCase()))
      const notFoundEmails = emails.filter(
        (email) => email && !foundEmails.has(email.toLowerCase()),
      )

      console.log(
        `查询结果: 找到 ${userInfos.length} 个用户，${notFoundEmails.length} 个邮箱未找到`,
      )

      return reply.send(
        successResponse({
          users: userInfos,
          stats: {
            total: emails.length,
            found: userInfos.length,
            notFound: notFoundEmails.length,
            notFoundEmails,
          },
        }),
      )
    } catch (error) {
      console.error('查询用户信息失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.put('/timezone', withAuth(), async (request, reply) => {
    const { timezone } = request.body as { timezone: string }
    const { user } = request as any

    if (!timezone) {
      return reply.status(400).send(serverErrorResponse('timezone is required'))
    }

    if (!/^[A-Z][a-z]+\/[A-Za-z_]+$/.test(timezone)) {
      return reply
        .status(400)
        .send(
          serverErrorResponse(
            'Invalid timezone format. Must be in format: State/City (e.g. America/New_York)',
          ),
        )
    }

    try {
      const updatedMembership = await MembershipService.getInstance().updateTimezone(
        user.id,
        timezone,
      )

      console.log(`Timezone updated for user ${user.id} to ${timezone}`)

      return reply
        .status(200)
        .send(successResponse(updatedMembership, 'Timezone updated successfully'))
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid timezone')) {
        return reply.status(400).send(serverErrorResponse(error.message))
      }
      console.error(`Error updating timezone for user ${user.id}: ${error}`)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get<{
    Querystring: {
      email: string
      startDate?: string
      endDate?: string
    }
  }>('/quota/daily', async (request, reply) => {
    try {
      const { email, startDate, endDate } = request.query

      if (!email) {
        return reply.status(400).send(notFoundResponse('email is required'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }
      const dailyQuota = await MembershipService.getInstance().getDailyQuota(email, dateFilter)
      return reply.send(successResponse(dailyQuota))
    } catch (error) {
      console.error('Failed to fetch daily quota:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get<{
    Querystring: {
      email: string
      startDate?: string
      endDate?: string
    }
  }>('/quota/details', async (request, reply) => {
    try {
      const { email, startDate, endDate } = request.query

      if (!email) {
        return reply.status(400).send(notFoundResponse('email is required'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }
      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }
      const quotaDetails = await MembershipService.getInstance().getQuotaDetails(email, dateFilter)
      return reply.send(successResponse(quotaDetails))
    } catch (error) {
      console.error('Failed to fetch quota details:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/enterprise-info', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const enterpriseInfo = await MembershipService.getInstance().getEnterpriseInfo(user.id)
      return reply.send(successResponse(enterpriseInfo))
    } catch (error) {
      console.error('Failed to fetch enterprise info:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
  // 辅助函数
  function isValidDate(dateStr: string): boolean {
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date.getTime())
  }
  //获取订阅了卡片包月的用户列表
  fastify.get('/card-subscription-users', async (request, reply) => {
    try {
      const cardSubscriptionUsers = await MembershipService.getInstance().getCardSubscriptionUsers()
      return reply.send(successResponse(cardSubscriptionUsers))
    } catch (error) {
      console.error('Failed to fetch card subscription users:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 通过邮箱给用户订阅卡片包月
  fastify.post('/card-subscription', async (request, reply) => {
    try {
      const { emails, duration = 1 } = request.body as {
        emails: string[]
        duration?: number
      }

      // 验证邮箱数组
      if (!Array.isArray(emails) || emails.length === 0) {
        return reply.status(400).send(notFoundResponse('emails array is required'))
      }

      // 验证邮箱数量限制
      if (emails.length > 5) {
        return reply.status(400).send(notFoundResponse('Maximum 5 emails allowed per request'))
      }

      // 验证 duration 参数
      if (!Number.isInteger(duration)) {
        return reply.status(400).send(notFoundResponse('Duration must be a integer'))
      }

      const results = await Promise.allSettled(
        emails.map((email) => MembershipService.getInstance().subscribeCard(email, duration)),
      )

      // 处理结果
      const successResults = results
        .filter((r): r is PromiseFulfilledResult<any> => r.status === 'fulfilled')
        .map((r) => r.value)

      const failedResults = results
        .filter((r): r is PromiseRejectedResult => r.status === 'rejected')
        .map((r, index) => ({
          email: emails[index],
          error: r.reason instanceof Error ? r.reason.message : 'Unknown error',
        }))

      return reply.send(
        successResponse({
          success: successResults,
          failed: failedResults,
          stats: {
            total: emails.length,
            successful: successResults.length,
            failed: failedResults.length,
          },
        }),
      )
    } catch (error) {
      console.error('Failed to subscribe card:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
  // 获取用户信息
  fastify.get('/userInfo', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const userInfo = await prisma.userInfo.findUnique({
        where: {
          userId: user.id,
        },
        select: {
          userId: true,
          email: true,
          createdAt: true,
          membership: {
            include: {
              enterprise: true,
            },
          },
          stripeCustomer: true,
          stripeSubscriptions: {
            where: {
              OR: [{ status: 'ACTIVE' }, { status: 'PAST_DUE' }],
            },
          },
        },
      })
      const isAdminUser = await isAllowedEmail(user.id)
      return reply.send(
        successResponse({
          ...userInfo,
          membership: {
            ...userInfo?.membership,
          },
          isAdmin: isAdminUser,
        }),
      )
    } catch (error: any) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
}

export default router
