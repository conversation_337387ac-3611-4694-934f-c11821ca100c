import { successResponse } from '@/common/response/response'
import {
  FEATURE_DEVICE_BAN,
  INSTAGRAM_EMBEDDING_TYPE,
  TIKTOK_EMBEDDING_TYPE,
  TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT,
  TIK<PERSON><PERSON>_SEARCH_WORDS_VIDEOS_COUNT,
  TIKTOK_USE_HASHTAGS,
  TIKTOK_USE_SEARCH_WORDS,
  TIKTOK_USE_TAG_VIDEOS,
  YOUTUBE_EMBEDDING_TYPE,
  YOUTUBE_RELATED_VIDEO_COUNT,
  YOUTUBE_USER_VIDEO_COUNT,
} from '@/config/env'
import { isAdmin, withAuth } from '@/middlewares/auth'
import { EmailAuditLogReq, EmailAuditLogReqSchame } from '@/services/admin/emailAuditLog'
import SystemNotifyService from '@/services/admin/systemNotify'
import EmailService from '@/services/email'
import { getUpdatePageOn, setUpdatePageOn } from '@/services/updatePage'
import { EmailSourceType } from '@/types/email'
import { EmailVerifyStatus, KolPlatform, SystemNotifyPosition, prisma } from '@repo/database'
import { FastifyPluginAsync } from 'fastify'

interface Feature {
  code: string
  value: string | number
  description: string
}
const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/features', withAuth(isAdmin), async (request, reply) => {
    const global: Feature[] = [
      {
        code: 'YOUTUBE_EMBEDDING_TYPE',
        value: YOUTUBE_EMBEDDING_TYPE,
        description: 'Youtube 使用哪种 embedding 模型',
      },
      {
        code: 'TIKTOK_EMBEDDING_TYPE',
        value: TIKTOK_EMBEDDING_TYPE,
        description: 'Tiktok 使用哪种 embedding 模型',
      },
      {
        code: 'INSTAGRAM_EMBEDDING_TYPE',
        value: INSTAGRAM_EMBEDDING_TYPE,
        description: 'Instagram 使用哪种 embedding 模型',
      },
      {
        code: 'FEATURE_DEVICE_BAN',
        value: FEATURE_DEVICE_BAN,
        description: '是否启用同设备多免费账号登录限制',
      },
    ]
    const youtube: Feature[] = [
      {
        code: 'YOUTUBE_USER_VIDEO_COUNT',
        value: YOUTUBE_USER_VIDEO_COUNT,
        description: 'Youtube 搜索源头用户视频数量',
      },
      {
        code: 'YOUTUBE_RELATED_VIDEO_COUNT',
        value: YOUTUBE_RELATED_VIDEO_COUNT,
        description: 'Youtube 视频查询相关视频数量',
      },
    ]
    const tiktok: Feature[] = [
      {
        code: 'TIKTOK_USE_SEARCH_WORDS',
        value: TIKTOK_USE_SEARCH_WORDS,
        description: 'Tiktok 是否使用搜索关键词',
      },
      {
        code: 'TIKTOK_USE_HASHTAGS',
        value: TIKTOK_USE_HASHTAGS,
        description: 'Tiktok 是否使用搜索 hashtag',
      },
      {
        code: 'TIKTOK_USE_TAG_VIDEOS',
        value: TIKTOK_USE_TAG_VIDEOS,
        description: 'Tiktok 是否使用搜索 tag 视频',
      },
      {
        code: 'TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT',
        value: TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT,
        description: 'Tiktok 搜索 hashtag 视频数量',
      },
      {
        code: 'TIKTOK_SEARCH_WORDS_VIDEOS_COUNT',
        value: TIKTOK_SEARCH_WORDS_VIDEOS_COUNT,
        description: 'Tiktok 搜索关键词视频数量',
      },
    ]
    const data = {
      global,
      youtube,
      tiktok,
    }
    return reply.status(200).send(successResponse(data))
  })

  fastify.get('/switches', withAuth(isAdmin), async (request, reply) => {
    const isOn = await getUpdatePageOn()
    return reply.status(200).send(successResponse({ updatePageOn: isOn }))
  })

  fastify.put('/switches', withAuth(isAdmin), async (request, reply) => {
    const { updatePageOn } = request.body as { updatePageOn: boolean }
    if (updatePageOn !== undefined) {
      await setUpdatePageOn(updatePageOn)
    }

    return reply.status(200).send(successResponse({ updatePageOn }))
  })

  fastify.get('/system-notify', withAuth(isAdmin), async (request, reply) => {
    const notifies = await SystemNotifyService.getSystemNotifies()
    return reply.status(200).send(successResponse(notifies))
  })

  fastify.post('/system-notify', withAuth(isAdmin), async (request, reply) => {
    const {
      content,
      valid,
      position = SystemNotifyPosition.SIDEPANEL,
    } = request.body as {
      content: string
      valid: boolean
      position?: SystemNotifyPosition
    }
    await SystemNotifyService.createSystemNotify({ content, valid, position })
    return reply.status(200).send(successResponse({}))
  })

  fastify.put('/system-notify/:id', withAuth(isAdmin), async (request, reply) => {
    const { id } = request.params as { id: string }
    const { content, valid, position } = request.body as {
      content: string
      valid: boolean
      position?: SystemNotifyPosition
    }
    await SystemNotifyService.updateSystemNotify(id, { content, valid, position })
    return reply.status(200).send(successResponse({}))
  })

  fastify.delete('/system-notify/:id', withAuth(isAdmin), async (request, reply) => {
    const { id } = request.params as { id: string }
    await SystemNotifyService.deleteSystemNotify(id)
    return reply.status(200).send(successResponse({}))
  })

  // 1. 获取 UserSubmitEmail 表的数据并关联 kolInfo 的基础信息
  fastify.post('/user-submit-emails', withAuth(isAdmin), async (request, reply) => {
    const {
      status,
      platform,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = request.body as {
      status?: string
      platform?: KolPlatform
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
    }

    try {
      const where: any = {}

      if (status) {
        where.status = status as EmailVerifyStatus
      }

      if (platform) {
        where.kol = {
          platform: platform,
        }
      }

      const orderBy: any = {}
      orderBy[sortBy] = sortOrder

      const userSubmitEmails = await prisma.userSubmitEmail.findMany({
        where,
        include: {
          kol: {
            select: {
              id: true,
              title: true,
              platform: true,
              platformAccount: true,
              email: true,
              avatar: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          user: {
            include: {
              membership: {
                include: {
                  enterprise: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy,
      })

      return reply.status(200).send(successResponse(userSubmitEmails))
    } catch (error) {
      console.error('获取用户提交邮箱失败:', error)
      return reply.status(500).send(successResponse({ error: '获取用户提交邮箱失败' }))
    }
  })

  /**
   * 审核通过接口
   */
  fastify.post('/user-submit-emails/approve', withAuth(isAdmin), async (request, reply) => {
    const { id } = request.body as { id: string }

    try {
      const userSubmitEmail = await prisma.userSubmitEmail.findUnique({
        where: { id },
        include: { kol: true },
      })

      if (!userSubmitEmail) {
        return reply.status(404).send(successResponse({ error: '记录不存在' }))
      }

      if (!userSubmitEmail.kol) {
        return reply.status(400).send(successResponse({ error: '关联的博主不存在' }))
      }

      // audit log
      EmailService.getInstance().addEmailAuditLog(
        userSubmitEmail.userId,
        userSubmitEmail.kolId,
        userSubmitEmail.email,
        EmailSourceType.USER_SUBMIT,
      )
      // 审核则一定会更新邮箱
      await prisma.kolInfo.update({
        where: { id: userSubmitEmail.kolId },
        data: {
          email: userSubmitEmail.email,
          emailSource: EmailSourceType.USER_SUBMIT,
          emailUpdatedAt: new Date(),
        },
      })

      // 更新 UserSubmitEmail 记录状态为 APPROVED
      await prisma.userSubmitEmail.update({
        where: { id },
        data: {
          status: EmailVerifyStatus.APPROVED,
        },
      })

      return reply.status(200).send(successResponse({ message: '审核通过成功' }))
    } catch (error) {
      console.error('审核通过失败:', error)
      return reply.status(500).send(successResponse({ error: '审核通过失败' }))
    }
  })

  /**
   * 审核驳回接口
   */
  fastify.post('/user-submit-emails/reject', withAuth(isAdmin), async (request, reply) => {
    const { id } = request.body as { id: string }

    try {
      const userSubmitEmail = await prisma.userSubmitEmail.findUnique({
        where: { id },
      })

      if (!userSubmitEmail) {
        return reply.status(404).send(successResponse({ error: '记录不存在' }))
      }

      // 更新 UserSubmitEmail 记录状态为 REJECTED
      await prisma.userSubmitEmail.update({
        where: { id },
        data: {
          status: EmailVerifyStatus.REJECTED,
        },
      })

      return reply.status(200).send(successResponse({ message: '审核驳回成功' }))
    } catch (error) {
      console.error('审核驳回失败:', error)
      return reply.status(500).send(successResponse({ error: '审核驳回失败' }))
    }
  })

  fastify.post(
    '/email-audit-logs/list',
    {
      schema: {
        body: EmailAuditLogReqSchame,
      },
      ...withAuth(isAdmin),
    },
    async (request, reply) => {
      const { kolId, query, platform, page = 1, pageSize = 10 } = request.body as EmailAuditLogReq

      try {
        const where: any = {}

        if (kolId) {
          where.kolId = kolId
        }

        if (query) {
          where.kol = {
            platformAccount: {
              contains: query,
            },
          }
        }

        if (platform) {
          where.kol = {
            ...where.kol,
            platform: platform,
          }
        }

        console.log(where)
        const [total, logs] = await Promise.all([
          prisma.kolEmailAuditLog.count({
            where,
          }),
          prisma.kolEmailAuditLog.findMany({
            where,
            include: {
              kol: {
                select: {
                  id: true,
                  platform: true,
                  platformAccount: true,
                  title: true,
                  avatar: true,
                },
              },
              user: {
                select: {
                  userId: true,
                  email: true,
                  avatar: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            skip: (page - 1) * pageSize,
            take: pageSize,
          }),
        ])

        return reply.status(200).send(
          successResponse({
            data: logs,
            pagination: {
              total,
              page,
              pageSize,
              totalPages: Math.ceil(total / pageSize),
            },
          }),
        )
      } catch (error) {
        console.error('获取邮箱更新记录失败:', error)
        return reply.status(500).send(successResponse({ error: '获取邮箱更新记录失败' }))
      }
    },
  )
}

export default router
