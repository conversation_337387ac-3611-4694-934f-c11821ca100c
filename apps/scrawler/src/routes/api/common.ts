import {
  errorResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
} from '@/common/response/response'
import { DEFAULT_WELCOME_PAGE_URL } from '@/config/env'
import Sentry from '@/infras/sentry'
import { ContactItem } from '@/services/contact/contact'
import { newExternalLink } from '@/services/contact/contact.utils'
import { getUpdatePageOn } from '@/services/updatePage'
import { ContactReq, ContactReqSchema } from '@/types/request/common'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { prisma } from '@repo/database'
import Bluebird from 'bluebird'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/welcome-page', async (request, reply) => {
    const isOn = await getUpdatePageOn()
    if (isOn) {
      return reply.status(200).send(successResponse({ url: DEFAULT_WELCOME_PAGE_URL }))
    } else {
      return reply.status(404).send(errorResponse(StatusCodes.NOT_FOUND))
    }
  })

  fastify.get('/notifies', async (request, reply) => {
    try {
      const notifies = await prisma.systemNotify.findMany({
        where: {
          valid: true,
        },
      })
      return reply.status(200).send(successResponse(notifies))
    } catch (error) {
      console.error(error)
      return reply.status(200).send([])
    }
  })

  fastify.post(
    '/contact',
    {
      schema: {
        tags: ['common'],
        summary: '获取博主的联系信息',
        body: ContactReqSchema,
      },
    },
    async (request, reply) => {
      const { links } = request.body as ContactReq
      if (!links?.length) {
        return reply.status(200).send(successResponse([]))
      }
      const resultMap: Map<string, ContactItem[]> = new Map()
      await Bluebird.map(links, async (link: string) => {
        try {
          const extLink = await newExternalLink(link, link)
          if ('parse' in extLink && typeof extLink.parse === 'function') {
            resultMap.set(link, await extLink.parse())
          }
        } catch (err) {
          Sentry.captureException(err)
          console.log(`failed parse link: ${link}`)
          resultMap.set(link, [])
        }
      })
      return reply.status(200).send(successResponse(Object.fromEntries(resultMap)))
    },
  )

  fastify.get('/rapidApiStats', async (request, reply) => {
    const { date } = request.query as { date?: string }
    try {
      const stats = await RapidApiStats.getInstance().getDailyStats(date)
      return reply.status(200).send(successResponse(stats))
    } catch (error) {
      console.error('Failed to get rapid api stats:', error)
      return reply.status(500).send(serverErrorResponse('Internal server error'))
    }
  })
}
export default router
