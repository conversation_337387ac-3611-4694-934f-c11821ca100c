import TiktokApi from '@/api/tiktok'
import { handleUnknownError } from '@/common/errorHandler'
import {
  StatusCodes,
  errorResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { QuotaCost } from '@/enums/QuotaCost'
import { withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota, handleQuotaWithResponse } from '@/middlewares/quota'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { getProjectCandidatesWithPostsByTaskType } from '@/services/project'
import TaskService from '@/services/task.ts'
import { TtSearchInputBreakRequest } from '@/types/request/search'
import { TtFollowingListRequest } from '@/types/request/ttInfo'
import {
  TiktokBgmBreakRequest,
  TiktokWebListRequest,
  TtBgmBreakTaskParams,
  TtFollowingListTaskParams,
  TtSearchInputBreakTaskParams,
  TtWebListTaskParams,
} from '@/types/task'
import { KolPlatform, QuotaType, TaskReason, TaskType, prisma } from '@repo/database'
import assert, { AssertionError } from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // tiktok 的 bgm 爆破
  fastify.post<{ Body: TiktokBgmBreakRequest }>(
    '/bgm',
    withAuth(checkQuota(QuotaCost.TT_BGM_BREAK, QuotaType.TT_BGM_BREAK)),
    async (req: FastifyRequest<{ Body: TiktokBgmBreakRequest }>, reply) => {
      try {
        const { projectId, platform, musicUrl, reason } = req.body
        const user = (req as any).user

        assert(projectId, 'projectId is required!')
        assert(musicUrl && musicUrl.trim() !== '', 'musicUrl is required and cannot be empty!')
        assert(platform === KolPlatform.TIKTOK, 'platform must be TIKTOK!')
        assert(
          reason && (reason === TaskReason.SEARCH || reason === TaskReason.NEXT_PAGE),
          'reason is invalid!',
        )

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        const musicInfo = await TiktokApi.getInstance().getMusicInfo(musicUrl)
        assert(
          musicInfo && musicInfo.data && musicInfo.data.id !== null,
          `musicUrl '${musicUrl}' not found!`,
        )
        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.HASH_TAG_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const params: TtBgmBreakTaskParams = {
          projectId,
          platform,
          musicUrl,
          musicId: musicInfo.data.id,
          reason: reason || TaskReason.SEARCH,
          cursor,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.BGM_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // tiktok 的 url 爆破
  fastify.post<{ Body: TiktokWebListRequest }>(
    '/url-list',
    withAuth(handleQuotaWithResponse(QuotaCost.TT_WEB_LIST, QuotaType.TT_WEB_LIST)),
    async (req: FastifyRequest<{ Body: TiktokWebListRequest }>, reply) => {
      try {
        const { projectId, urls, platform, reason } = req.body
        const user = (req as any).user
        assert(projectId, 'projectId is required!')
        assert(
          urls && Array.isArray(urls) && urls.length > 0,
          'urls is required and cannot be empty!',
        )
        assert(platform === KolPlatform.TIKTOK, 'platform must be TIKTOK!')
        assert(reason && reason === TaskReason.SEARCH, 'reason is invalid!')

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        const params: TtWebListTaskParams = {
          projectId,
          platform,
          urls,
          reason: reason || TaskReason.SEARCH,
        }
        console.log(`[API] 创建 WEB_LIST 任务 ${projectId}`, JSON.stringify(params))
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.WEB_LIST,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
  //  创建 tiktok 的关键词搜索任务
  fastify.post<{ Body: TtSearchInputBreakRequest }>(
    '/searchInput',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateInputSearchQuota,
        QuotaType.TT_SEARCH_INPUT_BREAK,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtSearchInputBreakRequest }>, reply) => {
      try {
        const { projectId, searchInput, reason, sortType, publishTimeType } = req.body
        const { maxVideoCount } = req.body ?? 100
        const user = (req as any).user
        if (!projectId || !searchInput) {
          return reply
            .status(400)
            .send(
              errorResponse(
                StatusCodes.BAD_REQUEST,
                'BAD_REQUEST',
                'projectId,platform is required!',
              ),
            )
        }
        if (![0, 1, 3].includes(sortType)) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'sortType is invalid!'))
        }

        if (![0, 1, 7, 30, 90, 180].includes(publishTimeType)) {
          return reply
            .status(400)
            .send(
              errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'publishTimeType is invalid!'),
            )
        }

        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.SEARCH_INPUT_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const taskParams: TtSearchInputBreakTaskParams = {
          projectId,
          platform: KolPlatform.TIKTOK,
          searchInput,
          sortType,
          publishTimeType,
          cursor,
          maxVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.SEARCH_INPUT_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取searchInput的结果
  fastify.get('/searchInput', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, page = 1, pageSize = 100 } = request.query as any
    if (!projectId) {
      return reply.status(400).send(serverErrorResponse('projectId is required'))
    }
    if (isNaN(page) || isNaN(pageSize)) {
      return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
    }
    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.SEARCH_INPUT_BREAK,
        KolPlatform.TIKTOK,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 创建 tiktok 的关注列表任务
  fastify.post(
    '/followingList',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateFollowingBreakQuota,
        QuotaType.TT_FOLLOWING_LIST,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtFollowingListRequest }>, reply) => {
      const {
        projectId,
        uniqueId,
        reason = TaskReason.SEARCH,
        currentCount = 0,
        maxCount = 100,
      } = req.body as TtFollowingListRequest
      const user = (req as any).user
      if (!projectId || !uniqueId) {
        return reply.status(400).send(serverErrorResponse('projectId,uniqueId is required'))
      }
      try {
        const userDetail = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
        if (!userDetail || !userDetail.user || !userDetail.user.id) {
          throw new Error('该博主系统无法解析')
        }
        const testFollowingResponse = await TiktokApi.getInstance().getUserFollowing(
          userDetail.user.id,
          1,
          0,
        )
        if (!testFollowingResponse) {
          throw new Error('该博主的关注列表未开放')
        }
        let time = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.FOLLOWING_LIST,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          time = meta.time || 0
        }

        const taskParams: TtFollowingListTaskParams = {
          projectId,
          platform: KolPlatform.TIKTOK,
          uniqueId,
          userId: userDetail.user.id,
          currentTime: time,
          maxCount,
          currentCount,
        }

        console.log('taskParams', taskParams)
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWING_LIST,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // get tiktok 的关注列表结果
  fastify.get('/followingList', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, page, pageSize } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')
    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.FOLLOWING_LIST,
        KolPlatform.TIKTOK,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
}

export default router
