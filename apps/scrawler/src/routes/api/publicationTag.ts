import { serverErrorResponse, successResponse } from '@/common/response/response'
import { withAuth } from '@/middlewares/auth'
import { GetPublicationsParams, publicationTagService } from '@/services/publicationTag.service'
import {
  AddTagToPublicationParams,
  GetPublicationTagsResponse,
  RemoveTagFromPublicationParams,
} from '@/types/publicationTag'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  addTagToPublicationSchema,
  getPublicationTagsSchema,
  getTagPostLinksSchema,
  getTagPublicationsSchema,
  removeTagFromPublicationSchema,
} from '../schemas/publicationTag'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post<{
    Body: AddTagToPublicationParams
  }>(
    '/',
    {
      schema: addTagToPublicationSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId, tagId } = request.body
        const result = await publicationTagService.addTagToPublication(
          user.id,
          publicationId,
          tagId,
        )
        return reply.send(successResponse(result, '标签添加成功'))
      } catch (error) {
        return reply
          .status(500)
          .send(serverErrorResponse(error instanceof Error ? error.message : '服务器内部错误'))
      }
    },
  )

  fastify.delete<{
    Body: RemoveTagFromPublicationParams
  }>(
    '/',
    {
      schema: removeTagFromPublicationSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId, tagId } = request.body
        await publicationTagService.removeTagFromPublication(user.id, publicationId, tagId)
        return reply.send(successResponse(null, '标签移除成功'))
      } catch (error) {
        return reply
          .status(500)
          .send(serverErrorResponse(error instanceof Error ? error.message : '服务器内部错误'))
      }
    },
  )

  fastify.get<{
    Params: { publicationId: string }
  }>(
    '/:publicationId',
    {
      schema: getPublicationTagsSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params
        const tags: GetPublicationTagsResponse = await publicationTagService.getPublicationTags(
          user.id,
          publicationId,
        )
        return reply.send(successResponse(tags, '获取投放数据标签列表成功'))
      } catch (error) {
        return reply
          .status(500)
          .send(serverErrorResponse(error instanceof Error ? error.message : '服务器内部错误'))
      }
    },
  )

  fastify.post<{
    Body: GetPublicationsParams
  }>(
    '/publications',
    {
      schema: getTagPublicationsSchema,
      ...withAuth(),
    },
    async (
      request: FastifyRequest<{
        Body: GetPublicationsParams
      }>,
      reply,
    ) => {
      const user = (request as any).user
      const result = await publicationTagService.getTagPublications(user.id, request.body)
      return reply.send(successResponse(result))
    },
  )

  fastify.get<{
    Params: { id: string }
  }>(
    '/:id/post-links',
    {
      schema: getTagPostLinksSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const { id } = request.params
      const result = await publicationTagService.getAllPostLinks(user.id, id)
      return reply.send(successResponse(result))
    },
  )
}

export default router
