import { uploadFileByBuffer } from '@/api/aliyun'
import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import {
  errorResponse,
  successResponse,
  successResponseWithoutData,
} from '@/common/response/response'
import { HOST } from '@/config/env'
import { FullResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import { kolNoteService } from '@/services/tag_and_note/kolNote'
import { kolTagService } from '@/services/tag_and_note/kolTag'
import { tagService } from '@/services/tag_and_note/tag'
import { getUserAndEntepriseId, isEnterpriseAdmin } from '@/utils/auth'
import { exportToExcel } from '@/utils/excel'
import { FastifyPluginAsync } from 'fastify'
import {
  AddSingleKolTagReq,
  AddSingleKolTagReqSchema,
  CreateCreatorTagReq,
  CreateCreatorTagReqSchema,
  DeleteCreatorTagReq,
  DeleteCreatorTagReqSchema,
  DeleteKolTagReq,
  DeleteKolTagReqSchema,
  DeleteSingleKolTagReq,
  DeleteSingleKolTagReqSchema,
  GetKolTagAndNoteReq,
  GetKolTagAndNoteReqSchema,
  GetKolTagAndNoteResSchema,
  ListKolTagReq,
  ListKolTagReqSchema,
  ListKolTagResSchema,
  ListTagsResSchema,
  ListTagsWithCountResSchema,
  UpdateCreatorTagReq,
  UpdateCreatorTagReqSchema,
  UpdateKolNoteReq,
  UpdateKolNoteReqSchema,
  UpdateKolTagReq,
  UpdateKolTagReqSchema,
} from '../schemas/tagAndNote'

const ENTERPRISE_ADMIN_ONLY_ERROR = 'Only enterprise admin can access this endpoint'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // tags 查
  fastify.get(
    '/list',
    {
      schema: {
        summary: '获取所有标签列表',
        description: '获取所有标签列表，如果是用户则返回个人的，企业则返回企业共享的',
        tags: ['tagAndNote'],
        response: FullResponseSchema(ListTagsResSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const tags = await tagService.getAllTags(userId, enterpriseId)
      return reply.send(successResponse(tags))
    },
  )

  // tags 查（带数量）
  fastify.get(
    '/listWithCount',
    {
      schema: {
        summary: '获取所有标签列表和对应的使用数量',
        description:
          '获取所有标签列表，如果是用户则返回个人的，企业则返回企业共享的，包含对应的使用数量',
        tags: ['tagAndNote'],
        response: FullResponseSchema(ListTagsWithCountResSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const tags = await tagService.getAllTagsWithUseCount(userId, enterpriseId)
      return reply.send(successResponse(tags))
    },
  )

  // tag 增
  fastify.post(
    '/tag',
    {
      schema: {
        summary: '新增标签',
        description: '新增标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: CreateCreatorTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      if (enterpriseId && !isEnterpriseAdmin(userId)) {
        throwError(StatusCodes.FORBIDDEN, ENTERPRISE_ADMIN_ONLY_ERROR)
      }
      const { name, color } = request.body as CreateCreatorTagReq

      const tag = await tagService.addTag(userId, enterpriseId, name, color)

      return reply.send(successResponse(tag))
    },
  )

  // tag 改
  fastify.put(
    '/tag',
    {
      schema: {
        summary: '更新标签',
        description: '更新标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: UpdateCreatorTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      if (enterpriseId && !isEnterpriseAdmin(userId)) {
        throwError(StatusCodes.FORBIDDEN, ENTERPRISE_ADMIN_ONLY_ERROR)
      }
      const { id, name, color } = request.body as UpdateCreatorTagReq

      const tag = await tagService.updateTag(id, name, color, userId, enterpriseId)

      return reply.send(successResponse(tag))
    },
  )

  // tag 删
  fastify.delete(
    '/tag',
    {
      schema: {
        summary: '删除标签',
        description: '删除标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: DeleteCreatorTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      if (enterpriseId && !isEnterpriseAdmin(userId)) {
        throwError(StatusCodes.FORBIDDEN, ENTERPRISE_ADMIN_ONLY_ERROR)
      }
      const { id } = request.body as DeleteCreatorTagReq

      await tagService.deleteTag(id, userId, enterpriseId)

      return reply.send(successResponseWithoutData())
    },
  )

  // kol tags & note 查
  fastify.get(
    '/kol',
    {
      schema: {
        summary: '获取指定博主标签和 note',
        description: '获取指定博主的标签和 note',
        tags: ['tagAndNote'],
        response: FullResponseSchema(GetKolTagAndNoteResSchema),
        querystring: GetKolTagAndNoteReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { kolId } = request.query as GetKolTagAndNoteReq

      const result = await kolTagService.getKolTagAndNote(kolId, userId, enterpriseId)
      return reply.send(successResponse(result))
    },
  )

  // kol tags 修改
  fastify.post(
    '/kol/tag',
    {
      schema: {
        summary: '更新指定博主标签',
        description: '更新指定博主的标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(GetKolTagAndNoteResSchema),
        body: UpdateKolTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { kolId, tagIds } = request.body as UpdateKolTagReq

      const tags = await kolTagService.updateKolTags(kolId, tagIds, userId, enterpriseId)

      return reply.send(successResponse(tags))
    },
  )

  // kol tag 单个添加
  fastify.put(
    '/kol/tag',
    {
      schema: {
        summary: '为指定博主添加单个标签',
        description: '为指定博主添加单个标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: AddSingleKolTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { kolId, tagId } = request.body as AddSingleKolTagReq

      const tag = await kolTagService.addSingleTag(kolId, tagId, userId, enterpriseId)

      return reply.send(successResponse(tag))
    },
  )

  // kol tag 单个删除
  fastify.delete(
    '/kol/tag',
    {
      schema: {
        summary: '删除指定博主的单个标签',
        description: '删除指定博主的单个标签',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: DeleteSingleKolTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { kolId, tagId } = request.body as DeleteSingleKolTagReq

      await kolTagService.deleteSingleTag(kolId, tagId, userId, enterpriseId)

      return reply.send(successResponseWithoutData())
    },
  )

  // kol note 改
  fastify.post(
    '/kol/note',
    {
      schema: {
        summary: '更新指定博主 note',
        description: '更新指定博主的 note',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: UpdateKolNoteReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { kolId, note } = request.body as UpdateKolNoteReq

      await kolNoteService.upsert(kolId, note, userId, enterpriseId)

      return reply.send(successResponseWithoutData())
    },
  )

  // kol tags & notes 分页查询
  fastify.post(
    '/kol/list',
    {
      schema: {
        summary: '分页查询指定博主标签和 note',
        description: '分页查询指定博主标签和 note',
        tags: ['tagAndNote'],
        response: FullResponseSchema(ListKolTagResSchema),
        body: ListKolTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      const { page, pageSize, tags } = request.body as ListKolTagReq

      const KolWithTagsAndNote = await kolTagService.list(
        page,
        pageSize,
        tags,
        userId,
        enterpriseId,
      )

      return reply.send(successResponse(KolWithTagsAndNote))
    },
  )

  fastify.delete(
    '/kol',
    {
      schema: {
        summary: '删除指定博主记录',
        description: '删除指定博主记录，包括标签和 note',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
        body: DeleteKolTagReqSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)
      if (enterpriseId && !isEnterpriseAdmin(userId)) {
        throwError(StatusCodes.FORBIDDEN, ENTERPRISE_ADMIN_ONLY_ERROR)
      }
      const { kolId } = request.body as DeleteKolTagReq

      await kolTagService.deleteKolRecord(kolId, userId, enterpriseId)

      return reply.send(successResponseWithoutData())
    },
  )

  fastify.post(
    '/export',
    {
      schema: {
        summary: '导出博主标签和备注',
        description: '导出博主标签和备注数据到Excel文件',
        tags: ['tagAndNote'],
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { userId, enterpriseId } = await getUserAndEntepriseId(request)

      try {
        // 获取导出数据
        const exportData = await kolTagService.exportKolTagsAndNotes(userId, enterpriseId)

        if (exportData.length === 0) {
          return reply.status(200).send(
            successResponse({
              url: null,
              message: '暂无数据可导出',
            }),
          )
        }

        // 定义Excel列
        const columns = [
          { header: 'ID', key: 'id', width: 35 },
          { header: '名称', key: 'platformAccount', width: 25, hyperlink: 'link' },
          { header: '平台', key: 'platform', width: 10 },
          { header: '标签', key: 'tags', width: 30 },
          { header: '备注', key: 'notes', width: 50 },
        ]

        // 准备Excel表格
        const sheets = [
          {
            name: 'KOL Tags & Notes',
            data: exportData,
            columns,
            styles: {
              headerStyle: {
                font: { bold: true },
                fill: {
                  type: 'pattern' as const,
                  pattern: 'solid' as const,
                  fgColor: { argb: 'FFE0E0E0' },
                },
              },
            },
          },
        ]

        // 生成Excel文件
        const buffer = await exportToExcel(sheets)

        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const fileName = `kol-tags-notes-${userId}-${timestamp}.xlsx`
        const filePath = `excel/${fileName}`

        // 上传到OSS
        await uploadFileByBuffer(filePath, buffer)

        return reply.status(200).send(
          successResponse({
            url: `${HOST ?? ''}/files/excel/${fileName}`,
            message: '导出成功',
          }),
        )
      } catch (error) {
        console.error('[Export KOL Tags & Notes] Error during export process:', error)
        return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, 'Export failed'))
      }
    },
  )
}

export default router
