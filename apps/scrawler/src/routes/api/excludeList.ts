import { errorResponse, StatusCodes, successResponse } from '@/common/response/response'
import { HOST } from '@/config/env'
import { CommonResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import * as ExcludeListService from '@/services/excludeList'
import { MembershipService } from '@/services/membership.service'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  /**
   * 用户上传列表
   */
  fastify.post('/upload', withAuth(), async (request, reply) => {
    const { links } = (request as any).body
    const user = (request as any).user

    if (!links || links.length == 0) {
      return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST))
    }
    const count = links.split('\n').length
    if (count == 0 || count > 1000) {
      return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST))
    }
    const result = await ExcludeListService.uploadList(links, user.id)
    return reply.status(200).send(successResponse(result))
  })

  /**
   * 用户查看统计信息
   */
  fastify.get('/statistic', withAuth(), async (request, reply) => {
    const { user } = request as any
    return reply.status(200).send(successResponse(await ExcludeListService.getStatistic(user.id)))
  })

  /**
   * 用户清空列表
   */
  fastify.delete('/', withAuth(), async (request, reply) => {
    const { user } = request as any
    await ExcludeListService.clearList(user.id)
    return reply.status(200).send(successResponse('success'))
  })

  /**
   * 导出去重列表
   */
  fastify.post(
    '/export',
    {
      schema: {
        summary: '导出去重列表',
        tags: ['excludeList'],
        description: '导出去重列表',
        response: CommonResponseSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any

      try {
        const membership = await MembershipService.getInstance().getMembership(user.id)
        if (!membership) {
          return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST))
        }
        // 获取去重列表数据
        const excludeList = await ExcludeListService.getExcludeList(user.id)

        if (!excludeList.length) {
          return reply.status(200).send(
            successResponse({
              url: null,
            }),
          )
        }

        console.log('excludeList', excludeList.length)
        const fileName = await ExcludeListService.exportExcludeListToOSS(excludeList, membership)

        return reply.status(200).send(
          successResponse({
            url: `${HOST ?? ''}/files/excel/${fileName}`,
          }),
        )
      } catch (error) {
        console.error('[Export Exclude List] Error during export process:', error)
        return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, 'Export failed'))
      }
    },
  )
}

export default router
