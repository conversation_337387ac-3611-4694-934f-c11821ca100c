import { handleUnknownError } from '@/common/errorHandler'
import { serverErrorResponse, successResponse } from '@/common/response/response'
import Sentry from '@/infras/sentry'
import { withAuth } from '@/middlewares/auth'
import * as strategyTemplateService from '@/services/strategyTemplate.service'
import {
  CreateStrategyTemplatePayload,
  UpdateStrategyTemplatePayload,
  UpdateTemplateStatusPayload,
} from '@/types/strategyTemplate'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify): Promise<void> => {
  fastify.post<{ Body: CreateStrategyTemplatePayload }>('/', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const createPayload = request.body
      const newTemplate = await strategyTemplateService.createStrategyTemplate(
        user.id,
        createPayload,
      )
      return reply.status(201).send(successResponse(newTemplate, '策略模板创建成功'))
    } catch (error: any) {
      console.error('Failed to create strategy template:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.put<{ Body: UpdateStrategyTemplatePayload }>('/', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const updatePayload = request.body
      const updatedTemplate = await strategyTemplateService.updateStrategyTemplate(
        user.id,
        updatePayload,
      )
      return reply.send(successResponse(updatedTemplate, '策略模板更新成功'))
    } catch (error: any) {
      console.error('Failed to update strategy template:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const template = await strategyTemplateService.getStrategyTemplate(user.id)
      return reply.send(successResponse(template, '获取策略模板成功'))
    } catch (error: any) {
      console.error('Failed to get strategy template:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 更新策略模板状态（启用/禁用）
  fastify.patch<{ Body: UpdateTemplateStatusPayload }>(
    '/status',
    withAuth(),
    async (request, reply) => {
      try {
        const user = (request as any).user
        const statusPayload = request.body
        const updatedTemplate = await strategyTemplateService.updateTemplateStatus(
          user.id,
          statusPayload,
        )
        const statusMessage = statusPayload.isActive ? '启用' : '禁用'
        return reply.send(successResponse(updatedTemplate, `策略模板${statusMessage}成功`))
      } catch (error: any) {
        console.error('Failed to update strategy template status:', error)
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
}

export default router
