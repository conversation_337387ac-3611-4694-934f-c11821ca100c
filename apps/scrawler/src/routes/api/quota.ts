import { uploadFileByBuffer } from '@/api/aliyun'
import { handleUnknownError } from '@/common/errorHandler'
import { notFoundResponse, serverErrorResponse, successResponse } from '@/common/response/response'
import { HOST } from '@/config/env'
import { withAuth } from '@/middlewares/auth'
import QuotaService from '@/services/quota.service'
import { exportToExcel } from '@/utils/excel'
import { prisma } from '@repo/database'
import assert from 'assert'
import dayjs from 'dayjs'
import 'dayjs/plugin/timezone.js'
import 'dayjs/plugin/utc.js'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get<{
    Querystring: {
      startDate?: string
      endDate?: string
      page?: number
      pageSize?: number
      userId?: string
    }
  }>('/dailyUseage', withAuth(), async (request, reply) => {
    try {
      const { startDate, endDate, page, pageSize, userId } = request.query
      const user = (request as any).user

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }

      if (userId) {
        const dailyQuota = await QuotaService.getDailyQuota(userId, dateFilter, { page, pageSize })
        return reply.send(successResponse(dailyQuota))
      } else {
        const dailyQuota = await QuotaService.getDailyQuota(user.id, dateFilter, { page, pageSize })
        return reply.send(successResponse(dailyQuota))
      }
    } catch (error) {
      console.error('Failed to fetch daily quota:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get<{
    Querystring: {
      startDate?: string
      endDate?: string
      page?: number
      pageSize?: number
      userId?: string
    }
  }>('/useageDetails', withAuth(), async (request, reply) => {
    try {
      const { startDate, endDate, page, pageSize, userId } = request.query
      const user = (request as any).user

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }
      if (userId) {
        const quotaDetails = await QuotaService.getQuotaDetails(userId, dateFilter, {
          page,
          pageSize,
        })
        return reply.send(successResponse(quotaDetails))
      } else {
        const quotaDetails = await QuotaService.getQuotaDetails(user.id, dateFilter, {
          page,
          pageSize,
        })
        return reply.send(successResponse(quotaDetails))
      }
    } catch (error) {
      console.error('Failed to fetch quota details:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取企业每日配额统计
  fastify.get<{
    Querystring: {
      enterpriseId: string
      startDate?: string
      endDate?: string
      page?: number
      pageSize?: number
    }
  }>('/enterprise-dailyUseage', withAuth(), async (request, reply) => {
    try {
      const { enterpriseId, startDate, endDate, page, pageSize } = request.query

      if (!enterpriseId) {
        return reply.status(400).send(notFoundResponse('enterpriseId is required'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }

      const enterpriseQuota = await QuotaService.getEnterpriseDailyQuota(enterpriseId, dateFilter, {
        page,
        pageSize,
      })
      return reply.send(successResponse(enterpriseQuota))
    } catch (error) {
      console.error('Failed to fetch enterprise daily quota:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取企业配额详情
  fastify.get<{
    Querystring: {
      enterpriseId: string
      startDate?: string
      endDate?: string
      page?: number
      pageSize?: number
    }
  }>('/enterprise-useageDetails', withAuth(), async (request, reply) => {
    try {
      const { enterpriseId, startDate, endDate, page, pageSize } = request.query

      if (!enterpriseId) {
        return reply.status(400).send(notFoundResponse('enterpriseId is required'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        // 将东八区 00:00:00 转换为 UTC
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      }
      if (endDate) {
        // 将东八区 23:59:59 转换为 UTC
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      }

      const quotaDetails = await QuotaService.getEnterpriseQuotaDetails(enterpriseId, dateFilter, {
        page,
        pageSize,
      })
      return reply.send(successResponse(quotaDetails))
    } catch (error) {
      console.error('Failed to fetch enterprise quota details:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 导出企业用户的quotaLogs
  fastify.get<{
    Querystring: {
      enterpriseId: string
      startDate?: string
      endDate?: string
    }
  }>('/enterprise/export-quota-logs', withAuth(), async (request, reply) => {
    try {
      const { enterpriseId, startDate, endDate } = request.query

      if (!enterpriseId) {
        return reply.status(400).send(notFoundResponse('enterpriseId is required'))
      }
      const enterprise = await prisma.enterprise.findUnique({
        where: { id: enterpriseId },
        select: { name: true },
      })

      if (!enterprise) {
        return reply.status(404).send(notFoundResponse('Enterprise not found'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      } else {
        // 默认60天前
        dateFilter.gte = dayjs()
          .tz('Asia/Shanghai')
          .subtract(60, 'day')
          .startOf('day')
          .utc()
          .toDate()
      }
      if (endDate) {
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      } else {
        dateFilter.lte = dayjs().tz('Asia/Shanghai').endOf('day').utc().toDate()
      }

      const quotaLogs = await QuotaService.exportEnterpriseQuotaLogs(enterpriseId, dateFilter)

      const dateStr = dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD-HH-mm-ss')
      const fileName = sanitizeFileName(`${enterprise.name}_${dateStr}`)

      const sheets = [
        {
          name: 'Quota Logs',
          data: quotaLogs,
          columns: [
            { header: '时间', key: 'time', width: 20 },
            { header: '类型', key: 'type', width: 15 },
            { header: '配额', key: 'quota', width: 15 },
            { header: '描述', key: 'description', width: 40 },
            { header: '邮箱', key: 'email', width: 30 },
            { header: '企业名称', key: 'enterpriseName', width: 20 },
          ],
        },
      ]

      const buffer = await exportToExcel(sheets)
      const filePath = `excel/${fileName}.xlsx`
      await uploadFileByBuffer(filePath, buffer)

      return reply.send(
        successResponse({
          url: `${HOST ?? ''}/files/${filePath}`,
        }),
      )
    } catch (error) {
      console.error('Failed to export quota logs:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 导出用户配额日志
  fastify.get<{
    Querystring: {
      userId?: string
      startDate?: string
      endDate?: string
    }
  }>('/user/export-quota-logs', withAuth(), async (request, reply) => {
    try {
      const { startDate, endDate, userId } = request.query
      const user = (request as any).user

      const userInfo = await prisma.userInfo.findUnique({
        where: { userId: userId || user.id },
        select: { email: true },
      })

      if (!userInfo) {
        return reply.status(404).send(notFoundResponse('User not found'))
      }

      if (startDate && !isValidDate(startDate)) {
        return reply.status(400).send(notFoundResponse('Invalid startDate format. Use YYYY-MM-DD'))
      }
      if (endDate && !isValidDate(endDate)) {
        return reply.status(400).send(notFoundResponse('Invalid endDate format. Use YYYY-MM-DD'))
      }

      const dateFilter: { gte?: Date; lte?: Date } = {}
      if (startDate) {
        dateFilter.gte = dayjs.tz(`${startDate} 00:00:00`, 'Asia/Shanghai').utc().toDate()
      } else {
        // 默认60天前
        dateFilter.gte = dayjs()
          .tz('Asia/Shanghai')
          .subtract(60, 'day')
          .startOf('day')
          .utc()
          .toDate()
      }
      if (endDate) {
        dateFilter.lte = dayjs.tz(`${endDate} 23:59:59`, 'Asia/Shanghai').utc().toDate()
      } else {
        dateFilter.lte = dayjs().tz('Asia/Shanghai').endOf('day').utc().toDate()
      }

      const quotaLogs = await QuotaService.exportUserQuotaLogs(userId || user.id, dateFilter)
      const dateStr = dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD-HH-mm-ss')
      const fileName = sanitizeFileName(`${userInfo.email}_${dateStr}`)

      const sheets = [
        {
          name: 'Quota Logs',
          data: quotaLogs,
          columns: [
            { header: '时间', key: 'time', width: 20 },
            { header: '类型', key: 'type', width: 15 },
            { header: '配额', key: 'quota', width: 15 },
            { header: '描述', key: 'description', width: 40 },
            { header: '邮箱', key: 'email', width: 30 },
          ],
        },
      ]

      const buffer = await exportToExcel(sheets)
      const filePath = `excel/${fileName}.xlsx`
      await uploadFileByBuffer(filePath, buffer)

      return reply.send(
        successResponse({
          url: `${HOST ?? ''}/files/${filePath}`,
        }),
      )
    } catch (error) {
      console.error('Failed to export quota logs:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取用户配额信息
  fastify.get('/user-info', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const quotaInfo = await QuotaService.getUserQuotaInfo(user.id)
      return reply.send(successResponse(quotaInfo))
    } catch (err: any) {
      console.error('Failed to fetch user quota info:', err)
      if (err.message === 'User or membership not found') {
        return reply.status(404).send(notFoundResponse(err.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(err)))
    }
  })

  // 获取企业配额信息
  fastify.get('/enterprise-info/:enterpriseId', withAuth(), async (request, reply) => {
    try {
      const { enterpriseId } = request.params as { enterpriseId: string }
      const quotaInfo = await QuotaService.getEnterpriseQuotaInfo(enterpriseId)
      return reply.send(successResponse(quotaInfo))
    } catch (err: any) {
      console.error('Failed to fetch enterprise quota info:', err)
      if (err.message === 'Enterprise not found') {
        return reply.status(404).send(notFoundResponse(err.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(err)))
    }
  })

  // 个人配额的用量统计
  fastify.get('/user/quota-usage-stats', withAuth(), async (request, reply) => {
    try {
      const { userId } = request.query as { userId?: string }
      const user = (request as any).user
      if (!userId && !user.id) {
        return reply.status(400).send(notFoundResponse('userId is required'))
      }
      const quotaUsageStats = await QuotaService.getUserQuotaUsageStats(userId || user.id)
      return reply.send(successResponse(quotaUsageStats))
    } catch (error) {
      console.error('Failed to fetch user quota usage stats:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/enterprise/quota-usage-stats/:enterpriseId', withAuth(), async (request, reply) => {
    const { enterpriseId } = request.params as { enterpriseId: string }
    assert(enterpriseId, 'enterpriseId is required')
    const quotaUsageStats = await QuotaService.getEnterpriseQuotaUsageStats(enterpriseId)
    return reply.send(successResponse(quotaUsageStats))
  })
  function isValidDate(dateStr: string): boolean {
    const date = new Date(dateStr)
    return date instanceof Date && !isNaN(date.getTime())
  }

  function sanitizeFileName(name: string): string {
    return name
      .replace(/[<>:"/\\|?*#]/g, '_') // 替换Windows非法字符和URL特殊字符
      .replace(/\s+/g, '_') // 替换空格为下划线
      .replace(/[^\x00-\x7F]/g, '') // 移除非ASCII字符
      .replace(/_+/g, '_') // 将多个连续下划线替换为单个
      .trim()
  }
}

export default router
