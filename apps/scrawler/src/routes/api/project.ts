import { handleUnknownError } from '@/common/errorHandler'
import {
  errorResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
} from '@/common/response/response'
import Sentry from '@/infras/sentry'
import { needSupabaseAuth, withAuth } from '@/middlewares/auth'
import { versionStatistic } from '@/middlewares/versionStatistic'
import {
  createProjectWithUser,
  deleteProject,
  findProjectById,
  getAllAvailableProjectByUser,
  hasAuthForProject,
  resetProject,
  terminateProjectByTaskType,
} from '@/services/project'
import { ProjectConfig } from '@/types/project'
import { prisma, TaskType } from '@repo/database'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { title, description } = request.body as any
        if (!title) {
          return reply.status(400).send({ error: 'title is required' })
        }
        const user = (request as any).user
        const project = await createProjectWithUser(user.id, {
          title,
          description,
        })
        return reply.status(201).send(project)
      } catch (error) {
        return reply.status(500).send({ error: 'An error occurred while creating the project.' })
      }
    },
  )

  fastify.get(
    '/:id',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { id } = request.params as any
        const { user } = request as any

        const project = await findProjectById(id)

        if (!project) {
          return reply.status(404).send({ error: 'Project not found or deleted' })
        }

        if (!(await hasAuthForProject(user.id, id))) {
          return reply.status(403).send({ error: 'Forbidden' })
        }

        return reply.send(project)
      } catch (error) {
        return reply.status(500).send({ error: 'An error occurred while retrieving the project.' })
      }
    },
  )

  fastify.get('/', withAuth(versionStatistic), async (request, reply) => {
    try {
      const { user } = request as any
      const projects = await getAllAvailableProjectByUser(user.id)
      return reply.send({
        success: true,
        data: projects,
      })
    } catch (error) {
      return reply.status(500).send({ error: 'An error occurred while retrieving the projects.' })
    }
  })
  /**
   * @description 根据任务类型终止任务
   */

  fastify.post(
    '/terminateByTaskType',
    { preHandler: needSupabaseAuth },
    async (
      request: FastifyRequest<{
        Body: { projectId: string; taskType: TaskType }
      }>,
      reply,
    ) => {
      try {
        const { projectId, taskType } = request.body
        const user = (request as any).user
        if (!projectId) {
          return reply.status(400).send(serverErrorResponse('projectId is required'))
        }
        if (!taskType || !Object.values(TaskType).includes(taskType)) {
          return reply.status(400).send(serverErrorResponse('taskType is required'))
        }
        if (!(await hasAuthForProject(user.id, projectId))) {
          return reply.status(403).send(serverErrorResponse('Forbidden'))
        }
        await terminateProjectByTaskType(projectId, taskType)
        return reply.status(200).send(successResponse('terminate project by task type success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.delete('/:projectId', { preHandler: needSupabaseAuth }, async (request, reply) => {
    try {
      const { projectId } = request.params as any
      const { user } = request as any
      await deleteProject(projectId, user.id)
      return reply.status(200).send(successResponse('delete project success'))
    } catch (error) {
      return reply.status(500).send(error)
    }
  })
  fastify.post('/reset', { preHandler: needSupabaseAuth }, async (request, reply) => {
    const { projectId } = request.body as any
    const { user } = request as any
    if (!projectId) {
      return reply.status(400).send({ error: 'projectId is required' })
    }
    if (!(await hasAuthForProject(user.id, projectId))) {
      return reply.status(403).send({ error: 'Forbidden' })
    }
    try {
      await resetProject(projectId)
      return reply.status(200).send(successResponse('reset project success'))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.patch('/:projectId', { preHandler: needSupabaseAuth }, async (request, reply) => {
    try {
      const { projectId } = request.params as any
      const config = request.body as ProjectConfig
      if (!projectId) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, '', ''))
      }
      const project = await prisma.project.findUnique({
        where: {
          id: projectId,
          deletedAt: null,
        },
      })
      if (!project) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, '', 'Project not found'))
      }
      const pureConfig = {
        allowList: config.allowList ?? undefined,
        banList: config.banList ?? undefined,
        kolDescription: config.kolDescription ?? undefined,
      } as ProjectConfig
      await prisma.project.update({
        where: {
          id: projectId,
        },
        data: {
          config: pureConfig,
        },
      })
      return reply.status(200).send(successResponse('success'))
    } catch (err) {
      Sentry.captureException(err)
      console.error(err)
      return reply.status(500).send(errorResponse)
    }
  })
}

export default router
