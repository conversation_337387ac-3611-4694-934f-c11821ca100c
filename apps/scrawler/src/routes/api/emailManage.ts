import { serverErrorResponse, successResponse } from '@/common/response/response'
import { withAuth } from '@/middlewares/auth'
import EmailManageService from '@/services/emailManage'
import { getUser } from '@/utils/auth'
import { FastifyPluginCallback } from 'fastify'
import {
  GetEmailFollowupQuerySchema,
  GetFollowupThreadsQuery,
  GetFollowupThreadsQuerySchema,
  UpdateFollowupRequest,
  UpdateFollowupSchema,
} from '../schemas/emailManage'

const emailManageRouter: FastifyPluginCallback = async (fastify) => {
  // 创建邮件跟进配置
  fastify.post<{
    Body: UpdateFollowupRequest
  }>(
    '/templates/followup',
    {
      schema: {
        summary: '创建邮件跟进配置',
        description: '为指定的邮件模板创建跟进邮件配置，包括跟进邮件的主题、内容和发送时间间隔',
        tags: ['邮件管理'],
        body: UpdateFollowupSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { templateId, followupEmails } = request.body as UpdateFollowupRequest
      const user = getUser(request)

      const followup = await EmailManageService.upsertEmailFollowup({
        userId: user.id,
        emailTemplateId: templateId,
        followupEmails,
      })

      return reply.send(successResponse(followup))
    },
  )

  // 获取邮件模板的跟进配置
  fastify.get<{
    Querystring: { templateId: string }
  }>(
    '/templates/followup',
    {
      schema: {
        summary: '获取邮件跟进配置',
        description: '获取指定邮件模板的跟进邮件配置信息',
        tags: ['邮件管理'],
        querystring: GetEmailFollowupQuerySchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { templateId } = request.query
      const user = getUser(request)

      const followup = await EmailManageService.getEmailFollowup(templateId, user.id)

      if (!followup) {
        return reply.send(
          successResponse({
            templateId,
            userId: user.id,
            followupEmails: [],
          }),
        )
      }

      return reply.send(successResponse(followup))
    },
  )

  // 获取用户的跟进线程列表
  fastify.get<{
    Querystring: GetFollowupThreadsQuery
  }>(
    '/threads',
    {
      schema: {
        summary: '（暂时不用）获取跟进线程列表',
        description: '获取当前用户的所有邮件跟进线程，可根据状态筛选',
        tags: ['邮件管理'],
        querystring: GetFollowupThreadsQuerySchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { status } = request.query
      const user = getUser(request)

      try {
        const threads = await EmailManageService.getUserFollowupThreads(user.id, status)
        return reply.send(successResponse(threads, 'Followup threads retrieved successfully'))
      } catch (error: any) {
        console.error('Error getting followup threads:', error)
        return reply.status(500).send(serverErrorResponse('Failed to get followup threads'))
      }
    },
  )
}

export default emailManageRouter
