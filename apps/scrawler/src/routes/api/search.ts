import { uploadFileByBuffer } from '@/api/aliyun'
import Tiktok<PERSON><PERSON> from '@/api/tiktok.ts'
import { handleUnknownError } from '@/common/errorHandler'
import { throwError } from '@/common/errors/statusCodes'
import {
  StatusCodes,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { HOST } from '@/config/env'
import { QuotaCost } from '@/enums/QuotaCost'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota } from '@/middlewares/quota'
import { AudienceService } from '@/services/audience.service'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { commonStyle, exportTaskResult } from '@/services/export.service'
import { MembershipService } from '@/services/membership.service'
import { getProjectCandidatesWithPostsByTaskType } from '@/services/project.ts'
import { similarSearchForAllPlatforms } from '@/services/similar'
import TaskService from '@/services/task.ts'
import { TimezoneService } from '@/services/timezone.service'
import {
  TtFollowersSimilarRequest,
  TtFollowingListRequest,
  TtHashTagBreakRequest,
  TtSearchInputBreakRequest,
} from '@/types/request/search.ts'
import {
  AudienceAnalysisTaskParams,
  InsLongCrawlerProgress,
  InsLongCrawlerTaskParams,
  TtFollowersSimilarTaskParams,
  TtFollowingListTaskParams,
  TtHashTagBreakTaskParams,
  TtSearchInputBreakTaskParams,
} from '@/types/task.ts'
import { CellDataType, exportToExcel } from '@/utils/excel'
import {
  $Enums,
  KolPlatform,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskType,
  prisma,
} from '@repo/database'
import assert, { AssertionError } from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  LongCrawlerExportRequest,
  LongCrawlerExportSchema,
  LongCrawlerRequest,
  LongCrawlerRequestSchema,
  TaskStatusQueryParams,
  TaskStatusQueryParamsSchema,
  UpdateLongCrawlerParams,
  UpdateLongCrawlerParamsSchema,
  WebExportParams,
  WebExportParamsSchema,
} from '../schemas/search'
import TaskReason = $Enums.TaskReason

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // tiktok 的 hashTag爆破
  fastify.post<{ Body: TtHashTagBreakRequest }>(
    '/hashTag',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateHashTagSearchQuota,
        QuotaType.TT_HASH_TAG_BREAK,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtHashTagBreakRequest }>, reply) => {
      try {
        const { projectId, platform, reason, maxVideoCount, currentVideoCount } = req.body
        let { tag } = req.body
        tag = tag.replace('#', '')
        tag = tag.trim()
        tag = tag.replace(/ /g, '')
        tag = tag.toLowerCase()
        const user = (req as any).user
        assert(tag, 'tag is required and cannot be empty!')

        const hashTagInfo = await TiktokApi.getInstance().getHashTag(tag)
        assert(hashTagInfo?.id, 'tag is not found!')

        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.HASH_TAG_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const params: TtHashTagBreakTaskParams = {
          projectId,
          platform,
          tag,
          cursor,
          maxVideoCount,
          currentVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.HASH_TAG_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // tiktok 的关键词搜索爆破
  fastify.post<{ Body: TtSearchInputBreakRequest }>(
    '/searchInput',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateInputSearchQuota,
        QuotaType.TT_SEARCH_INPUT_BREAK,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtSearchInputBreakRequest }>, reply) => {
      try {
        const { projectId, platform, searchInput, reason, sortType, publishTimeType } = req.body
        const { maxVideoCount } = req.body ?? 100
        const user = (req as any).user
        if (!projectId || !platform || !searchInput) {
          return reply
            .status(400)
            .send(
              errorResponse(
                StatusCodes.BAD_REQUEST,
                'BAD_REQUEST',
                'projectId,platform is required!',
              ),
            )
        }
        if (![0, 1, 3].includes(sortType)) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'sortType is invalid!'))
        }

        if (![0, 1, 7, 30, 90, 180].includes(publishTimeType)) {
          return reply
            .status(400)
            .send(
              errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'publishTimeType is invalid!'),
            )
        }

        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.SEARCH_INPUT_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const taskParams: TtSearchInputBreakTaskParams = {
          projectId,
          platform,
          searchInput,
          sortType,
          publishTimeType,
          cursor,
          maxVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.SEARCH_INPUT_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
  /**
   * 通过粉丝列表获取相似用户(没有翻页)
   */
  fastify.post<{ Body: TtFollowersSimilarRequest }>(
    '/followersSimilar',
    withAuth(checkQuota(QuotaCost.TT_FOLLOWERS_SIMILAR, QuotaType.TT_FOLLOWERS_SIMILAR)),
    async (req: FastifyRequest<{ Body: TtFollowersSimilarRequest }>, reply) => {
      const { projectId, platform, uniqueId, excludeWords, reason } =
        req.body as TtFollowersSimilarRequest
      const user = (req as any).user
      if (!projectId || !platform || !uniqueId) {
        return reply
          .status(400)
          .send(serverErrorResponse('projectId,platform,uniqueId is required'))
      }
      try {
        const excludeWordsArray = excludeWords ? excludeWords.split(',') : undefined
        const taskParams: TtFollowersSimilarTaskParams = {
          projectId,
          platform,
          uniqueId,
          excludeWords: excludeWordsArray,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWERS_SIMILAR,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  /**
   * 获取用户关注列表
   */
  fastify.post(
    '/followingList',
    withAuth(checkQuota(QuotaCost.TT_FOLLOWING_LIST, QuotaType.TT_FOLLOWING_LIST)),
    async (req: FastifyRequest<{ Body: TtFollowingListRequest }>, reply) => {
      const { projectId, platform, uniqueId, reason } = req.body as TtFollowingListRequest
      const user = (req as any).user
      if (!projectId || !platform || !uniqueId) {
        return reply
          .status(400)
          .send(serverErrorResponse('projectId,platform,uniqueId is required'))
      }
      try {
        const userDetail = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
        if (!userDetail || !userDetail.user || !userDetail.user.id) {
          throw new Error('该博主系统无法解析')
        }
        const followingResponse = await TiktokApi.getInstance().getUserFollowing(
          userDetail.user.id,
          1,
          0,
        )
        if (!followingResponse) {
          throw new Error('该博主的关注列表未开放')
        }
        let time = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.FOLLOWING_LIST,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          time = meta.time || 0
        }

        const taskParams: TtFollowingListTaskParams = {
          projectId,
          platform,
          uniqueId,
          userId: userDetail.user.id,
          currentTime: time,
          maxCount: 100,
          currentCount: 0,
        }

        console.log('taskParams', taskParams)
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWING_LIST,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  /**
   *  三个平台的受众分析任务
   */
  fastify.post(
    '/audienceAnalysis',
    withAuth(checkQuota(QuotaCost.AUDIENCE_ANALYSIS, QuotaType.AUDIENCE_ANALYSIS)),
    async (request: FastifyRequest, reply) => {
      const { projectId, platform, source } = request.body as any
      const user = (request as any).user

      const validPlatforms = [KolPlatform.TIKTOK, KolPlatform.YOUTUBE, KolPlatform.INSTAGRAM]
      if (!projectId || !platform || !source || !validPlatforms.includes(platform)) {
        return reply.status(400).send(notFoundResponse('projectId,platform or source is invalid'))
      }

      const params: AudienceAnalysisTaskParams = {
        projectId,
        platform,
        source,
      }

      switch (platform) {
        case KolPlatform.TIKTOK:
          const userDetail = await TiktokApi.getInstance().getUserDetail({
            unique_id: params.source.toLowerCase(),
          })
          if (userDetail?.user?.uniqueId) {
            params.source = userDetail.user.uniqueId
          } else {
            return reply
              .status(400)
              .send(
                errorResponse(
                  StatusCodes.BAD_REQUEST,
                  `User ${source} cannot found. Please try again.`,
                  `User ${source} cannot found. Please try again.`,
                ),
              )
          }
          break
        case KolPlatform.INSTAGRAM:
          let insUser
          try {
            insUser = await InstagramRapidApiV3.getInstance().getUserWithoutAbout({
              username: source,
            })
            if (!insUser?.id) {
              return reply
                .status(400)
                .send(
                  errorResponse(
                    StatusCodes.BAD_REQUEST,
                    `User ${source} cannot found. Please try again.`,
                    `User ${source} cannot found. Please try again.`,
                  ),
                )
            }
          } catch (err) {
            console.error('create task failed:', err)
          }
          break
        default:
          break
      }

      try {
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          TaskReason.AUDIENCE_ANALYSIS,
          TaskType.AUDIENCE_ANALYSIS,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 目前先检查,不扣除配额
  // todo 后续每个请求都会扣除配额，依据createUserIds
  fastify.get(
    '/audienceAnalysis',
    withAuth(checkQuota(QuotaCost.AUDIENCE_ANALYSIS)),
    async (request: FastifyRequest, reply) => {
      const { platform, source } = request.query as any
      const user = (request as any).user

      let validSource = source
      const validPlatforms = [KolPlatform.TIKTOK, KolPlatform.YOUTUBE, KolPlatform.INSTAGRAM]

      if (!platform || !validPlatforms.includes(platform)) {
        return reply.status(400).send(notFoundResponse('Invalid platform'))
      }

      if (platform === KolPlatform.TIKTOK) {
        const userDetail = await TiktokApi.getInstance().getUserDetail({
          unique_id: source.toLowerCase(),
        })

        if (userDetail?.user?.uniqueId) {
          validSource = userDetail.user.uniqueId
        }
      }

      if (!validSource) {
        return reply.status(400).send(notFoundResponse('Invalid source'))
      }

      try {
        const kolInfo = await prisma.kolInfo.findUnique({
          where: {
            platform_platformAccount: {
              platform,
              platformAccount: validSource,
            },
          },
          select: {
            audienceAnalysis: true,
          },
        })

        if (!kolInfo || !kolInfo.audienceAnalysis) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'kolInfo not found,need create task to analysis',
            ),
          )
        }
        const { userPortraitResult, regionAnalysisResult, fakeRadarData, updatedAt, kolId } =
          kolInfo.audienceAnalysis as any

        // limit 30 days
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const updateTime = new Date(updatedAt)

        if (updateTime < thirtyDaysAgo) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'audienceAnalysis is expired,need create task to analysis',
            ),
          )
        }

        if (!userPortraitResult || !regionAnalysisResult) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'audienceAnalysis is empty,need create task to analysis',
            ),
          )
        }

        return reply.status(200).send(
          successResponse({
            userPortraitResult,
            regionAnalysisResult,
            fakeRadarData,
            kolId,
            updatedAt,
            needCreateTask: false,
          }),
        )
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 导出受众分析excel
  fastify.get('/audienceAnalysis/export', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, source } = request.query as any
    const user = (request as any).user
    assert(
      Object.values(KolPlatform).includes(platform as KolPlatform),
      new Error('platform is invalid!'),
    )
    assert(source, new Error('source is required!'))
    try {
      // projectId 作用是找到启动过任务的那个task。只限定创建过任务人能够导出（暂废除）
      // 找到最近的一个source的受众分析
      const task = await prisma.similarChannelTask.findFirst({
        where: {
          type: TaskType.AUDIENCE_ANALYSIS,
          params: {
            path: ['source'],
            equals: source,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
      assert(task, new Error('task not found, export failed'))
      const audienceAnalysis = await prisma.audienceAnalysis.findMany({
        where: {
          taskId: task.id,
        },
        orderBy: [{ region: 'desc' }, { city: 'desc' }],
      })
      assert(
        audienceAnalysis && audienceAnalysis.length,
        new Error('No audience analysis data found'),
      )

      console.log(
        `[ExportAudienceAnalysis] Found ${audienceAnalysis.length} records for task ${task.id}`,
      )

      // 获取用户的时区设置
      const membership = await MembershipService.getInstance().getMembership(user.id)

      const buffer = await AudienceService.exportAudienceAnalysisExcel(audienceAnalysis, source)

      const dateStr = TimezoneService.formatToUserTimezone(
        new Date(),
        membership?.timezone || 'Asia/Shanghai',
      )
        .slice(0, 19)
        .replace(/\s+/g, '_')
        .replace(/[T:]/g, '-')

      const fileName = `@${source}_${dateStr}.xlsx`
      const filePath = `excel/${fileName}`

      await uploadFileByBuffer(filePath, buffer)

      return reply.send(
        successResponse(
          {
            url: `${HOST ?? ''}/files/excel/${fileName}`,
          },
          'success',
        ),
      )
    } catch (error) {
      console.error('[ExportAudienceAnalysis] Error:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取hashTag的结果
  fastify.get('/web/hashTag', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.HASH_TAG_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取searchInput的结果
  fastify.get('/web/searchInput', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    if (!projectId || !platform) {
      return reply.status(400).send(serverErrorResponse('projectId and platform are required'))
    }
    if (isNaN(page) || isNaN(pageSize)) {
      return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
    }
    try {
      switch (platform) {
        case KolPlatform.TIKTOK:
          const result = await getProjectCandidatesWithPostsByTaskType(
            projectId,
            TaskType.SEARCH_INPUT_BREAK,
            platform,
            {
              page,
              pageSize,
            },
          )
          return reply.status(200).send(successResponse(result))
        default:
          return reply.status(400).send(serverErrorResponse('platform is invalid'))
      }
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/followersSimilar', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform } = request.query as any
    if (!projectId || !platform) {
      return reply.status(400).send(serverErrorResponse('projectId and platform are required'))
    }
    try {
      switch (platform) {
        case KolPlatform.TIKTOK:
          const result = await getProjectCandidatesWithPostsByTaskType(
            projectId,
            TaskType.FOLLOWERS_SIMILAR,
            platform,
          )
          return reply.status(200).send(successResponse(result))
        default:
          return reply.status(400).send(serverErrorResponse('platform is invalid'))
      }
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/followingList', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page, pageSize } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')
    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.FOLLOWING_LIST,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取hashTag的结果
  fastify.get('/web/bgm', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.BGM_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/webList', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.WEB_LIST,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/taggedBreak', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.TAGGED_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/similar', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId } = request.query as any
    const data = await similarSearchForAllPlatforms(projectId)
    return reply.status(200).send(successResponse(data))
  })

  fastify.get<{ Querystring: WebExportParams }>(
    '/web/export',
    {
      schema: {
        tags: ['search'],
        summary: '导出搜索结果',
        description: '导出搜索结果到Excel',
        querystring: WebExportParamsSchema,
      },
      // ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.query as WebExportParams
      const file = await exportTaskResult(taskId)
      reply.header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      )
      reply.header(
        'Content-Disposition',
        `attachment; filename=export-${taskId}-${new Date().toISOString().substring(0, 10)}.xlsx`,
      )
      return reply.send(file)
    },
  )

  /**
   * 创建长时间爬取任务
   */
  fastify.post<{ Body: LongCrawlerRequest }>(
    '/longCrawler',
    {
      schema: {
        tags: ['search'],
        summary: '创建长时间爬取任务',
        description: '创建一个长时间运行的爬取任务，用于持续爬取符合条件的用户',
        body: LongCrawlerRequestSchema,
      },
      ...withAuth(dynamicCheckQuota(DynamicQuotaService.calculateLongCrawlerQuota)),
    },
    async (request: FastifyRequest<{ Body: LongCrawlerRequest }>, reply) => {
      const { platform, projectId, filters, seedUsers, numberOfRuns } = request.body
      const user = (request as any).user

      const project = await prisma.project.findUnique({
        where: {
          id: projectId,
          deletedAt: null,
        },
      })

      if (!project) {
        throwError(StatusCodes.BAD_REQUEST, '项目不存在')
      }

      if (platform === KolPlatform.INSTAGRAM) {
        try {
          const firstUser = await InstagramRapidApiV3.getInstance().getUser({
            username: seedUsers[0],
          })
          if (!firstUser?.id) {
            throwError(StatusCodes.BAD_REQUEST, `种子用户 ${seedUsers[0]} 不存在，请检查用户名`)
          }
        } catch (err) {
          console.error('验证种子用户失败:', err)
          throwError(StatusCodes.BAD_REQUEST, `验证种子用户失败，请检查用户名或稍后重试`)
        }
      } else {
        // 目前仅支持Instagram平台
        throwError(StatusCodes.BAD_REQUEST, `目前仅支持Instagram平台的长时间爬取任务`)
      }

      // 构建任务参数
      const taskParams: InsLongCrawlerTaskParams = {
        projectId,
        platform,
        filters,
        seedUsers,
        maxQuotaCost: numberOfRuns * 10,
      }

      const initialProgress: InsLongCrawlerProgress = {
        processedUsers: [],
        pendingUsers: [...seedUsers],
        matchedUsers: [],
        totalProcessed: 0,
        totalMatched: 0,
        lastProcessedAt: new Date().toISOString(),
        currentBatch: 0,
        batchLogs: [],
        hasConsumedQuotaCount: 0,
        allProcessedUsers: [],
      }

      const task = await TaskService.getInstance().createLongCrawlerTask(
        projectId,
        taskParams,
        user.id,
        TaskReason.SEARCH,
        TaskType.LONG_CRAWLER,
        { ...initialProgress },
      )

      return reply.status(200).send(successResponse(task, '长时间爬取任务创建成功'))
    },
  )

  /**
   * 获取任务状态
   */
  fastify.get<{ Params: TaskStatusQueryParams }>(
    '/longCrawler/:taskId',
    {
      schema: {
        tags: ['search'],
        summary: '获取任务状态',
        description: '获取长时间爬取任务的当前状态和进度',
        params: TaskStatusQueryParamsSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Params: TaskStatusQueryParams }>, reply) => {
      const { taskId } = request.params
      const user = (request as any).user

      const task = await prisma.similarChannelTask.findUnique({
        where: {
          id: taskId,
        },
      })
      if (!task) {
        throwError(StatusCodes.NOT_FOUND, 'task not found')
      }

      if (!(task?.createdBy === user.id)) {
        throwError(StatusCodes.FORBIDDEN, 'no permission to view this task')
      }

      const meta = task?.meta as unknown as InsLongCrawlerProgress
      const { allProcessedUsers, ...progress } = meta || {}

      const responseData = {
        taskId: task?.id,
        type: task?.type,
        reason: task?.reason,
        status: task?.status,
        meta: progress, // rm allProcessedUsers
        params: task?.params,
        candidates: task?.candidate,
        createdAt: task?.createdAt,
        updatedAt: task?.updatedAt,
      }

      return reply.status(200).send(successResponse(responseData))
    },
  )

  // type aiFilter or match,export excel。
  // Perfect Fit,Others – Content OK
  fastify.post<{ Body: LongCrawlerExportRequest }>(
    '/longCrawler/export',
    {
      schema: {
        tags: ['search', 'longCrawler', 'export'],
        summary: '导出长时间爬取任务结果',
        description: '导出长时间爬取任务结果到Excel',
        body: LongCrawlerExportSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: LongCrawlerExportRequest }>, reply) => {
      const { taskId, type } = request.body
      const user = (request as any).user

      if (!['aiFilter', 'match'].includes(type)) {
        throwError(StatusCodes.BAD_REQUEST, 'type should be aiFilter or match')
      }

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: {
          id: taskId,
        },
      })

      if (!task.candidate) {
        throwError(StatusCodes.BAD_REQUEST, 'No candidate data found in task')
      }

      const candidateData = task.candidate as {
        timeStamp: number
        AIfilterUsers: string[]
        matchUsers: string[]
      }
      const usernames =
        type === 'aiFilter' ? candidateData.AIfilterUsers || [] : candidateData.matchUsers || []

      if (usernames.length === 0) {
        throwError(StatusCodes.BAD_REQUEST, `No ${type} users found in task`)
      }

      const params = task.params as unknown as InsLongCrawlerTaskParams
      const platform = params.platform

      if (platform !== 'INSTAGRAM') {
        throwError(StatusCodes.BAD_REQUEST, 'Only INSTAGRAM platform is supported')
      }

      const [kolInfos, instagramUsers] = await Promise.all([
        prisma.kolInfo.findMany({
          where: {
            platform: KolPlatform.INSTAGRAM,
            platformAccount: {
              in: usernames,
            },
          },
        }),
        prisma.instagramUserInfo.findMany({
          where: {
            username: {
              in: usernames,
            },
          },
        }),
      ])

      const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
      const instagramUserMap = new Map(instagramUsers.map((user) => [user.username, user]))

      const exportData = usernames.map((username: string) => {
        const kolInfo = kolInfoMap.get(username)
        const instagramUser = instagramUserMap.get(username)

        return {
          kolTitle: instagramUser?.fullName || kolInfo?.title || username,
          platformAccount: username,
          country: instagramUser?.region || '',
          platform: KolPlatform.INSTAGRAM,
          numericSubscriberCount: String(instagramUser?.followerCount || 0),
          videosAverageViewCount: String(instagramUser?.averageLikeCount || 0),
          kolEmail: kolInfo?.email || '',
          url: `https://instagram.com/${username}`,
        }
      })

      // 定义Excel列
      const columns = [
        { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
        { header: '@username', key: 'platformAccount', width: 20 },
        { header: 'Region', key: 'country', width: 15 },
        { header: 'Platform', key: 'platform', width: 15 },
        {
          header: 'Followers',
          key: 'numericSubscriberCount',
          width: 15,
          dataType: 'number' as CellDataType,
          format: '0',
        },
        {
          header: 'Avg. Likes',
          key: 'videosAverageViewCount',
          width: 15,
          dataType: 'number' as CellDataType,
          format: '0',
        },
        { header: 'Email', key: 'kolEmail', width: 25 },
        { header: 'URL', key: 'url', width: 25 },
      ]

      // 准备Excel表格
      const sheets = [
        {
          name: type === 'aiFilter' ? 'Others – Content OK' : 'Perfect Fit',
          data: exportData,
          columns,
          styles: commonStyle,
        },
      ]

      // 生成并返回Excel文件
      try {
        const buffer = await exportToExcel(sheets)
        const dateStr = new Date()
          .toISOString()
          .slice(0, 19)
          .replace(/\s+/g, '_')
          .replace(/[T:]/g, '-')

        const fileName = `${user.email}_${sheets[0].name}_${dateStr}.xlsx`
        const filePath = `excel/${fileName}`
        await uploadFileByBuffer(filePath, buffer)

        return reply.status(200).send(successResponse(`${HOST ?? ''}/files/excel/${fileName}`))
      } catch (error) {
        console.error('[Export] Error during export process:', error)
        throw error
      }
    },
  )

  /**
   * 更新长时间爬取任务参数
   */
  fastify.put<{ Body: UpdateLongCrawlerParams }>(
    '/longCrawler/params',
    {
      schema: {
        tags: ['search'],
        summary: '增加长时间爬取任务配额',
        description: '增加长时间爬取任务的配额消耗',
        body: UpdateLongCrawlerParamsSchema,
      },
      ...withAuth(dynamicCheckQuota(DynamicQuotaService.calculateAddLongCrawlerQuota)),
    },
    async (request: FastifyRequest<{ Body: UpdateLongCrawlerParams }>, reply) => {
      const { taskId, params } = request.body
      const user = (request as any).user

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task.createdBy !== user.id) {
        throwError(StatusCodes.FORBIDDEN, '无权限更新此任务')
      }

      const currentParams = task.params as unknown as InsLongCrawlerTaskParams

      // calculate new quota
      const addQuota = params.addNumberOfRuns * 10
      const newMaxQuotaCost = (currentParams.maxQuotaCost || 0) + addQuota

      const updatedParams = {
        ...currentParams,
        maxQuotaCost: newMaxQuotaCost,
      }

      const updatedTask = await prisma.similarChannelTask.update({
        where: { id: taskId },
        data: { params: updatedParams },
      })

      if (updatedTask.status === SimilarChannelTaskStatus.COMPLETED) {
        await TaskService.getInstance().resumeCompletedLongCrawlerTask(updatedTask)
      }

      return reply.status(200).send(
        successResponse(
          {
            taskId: updatedTask.id,
            params: updatedTask.params,
            addedQuota: addQuota,
            newMaxQuotaCost: newMaxQuotaCost,
          },
          '任务配额增加成功',
        ),
      )
    },
  )
}

export default router
