import { needSupaba<PERSON><PERSON><PERSON>, withAuth } from '@/middlewares/auth'
import EmailService, { SenderPrepareError } from '@/services/email'
import { createTemplate, deleteTemplate } from '@/services/template'
import { EmailCredentialType, prisma, ProviderType, ScopeType } from '@repo/database'
import { FastifyPluginAsync } from 'fastify'

import { SendEmailRequest } from '@/types/email'

import { handleUnknownError } from '@/common/errorHandler.ts'
import { throwError } from '@/common/errors/statusCodes'
import {
  notFoundResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
  successResponseWithoutData,
} from '@/common/response/response.ts'
import { FullResponseSchema } from '@/config/swagger'
import {
  MAX_TEMPLATE_CONTENT_LENGTH,
  MAX_TEMPLATE_COUNT,
  MAX_TEMPLATE_SUBJECT_LENGTH,
} from '@/constants/email'
import { getUser } from '@/utils/auth'
import { Type } from '@sinclair/typebox'
import {
  CreateTemplateRequest,
  CreateTemplateRequestSchema,
  EmailTemplateSchema,
  UpdateTemplateRequest,
  UpdateTemplateRequestSchema,
} from '../schemas/email'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post<{ Body: CreateTemplateRequest }>(
    '/templates',
    {
      schema: {
        summary: '创建邮件模板',
        description: '创建邮件模板',
        tags: ['邮件管理'],
        body: CreateTemplateRequestSchema,
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { name, subject, content, tags, cc } = request.body as CreateTemplateRequest
        const user = getUser(request)
        const count = await prisma.emailTemplate.count({
          where: { createdBy: user.id, deletedAt: null },
        })
        if (count >= MAX_TEMPLATE_COUNT) {
          throwError(StatusCodes.BAD_REQUEST, 'Template count exceeded')
        }
        if (subject && subject.length > MAX_TEMPLATE_SUBJECT_LENGTH) {
          throwError(StatusCodes.BAD_REQUEST, 'Subject length exceeded')
        }
        if (content && content.length > MAX_TEMPLATE_CONTENT_LENGTH) {
          throwError(StatusCodes.BAD_REQUEST, 'Content length exceeded')
        }
        const templates = await createTemplate({
          name,
          subject: subject ?? '',
          content: content ?? '',
          tags: tags ?? [],
          cc: cc ?? [],
          createdBy: user,
        })
        return reply.status(201).send(successResponse(templates, 'Create template successfully'))
      } catch (error: any) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get(
    '/templates',
    {
      schema: {
        summary: '获取邮件模板列表',
        description: '获取邮件模板列表',
        tags: ['邮件管理'],
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = getUser(request)
      const templates = await prisma.emailTemplate.findMany({
        where: { createdBy: user.id, deletedAt: null },
      })
      return reply.send(successResponse(templates))
    },
  )

  fastify.get(
    '/templates/:id',
    {
      schema: {
        summary: '获取邮件模板详情',
        description: '获取邮件模板详情',
        tags: ['邮件管理'],
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { id } = request.params as any
      if (!id) {
        throwError(StatusCodes.BAD_REQUEST, 'id is required')
      }
      const template = await prisma.emailTemplate.findUnique({
        where: { id, deletedAt: null },
      })
      if (!template) {
        throwError(StatusCodes.NOT_FOUND, 'Template not found')
      }
      return reply.send(successResponse(template))
    },
  )

  fastify.patch<{ Body: UpdateTemplateRequest }>(
    '/templates/:id',
    {
      schema: {
        summary: '更新邮件模板',
        description: '更新邮件模板',
        tags: ['邮件管理'],
        body: UpdateTemplateRequestSchema,
        response: FullResponseSchema(EmailTemplateSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { id } = request.params as any
      if (!id) {
        throwError(StatusCodes.BAD_REQUEST, 'id is required')
      }
      const user = getUser(request)
      const template = await EmailService.getInstance().updateTemplate(id, request.body, user.id)
      return reply.send(successResponse(template, 'Update template successfully'))
    },
  )

  fastify.delete(
    '/templates/:id',
    {
      schema: {
        summary: '删除邮件模板',
        description: '删除邮件模板',
        tags: ['邮件管理'],
        params: Type.Object({
          id: Type.String({
            description: '邮件模板ID',
          }),
        }),
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { id } = request.params as any
      if (!id) {
        return reply.status(400).send(notFoundResponse('id is required'))
      }
      try {
        const template = await deleteTemplate(id, (request as any).user.id)
        return reply.status(200).send(successResponse(template, 'Delete template successfully'))
      } catch (error: any) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.post(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { email, credentialId } = request.body as any
      const user = getUser(request)

      const credential = await prisma.providerCredential.findFirst({
        where: { id: credentialId, createdBy: user.id },
        include: { Provider: true },
      })

      if (!credential) {
        return reply.status(404).send({ error: 'Credential not found' })
      }

      let type: EmailCredentialType
      if (
        credential.Provider.type === ProviderType.GOOGLE &&
        credential.Provider.scopes.includes(ScopeType.GMAIL)
      ) {
        type = EmailCredentialType.GMAIL
      } else {
        return reply.status(400).send({ error: 'Unsupported provider for email' })
      }

      const sender = await prisma.emailSender.upsert({
        where: {
          email_createdBy: {
            email,
            createdBy: user.id,
          },
        },
        create: {
          email,
          type,
          providerCredentialId: credentialId,
          createdBy: user.id,
        },
        update: {
          email,
          type,
          providerCredentialId: credentialId,
        },
      })

      return reply.send(successResponse(EmailService.getInstance().populateEmailSender(sender)))
    },
  )

  fastify.get(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { user } = request as any
      const sender = await prisma.emailSender.findMany({
        where: { createdBy: user.id },
      })
      reply.send(successResponse(sender.map(EmailService.getInstance().populateEmailSender)))
    },
  )

  fastify.delete(
    '/:id',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { id } = request.params as any
      const { user } = request as any

      try {
        await prisma.emailSender.delete({
          where: { id, createdBy: user.id },
        })
        return reply.send(successResponseWithoutData())
      } catch (error: any) {
        if (error?.code === 'P2025') {
          // P2025 是 Prisma 的 "Record to delete does not exist" 错误代码
          return reply.status(404).send({ error: 'Email Sender not found' })
        }
        // 处理其他错误
        return reply.status(500).send({ error: 'An error occurred' })
      }
    },
  )

  //  建联发送邮件接口
  fastify.post<{
    Body: SendEmailRequest
  }>('/sendWithPolish', { preHandler: needSupabaseAuth }, async (request, reply) => {
    const user = (request as any).user
    try {
      const result = await EmailService.getInstance().sendEmailWithPolish(
        user.id,
        request.body as SendEmailRequest,
      )
      return reply.status(200).send(successResponse(result, 'Send email successfully'))
    } catch (error: any) {
      if (error instanceof SenderPrepareError) {
        // 处理特殊错误，方便排查
        return reply.status(error.code).send({
          success: false,
          message: error.message,
          errorCode: error.code,
        })
      }
      console.error('something be wrong: ', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get(
    '/gmail-scopes',
    {
      schema: {
        summary: '获取用户Gmail授权scope列表',
        description: '获取当前用户在Gmail中实际授权的scope列表',
        tags: ['邮件管理'],
        response: FullResponseSchema(),
      },
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const scopeResults = await EmailService.getInstance().getGmailScopes(user.id)
        return reply
          .status(200)
          .send(successResponse(scopeResults, 'Get Gmail scopes successfully'))
      } catch (error: any) {
        if (error.message === 'No Google credentials found for this user') {
          return reply.status(404).send(notFoundResponse(error.message))
        }
        console.error('Error getting Gmail scopes:', error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
}

export default router
