// apps/scrawler/src/routes/api/label.ts
import {
  StatusCodes,
  errorResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { LabelColor } from '@/enums/LabelColor'
import { withAuth } from '@/middlewares/auth'
import { tagService } from '@/services/tag.service'
import { PaginationParams } from '@/types/pagination'
import { CreateTagParams } from '@/types/tag'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/list', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { page, pageSize } = request.query as PaginationParams
      const labels = await tagService.getTags(user.id, { page, pageSize })
      return reply.send(successResponse(labels))
    } catch (error) {
      return reply
        .status(500)
        .send(serverErrorResponse(error instanceof Error ? error.message : '服务器内部错误'))
    }
  })

  fastify.post('/', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { name, color } = request.body as CreateTagParams

      // 验证颜色是否在枚举中
      if (!Object.keys(LabelColor).includes(color)) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, '无效的标签颜色'))
      }

      const tag = await tagService.createTag(user.id, { name, color })
      return reply.send(successResponse(tag))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '服务器内部错误'

      if (
        errorMessage.includes('标签数量上限') ||
        errorMessage.includes('标签名称不能超过') ||
        errorMessage.includes('标签名称已存在')
      ) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, errorMessage))
      }

      return reply.status(500).send(serverErrorResponse(errorMessage))
    }
  })

  fastify.delete('/:id', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { id } = request.params as { id: string }
      if (!id) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, '标签ID不能为空'))
      }
      await tagService.deleteTag(user.id, id)
      return reply.send(successResponse(null, '标签删除成功'))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '服务器内部错误'

      if (errorMessage.includes('标签不存在或无权限删除')) {
        return reply.status(403).send(errorResponse(StatusCodes.FORBIDDEN, errorMessage))
      }

      return reply.status(500).send(serverErrorResponse(errorMessage))
    }
  })
}

export default router
