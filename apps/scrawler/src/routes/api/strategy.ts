import { handleUnknownError } from '@/common/errorHandler'
import { serverErrorResponse, successResponse } from '@/common/response/response'
import Sentry from '@/infras/sentry'
import { withAuth } from '@/middlewares/auth'
import strategyService from '@/services/strategy.service'
import { CreateStrategyPayload } from '@/types/strategy'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify): Promise<void> => {
  fastify.post<{ Body: CreateStrategyPayload }>('/', withAuth(), async (request, reply) => {
    try {
      const user = (request as any).user
      const createPayload = request.body
      const newStrategy = await strategyService.createStrategy(user.id, createPayload)
      return reply.status(201).send(successResponse(newStrategy, 'Strategy created successfully'))
    } catch (error: any) {
      console.error('Failed to create strategy:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
}

export default router
