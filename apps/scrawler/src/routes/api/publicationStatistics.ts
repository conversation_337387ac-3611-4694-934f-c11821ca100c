import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import { serverErrorResponse, successResponse } from '@/common/response/response'
import { QuotaCost } from '@/enums/QuotaCost'
import Sentry from '@/infras/sentry'
import { withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota, handleQuotaWithResponse } from '@/middlewares/quota'
import { publicationService } from '@/services/PublicationStatistics.service'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import TaskService from '@/services/task'
import { OVERSEAS_PLATFORMS } from '@/types/kol'
import { PaginatedResponse, PaginationParams } from '@/types/pagination'
import {
  PluginPublicationAudienceTaskParams,
  PublicationAudienceTaskParams,
  TrackEasyKOLTaskRequestParams,
  UpdatePublicationDataParams,
  UpdatePublicationDataParamsWithIds,
  UpdateRecentDataParams,
  UpdateVideoDataRequestParams,
} from '@/types/publicationStatistics'
import { TrackEasyKOLTaskParams } from '@/types/task'
import Logger from '@/utils/logger'
import { PaginationService } from '@/utils/pagination'
import { parseUrlUtils } from '@/utils/parseUrl'
import {
  KolPlatform,
  PublicationStatisticsSheetData,
  QuotaType,
  TaskReason,
  TaskType,
  prisma,
} from '@repo/database'
import assert from 'assert'
import { FastifyPluginAsync } from 'fastify'
import {
  AudienceRequest,
  AudienceSchema,
  BatchDeletePublicationsRequest,
  BatchDeletePublicationsSchema,
  PluginAudienceRequest,
  PluginAudienceResultParams,
  PluginAudienceResultParamsSchema,
  PluginAudienceSchema,
  TrackByPublicationIdsRequest,
  TrackByPublicationIdsSchema,
} from '../schemas/publicationStatistics'

const router: FastifyPluginAsync = async (fastify): Promise<void> => {
  fastify.post(
    '/task/track-new',
    withAuth(
      handleQuotaWithResponse(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL),
    ),
    async (request, reply) => {
      const { tiktok, youtube, instagram, douyin, xhs, tagIds } =
        request.body as TrackEasyKOLTaskRequestParams
      const user = (request as any).user

      const platformData = {
        tiktok: {
          ids: [...(tiktok?.videosIds || [])].filter((id) => id && id.trim() !== ''),
          urls: (tiktok?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        youtube: {
          ids: [...(youtube?.videosIds || []), ...(youtube?.shortsIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (youtube?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        instagram: {
          ids: [...(instagram?.postsIds || []), ...(instagram?.reelsIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (instagram?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        douyin: {
          ids: [...(douyin?.videosIds || []), ...(douyin?.noteIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (douyin?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        xhs: {
          ids: [...(xhs?.noteIds || [])].filter((id) => id && id.trim() !== ''),
          urls: (xhs?.urls || []).filter((url) => url && url.trim() !== ''),
        },
      }

      const hasData = Object.values(platformData).some(
        (platform) => platform.ids.length > 0 || platform.urls.length > 0,
      )

      if (!hasData) {
        return reply
          .status(400)
          .send(serverErrorResponse('at least one platform video data is required'))
      }

      try {
        const userGoogleSheet = await prisma.userGoogleSheet.findFirst({
          where: {
            userId: user.id,
          },
        })

        if (!userGoogleSheet) {
          return reply.status(404).send(serverErrorResponse('no google sheet found'))
        }

        const params: TrackEasyKOLTaskParams = {
          spreadsheetId: userGoogleSheet.spreadsheetId,
          tiktok,
          youtube,
          instagram,
          douyin,
          xhs,
          tagIds,
        }

        const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
        console.log(`[task/track-new] 用户 ${user.id} 创建任务 ${projectId}`)

        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          TaskReason.EASYKOL_TRACK,
          TaskType.EASYKOL_TRACK,
        )

        return reply
          .status(200)
          .send(
            successResponse(
              task,
              'create task success, we will handle your request in the background',
            ),
          )
      } catch (error) {
        console.error('[task/track-new] 创建任务失败:', error)
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.post(
    '/task/track-recent',
    withAuth(
      handleQuotaWithResponse(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL),
    ),
    async (request, reply) => {
      const { user } = request as any
      const { page = 1, pageSize = 20 } = request.body as UpdateRecentDataParams
      assert(
        page && typeof page === 'number' && page > 0,
        new Error('page must be a positive integer'),
      )
      assert(
        pageSize && typeof pageSize === 'number' && pageSize > 0,
        new Error('pageSize must be a positive integer'),
      )

      try {
        const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
          where: { userId: user.id },
        })

        assert(userGoogleSheet, new Error('no google sheet found'))

        const { skip } = PaginationService.handlePagination({ page, pageSize })
        const result = await prisma.publicationStatisticsSheetData.findMany({
          where: {
            spreadsheetId: userGoogleSheet.spreadsheetId,
          },
          orderBy: { publishDate: 'desc' },
          skip,
          take: pageSize,
        })
        assert(result.length > 0, new Error('no recent posts found'))

        const postRequestParams: UpdateVideoDataRequestParams = {
          spreadsheetId: userGoogleSheet.spreadsheetId,
          tiktok: {
            videosIds: result
              .filter(
                (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            urls: result
              .filter(
                (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
              )
              .map((item) => item.postLink)
              .filter((item) => item !== null) as string[],
          },
          youtube: {
            videosIds: result
              .filter(
                (item) =>
                  item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            shortsIds: result
              .filter(
                (item) =>
                  item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            urls: result
              .filter((item) => item.platform === KolPlatform.YOUTUBE)
              .map((item) => item.postLink)
              .filter((item) => item !== null) as string[],
          },
          instagram: {
            reelsIds: result
              .filter(
                (item) =>
                  item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_REEL',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            postsIds: result
              .filter(
                (item) =>
                  item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_POST',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            urls: result
              .filter((item) => item.platform === KolPlatform.INSTAGRAM)
              .map((item) => item.postLink)
              .filter((item) => item !== null) as string[],
          },
          douyin: {
            videosIds: result
              .filter(
                (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_VIDEO',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            noteIds: result
              .filter(
                (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_NOTE',
              )
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            urls: result
              .filter((item) => item.platform === KolPlatform.DOUYIN)
              .map((item) => item.postLink)
              .filter((item) => item !== null) as string[],
          },
          xhs: {
            noteIds: result
              .filter((item) => item.platform === KolPlatform.XHS)
              .map((item) => item.videoId)
              .filter((item) => item !== null) as string[],
            urls: result
              .filter((item) => item.platform === KolPlatform.XHS)
              .map((item) => item.postLink)
              .filter((item) => item !== null) as string[],
          },
        }

        const tiktokCount = postRequestParams.tiktok?.urls?.length || 0
        const youtubeCount = postRequestParams.youtube?.urls?.length || 0
        const instagramCount = postRequestParams.instagram?.urls?.length || 0
        const douyinCount = postRequestParams.douyin?.urls?.length || 0
        const xhsCount = postRequestParams.xhs?.urls?.length || 0

        const totalItemsToTrack =
          tiktokCount + youtubeCount + instagramCount + douyinCount + xhsCount

        assert(totalItemsToTrack > 0, new Error('no video data found'))
        console.log(
          `[task/track-recent] 需要跟踪的数据: TikTok: ${tiktokCount}, YouTube: ${youtubeCount}, Instagram: ${instagramCount}`,
        )

        const params: TrackEasyKOLTaskParams = {
          spreadsheetId: userGoogleSheet.spreadsheetId,
          tiktok: postRequestParams.tiktok,
          youtube: postRequestParams.youtube,
          instagram: postRequestParams.instagram,
          douyin: postRequestParams.douyin,
          xhs: postRequestParams.xhs,
        }

        const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          TaskReason.EASYKOL_TRACK,
          TaskType.EASYKOL_TRACK,
        )

        return reply
          .status(200)
          .send(
            successResponse(
              task,
              'create task success, we will handle your request in the background',
            ),
          )
      } catch (error) {
        console.error('[task/track-recent] create task failed:', error)
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 通过publicationIds数组追踪数据
  fastify.post<{ Body: TrackByPublicationIdsRequest }>(
    '/task/update-batch',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '通过发布ID数组创建追踪任务',
        body: TrackByPublicationIdsSchema,
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculatePublicationStatisticsUpdateBatch,
          QuotaType.EASYKOL_DATA_TRACK_URL,
        ),
      ),
    },
    async (request, reply) => {
      const { user } = request as any
      const { publicationIds } = request.body

      const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
        where: { userId: user.id },
      })

      if (!userGoogleSheet) {
        throwError(StatusCodes.NOT_FOUND, 'no google sheet found')
      }

      const result = await prisma.publicationStatisticsSheetData.findMany({
        where: {
          id: { in: publicationIds },
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
      })

      if (result.length === 0) {
        throwError(StatusCodes.NOT_FOUND, 'no publications found with provided ids')
      }

      const postRequestParams: UpdateVideoDataRequestParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        youtube: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          shortsIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.YOUTUBE)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        instagram: {
          reelsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_REEL',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          postsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.INSTAGRAM)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        douyin: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          noteIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_NOTE',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.DOUYIN)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        xhs: {
          noteIds: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
      }

      const tiktokCount = postRequestParams.tiktok?.urls?.length || 0
      const youtubeCount = postRequestParams.youtube?.urls?.length || 0
      const instagramCount = postRequestParams.instagram?.urls?.length || 0
      const douyinCount = postRequestParams.douyin?.urls?.length || 0
      const xhsCount = postRequestParams.xhs?.urls?.length || 0

      const totalItemsToTrack = tiktokCount + youtubeCount + instagramCount + douyinCount + xhsCount

      if (totalItemsToTrack === 0) {
        throwError(StatusCodes.NOT_FOUND, 'no video data found')
      }
      console.log(
        `[task/track-by-ids] 需要跟踪的数据: TikTok: ${tiktokCount}, YouTube: ${youtubeCount}, Instagram: ${instagramCount}, Douyin: ${douyinCount}, XHS: ${xhsCount}`,
      )

      const params: TrackEasyKOLTaskParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: postRequestParams.tiktok,
        youtube: postRequestParams.youtube,
        instagram: postRequestParams.instagram,
        douyin: postRequestParams.douyin,
        xhs: postRequestParams.xhs,
      }

      const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
      const task = await TaskService.getInstance().createTask(
        projectId,
        params,
        user.id,
        TaskReason.EASYKOL_TRACK,
        TaskType.EASYKOL_TRACK,
      )

      return reply
        .status(200)
        .send(
          successResponse(
            task,
            'create task success, we will handle your request in the background',
          ),
        )
    },
  )

  // 获取用户的投放视频数据
  fastify.get('/get-user-post-data', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { page: pageParam, pageSize: pageSizeParam } = request.query as PaginationParams
      const page = Number(pageParam) || 1
      const pageSize = Number(pageSizeParam) || 20

      const result = await publicationService.getUserPostData(user.id, { page, pageSize })
      const responsePaginatedData: PaginatedResponse<PublicationStatisticsSheetData> = {
        data: result.data,
        total: result.total,
        page,
        pageSize,
        totalPages: Math.ceil(result.total / pageSize),
      }
      return reply.send(successResponse(responsePaginatedData))
    } catch (error) {
      console.error('获取投放视频数据失败:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.delete('/:publicationId', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { publicationId } = request.params as { publicationId: string }
      assert(publicationId, new Error('id is required'))
      const result = await publicationService.deleteUserPostData(user.id, publicationId)
      return reply.send(successResponse(result))
    } catch (error) {
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 批量删除投放数据
  fastify.delete<{ Body: BatchDeletePublicationsRequest }>(
    '/delete-batch',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '批量删除投放数据',
        description: '批量删除多个投放视频数据',
        body: BatchDeletePublicationsSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const { publicationIds } = request.body

      const result = await publicationService.batchDeleteUserPostData(user.id, publicationIds)
      return reply.send(successResponse(result, '批量删除成功'))
    },
  )

  fastify.put(
    '/track/:publicationId',
    withAuth(checkQuota(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL)),
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params as { publicationId: string }
        assert(publicationId, new Error('id is required'))
        const result = await publicationService.trackExsitingUserPostData(user.id, publicationId)
        return reply.send(successResponse(result))
      } catch (error) {
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.put('/update/:publicationId', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { publicationId, totalCost, notes1, notes2, contactInformation } =
        request.body as UpdatePublicationDataParamsWithIds
      if (!publicationId) {
        throwError(StatusCodes.BAD_REQUEST, 'id is required')
      }
      if (totalCost && typeof totalCost !== 'number' && totalCost <= 0) {
        throwError(StatusCodes.BAD_REQUEST, 'totalCost is invalid')
      }

      const result = await publicationService.updatePublicationData(user.id, publicationId, {
        totalCost,
        notes1,
        notes2,
        contactInformation,
      } as UpdatePublicationDataParams)
      return reply.send(successResponse(result))
    } catch (error) {
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/proxy-image', async (request, reply) => {
    const { publicationId } = request.query as { publicationId: string }
    if (!publicationId) {
      return reply.status(400).send(serverErrorResponse('publicationId is required'))
    }
    try {
      const result = await publicationService.getPublicationImage(publicationId)
      const response = await fetch(result.imageUrl)
      const arrayBuffer = await response.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      reply.header('Content-Type', response.headers.get('content-type'))
      reply.header('Cache-Control', 'public, max-age=86400') // 缓存24小时
      return reply.send(buffer)
    } catch (error) {
      console.error('代理图片失败:', error)
      return reply.status(500).send(serverErrorResponse('Error fetching image'))
    }
  })

  // post audience
  fastify.post<{ Body: AudienceRequest }>(
    '/audience',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '创建受众分析任务',
        body: AudienceSchema,
      },
      ...withAuth(checkQuota(QuotaCost.POST_AUDIENCE, QuotaType.POST_AUDIENCE)),
    },
    async (request, reply) => {
      const { publicationId } = request.body
      const userId = (request as any).user?.id

      const publication = await prisma.publicationStatisticsSheetData.findUnique({
        where: { id: publicationId },
        include: {
          userGoogleSheet: true,
        },
      })

      if (!publication) {
        throwError(StatusCodes.NOT_FOUND, '发布统计数据不存在')
      }

      if (publication.userGoogleSheet.userId !== userId) {
        throwError(StatusCodes.FORBIDDEN, '无权访问此数据')
      }

      // post audience task
      const taskParams: PublicationAudienceTaskParams = {
        publicationId,
        googleSheetId: publication.userGoogleSheet.spreadsheetId,
        platform: publication.platform!,
        postLink: publication.postLink!,
        videoId: publication.videoId!,
      }

      // only support overseas platforms
      if (!OVERSEAS_PLATFORMS.includes(publication.platform! as any)) {
        throwError(StatusCodes.BAD_REQUEST, '只支持海外平台(YouTube/TikTok/Instagram)的受众分析')
      }

      const task = await TaskService.getInstance().createTask(
        `PostAudience:${publicationId}`, // fake projectId
        taskParams,
        userId,
        TaskReason.SEARCH,
        TaskType.POST_AUDIENCE,
      )

      return reply.status(200).send(successResponse(task))
    },
  )

  // post audience
  fastify.post<{ Body: PluginAudienceRequest }>(
    '/plugin/audience',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '创建受众分析任务',
        body: PluginAudienceSchema,
      },
      ...withAuth(checkQuota(QuotaCost.POST_AUDIENCE, QuotaType.POST_AUDIENCE)),
    },
    async (request, reply) => {
      const { url } = request.body
      const userId = (request as any).user?.id
      const videoId = parseUrlUtils.extractVideoId(url)
      const platform = parseUrlUtils.getPlatformFromUrl(url)

      Logger.info(`plugin post audience url: ${url}, videoId: ${videoId}, platform: ${platform}`)

      if (!videoId) {
        throwError(StatusCodes.NOT_FOUND, '视频链接不存在或不支持')
      }

      if (!platform || !OVERSEAS_PLATFORMS.includes(platform)) {
        throwError(StatusCodes.BAD_REQUEST, '不支持的视频平台')
      }

      // post audience task
      const taskParams: PluginPublicationAudienceTaskParams = {
        videoId: videoId,
        platform: platform,
        postLink: url,
      }

      // TODO: check the videoId already exists. to invoke the rapid api

      const task = await TaskService.getInstance().createTask(
        `Plugin PostAudience:${videoId}`, // fake projectId
        taskParams,
        userId,
        TaskReason.SEARCH,
        TaskType.POST_AUDIENCE,
      )

      return reply.status(200).send(successResponse(task))
    },
  )

  // get plugin audience task result
  fastify.get(
    '/plugin/audience/:taskId',
    {
      schema: {
        params: PluginAudienceResultParamsSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.params as PluginAudienceResultParams

      const task = await prisma.similarChannelTask.findUnique({
        where: { id: taskId },
        select: {
          result: true,
        },
      })

      if (!task) {
        throwError(StatusCodes.NOT_FOUND, '任务不存在')
      }

      return reply.status(200).send(successResponse(task.result))
    },
  )
}
export default router
