import { errorResponse, StatusCodes, successResponse } from '@/common/response/response'
import { STRIPE_WEBHOOK_SECRET } from '@/config/stripe'
import { withAuth } from '@/middlewares/auth'
import { subscriptionLimit } from '@/middlewares/subscriptionLimit'
import {
  cancelSubscription,
  createCheckoutSession,
  createPortalSession,
  findUserActiveSubscription,
  handleWebhookEvent,
  retrieveCheckoutSession,
  stripe,
} from '@/services/stripe'
import { prisma, SubscriptionStatus, TransitionStatus } from '@repo/database'
import assert from 'assert'
import { FastifyPluginAsync, FastifyReply, FastifyRequest } from 'fastify'
import {
  cancelSubscriptionSchema,
  createCheckoutSchema,
  createPortalSchema,
  resubscribeSchema,
  subscriptionPollingSchema,
  subscriptionStatusSchema,
  verifySessionSchema,
} from '../schemas/subscription'
interface SubscribeRequest {
  planType: 'BASIC' | 'PRO'
}

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // create checkout session
  fastify.post<{ Body: SubscribeRequest }>(
    '/create-checkout',
    {
      schema: createCheckoutSchema,
      ...withAuth(subscriptionLimit),
    },
    async (request, reply) => {
      const { planType } = request.body
      const user = (request as any).user

      const existingSubscription = await findUserActiveSubscription(user.id)

      if (existingSubscription && existingSubscription.planType === planType) {
        if (existingSubscription.status === SubscriptionStatus.PAST_DUE) {
          return reply
            .status(400)
            .send(
              errorResponse(
                StatusCodes.PAYMENT_REQUIRED,
                'PaymentRequired',
                '您已有此计划的订阅，但付款失败。请更新支付方式或取消现有订阅后重试。',
              ),
            )
        }

        if (existingSubscription.cancelAtPeriodEnd) {
          return reply
            .status(400)
            .send(
              errorResponse(
                StatusCodes.CONFLICT,
                'DuplicateSubscription',
                '您的订阅已取消，请恢复订阅',
              ),
            )
        }

        return reply
          .status(400)
          .send(errorResponse(StatusCodes.CONFLICT, 'DuplicateSubscription', '您已经订阅了此计划'))
      }

      const session = await createCheckoutSession(user.id, user.email, planType)

      return reply.status(200).send(
        successResponse({
          url: session.url,
          sessionId: session.id,
        }),
      )
    },
  )
  // cancel
  fastify.post(
    '/cancel',
    {
      schema: cancelSubscriptionSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user

      const subscription = await findUserActiveSubscription(user.id)

      if (!subscription) {
        return reply
          .status(400)
          .send(
            errorResponse(
              StatusCodes.SUBSCRIPTION_INVALID,
              'NoActiveSubscription',
              '您没有活跃的订阅可以取消',
            ),
          )
      }

      if (subscription.cancelAtPeriodEnd) {
        return reply
          .status(400)
          .send(
            errorResponse(
              StatusCodes.SUBSCRIPTION_INVALID,
              'AlreadyCanceled',
              '该订阅已经被取消，将在当前计费周期结束后停止',
            ),
          )
      }

      await prisma.stripeSubscription.update({
        where: { id: subscription.id },
        data: { transitionStatus: TransitionStatus.CANCELED_IN_PROGRESS },
      })

      const canceledSubscription = await cancelSubscription(subscription.stripeSubscriptionId)

      fastify.log.info(
        `用户 ${user.id} 已取消订阅 ${subscription.stripeSubscriptionId}，权益保留至当前计费周期结束`,
      )
      return reply.status(200).send(successResponse(canceledSubscription, '订阅取消中，请稍候'))
    },
  )
  // resubscribe
  fastify.post(
    '/resubscribe',
    {
      schema: resubscribeSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user

      const existingSubscription = await findUserActiveSubscription(user.id)

      if (!existingSubscription) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'NoSubscriptionFound', '未找到任何订阅记录'))
      }

      if (
        !(
          (existingSubscription.status === 'ACTIVE' ||
            existingSubscription.status === 'PAST_DUE') &&
          existingSubscription.cancelAtPeriodEnd === true
        )
      ) {
        let errorMessage = '只有已取消但未到期的订阅可以恢复。'
        if (
          existingSubscription.status === 'CANCELED' ||
          existingSubscription.status === 'INACTIVE'
        ) {
          errorMessage = '您的订阅已完全到期，请使用订阅功能创建新订阅'
        } else if (
          (existingSubscription.status === 'ACTIVE' ||
            existingSubscription.status === 'PAST_DUE') &&
          !existingSubscription.cancelAtPeriodEnd
        ) {
          errorMessage = '您的订阅当前处于活跃状态，无需恢复'
        }
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'InvalidSubscriptionState', errorMessage))
      }

      await prisma.stripeSubscription.update({
        where: { id: existingSubscription.id },
        data: { transitionStatus: TransitionStatus.RESTORING },
      })

      const reSubscription = await stripe.subscriptions.update(
        existingSubscription.stripeSubscriptionId,
        {
          cancel_at_period_end: false,
        },
      )

      fastify.log.info(`用户 ${user.id} 已恢复订阅 ${existingSubscription.stripeSubscriptionId}`)
      return reply.status(200).send(successResponse(reSubscription, '订阅恢复中，请稍候'))
    },
  )
  // status
  fastify.get(
    '/status',
    {
      schema: subscriptionStatusSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any

      const subscription = await findUserActiveSubscription(user.id)

      return reply.status(200).send(
        successResponse(
          subscription || {
            status: 'INACTIVE',
          },
        ),
      )
    },
  )
  // portal
  fastify.post(
    '/portal',
    {
      schema: createPortalSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const customer = await prisma.stripeCustomer.findUnique({
        where: { userId: user.id },
      })
      assert(customer, '未找到对应的支付客户')

      const session = await createPortalSession(customer.stripeCustomerId)
      return reply.status(200).send(
        successResponse({
          url: session.url,
        }),
      )
    },
  )
  // verify checkout session
  fastify.get(
    '/session/verify',
    {
      schema: verifySessionSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const { sessionId } = request.query as { sessionId: string }

      const session = await retrieveCheckoutSession(sessionId)
      assert(session, 'session not found')

      return reply.status(200).send(successResponse(session))
    },
  )
  // webhook
  fastify.post('/webhook', async (request: FastifyRequest, reply: FastifyReply) => {
    const signature = request.headers['stripe-signature']
    const rawBody = request.rawBody

    const quickResponse = () => reply.status(200).send(successResponse({ received: true }))
    if (!signature || !rawBody) {
      fastify.log.error('缺少必要信息:', {
        hasSignature: !!signature,
        hasRawBody: !!rawBody,
      })
      return quickResponse()
    }

    try {
      const strSignature = Array.isArray(signature) ? signature[0] : signature
      const event = stripe.webhooks.constructEvent(
        rawBody,
        strSignature,
        STRIPE_WEBHOOK_SECRET as string,
      )

      fastify.log.info(`事件验证成功: ${event.type} [${event.id}]`)

      quickResponse()

      handleWebhookEvent(event)
        .then(() => fastify.log.info(`事件处理成功: ${event.id}`))
        .catch((err) => fastify.log.error(`事件处理失败: ${err.message}`))
    } catch (err: any) {
      fastify.log.error('webhook处理失败:', err.message)
      return quickResponse()
    }
  })

  // 订阅状态轮询
  fastify.get<{ Querystring: { subscriptionId: string } }>(
    '/subscription-polling',
    {
      schema: subscriptionPollingSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const { subscriptionId } = request.query

      const subscription = await prisma.stripeSubscription.findUnique({
        where: { stripeSubscriptionId: subscriptionId },
      })
      assert(subscription, new Error('subscription not found'))
      const isProcessingComplete = subscription.transitionStatus === TransitionStatus.NORMAL
      return reply.status(200).send(successResponse(isProcessingComplete))
    },
  )
}

export default router
