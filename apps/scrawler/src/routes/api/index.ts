import { FastifyPluginAsync } from 'fastify'
import adminRouter from './admin'
import commonRouter from './common'
import credentialRouter from './credential'
import debugRouter from './debug'
import emailRouter from './email'
import emailManageRouter from './emailManage'
import enterpriseRouter from './enterprise'
import excludeListRouter from './excludeList'
import feishuRouter from './feishu'
import googleSheetRouter from './googleSheet'
import insRouter from './ins'
import kolRouter from './kol'
import projectRouter from './project'
import publicationStatisticsRouter from './publicationStatistics'
import publicationTagRouter from './publicationTag'
import quotaRouter from './quota'
import searchRouter from './search'
import similarRouter from './similar'
import strategyRouter from './strategy'
import strategyTemplateRouter from './strategyTemplate'
import subscriptionRouter from './subscription'
import tagRouter from './tag'
import tagAndNoteRouter from './tagAndNote'
import taskRouter from './task'
import ttInfoRouter from './tt'
import userMemberRouter from './userMember'
import xhsRouter from './xhs'
import ytbRouter from './ytb'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.register(emailRouter, { prefix: '/emails' })
  fastify.register(emailManageRouter, { prefix: '/email-manage' })
  fastify.register(projectRouter, { prefix: '/projects' })
  fastify.register(similarRouter, { prefix: '/similars' })
  fastify.register(kolRouter, { prefix: '/kols' })
  fastify.register(credentialRouter, { prefix: '/credentials' })
  fastify.register(searchRouter, { prefix: '/search' })
  fastify.register(ttInfoRouter, { prefix: '/ttInfo' })
  fastify.register(debugRouter, { prefix: '/debug' })
  fastify.register(commonRouter, { prefix: '/common' })
  fastify.register(userMemberRouter, { prefix: '/userMember' })
  fastify.register(enterpriseRouter, { prefix: '/enterprise' })
  fastify.register(googleSheetRouter, { prefix: '/googleSheet' })
  fastify.register(publicationStatisticsRouter, { prefix: '/publicationStatistics' })
  fastify.register(excludeListRouter, { prefix: '/excludeList' })
  fastify.register(quotaRouter, { prefix: '/quota' })
  fastify.register(taskRouter, { prefix: '/tasks' })
  fastify.register(tagRouter, { prefix: '/tags' })
  fastify.register(publicationTagRouter, { prefix: '/publicationTags' })
  fastify.register(adminRouter, { prefix: '/admin' })
  fastify.register(ytbRouter, { prefix: '/ytb' })
  fastify.register(insRouter, { prefix: '/ins' })
  fastify.register(xhsRouter, { prefix: '/xhs' })
  fastify.register(strategyRouter, { prefix: '/strategy' })
  fastify.register(strategyTemplateRouter, { prefix: '/strategyTemplate' })
  fastify.register(feishuRouter, { prefix: '/feishu' })
  fastify.register(subscriptionRouter, { prefix: '/subscription' })
  fastify.register(tagAndNoteRouter, { prefix: '/tagAndNote' })
}

export default router
