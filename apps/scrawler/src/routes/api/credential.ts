import {
  errorResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
} from '@/common/response/response'
import { CommonResponseSchema } from '@/config/swagger'
import Sentry from '@/infras/sentry'
import { needSupabaseAuth } from '@/middlewares/auth'
import { isWeb, verifyDeviceBan } from '@/utils/auth'
import { populateProviderCrential } from '@/utils/populate'
import { prisma, UserSignatureLog } from '@repo/database'
import { FastifyPluginAsync } from 'fastify'
import { CredentialReq, CredentialReqSchema } from '../schemas/credential'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { accessToken, refreshToken, provider: providerKey } = request.body as any
      const user = (request as any).user

      const provider = await prisma.provider.findFirst({
        where: {
          key: provider<PERSON>ey,
        },
      })

      if (!provider) {
        return reply.status(400).send({ error: 'Invalid provider' })
      }

      const credential = await prisma.providerCredential.upsert({
        where: {
          providerId_createdBy: {
            createdBy: user.id,
            providerId: provider.id,
          },
        },
        create: {
          providerId: provider.id,
          createdBy: user.id,
          accessToken,
          refreshToken,
        },
        update: {
          accessToken,
          refreshToken,
        },
        include: {
          Provider: true,
        },
      })

      // await prepareCredential(credential);

      return reply.status(201).send(populateProviderCrential(credential))
    },
  )

  fastify.get(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { user } = request as any
      const credential = await prisma.providerCredential.findMany({
        where: {
          createdBy: user.id,
        },
        include: {
          Provider: true,
        },
      })
      return reply.send({
        success: true,
        data: credential.map(populateProviderCrential),
      })
    },
  )

  fastify.post(
    '/signature',
    {
      schema: {
        summary: '用户浏览器签名',
        tags: ['credential'],
        body: CredentialReqSchema,
        response: CommonResponseSchema,
      },
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { browserId, extensionId } = request.body as CredentialReq
        const { easykolversion } = request.headers
        const user = (request as any).user
        if (!browserId && !extensionId) {
          return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST))
        }
        await prisma.userSignatureLog.create({
          data: {
            userId: user.id,
            browserId: browserId ?? '',
            extensionId: extensionId ?? '',
            version: easykolversion ?? '',
          } as UserSignatureLog,
        })
        // web端不封禁
        verifyDeviceBan(user.id, browserId ?? '', await isWeb(request))
        return reply.status(200).send(successResponse('success'))
      } catch (err) {
        Sentry.captureException(err)
        return reply.status(500).send(serverErrorResponse)
      }
    },
  )
}

export default router
