import { uploadFileByBuffer } from '@/api/aliyun'
import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes as ErrorCodes, throwError } from '@/common/errors/statusCodes'
import {
  StatusCodes,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { HOST } from '@/config/env'
import {
  audienceAnalysisAuthorsColumns,
  audienceAnalysisPostsColumns,
} from '@/config/exportColumns'
import {
  InstagramEmbeddingTaskMetrics,
  InstagramVisualTaskMetrics,
  TiktokEmbeddingTaskMetrics,
  TiktokVisualTaskMetrics,
  YoutubeEmbeddingTaskMetrics,
  YoutubeVisualTaskMetrics,
} from '@/config/metricsDescriptions'
import { FullResponseSchema } from '@/config/swagger'
import { QuotaCost } from '@/enums/QuotaCost'
import { BatchAudienceMode } from '@/enums/TaskMode'
import Sentry from '@/infras/sentry'
import { withAuth } from '@/middlewares/auth'
import { responseBasedQuota } from '@/middlewares/quota'
import TaskService from '@/services/task.ts'
import { AudienceAnalysisResult } from '@/types/audience'
import { exportToExcel } from '@/utils/excel'
import { KolPlatform, QuotaType, SimilarChannelTaskStatus, TaskType, prisma } from '@repo/database'
import dayjs from 'dayjs'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  AudienceTasksExportResponseSchema,
  BatchAudienceRequest,
  BatchAudienceRequestSchema,
  BatchAudienceTasksResponseSchema,
  BatchFailQueueTasksRequest,
  BatchFailQueueTasksSchema,
  BatchTaskDetailParams,
  BatchTaskDetailParamsSchema,
  BatchTaskDetailResponse,
  BatchTaskDetailResponseSchema,
  BatchTaskExportParams,
  BatchTaskExportParamsSchema,
  BatchTaskExportResponseSchema,
  BatchTaskIdsRequest,
  BatchTaskIdsRequestSchema,
  BatchTaskListQuery,
  BatchTaskListQuerySchema,
  BatchTaskListResponse,
  BatchTaskListResponseSchema,
  FailAllQueueTasksRequest,
  FailAllQueueTasksSchema,
  FailQueueTaskByIdBody,
  FailQueueTaskByIdBodySchema,
  FailQueueTaskByIdParams,
  FailQueueTaskByIdParamsSchema,
  QueueTasksQuery,
  QueueTasksQuerySchema,
  TaskActionRequest,
  TaskActionRequestSchema,
  TaskIdParams,
  TaskIdParamsSchema,
  TaskListQuery,
  TaskListQuerySchema,
  UnterminatedTasksQuery,
  UnterminatedTasksQuerySchema,
} from '../schemas/task'

/*
 * 任务相关接口.
 */
const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // 获取用户项目列表
  fastify.get<{
    Querystring: {
      email: string
    }
  }>('/projects', withAuth(), async (request, reply) => {
    try {
      const { email } = request.query

      if (!email) {
        return reply.status(400).send(notFoundResponse('email is required'))
      }

      // 查找用户ID
      const user = await prisma.userInfo.findFirst({
        where: { email },
        select: { userId: true },
      })

      if (!user) {
        return reply.status(404).send(notFoundResponse('user not found'))
      }

      // 查找该用户有任务的项目
      const projects = await prisma.project.findMany({
        where: {
          ProjectMembership: {
            some: {
              userId: user.userId,
            },
          },
          SimilarChannelTask: {
            some: {}, // 确保项目有任务
          },
          deletedAt: null,
        },
        select: {
          id: true,
          title: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      return reply.send(successResponse(projects))
    } catch (error) {
      console.error('Failed to fetch projects:', error)
      return reply
        .status(500)
        .send(errorResponse(StatusCodes.SERVER_ERROR, 'Failed to fetch projects'))
    }
  })

  // 查询任务列表
  fastify.get<{ Querystring: TaskListQuery }>(
    '/list',
    {
      schema: {
        tags: ['task'],
        summary: '查询任务列表',
        description: '查询任务列表，支持按照邮箱、项目、平台、状态、类型等条件筛选',
        querystring: TaskListQuerySchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { email, projectId, status, taskType, taskReason, platform, isZeroSimilarTask } =
        request.query
      const page = Number(request.query.page) || 1
      const pageSize = Number(request.query.pageSize) || 10

      // 构建查询条件
      const conditions = []

      // 如果提供了邮箱，先查找用户
      if (email) {
        const user = await prisma.userInfo.findFirst({
          where: { email },
          select: { userId: true },
        })
        if (user) {
          conditions.push(`"createdBy" = '${user.userId}'`)
        }
      }

      if (projectId) {
        conditions.push(`t."projectId" = '${projectId}'`)
      }

      if (platform) {
        conditions.push(`t.params->>'platform' = '${platform.toUpperCase()}'`)
      }

      // is zero only support  similar task
      if (isZeroSimilarTask) {
        conditions.push(
          `t.status = '${SimilarChannelTaskStatus.COMPLETED}' and t.type = '${TaskType.SIMILAR}' and (t.meta->>'final_kols_count' is null or t.meta->>'final_kols_count' = '0') and t.meta->>'worker_pid' is not null `,
        )
      } else {
        if (taskType) {
          conditions.push(`t.type = '${taskType}'`)
        }
        if (status) {
          conditions.push(`t.status = '${status}'`)
        }
      }

      if (taskReason) {
        conditions.push(`t.reason = '${taskReason}'`)
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

      // 查询总数
      const countSql = `
        SELECT COUNT(*) as count 
        FROM "SimilarChannelTask" t
        ${whereClause}
      `
      const totalResult = await prisma.$queryRawUnsafe(countSql)
      const total = Number((totalResult as any)[0].count)

      // 查询任务列表
      const listSql = `
        SELECT 
          t.*,
          p.title as "projectTitle",
          u.email as "userEmail",
          upv."version" as "version"
        FROM "SimilarChannelTask" t
        LEFT JOIN "Project" p ON t."projectId" = p.id
        LEFT JOIN "UserInfo" u ON t."createdBy" = u."userId"
        LEFT JOIN "UserPluginVersion" upv ON t."createdBy" = upv."userId"
        ${whereClause}
        ORDER BY t."createdAt" DESC
        LIMIT ${pageSize}
        OFFSET ${(page - 1) * pageSize}
      `
      const tasks = await prisma.$queryRawUnsafe(listSql)

      // 处理meta字段映射的辅助函数
      const mapMetaFields = (meta: any, platform: string, taskType: string, params: any) => {
        if (!meta || typeof meta !== 'object') return { mappedMeta: meta, metaOrder: null }

        let metricsMap = null

        if (platform === 'INSTAGRAM' && taskType === 'SIMILAR') {
          metricsMap =
            params?.insMode === 1 ? InstagramEmbeddingTaskMetrics : InstagramVisualTaskMetrics
        } else if (platform === 'YOUTUBE' && taskType === 'SIMILAR') {
          metricsMap =
            params?.ytbMode === 1 ? YoutubeEmbeddingTaskMetrics : YoutubeVisualTaskMetrics
        } else if (platform === 'TIKTOK' && taskType === 'SIMILAR') {
          metricsMap = params?.ttMode === 1 ? TiktokEmbeddingTaskMetrics : TiktokVisualTaskMetrics
        }

        if (!metricsMap) {
          return { mappedMeta: meta, metaOrder: null }
        }

        const mappedMeta: any = {}
        const metaOrder: string[] = []

        Object.entries(metricsMap).forEach(([key, description]) => {
          if (key in meta) {
            mappedMeta[description as string] = meta[key]
            metaOrder.push(description as string)
          }
        })

        Object.entries(meta).forEach(([key, value]) => {
          if (!(key in metricsMap)) {
            mappedMeta[key] = value
            metaOrder.push(key)
          }
        })

        return { mappedMeta, metaOrder }
      }

      const data = (tasks as any[]).map((task) => {
        const params = task.params as any
        const platform = params?.platform || ''
        const taskType = task.type
        const metaResult = mapMetaFields(task.meta, platform, taskType, params)

        return {
          id: task.id,
          email: task.userEmail || '',
          projectName: task.projectTitle || '',
          platform,
          source: params?.source || '',
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
          params: task.params,
          errors: task.errors,
          hasResult: !!task.result,
          result: task.result,
          meta: metaResult.mappedMeta,
          metaOrder: metaResult.metaOrder,
          taskType,
          reason: task.reason,
          isTerminated: task.isTerminated,
          status: task.status,
          candidate: task.candidate,
          version: task.version,
        }
      })

      return reply.send(
        successResponse({
          total,
          page,
          pageSize,
          data,
        }),
      )
    },
  )

  /**
   * 获取最近一周内有失败任务过的邮箱,utc时间
   */
  fastify.get('/failed-emails', withAuth(), async (request, reply) => {
    try {
      const nowTime = dayjs().utc()
      const failedTasks = await prisma.similarChannelTask.findMany({
        where: {
          status: SimilarChannelTaskStatus.FAILED,
          createdAt: {
            gte: nowTime.subtract(7, 'day').toDate(),
          },
          createdBy: {
            not: null,
          },
        },
        select: {
          createdBy: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      const userIds = [
        ...new Set(
          failedTasks.map((task) => task.createdBy).filter((id): id is string => id !== null),
        ),
      ]

      if (userIds.length === 0) {
        return reply.status(200).send(successResponse([]))
      }

      const users = await prisma.userInfo.findMany({
        where: {
          userId: {
            in: userIds,
          },
          email: {
            not: null,
          },
        },
        select: {
          email: true,
        },
      })

      const emails = users.map((user) => user.email as string)
      return reply.status(200).send(successResponse(emails))
    } catch (error) {
      console.error('Failed to fetch failed emails:', error)
      return reply
        .status(500)
        .send(errorResponse(StatusCodes.SERVER_ERROR, 'Failed to fetch failed emails'))
    }
  })

  // 根据taskId获取任务的result
  fastify.get('/result/:taskId', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { taskId } = request.params as { taskId: string }

      if (!taskId) {
        return reply.status(400).send(serverErrorResponse('taskId不能为空'))
      }

      const task = await prisma.similarChannelTask.findUnique({
        where: { id: taskId },
      })

      if (!task) {
        return reply.status(404).send(serverErrorResponse('任务不存在'))
      }

      if (task.createdBy !== user.id) {
        return reply.status(403).send(serverErrorResponse('您没有权限查看此任务'))
      }

      const responseData = {
        id: task.id,
        status: task.status,
        result: task.result,
      }

      return reply.status(200).send(successResponse(responseData))
    } catch (error) {
      console.error('获取任务详情失败:', error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  /**
   * 让用户支持绕过项目概念，直接获取所有项目中最新的任务
   */
  fastify.get('/latest', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { taskType } = request.query as { taskType: TaskType }
      if (!taskType) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, 'taskType不能为空'))
      }
      const tasks = await prisma.similarChannelTask.findMany({
        where: {
          createdBy: user.id,
          type: taskType,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      })
      return reply.status(200).send(successResponse(tasks[0] || null))
    } catch (error) {
      console.error('获取最新任务失败:', error)
      Sentry.captureException(error)
      return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, '获取最新任务失败'))
    }
  })

  /**
   * 控制任务状态（暂停/恢复/完成）
   */
  fastify.post<{ Params: TaskIdParams; Body: TaskActionRequest }>(
    '/:taskId/action',
    {
      schema: {
        tags: ['task'],
        summary: '控制任务状态',
        description: '控制任务的状态，可以暂停、恢复或完成任务',
        params: TaskIdParamsSchema,
        body: TaskActionRequestSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Params: TaskIdParams; Body: TaskActionRequest }>, reply) => {
      const { taskId } = request.params
      const { action, reason } = request.body
      const user = (request as any).user

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task?.createdBy !== user.id) {
        throwError(StatusCodes.FORBIDDEN, 'FORBIDDEN', '您没有权限控制此任务')
      }

      const updatedTask = await TaskService.getInstance().controlTask(taskId, action, reason)

      let message = ''
      switch (action) {
        case 'pause':
          message = '任务已成功暂停'
          break
        case 'resume':
          message = '任务已成功恢复'
          break
        case 'complete':
          message = '任务已成功完成'
          break
      }

      return reply.status(200).send(successResponse(updatedTask, message))
    },
  )

  /**
   * 获取队列中正在活动的任务
   */
  fastify.get<{ Querystring: QueueTasksQuery }>(
    '/queue/status',
    {
      schema: {
        tags: ['task'],
        summary: '获取队列中正在活动的任务',
        description: '根据队列名称获取当前正在处理的任务列表',
        querystring: QueueTasksQuerySchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Querystring: QueueTasksQuery }>, reply) => {
      const { queueName, status } = request.query
      const activeTasks = await TaskService.getInstance().getQueueTasksByStatus(queueName, status)

      return reply.status(200).send(
        successResponse(
          {
            queueName,
            count: activeTasks.length,
            tasks: activeTasks,
          },
          `成功获取 ${queueName} 队列中的活动任务`,
        ),
      )
    },
  )

  /**
   * 根据队列名称手动失败所有活动任务
   */
  fastify.post<{ Body: FailAllQueueTasksRequest }>(
    '/queue/fail-all',
    {
      schema: {
        tags: ['task'],
        summary: '手动失败队列中所有活动任务',
        description: '根据队列名称手动失败所有正在处理中的任务',
        body: FailAllQueueTasksSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: FailAllQueueTasksRequest }>, reply) => {
      const { queueName, reason } = request.body

      const result = await TaskService.getInstance().failAllQueueTasks(
        queueName,
        reason || '管理员手动操作',
      )

      return reply
        .status(200)
        .send(
          successResponse(
            result,
            result.count > 0
              ? `成功将 ${queueName} 队列中的 ${result.count} 个任务标记为失败`
              : `${queueName} 队列中没有活动的任务`,
          ),
        )
    },
  )

  /**
   * 根据任务ID手动失败特定任务
   */
  fastify.post<{ Params: FailQueueTaskByIdParams; Body: FailQueueTaskByIdBody }>(
    '/queue/:queueName/job/:jobId/fail',
    {
      schema: {
        tags: ['task'],
        summary: '手动失败特定队列任务',
        description: '根据队列名称和任务ID手动失败特定任务',
        params: FailQueueTaskByIdParamsSchema,
        body: FailQueueTaskByIdBodySchema,
      },
      ...withAuth(),
    },
    async (
      request: FastifyRequest<{ Params: FailQueueTaskByIdParams; Body: FailQueueTaskByIdBody }>,
      reply,
    ) => {
      const { queueName, jobId } = request.params
      const { reason } = request.body

      const result = await TaskService.getInstance().failQueueTaskById(
        queueName,
        jobId,
        reason || '管理员手动操作',
      )

      if (result.success) {
        return reply.status(200).send(successResponse(result, result.message))
      } else {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, result.message))
      }
    },
  )

  /**
   * 批量手动失败队列任务
   */
  fastify.post<{ Body: BatchFailQueueTasksRequest }>(
    '/queue/jobs/fail',
    {
      schema: {
        tags: ['task'],
        summary: '批量手动失败队列任务',
        description: '根据队列名称和任务ID列表批量手动失败任务',
        body: BatchFailQueueTasksSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: BatchFailQueueTasksRequest }>, reply) => {
      const { queueName, jobIds, reason } = request.body
      if (!jobIds || jobIds.length === 0) {
        throwError(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', '任务ID列表不能为空')
      }

      // 批量处理任务
      const results = await Promise.all(
        jobIds.map((jobId) =>
          TaskService.getInstance().failQueueTaskById(queueName, jobId, reason || '管理员手动操作'),
        ),
      )

      // 统计成功和失败的数量
      const successCount = results.filter((r) => r.success).length
      const failedCount = results.length - successCount

      // 构建详细结果
      const detailedResults = jobIds.map((jobId, index) => ({
        jobId,
        success: results[index].success,
        message: results[index].message,
      }))

      return reply.status(200).send(
        successResponse(
          {
            totalJobs: jobIds.length,
            successCount,
            failedCount,
            details: detailedResults,
          },
          `批量处理完成：${successCount}个成功，${failedCount}个失败`,
        ),
      )
    },
  )

  // 查询项目下指定类型的未终结任务
  fastify.get<{
    Querystring: UnterminatedTasksQuery
  }>(
    '/unterminatedTasks',
    {
      schema: {
        tags: ['tasks'],
        summary: '查询未终结任务',
        description: '根据项目ID和任务类型查询该项目下最新一批未终结的任务',
        querystring: UnterminatedTasksQuerySchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Querystring: UnterminatedTasksQuery }>, reply) => {
      const { projectId, taskType } = request.query
      const tasks = await TaskService.getInstance().findUnterminatedTasksByType(projectId, taskType)
      return reply.status(200).send(successResponse(tasks))
    },
  )

  // get batch task details
  fastify.post<{ Body: BatchTaskIdsRequest }>(
    '/batch',
    {
      schema: {
        tags: ['tasks'],
        summary: '批量轮询任务状态',
        description: '传入一组taskId，批量获取任务状态和详情',
        body: BatchTaskIdsRequestSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: BatchTaskIdsRequest }>, reply) => {
      const { taskIds } = request.body
      const { user } = request as any

      const tasks = await TaskService.getInstance().getUserBatchTasks(taskIds, user.id)
      return reply.status(200).send(successResponse(tasks))
    },
  )

  // get task detail
  fastify.get<{ Params: TaskIdParams }>(
    '/:taskId',
    {
      schema: {
        tags: ['tasks'],
        summary: '获取单个任务详情',
        description: '根据taskId获取任务的完整详情',
        params: TaskIdParamsSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Params: TaskIdParams }>, reply) => {
      const { taskId } = request.params
      const { user } = request as any
      const task = await TaskService.getInstance().getTaskById(taskId)

      if (!task.createdBy || task.createdBy !== user.id) {
        throwError(ErrorCodes.FORBIDDEN, 'no permission')
      }

      return reply.status(200).send(successResponse(task))
    },
  )

  // create-batch-audience
  fastify.post<{ Body: BatchAudienceRequest }>(
    '/audience-tasks',
    {
      schema: {
        tags: ['tasks'],
        summary: '批量创建受众分析任务',
        description: '批量创建用户或帖子的受众分析任务，支持自动分批和延迟执行',
        body: BatchAudienceRequestSchema,
        response: FullResponseSchema(BatchAudienceTasksResponseSchema),
      },
      ...withAuth(
        responseBasedQuota({
          // 检查配额 - 按最大可能扣费检查
          checkQuota: (request) => {
            const { links } = request.body as BatchAudienceRequest
            return links.length * QuotaCost.AUDIENCE_ANALYSIS
          },
          // 根据响应计算实际扣费
          calculateCost: (responseData) => {
            const successCount = responseData?.data?.summary?.success || 0
            return successCount * QuotaCost.AUDIENCE_ANALYSIS
          },
          // 根据mode动态确定配额类型
          quotaType: (responseData, request) => {
            const { mode } = request.body as BatchAudienceRequest
            return mode === BatchAudienceMode.AUTHOR
              ? QuotaType.AUDIENCE_ANALYSIS
              : QuotaType.POST_AUDIENCE
          },
          // 提取元数据
          extractMetadata: (responseData, request) => {
            const { links } = request.body as BatchAudienceRequest
            const summary = responseData?.data?.summary || {}
            const results = responseData?.data?.results || []
            return {
              batchId: results[0]?.taskId ? results[0].taskId.split('_')[0] : '',
              totalLinks: links.length,
              successCount: summary.success || 0,
              failedCount: summary.failed || 0,
              mode: summary.mode || '',
            }
          },
        }),
      ),
    },
    async (request: FastifyRequest<{ Body: BatchAudienceRequest }>, reply) => {
      const { links, mode } = request.body
      const user = (request as any).user

      const result = await TaskService.getInstance().createBatchAudienceTasks(links, mode, user.id)
      return reply.status(200).send(successResponse(result, 'Batch tasks created successfully'))
    },
  )

  // 查询用户的批次任务列表
  fastify.get<{ Querystring: BatchTaskListQuery }>(
    '/batch-tasks',
    {
      schema: {
        tags: ['tasks'],
        summary: '查询批次任务列表',
        description: '查询当前用户创建的所有批次任务',
        querystring: BatchTaskListQuerySchema,
        response: FullResponseSchema(BatchTaskListResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const { page = 1, pageSize = 20, taskType } = request.query

      const where: any = {
        userId: user.id,
      }

      if (taskType) {
        where.taskType = taskType
      }

      const [total, records] = await Promise.all([
        prisma.batchTaskRecord.count({ where }),
        prisma.batchTaskRecord.findMany({
          where,
          skip: (page - 1) * pageSize,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
        }),
      ])

      // 批量查询每个批次的任务状态
      const batchesWithProgress = await Promise.all(
        records.map(async (batch) => {
          const tasks = await prisma.similarChannelTask.findMany({
            where: {
              id: {
                in: batch.tasks,
              },
            },
            select: {
              status: true,
            },
          })

          const statusCounts = tasks.reduce(
            (acc, task) => {
              if (task.status === SimilarChannelTaskStatus.COMPLETED) {
                acc.completed++
              } else if (task.status === SimilarChannelTaskStatus.FAILED) {
                acc.failed++
              } else if (task.status === SimilarChannelTaskStatus.PROCESSING) {
                acc.processing++
              } else {
                acc.pending++
              }
              return acc
            },
            { completed: 0, failed: 0, processing: 0, pending: 0 },
          )

          const progress = Math.round((statusCounts.completed / tasks.length) * 100) || 0

          return {
            ...batch,
            progress,
            statusCounts,
            totalTasks: tasks.length,
          }
        }),
      )

      const response: BatchTaskListResponse = {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
        data: batchesWithProgress,
      }

      return reply.status(200).send(successResponse(response))
    },
  )

  // 查询批次任务进度
  fastify.get<{ Params: BatchTaskDetailParams }>(
    '/audience-tasks/batch/:batchId',
    {
      schema: {
        tags: ['tasks'],
        summary: '查询批次任务进度',
        description: '查询指定批次ID的任务执行进度和详情',
        params: BatchTaskDetailParamsSchema,
        response: FullResponseSchema(BatchTaskDetailResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { batchId } = request.params
      const { user } = request as any

      // 查询批次记录
      const batchRecord = await prisma.batchTaskRecord.findFirst({
        where: {
          id: batchId,
          userId: user.id,
        },
      })

      if (!batchRecord) {
        throwError(ErrorCodes.BAD_REQUEST, 'Batch task record not found')
      }

      // 查询所有关联任务的状态
      const tasks = await prisma.similarChannelTask.findMany({
        where: {
          id: {
            in: batchRecord.tasks,
          },
        },
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      const statusCounts = tasks.reduce(
        (acc, task) => {
          if (task.status === SimilarChannelTaskStatus.COMPLETED) {
            acc.completed++
          } else if (task.status === SimilarChannelTaskStatus.FAILED) {
            acc.failed++
          } else if (task.status === SimilarChannelTaskStatus.PROCESSING) {
            acc.processing++
          } else {
            acc.pending++
          }
          return acc
        },
        { completed: 0, failed: 0, processing: 0, pending: 0 },
      )

      const progress =
        Math.round(((statusCounts.completed + statusCounts.failed) / tasks.length) * 100) || 0

      const response: BatchTaskDetailResponse = {
        id: batchRecord.id,
        userId: batchRecord.userId,
        taskType: batchRecord.taskType,
        rawInput: batchRecord.rawInput,
        result: batchRecord.result,
        summary: batchRecord.summary,
        tasks: batchRecord.tasks,
        createdAt: batchRecord.createdAt,
        updatedAt: batchRecord.updatedAt,
        progress,
        statusCounts,
        totalTasks: tasks.length,
      }

      return reply.status(200).send(successResponse(response))
    },
  )

  // 根据批次ID导出受众分析任务结果
  fastify.get<{ Params: BatchTaskExportParams }>(
    '/audience-tasks/batch/:batchId/export',
    {
      schema: {
        tags: ['tasks'],
        summary: '导出批次受众分析结果',
        description: '根据批次ID导出该批次所有受众分析任务结果为Excel文件',
        params: BatchTaskExportParamsSchema,
        response: FullResponseSchema(BatchTaskExportResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { batchId } = request.params
      const { user } = request as any

      // 查询批次记录
      const batchRecord = await prisma.batchTaskRecord.findFirst({
        where: {
          id: batchId,
          userId: user.id,
        },
      })

      if (!batchRecord) {
        throwError(ErrorCodes.BAD_REQUEST, 'Batch task record not found')
      }

      const tasks = await prisma.similarChannelTask.findMany({
        where: {
          id: {
            in: batchRecord.tasks,
          },
          status: SimilarChannelTaskStatus.COMPLETED,
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      if (tasks.length === 0) {
        throwError(ErrorCodes.BAD_REQUEST, 'No completed tasks found in this batch')
      }

      const authorTasks = tasks.filter((task) => task.type === TaskType.AUDIENCE_ANALYSIS)
      const postTasks = tasks.filter((task) => task.type === TaskType.POST_AUDIENCE)

      const [authorsData, postsData] = await Promise.all([
        authorTasks.length > 0
          ? Promise.all(
              authorTasks.map(async (task) => {
                const params = task.params as any
                const result = task.result as unknown as AudienceAnalysisResult

                const topCountries = result?.regionAnalysisResult?.statistics?.slice(0, 5) || []
                const developmentStats = result?.regionAnalysisResult?.developmentStatistics || []

                const country1 = topCountries[0]
                  ? `${topCountries[0].region} (${topCountries[0].percentage})`
                  : ''
                const country2 = topCountries[1]
                  ? `${topCountries[1].region} (${topCountries[1].percentage})`
                  : ''
                const country3 = topCountries[2]
                  ? `${topCountries[2].region} (${topCountries[2].percentage})`
                  : ''
                const country4 = topCountries[3]
                  ? `${topCountries[3].region} (${topCountries[3].percentage})`
                  : ''
                const country5 = topCountries[4]
                  ? `${topCountries[4].region} (${topCountries[4].percentage})`
                  : ''

                const marketLevelText =
                  developmentStats.length > 0
                    ? developmentStats
                        .map((dev) => `${dev.developmentLevel}:${dev.percentage}`)
                        .join(', ')
                    : ''

                let link = ''
                const platform = params?.platform || ''
                const source = params?.source || ''

                switch (platform.toUpperCase()) {
                  case KolPlatform.TIKTOK:
                    link = `https://www.tiktok.com/@${source}`
                    break
                  case KolPlatform.YOUTUBE:
                    if (source.startsWith('UC')) {
                      link = `https://www.youtube.com/channel/${source}`
                    } else {
                      link = `https://www.youtube.com/@${source}`
                    }
                    break
                  case KolPlatform.INSTAGRAM:
                    link = `https://www.instagram.com/${source}`
                    break
                  default:
                    link = source
                }

                // 对于YOUTUBE平台，尝试从kolInfo表查询title作为username
                let username = source
                if (platform.toUpperCase() === KolPlatform.YOUTUBE) {
                  try {
                    const kolInfo = await prisma.kolInfo.findFirst({
                      where: {
                        platform: KolPlatform.YOUTUBE,
                        platformAccount: source,
                      },
                      select: {
                        title: true,
                      },
                    })
                    if (kolInfo?.title) {
                      username = kolInfo.title
                    }
                  } catch (error) {
                    // 如果查询失败，继续使用原来的source作为username
                    console.warn('Failed to query kolInfo for YouTube source:', source, error)
                  }
                }

                return {
                  username,
                  link,
                  platform,
                  malePercentage: result?.userPortraitResult?.gender?.male || '',
                  femalePercentage: result?.userPortraitResult?.gender?.female || '',
                  under18Percentage: result?.userPortraitResult?.age?.under18 || '',
                  age18to25Percentage: result?.userPortraitResult?.age?.age18to25 || '',
                  age25to45Percentage: result?.userPortraitResult?.age?.age25to45 || '',
                  above45Percentage: result?.userPortraitResult?.age?.above45 || '',
                  country1,
                  country2,
                  country3,
                  country4,
                  country5,
                  marketLevel: marketLevelText,
                  createdAt: dayjs(task.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                }
              }),
            )
          : Promise.resolve([]),

        postTasks.length > 0
          ? Promise.all(
              postTasks.map(async (task) => {
                const params = task.params as any
                const result = task.result as unknown as AudienceAnalysisResult

                const topCountries = result?.regionAnalysisResult?.statistics?.slice(0, 5) || []
                const developmentStats = result?.regionAnalysisResult?.developmentStatistics || []

                const country1 = topCountries[0]
                  ? `${topCountries[0].region} (${topCountries[0].percentage})`
                  : ''
                const country2 = topCountries[1]
                  ? `${topCountries[1].region} (${topCountries[1].percentage})`
                  : ''
                const country3 = topCountries[2]
                  ? `${topCountries[2].region} (${topCountries[2].percentage})`
                  : ''
                const country4 = topCountries[3]
                  ? `${topCountries[3].region} (${topCountries[3].percentage})`
                  : ''
                const country5 = topCountries[4]
                  ? `${topCountries[4].region} (${topCountries[4].percentage})`
                  : ''

                const marketLevelText =
                  developmentStats.length > 0
                    ? developmentStats
                        .map((dev) => `${dev.developmentLevel}:${dev.percentage}`)
                        .join(', ')
                    : ''

                return {
                  postLink: params?.postLink || '',
                  platform: params?.platform || '',
                  malePercentage: result?.userPortraitResult?.gender?.male || '',
                  femalePercentage: result?.userPortraitResult?.gender?.female || '',
                  under18Percentage: result?.userPortraitResult?.age?.under18 || '',
                  age18to25Percentage: result?.userPortraitResult?.age?.age18to25 || '',
                  age25to45Percentage: result?.userPortraitResult?.age?.age25to45 || '',
                  above45Percentage: result?.userPortraitResult?.age?.above45 || '',
                  country1,
                  country2,
                  country3,
                  country4,
                  country5,
                  marketLevel: marketLevelText,
                  createdAt: dayjs(task.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                }
              }),
            )
          : Promise.resolve([]),
      ])

      const sheets = []

      if (authorsData.length > 0) {
        sheets.push({
          name: 'Authors',
          data: authorsData,
          columns: audienceAnalysisAuthorsColumns,
        })
      }

      if (postsData.length > 0) {
        sheets.push({
          name: 'Posts',
          data: postsData,
          columns: audienceAnalysisPostsColumns,
        })
      }

      const dateStr = dayjs().format('YYYY-MM-DD_HH-mm-ss')
      const fileName = `Batch-Audience_${batchId}_${dateStr}_${user.email}.xlsx`
      const filePath = `excel/${fileName}`
      const buffer = await exportToExcel(sheets)

      await uploadFileByBuffer(filePath, buffer)

      reply
        .status(200)
        .send(
          successResponse(
            `${HOST ?? ''}/files/excel/${fileName}`,
            'Excel file exported successfully',
          ),
        )
    },
  )

  // 导出近30天受众分析任务结果
  fastify.get(
    '/audience-tasks/export',
    {
      schema: {
        tags: ['tasks'],
        summary: '导出受众分析历史',
        description: '导出该用户近一个月内创建的受众分析任务结果为Excel文件',
        response: FullResponseSchema(AudienceTasksExportResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const oneMonthAgo = dayjs().subtract(1, 'month').toDate()

      const tasks = await prisma.similarChannelTask.findMany({
        where: {
          createdBy: user.id,
          type: {
            in: [TaskType.AUDIENCE_ANALYSIS, TaskType.POST_AUDIENCE],
          },
          status: SimilarChannelTaskStatus.COMPLETED,
          createdAt: {
            gte: oneMonthAgo,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })

      if (tasks.length === 0) {
        throwError(ErrorCodes.BAD_REQUEST, 'no audience analysis history found')
      }

      const authorTasks = tasks.filter((task) => task.type === TaskType.AUDIENCE_ANALYSIS)
      const postTasks = tasks.filter((task) => task.type === TaskType.POST_AUDIENCE)

      // 并发处理数据转换
      const [authorsData, postsData] = await Promise.all([
        // 处理 Authors 数据
        authorTasks.length > 0
          ? Promise.all(
              authorTasks.map(async (task) => {
                const params = task.params as any
                const result = task.result as unknown as AudienceAnalysisResult

                const topCountries = result?.regionAnalysisResult?.statistics?.slice(0, 5) || []
                const developmentStats = result?.regionAnalysisResult?.developmentStatistics || []

                const country1 = topCountries[0]
                  ? `${topCountries[0].region} (${topCountries[0].percentage})`
                  : ''
                const country2 = topCountries[1]
                  ? `${topCountries[1].region} (${topCountries[1].percentage})`
                  : ''
                const country3 = topCountries[2]
                  ? `${topCountries[2].region} (${topCountries[2].percentage})`
                  : ''
                const country4 = topCountries[3]
                  ? `${topCountries[3].region} (${topCountries[3].percentage})`
                  : ''
                const country5 = topCountries[4]
                  ? `${topCountries[4].region} (${topCountries[4].percentage})`
                  : ''

                const marketLevelText =
                  developmentStats.length > 0
                    ? developmentStats
                        .map((dev) => `${dev.developmentLevel}:${dev.percentage}`)
                        .join(', ')
                    : ''

                let link = ''
                const platform = params?.platform || ''
                const source = params?.source || ''

                switch (platform.toUpperCase()) {
                  case KolPlatform.TIKTOK:
                    link = `https://www.tiktok.com/@${source}`
                    break
                  case KolPlatform.YOUTUBE:
                    if (source.startsWith('UC')) {
                      link = `https://www.youtube.com/channel/${source}`
                    } else {
                      link = `https://www.youtube.com/@${source}`
                    }
                    break
                  case KolPlatform.INSTAGRAM:
                    link = `https://www.instagram.com/${source}`
                    break
                  default:
                    link = source
                }

                // 对于YOUTUBE平台，尝试从kolInfo表查询title作为username
                let username = source
                if (platform.toUpperCase() === KolPlatform.YOUTUBE) {
                  try {
                    const kolInfo = await prisma.kolInfo.findFirst({
                      where: {
                        platform: KolPlatform.YOUTUBE,
                        platformAccount: source,
                      },
                      select: {
                        title: true,
                      },
                    })
                    if (kolInfo?.title) {
                      username = kolInfo.title
                    }
                  } catch (error) {
                    // 如果查询失败，继续使用原来的source作为username
                    console.warn('Failed to query kolInfo for YouTube source:', source, error)
                  }
                }

                return {
                  username, // 用户名
                  link, // 链接用于超链接
                  platform,
                  malePercentage: result?.userPortraitResult?.gender?.male || '',
                  femalePercentage: result?.userPortraitResult?.gender?.female || '',
                  under18Percentage: result?.userPortraitResult?.age?.under18 || '',
                  age18to25Percentage: result?.userPortraitResult?.age?.age18to25 || '',
                  age25to45Percentage: result?.userPortraitResult?.age?.age25to45 || '',
                  above45Percentage: result?.userPortraitResult?.age?.above45 || '',
                  country1,
                  country2,
                  country3,
                  country4,
                  country5,
                  marketLevel: marketLevelText,
                  createdAt: dayjs(task.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                }
              }),
            )
          : Promise.resolve([]),

        postTasks.length > 0
          ? Promise.all(
              postTasks.map(async (task) => {
                const params = task.params as any
                const result = task.result as unknown as AudienceAnalysisResult

                const topCountries = result?.regionAnalysisResult?.statistics?.slice(0, 5) || []
                const developmentStats = result?.regionAnalysisResult?.developmentStatistics || []

                const country1 = topCountries[0]
                  ? `${topCountries[0].region} (${topCountries[0].percentage})`
                  : ''
                const country2 = topCountries[1]
                  ? `${topCountries[1].region} (${topCountries[1].percentage})`
                  : ''
                const country3 = topCountries[2]
                  ? `${topCountries[2].region} (${topCountries[2].percentage})`
                  : ''
                const country4 = topCountries[3]
                  ? `${topCountries[3].region} (${topCountries[3].percentage})`
                  : ''
                const country5 = topCountries[4]
                  ? `${topCountries[4].region} (${topCountries[4].percentage})`
                  : ''

                const marketLevelText =
                  developmentStats.length > 0
                    ? developmentStats
                        .map((dev) => `${dev.developmentLevel}:${dev.percentage}`)
                        .join(', ')
                    : ''

                return {
                  postLink: params?.postLink || '',
                  platform: params?.platform || '',
                  malePercentage: result?.userPortraitResult?.gender?.male || '',
                  femalePercentage: result?.userPortraitResult?.gender?.female || '',
                  under18Percentage: result?.userPortraitResult?.age?.under18 || '',
                  age18to25Percentage: result?.userPortraitResult?.age?.age18to25 || '',
                  age25to45Percentage: result?.userPortraitResult?.age?.age25to45 || '',
                  above45Percentage: result?.userPortraitResult?.age?.above45 || '',
                  country1,
                  country2,
                  country3,
                  country4,
                  country5,
                  marketLevel: marketLevelText,
                  createdAt: dayjs(task.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                }
              }),
            )
          : Promise.resolve([]),
      ])

      const sheets = []

      if (authorsData.length > 0) {
        sheets.push({
          name: 'Authors',
          data: authorsData,
          columns: audienceAnalysisAuthorsColumns,
        })
      }

      if (postsData.length > 0) {
        sheets.push({
          name: 'Posts',
          data: postsData,
          columns: audienceAnalysisPostsColumns,
        })
      }

      const buffer = await exportToExcel(sheets)

      const dateStr = dayjs().format('YYYY-MM-DD_HH-mm-ss')
      const fileName = `Audience-History_${dateStr}_${user.email}.xlsx`
      const filePath = `excel/${fileName}`

      await uploadFileByBuffer(filePath, buffer)

      return reply.send(
        successResponse(
          `${HOST ?? ''}/files/excel/${fileName}`,
          'Excel file exported successfully',
        ),
      )
    },
  )
}

export default router
