import { downloadFileByStream } from '@/api/aliyun'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get(
    '/csv/:filename',
    {
      // preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { filename } = request.params as any
      // const { user } = request as any;

      try {
        const stream = await downloadFileByStream(`csv/${filename}`)
        reply.header('Content-Type', 'text/csv')
        reply.header('Content-Disposition', `attachment; filename="${filename}"`)
        return reply.send(stream)
      } catch (error) {
        console.error(error)
        return reply.status(500).send({ error: 'Failed to get CSV' })
      }
    },
  )

  fastify.get(
    '/excel/:filename',
    {
      // preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { filename } = request.params as any

      try {
        // 检查文件名是否包含用户导出文件的模式
        const filePath = `excel/${filename}`

        const stream = await downloadFileByStream(filePath)
        reply.header(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
        reply.header(
          'Content-Disposition',
          `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
        )
        reply.header('Cache-Control', 'no-cache')
        reply.header('Pragma', 'no-cache')
        reply.header('Expires', '0')
        return reply.send(stream)
      } catch (error) {
        console.error('Excel download error:', error)
        return reply.status(500).send({ error: 'Failed to get Excel file' })
      }
    },
  )
}

export default router
