import { Static, Type } from '@sinclair/typebox'

export const WebExportParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务 ID' }),
})

export type WebExportParams = Static<typeof WebExportParamsSchema>

// 长时间爬取任务的请求schema
export const LongCrawlerRequestSchema = Type.Object({
  platform: Type.String({ description: '平台', enum: ['INSTAGRAM', 'TIKTOK', 'YOUTUBE'] }),
  projectId: Type.String({ description: '项目ID' }),
  filters: Type.Object({
    // required
    kolDescription: Type.String({
      description: '内容类别描述',
      default: '',
    }),
    // optional
    followerRange: Type.Optional(
      Type.Object({
        min: Type.Optional(Type.Number({ description: '最小粉丝数', minimum: 0, default: 5000 })),
        max: Type.Optional(Type.Number({ description: '最大粉丝数', minimum: 0, default: 500000 })),
      }),
    ),
    // required
    regions: Type.Array(Type.String({ description: '地区/国家代码' }), {
      description: '地区/国家筛选',
      default: [],
    }),
    // optional
    averageLikeCount: Type.Optional(
      Type.Object({
        min: Type.Optional(Type.Number({ description: '最小平均点赞数', minimum: 0 })),
        max: Type.Optional(Type.Number({ description: '最大平均点赞数', minimum: 0 })),
      }),
    ),
  }),
  seedUsers: Type.Array(Type.String(), {
    description: '种子用户列表',
    minItems: 1,
  }),
  numberOfRuns: Type.Number({
    description: '消耗的任务次数',
    minimum: 1,
    default: 10,
  }),
})

export type LongCrawlerRequest = Static<typeof LongCrawlerRequestSchema>

// 任务状态查询参数schema
export const TaskStatusQueryParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
})

export type TaskStatusQueryParams = Static<typeof TaskStatusQueryParamsSchema>

// 任务列表查询参数schema
export const TaskListQueryParamsSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  taskType: Type.Optional(Type.String({ description: '任务类型' })),
  page: Type.Optional(Type.Number({ description: '页码', minimum: 1, default: 1 })),
  pageSize: Type.Optional(
    Type.Number({ description: '每页数量', minimum: 1, maximum: 100, default: 10 }),
  ),
})

export type TaskListQueryParams = Static<typeof TaskListQueryParamsSchema>

// 任务进度schema
export const TaskProgressSchema = Type.Object({
  processedUsers: Type.Array(Type.String(), { description: '已处理的用户列表' }),
  pendingUsers: Type.Array(Type.String(), { description: '待处理的用户列表' }),
  matchedUsers: Type.Array(Type.String(), { description: '匹配的用户列表' }),
  totalProcessed: Type.Number({ description: '总处理数量' }),
  totalMatched: Type.Number({ description: '总匹配数量' }),
  lastProcessedAt: Type.String({ description: '最后处理时间' }),
  currentBatch: Type.Number({ description: '当前批次' }),
})

export type TaskProgress = Static<typeof TaskProgressSchema>

export const LongCrawlerExportSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
  type: Type.String({
    description: '导出类型，aiFilter表示导出AI筛选用户，match表示导出匹配用户',
    enum: ['aiFilter', 'match'],
  }),
})

export type LongCrawlerExportRequest = Static<typeof LongCrawlerExportSchema>

export const UpdateLongCrawlerParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
  params: Type.Object({
    addNumberOfRuns: Type.Number({
      description: '增加消耗任务次数',
      minimum: 1,
    }),
    // other params
  }),
})

export type UpdateLongCrawlerParams = Static<typeof UpdateLongCrawlerParamsSchema>
