import { KolPlatform } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'

export const SimpleKolTagSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  color: Type.String(),
})

export type SimpleKolTag = Static<typeof SimpleKolTagSchema>

export const ListTagsResSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    name: Type.String(),
    color: Type.String(),
    createdAt: Type.String(),
  }),
)

export type ListTagsRes = Static<typeof ListTagsResSchema>

export const ListTagsWithCountResSchema = Type.Array(
  Type.Object({
    id: Type.String(),
    name: Type.String(),
    color: Type.String(),
    useCount: Type.Number(),
    createdAt: Type.String(),
  }),
)

export type ListTagsWithCountRes = Static<typeof ListTagsWithCountResSchema>

export const GetKolTagAndNoteReqSchema = Type.Object({
  kolId: Type.String(),
})

export type GetKolTagAndNoteReq = Static<typeof GetKolTagAndNoteReqSchema>

export const GetKolTagAndNoteResSchema = Type.Object({
  tags: Type.Array(
    Type.Object({
      id: Type.String(),
      name: Type.String(),
      color: Type.String(),
      createdAt: Type.String(),
      updatedBy: Type.Object({
        id: Type.String(),
        email: Type.String(),
      }),
    }),
  ),
  note: Type.String(),
})

export type GetKolTagAndNoteRes = Static<typeof GetKolTagAndNoteResSchema>

export const CreateCreatorTagReqSchema = Type.Object({
  name: Type.String(),
  color: Type.String(),
})

export type CreateCreatorTagReq = Static<typeof CreateCreatorTagReqSchema>

export const UpdateCreatorTagReqSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  color: Type.String(),
})

export type UpdateCreatorTagReq = Static<typeof UpdateCreatorTagReqSchema>

export const DeleteCreatorTagReqSchema = Type.Object({
  id: Type.String(),
})

export type DeleteCreatorTagReq = Static<typeof DeleteCreatorTagReqSchema>

export const UpdateKolTagReqSchema = Type.Object({
  kolId: Type.String(),
  tagIds: Type.Array(Type.String()),
})

export type UpdateKolTagReq = Static<typeof UpdateKolTagReqSchema>

export const UpdateKolNoteReqSchema = Type.Object({
  kolId: Type.String(),
  note: Type.String(),
})

export type UpdateKolNoteReq = Static<typeof UpdateKolNoteReqSchema>

export const ListKolTagReqSchema = Type.Object({
  page: Type.Number({ default: 1, description: '页码' }),
  pageSize: Type.Number({ default: 10, description: '每页条数' }),
  tags: Type.Optional(Type.Array(Type.String({ description: '标签id筛选，不传则不筛选' }))),
})

export const KolTagLogSchema = Type.Object({
  tag: Type.Union([Type.Null(), SimpleKolTagSchema]),
  note: Type.Union([Type.Null(), Type.String()]),
  type: Type.String(),
  description: Type.String(),
  createdAt: Type.String(),
  user: Type.Object({
    id: Type.String(),
    email: Type.String(),
  }),
})

export type KolTagLog = Static<typeof KolTagLogSchema>
export type ListKolTagReq = Static<typeof ListKolTagReqSchema>

export const ListKolTagItemSchema = Type.Object({
  kol: Type.Object({
    id: Type.String(),
    nickname: Type.String(),
    avatar: Type.String(),
    platform: Type.Enum(KolPlatform),
    url: Type.String(),
  }),
  tags: Type.Array(SimpleKolTagSchema),
  notes: Type.Array(
    Type.Object({
      note: Type.String(),
      user: Type.Object({
        id: Type.String(),
        email: Type.String(),
      }),
    }),
  ),
  updatedBy: Type.Object({
    id: Type.String(),
    email: Type.String(),
  }),
  updatedAt: Type.String(),
})

export type ListKolTagItem = Static<typeof ListKolTagItemSchema>

export const ListKolTagResSchema = Type.Object({
  data: Type.Array(ListKolTagItemSchema),
  meta: Type.Object({
    currentPage: Type.Number(),
    totalPages: Type.Number(),
    totalCount: Type.Number(),
    currentCount: Type.Number(),
    pageSize: Type.Number(),
  }),
})

export const ListKolTagLogReqSchema = Type.Object({
  kolId: Type.String(),
  page: Type.Number({ default: 1 }),
  pageSize: Type.Number({ default: 10 }),
})

export type ListKolTagLogReq = Static<typeof ListKolTagLogReqSchema>

export type ListKolTagRes = Static<typeof ListKolTagResSchema>

export const ListKolTagLogResSchema = Type.Object({
  data: Type.Array(KolTagLogSchema),
  meta: Type.Object({
    currentPage: Type.Number(),
    totalPages: Type.Number(),
    totalCount: Type.Number(),
    currentCount: Type.Number(),
    pageSize: Type.Number(),
  }),
})

export type ListKolTagLogRes = Static<typeof ListKolTagLogResSchema>

export const DeleteKolTagReqSchema = Type.Object({
  kolId: Type.String(),
})

export type DeleteKolTagReq = Static<typeof DeleteKolTagReqSchema>

export const AddSingleKolTagReqSchema = Type.Object({
  kolId: Type.String(),
  tagId: Type.String(),
})

export type AddSingleKolTagReq = Static<typeof AddSingleKolTagReqSchema>

export const DeleteSingleKolTagReqSchema = Type.Object({
  kolId: Type.String(),
  tagId: Type.String(),
})

export type DeleteSingleKolTagReq = Static<typeof DeleteSingleKolTagReqSchema>
