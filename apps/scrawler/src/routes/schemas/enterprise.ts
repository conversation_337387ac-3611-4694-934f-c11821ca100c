import { Static, Type } from '@sinclair/typebox'

export const BuyInfoCardForMemberSchema = Type.Object({
  enterpriseId: Type.String({ description: '企业ID' }),
  membershipId: Type.String({ description: '成员会员ID' }),
  months: Type.Number({
    description: '购买月数',
    minimum: 1,
    examples: [1, 3, 12],
  }),
})

export const UpdateEnterpriseBasicInfoSchema = Type.Object({
  name: Type.Optional(Type.String({ description: '企业名称' })),
  description: Type.Optional(Type.String({ description: '企业描述' })),
  contactPerson: Type.Optional(Type.String({ description: '联系人' })),
  contactPhone: Type.Optional(Type.String({ description: '联系电话' })),
  contactEmail: Type.Optional(Type.String({ description: '联系邮箱' })),
  industry: Type.Optional(Type.String({ description: '所属行业' })),
  scale: Type.Optional(Type.String({ description: '企业规模' })),
  address: Type.Optional(Type.String({ description: '企业地址' })),
  dailyLimit: Type.Optional(Type.Number({ description: '日额度', minimum: 0 })),
  memberUsageDailyLimit: Type.Optional(
    Type.Number({ description: '成员日额度，-1表示无限制', minimum: -1 }),
  ),
})

export type BuyInfoCardForMemberRequest = Static<typeof BuyInfoCardForMemberSchema>
export type UpdateEnterpriseBasicInfoRequest = Static<typeof UpdateEnterpriseBasicInfoSchema>
