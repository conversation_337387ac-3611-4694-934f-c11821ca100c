import { Static, Type } from '@sinclair/typebox'

export const FeishuTrackReqSchema = Type.String()

export type FeishuTrackReq = Static<typeof FeishuTrackReqSchema>

export const FeishuTrackResSchema = Type.Object({
  url: Type.String(),
  title: Type.String(),
  description: Type.String(),
  image: Type.String(),
  link: Type.String(),
})

export type FeishuTrackRes = Static<typeof FeishuTrackResSchema>

// 请求ID参数 Schema
export const FeishuLogParamsSchema = Type.Object({
  requestId: Type.String({ description: '日志请求ID' }),
})

export type FeishuLogParams = Static<typeof FeishuLogParamsSchema>

// 日志查询参数 Schema
export const FeishuLogsQuerySchema = Type.Object({
  apiKey: Type.Optional(Type.String()),
  platform: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  startDate: Type.Optional(Type.String({ format: 'date-time' })),
  endDate: Type.Optional(Type.String({ format: 'date-time' })),
  limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 50 })),
  offset: Type.Optional(Type.Number({ minimum: 0, default: 0 })),
})

export type FeishuLogsQuery = Static<typeof FeishuLogsQuerySchema>

// 统计查询参数 Schema
export const FeishuStatisticsQuerySchema = Type.Object({
  apiKey: Type.Optional(Type.String()),
  startDate: Type.Optional(Type.String({ format: 'date-time' })),
  endDate: Type.Optional(Type.String({ format: 'date-time' })),
})

export type FeishuStatisticsQuery = Static<typeof FeishuStatisticsQuerySchema>

// API Key 统计查询参数 Schema
export const FeishuApiKeyStatsQuerySchema = Type.Object({
  startDate: Type.Optional(Type.String({ format: 'date-time' })),
  endDate: Type.Optional(Type.String({ format: 'date-time' })),
})

export type FeishuApiKeyStatsQuery = Static<typeof FeishuApiKeyStatsQuerySchema>

// 清理日志参数 Schema
export const FeishuCleanupQuerySchema = Type.Object({
  daysToKeep: Type.Optional(Type.Number({ minimum: 1, default: 30 })),
})

export type FeishuCleanupQuery = Static<typeof FeishuCleanupQuerySchema>

// 日志项 Schema
export const FeishuLogSchema = Type.Object({
  id: Type.String(),
  requestId: Type.String(),
  apiKey: Type.String(),
  requestUrl: Type.String(),
  platform: Type.Union([Type.String(), Type.Null()]),
  linkType: Type.Union([Type.String(), Type.Null()]),
  accountName: Type.Union([Type.String(), Type.Null()]),
  displayName: Type.Union([Type.String(), Type.Null()]),
  status: Type.String(),
  responseCode: Type.Union([Type.Number(), Type.Null()]),
  errorMessage: Type.Union([Type.String(), Type.Null()]),
  processingTime: Type.Union([Type.Number(), Type.Null()]),
  quotaUsed: Type.Number(),
  ipAddress: Type.Union([Type.String(), Type.Null()]),
  createdAt: Type.String({ format: 'date-time' }),
})

export type FeishuLog = Static<typeof FeishuLogSchema>

// 日志列表响应 Schema
export const FeishuLogsResponseSchema = Type.Object({
  logs: Type.Array(FeishuLogSchema),
  total: Type.Number(),
  limit: Type.Number(),
  offset: Type.Number(),
  hasMore: Type.Boolean(),
})

export type FeishuLogsResponse = Static<typeof FeishuLogsResponseSchema>

// 平台统计 Schema
export const FeishuPlatformStatSchema = Type.Object({
  platform: Type.Union([Type.String(), Type.Null()]),
  requests: Type.Number(),
  quotaUsed: Type.Number(),
})

export type FeishuPlatformStat = Static<typeof FeishuPlatformStatSchema>

// 统计信息响应 Schema
export const FeishuStatisticsResponseSchema = Type.Object({
  totalRequests: Type.Number(),
  successRequests: Type.Number(),
  errorRequests: Type.Number(),
  successRate: Type.Number(),
  errorRate: Type.Number(),
  totalQuotaUsed: Type.Number(),
  avgProcessingTime: Type.Number(),
  platformStats: Type.Array(FeishuPlatformStatSchema),
})

export type FeishuStatisticsResponse = Static<typeof FeishuStatisticsResponseSchema>

// API Key 统计项 Schema
export const FeishuApiKeyStatSchema = Type.Object({
  apiKey: Type.String(),
  requests: Type.Number(),
  quotaUsed: Type.Number(),
})

export type FeishuApiKeyStat = Static<typeof FeishuApiKeyStatSchema>

// API Key 统计响应 Schema
export const FeishuApiKeyStatsResponseSchema = Type.Array(FeishuApiKeyStatSchema)

export type FeishuApiKeyStatsResponse = Static<typeof FeishuApiKeyStatsResponseSchema>

// 清理结果响应 Schema
export const FeishuCleanupResponseSchema = Type.Object({
  deletedCount: Type.Number(),
  cutoffDate: Type.String({ format: 'date-time' }),
})

export type FeishuCleanupResponse = Static<typeof FeishuCleanupResponseSchema>
