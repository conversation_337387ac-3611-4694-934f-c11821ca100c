import { TaskReason } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'

export const YoutubeHashTagBreakRequestSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  tag: Type.String({ description: 'hashtag' }),
  reason: Type.Optional(
    Type.Enum(
      { SEARCH: TaskReason.SEARCH, NEXT_PAGE: TaskReason.NEXT_PAGE },
      { description: '任务类型', default: TaskReason.SEARCH },
    ),
  ),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),
})

export type YoutubeHashTagBreakRequest = Static<typeof YoutubeHashTagBreakRequestSchema>

export const YoutubeInputSearchBreakRequestSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  searchInput: Type.String({ description: '搜索关键词' }),
  reason: Type.Optional(
    Type.Enum(
      { SEARCH: TaskReason.SEARCH, NEXT_PAGE: TaskReason.NEXT_PAGE },
      { description: '任务类型', default: TaskReason.SEARCH },
    ),
  ),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),

  // 一些额外参数
  geo: Type.Optional(Type.String({ description: '地理位置选项' })),
  lang: Type.Optional(Type.String({ description: '语言选项' })),
  duration: Type.Optional(Type.String({ description: '视频长度选项' })),
  sort_by: Type.Optional(Type.String({ description: '排序选项' })),
  upload_date: Type.Optional(Type.String({ description: '上传日期选项' })),
})

export type YoutubeInputSearchBreakRequest = Static<typeof YoutubeInputSearchBreakRequestSchema>
