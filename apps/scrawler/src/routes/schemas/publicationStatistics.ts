import { Static, Type } from '@sinclair/typebox'

export const AudienceSchema = Type.Object({
  publicationId: Type.String({
    description: '发布统计数据ID',
    minLength: 1,
  }),
})

export type AudienceRequest = Static<typeof AudienceSchema>

export const PluginAudienceSchema = Type.Object({
  url: Type.String({
    description: '视频链接',
    minLength: 1,
  }),
})

export type PluginAudienceRequest = Static<typeof PluginAudienceSchema>

export const PluginAudienceResultParamsSchema = Type.Object({
  taskId: Type.String({
    description: '任务ID',
    minLength: 1,
  }),
})

export type PluginAudienceResultParams = Static<typeof PluginAudienceResultParamsSchema>

export const TrackByPublicationIdsSchema = Type.Object({
  publicationIds: Type.Array(
    Type.String({
      description: '发布统计数据ID',
      minLength: 1,
    }),
    {
      description: '发布统计数据ID数组',
      minItems: 1,
      maxItems: 200,
    },
  ),
})

export type TrackByPublicationIdsRequest = Static<typeof TrackByPublicationIdsSchema>

export const BatchDeletePublicationsSchema = Type.Object({
  publicationIds: Type.Array(
    Type.String({
      description: '发布统计数据ID',
      minLength: 1,
    }),
    {
      description: '要删除的发布统计数据ID数组',
      minItems: 1,
      maxItems: 200,
    },
  ),
})

export type BatchDeletePublicationsRequest = Static<typeof BatchDeletePublicationsSchema>
