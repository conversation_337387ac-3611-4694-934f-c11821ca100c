import { EmailFollowupThreadStatus } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'

// 跟进邮件的 Schema
export const FollowupEmailSchema = Type.Object({
  subject: Type.String({ description: '邮件主题' }),
  content: Type.String({ description: '邮件内容' }),
  daysAfter: Type.Number({ description: '初始邮件发送后的天数', minimum: 1 }),
})

// 更新模板抄送/密送的 Schema
export const UpdateTemplateCCSchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
  cc: Type.Optional(
    Type.Array(Type.String({ format: 'email' }), { description: '抄送邮件地址列表' }),
  ),
  bcc: Type.Optional(
    Type.Array(Type.String({ format: 'email' }), { description: '密送邮件地址列表' }),
  ),
})

// 更新跟进的 Schema
export const UpdateFollowupSchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
  followupEmails: Type.Array(FollowupEmailSchema, {
    description: '跟进邮件列表',
  }),
})

// 查询参数的 Schema
export const GetFollowupThreadsQuerySchema = Type.Object({
  status: Type.Optional(
    Type.Enum(EmailFollowupThreadStatus, {
      description: '按线程状态筛选',
    }),
  ),
})

// 获取邮件跟进配置的查询参数
export const GetEmailFollowupQuerySchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
})

// 获取邮件模板抄送列表的查询参数
export const GetTemplateCCQuerySchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
})

// 删除邮件跟进配置的 Body Schema
export const DeleteEmailFollowupSchema = Type.Object({
  followupId: Type.String({ description: '邮件跟进ID' }),
})

// 更新线程状态的 Schema
export const UpdateFollowupThreadStatusSchema = Type.Object({
  threadId: Type.String({ description: '邮件跟进线程ID' }),
  status: Type.Enum(EmailFollowupThreadStatus, {
    description: '新的线程状态',
  }),
})

// 获取跟进统计信息的查询参数
export const GetFollowupStatsQuerySchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
})

// 路径参数的 Schema
export const TemplateIdParamSchema = Type.Object({
  templateId: Type.String({ description: '邮件模板ID' }),
})

export const FollowupIdParamSchema = Type.Object({
  followupId: Type.String({ description: '邮件跟进ID' }),
})

export const ThreadIdParamSchema = Type.Object({
  threadId: Type.String({ description: '邮件跟进线程ID' }),
})

// TypeScript types from schemas
export type FollowupEmail = Static<typeof FollowupEmailSchema>
export type UpdateTemplateCCRequest = Static<typeof UpdateTemplateCCSchema>
export type UpdateFollowupRequest = Static<typeof UpdateFollowupSchema>
export type GetFollowupThreadsQuery = Static<typeof GetFollowupThreadsQuerySchema>
export type UpdateFollowupThreadStatusRequest = Static<typeof UpdateFollowupThreadStatusSchema>
export type TemplateIdParam = Static<typeof TemplateIdParamSchema>
export type FollowupIdParam = Static<typeof FollowupIdParamSchema>
export type ThreadIdParam = Static<typeof ThreadIdParamSchema>
