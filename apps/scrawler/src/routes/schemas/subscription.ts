import { Type } from '@sinclair/typebox'
import { FastifySchema } from 'fastify'
import { responseSchema } from './common'

// 定义请求体Schema
export const createCheckoutSchema: FastifySchema = {
  body: Type.Object(
    {
      planType: Type.Union([Type.Literal('BASIC'), Type.Literal('PRO')], {
        description: '订阅计划类型',
      }),
    },
    {
      additionalProperties: false,
      description: '创建结账会话请求',
    },
  ),
  response: responseSchema,
}

// 定义续订Schema
export const resubscribeSchema: FastifySchema = {
  response: responseSchema,
}

// 定义订阅状态查询Schema
export const subscriptionStatusSchema: FastifySchema = {
  response: responseSchema,
}

// 定义客户门户创建Schema
export const createPortalSchema: FastifySchema = {
  response: responseSchema,
}

// 定义取消订阅Schema
export const cancelSubscriptionSchema: FastifySchema = {
  response: responseSchema,
}

// 定义验证会话Schema
export const verifySessionSchema: FastifySchema = {
  querystring: Type.Object(
    {
      sessionId: Type.String({ description: 'Checkout会话ID' }),
    },
    {
      additionalProperties: false,
      description: '验证会话请求',
    },
  ),
  response: responseSchema,
}

export const subscriptionPollingSchema: FastifySchema = {
  querystring: Type.Object(
    {
      subscriptionId: Type.String({ description: '订阅ID' }),
    },
    {
      additionalProperties: false,
      description: '订阅状态轮询请求',
    },
  ),
  response: responseSchema,
}
