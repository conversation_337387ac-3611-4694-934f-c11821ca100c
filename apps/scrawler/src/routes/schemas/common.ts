import { Static, TSchema, Type } from '@sinclair/typebox'
import { StatusCodes } from '../../common/response/response'

// 定义业务状态码枚举类型
const BusinessStatusCodes = Type.Union([
  // 成功类状态码
  Type.Literal(StatusCodes.SUCCESS), // 1000
  Type.Literal(StatusCodes.CREATED), // 1001
  Type.Literal(StatusCodes.ACCEPTED), // 1002
  Type.Literal(StatusCodes.NO_CONTENT), // 1003

  // 客户端错误类状态码
  Type.Literal(StatusCodes.BAD_REQUEST), // 1400
  Type.Literal(StatusCodes.UNAUTHORIZED), // 1401
  Type.Literal(StatusCodes.FORBIDDEN), // 1403
  Type.Literal(StatusCodes.NOT_FOUND), // 1404
  Type.Literal(StatusCodes.CONFLICT), // 1409
  Type.Literal(StatusCodes.TOKEN_OUTDATED), // 1411
  Type.Literal(StatusCodes.DEVICE_BAN), // 1412
  Type.Literal(StatusCodes.UNPROCESSABLE_ENTITY), // 1422
  Type.Literal(StatusCodes.VERSION_TOO_LOW), // 1426
  Type.Literal(StatusCodes.TOO_MANY_REQUESTS), // 1429

  // 服务器错误类状态码
  Type.Literal(StatusCodes.SERVER_ERROR), // 1500
  Type.Literal(StatusCodes.SERVICE_UNAVAILABLE), // 1503
  Type.Literal(StatusCodes.GATEWAY_TIMEOUT), // 1504

  // 自定义网络状态码
  Type.Literal(StatusCodes.NETWORK_ERROR), // 1700
  Type.Literal(StatusCodes.TIMEOUT), // 1701
  Type.Literal(StatusCodes.DNS_RESOLUTION_FAILED), // 1702

  // 文件处理相关状态码
  Type.Literal(StatusCodes.FILE_TOO_LARGE), // 1800
  Type.Literal(StatusCodes.UNSUPPORTED_FILE_TYPE), // 1801
  Type.Literal(StatusCodes.FILE_UPLOAD_FAILED), // 1802

  Type.Literal(StatusCodes.MEMBERSHIP_EXPIRED), // 1901
  Type.Literal(StatusCodes.MEMBERSHIP_NOT_FOUND), // 1902
  Type.Literal(StatusCodes.FREE_USER_RESTRICTED), // 1903
  Type.Literal(StatusCodes.FREE_QUOTA_EXCEEDED), // 1910
  Type.Literal(StatusCodes.FREE_INSUFFICIENT_QUOTA), // 1911
  Type.Literal(StatusCodes.PAID_QUOTA_EXCEEDED), // 1912
  Type.Literal(StatusCodes.PAID_INSUFFICIENT_QUOTA), // 1913
  Type.Literal(StatusCodes.ENTERPRISE_NOT_FOUND), // 1919
  Type.Literal(StatusCodes.ENTERPRISE_EXPIRED), // 1920
  Type.Literal(StatusCodes.ENTERPRISE_QUOTA_EXCEEDED), // 1921
  Type.Literal(StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA), // 1922
  Type.Literal(StatusCodes.ENTERPRISE_NOT_EFFECTIVE), // 1923
  Type.Literal(StatusCodes.ENTERPRISE_SUSPENDED), // 1924
  Type.Literal(StatusCodes.MEMBERSHIP_NOT_EFFECTIVE), // 1925
  Type.Literal(StatusCodes.MEMBERSHIP_SUSPENDED), // 1926
  Type.Literal(StatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED), // 1927
  Type.Literal(StatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED), // 1928
  Type.Literal(StatusCodes.TOO_MANY_SUPERLIKES), // 1949

  // 邮箱相关状态码
  Type.Literal(StatusCodes.EMAIL_REQUIRED), // 2000

  // 订阅相关状态码
  Type.Literal(StatusCodes.PAYMENT_REQUIRED), // 2002
  Type.Literal(StatusCodes.PAYMENT_FAILED), // 2003
  Type.Literal(StatusCodes.SUBSCRIPTION_INVALID), // 2004
  Type.Literal(StatusCodes.NO_ACTIVE_SUBSCRIPTION), // 2005
])

// 通用的响应对象
export const responseSchema = {
  200: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.Union([Type.Null(), Type.String()]),
    message: Type.String(),
    data: Type.Any(),
  }),
  201: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.Union([Type.Null(), Type.String()]),
    message: Type.String(),
    data: Type.Any(),
  }),
  204: Type.Null(),
  301: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.Null(),
    message: Type.String(),
    data: Type.Object({
      url: Type.String(),
    }),
  }),
  302: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.Null(),
    message: Type.String(),
    data: Type.Object({
      url: Type.String(),
    }),
  }),
  304: Type.Null(),
  400: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  401: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  403: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  404: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  409: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  429: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
  500: Type.Object({
    statusCode: BusinessStatusCodes,
    error: Type.String(),
    message: Type.String(),
    data: Type.Null(),
  }),
}

export const responseStatusCodes = {
  OK: 200, // 成功
  CREATED: 201, // 创建成功
  NO_CONTENT: 204, // 删除成功，无内容
  NOT_MODIFIED: 304, // 未修改
  MOVED_PERMANENTLY: 301, // 永久重定向
  FOUND: 302, // 临时重定向

  BAD_REQUEST: 400, // 请求错误
  UNAUTHORIZED: 401, // 未授权
  FORBIDDEN: 403, // 禁止访问
  NOT_FOUND: 404, // 资源不存在
  CONFLICT: 409, // 冲突
  TOO_MANY_REQUESTS: 429, // 请求过多

  INTERNAL_SERVER_ERROR: 500, // 服务器错误
}

export const responseSchemas = {
  success: responseSchema[200],
  error: responseSchema[400],
  redirect: responseSchema[301] || responseSchema[302],
  empty: responseSchema[204],
}

export const statusCodeSchemas = {
  [responseStatusCodes.OK]: responseSchema[200],
  [responseStatusCodes.CREATED]: responseSchema[201],
  [responseStatusCodes.NO_CONTENT]: responseSchema[204],
  [responseStatusCodes.NOT_MODIFIED]: responseSchema[304],
  [responseStatusCodes.MOVED_PERMANENTLY]: responseSchema[301],
  [responseStatusCodes.FOUND]: responseSchema[302],
  [responseStatusCodes.BAD_REQUEST]: responseSchema[400],
  [responseStatusCodes.UNAUTHORIZED]: responseSchema[401],
  [responseStatusCodes.FORBIDDEN]: responseSchema[403],
  [responseStatusCodes.NOT_FOUND]: responseSchema[404],
  [responseStatusCodes.CONFLICT]: responseSchema[409],
  [responseStatusCodes.TOO_MANY_REQUESTS]: responseSchema[429],
  [responseStatusCodes.INTERNAL_SERVER_ERROR]: responseSchema[500],
}

export const PaginatedListSchema = <T extends TSchema>(itemSchema: T) =>
  Type.Object({
    data: Type.Array(itemSchema),
    total: Type.Number(),
    page: Type.Number(),
    pageSize: Type.Number(),
    totalPages: Type.Number(),
    hasMore: Type.Boolean(),
    updatedAt: Type.String(),
    progress: Type.Optional(
      Type.Object({
        total: Type.Number(),
        current: Type.Number(),
      }),
    ),
  })

export const PaginateReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  page: Type.Number({ description: '页码', default: 1 }),
  pageSize: Type.Number({ description: '每页数量', default: 100 }),
})

export type PaginateReq = Static<typeof PaginateReqSchema>
