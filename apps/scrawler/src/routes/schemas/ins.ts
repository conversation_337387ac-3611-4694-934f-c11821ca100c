import { InstagramSortType } from '@/types/instagram'
import { KolPlatform, TaskReason } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'
import { PaginatedListSchema } from './common'

// 用户信息基础 schema
const InstagramUserSchema = Type.Object({
  id: Type.String(),
  username: Type.String(),
  fullName: Type.String(),
  profilePicUrl: Type.String(),
  isPrivate: Type.Boolean(),
  isVerified: Type.Boolean(),
  mediaCount: Type.Number(),
  followerCount: Type.Number(),
  followingCount: Type.Number(),
  biography: Type.String(),
  externalUrl: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

/******** 创建hashtag任务  ***************/
export const InsHashTagBreakReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  tag: Type.String({ description: '标签名称' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),
  sortType: Type.Optional(
    Type.String({
      description: '排序类型',
      enum: Object.values(InstagramSortType),
      default: InstagramSortType.TOP,
    }),
  ),
})

export type InsHashTagBreakReq = Static<typeof InsHashTagBreakReqSchema>
export const InsHashTagBreakResSchema = Type.Object({
  uniqueIds: Type.Array(Type.String()),
  paginationToken: Type.String(),
  hasMore: Type.Boolean(),
  total: Type.Number(),
  message: Type.Optional(Type.String()),
})

/******** 获取hashtag列表  ***************/
export const InsHashTagListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建following任务  ***************/
export const InsFollowingListReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  username: Type.String({ description: 'Instagram 用户名' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentCount: Type.Optional(Type.Number({ description: '当前数量', default: 0 })),
  maxCount: Type.Optional(Type.Number({ description: '最大数量', default: 100 })),
})

export type InsFollowingListReq = Static<typeof InsFollowingListReqSchema>

/******** 获取following列表  ***************/
export const InsFollowingListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建tagged任务  ***************/
export const InsTaggedBreakReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  username: Type.String({ description: 'Instagram 用户名' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),
})

export type InsTaggedBreakReq = Static<typeof InsTaggedBreakReqSchema>

export const InsTaggedBreakResSchema = Type.Object({
  uniqueIds: Type.Array(Type.String()),
  paginationToken: Type.String(),
  hasMore: Type.Boolean(),
  total: Type.Number(),
  message: Type.Optional(Type.String()),
})

/******** 获取tagged列表  ***************/
export const InsTaggedListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建url-list任务  ***************/
export const InsWebListReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  urls: Type.Array(Type.String({ description: 'Instagram URL 列表' })),
  platform: Type.String({ description: '平台', enum: Object.values(KolPlatform) }),
  reason: Type.Optional(Type.String({ description: '任务原因', enum: Object.values(TaskReason) })),
})
export type InsWebListReq = Static<typeof InsWebListReqSchema>

/******** 获取url-list列表  ***************/
export const InsWebListResSchema = PaginatedListSchema(InstagramUserSchema)
