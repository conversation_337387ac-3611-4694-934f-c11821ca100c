import { KolPlatform } from '@repo/database'
import { Type } from '@sinclair/typebox'
import { FastifySchema } from 'fastify'
import { responseSchema } from './common'

export const addTagToPublicationSchema: FastifySchema = {
  body: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
    tagId: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}

export const removeTagFromPublicationSchema: FastifySchema = {
  body: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
    tagId: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}

export const getPublicationTagsSchema: FastifySchema = {
  params: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
  }),
  response: responseSchema,
}

export const getTagPublicationsSchema: FastifySchema = {
  body: Type.Object({
    page: Type.Number({ minimum: 1, description: '页码，从1开始' }),
    pageSize: Type.Number({ minimum: 1, maximum: 100, description: '每页数量，最大100' }),
    ids: Type.Optional(Type.Array(Type.String(), { description: '标签ID列表' })),
    platforms: Type.Optional(
      Type.Array(
        Type.Union(
          Object.values(KolPlatform).map((platform) => Type.Literal(platform)),
          { description: '平台列表' },
        ),
      ),
    ),
  }),
  response: responseSchema,
}

export const getTagPostLinksSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}
