/**
 * Google Drive API 权限角色
 * @see https://developers.google.com/drive/api/v3/reference/permissions#resource
 */
export enum GoogleDriveRole {
  OWNER = 'owner', // 所有者
  ORGANIZER = 'organizer', // 组织者
  FILE_ORGANIZER = 'fileOrganizer', // 文件组织者
  WRITER = 'writer', // 可编辑
  COMMENTER = 'commenter', // 可评论
  READER = 'reader', // 可查看
}

/**
 * Google Drive API 权限类型
 * @see https://developers.google.com/drive/api/v3/reference/permissions#resource
 */
export enum GoogleDrivePermissionType {
  USER = 'user', // 特定用户
  GROUP = 'group', // 特定群组
  DOMAIN = 'domain', // 特定域名
  ANYONE = 'anyone', // 任何人
}

// 添加权限控制枚举
export enum FileCapability {
  SHARE = 'canShare', // 是否可以共享
  EDIT = 'canEdit', // 是否可以编辑
  CHANGE_PERMISSION = 'writersCanShare', // 编辑者是否可以更改权限
}
