import AihubmixService from '@/infras/monitoring/aihubmix'
import Sentry from '@/infras/sentry'
import { logger } from '@/infras/worker/log4js'
import EmailManageService from '@/services/emailManage'
import InstagramService from '@/services/instagram'
import { MembershipService } from '@/services/membership.service'
import strategyService from '@/services/strategy.service'
import TaskService from '@/services/task'
import {
  KolPlatform,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskReason,
  TaskType,
  prisma,
} from '@repo/database'
import Promise from 'bluebird'
import { emailPlanQueue } from './infras/worker/bullmq'
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export async function processStrategies() {
  while (true) {
    try {
      console.log('get due easykoltrack strategies ...')
      // 循环检验ins配额
      await InstagramService.getInstance().getInsQuota().catch(console.error)
      // 循环检查aihubmix的余额
      await AihubmixService.checkAihubmixBalance().catch(console.error)

      // 检查并处理待发送的邮件计划
      try {
        const pendingEmailPlans = await EmailManageService.getPendingEmailPlans(100)
        if (pendingEmailPlans.length > 0) {
          // 批量添加任务到队列
          const jobs = await Promise.map(
            pendingEmailPlans,
            async (plan) => {
              return emailPlanQueue.add({
                id: `email-plan-${plan.id}`,
                emailPlanId: plan.id,
                userId: plan.userId,
                createdAt: new Date(),
                createdBy: plan.userId,
              })
            },
            { concurrency: 10 },
          )
          logger.info(`添加了 ${jobs.length} 个邮件计划到发送队列`)
        }
      } catch (error) {
        logger.error('处理邮件计划时出错:', error)
        Sentry.captureException(error, {
          extra: { context: 'email_plan_scanning' },
        })
      }

      const now = new Date()
      const dueStrategies = await strategyService.findDueStrategies(now)

      if (dueStrategies.length > 0) {
        logger.info(`找到 ${dueStrategies.length} 个到期的策略需要处理`)

        await Promise.map(
          dueStrategies,
          async (strategy) => {
            try {
              const sheetData = await prisma.publicationStatisticsSheetData.findMany({
                where: {
                  easykolTrackStrategyId: strategy.id,
                },
              })

              if (sheetData.length === 0) {
                logger.warn(`策略 ${strategy.id} 没有关联的数据`)
                // 创建一个失败的任务记录
                await TaskService.getInstance().createTrackTask(
                  `EasyKOL_Track:${strategy.id}`,
                  {},
                  strategy.userId,
                  TaskReason.EASYKOL_TRACK,
                  TaskType.EASYKOL_TRACK,
                  strategy.id,
                  SimilarChannelTaskStatus.FAILED,
                  {
                    error: '没有找到关联的数据',
                  },
                )
                // 记录运行情况
                await strategyService.recordRunSituation(strategy.id)
                return
              }

              // 构建任务参数
              const postRequestParams = {
                tiktok: {
                  videosIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  urls: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
                    )
                    .map((item) => item.postLink)
                    .filter((url): url is string => url !== null),
                },
                youtube: {
                  videosIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  shortsIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  urls: sheetData
                    .filter((item) => item.platform === KolPlatform.YOUTUBE)
                    .map((item) => item.postLink)
                    .filter((url): url is string => url !== null),
                },
                instagram: {
                  reelsIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.INSTAGRAM &&
                        item.postType === 'INSTAGRAM_REEL',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  postsIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.INSTAGRAM &&
                        item.postType === 'INSTAGRAM_POST',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  urls: sheetData
                    .filter((item) => item.platform === KolPlatform.INSTAGRAM)
                    .map((item) => item.postLink)
                    .filter((url): url is string => url !== null),
                },
                douyin: {
                  videosIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_VIDEO',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  noteIds: sheetData
                    .filter(
                      (item) =>
                        item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_NOTE',
                    )
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  urls: sheetData
                    .filter((item) => item.platform === KolPlatform.DOUYIN)
                    .map((item) => item.postLink)
                    .filter((url): url is string => url !== null),
                },
                xhs: {
                  noteIds: sheetData
                    .filter((item) => item.platform === KolPlatform.XHS)
                    .map((item) => item.videoId)
                    .filter((id): id is string => id !== null),
                  urls: sheetData
                    .filter((item) => item.platform === KolPlatform.XHS)
                    .map((item) => item.postLink)
                    .filter((url): url is string => url !== null),
                },
              }

              const tiktokCount = postRequestParams.tiktok.urls.length
              const youtubeCount = postRequestParams.youtube.urls.length
              const instagramCount = postRequestParams.instagram.urls.length
              const douyinCount = postRequestParams.douyin.urls.length
              const xhsCount = postRequestParams.xhs.urls.length

              const totalItemsToTrack =
                tiktokCount + youtubeCount + instagramCount + douyinCount + xhsCount

              if (totalItemsToTrack === 0) {
                logger.warn(`策略 ${strategy.id} 没有需要跟踪的数据`)
                await TaskService.getInstance().createTrackTask(
                  `EasyKOL_Track:${strategy.id}`,
                  {},
                  strategy.userId,
                  TaskReason.EASYKOL_TRACK,
                  TaskType.EASYKOL_TRACK,
                  strategy.id,
                  SimilarChannelTaskStatus.FAILED,
                  {
                    error: '没有需要跟踪的数据',
                  },
                )
                await strategyService.recordRunSituation(strategy.id)
                return
              }

              // 检查用户状态
              try {
                await MembershipService.getInstance().checkMembershipByUserId(
                  strategy.userId,
                  totalItemsToTrack,
                )
              } catch (error) {
                logger.error(`处理策略 ${strategy.id} 时检查用户状态失败:`, error)
                await TaskService.getInstance().createTrackTask(
                  `EasyKOL_Track:${strategy.id}`,
                  {},
                  strategy.userId,
                  TaskReason.EASYKOL_TRACK,
                  TaskType.EASYKOL_TRACK,
                  strategy.id,
                  SimilarChannelTaskStatus.FAILED,
                  {
                    error: error instanceof Error ? error.message : '用户状态检查失败',
                  },
                )
                await strategyService.recordRunSituation(strategy.id)
                return
              }

              logger.info(
                `[strategy-processor] 策略 ${strategy.id} 需要跟踪的数据: TikTok: ${tiktokCount}, YouTube: ${youtubeCount}, Instagram: ${instagramCount}, Douyin: ${douyinCount}, XHS: ${xhsCount}`,
              )

              const task = await TaskService.getInstance().createTrackTask(
                `EasyKOL_Track:${strategy.id}`,
                {
                  spreadsheetId: sheetData[0].spreadsheetId,
                  tiktok: postRequestParams.tiktok,
                  youtube: postRequestParams.youtube,
                  instagram: postRequestParams.instagram,
                  douyin: postRequestParams.douyin,
                  xhs: postRequestParams.xhs,
                },
                strategy.userId,
                TaskReason.EASYKOL_TRACK,
                TaskType.EASYKOL_TRACK,
                strategy.id,
                SimilarChannelTaskStatus.PENDING,
              )

              logger.info(`为策略 ${strategy.id} 创建任务成功，任务ID: ${task.id}`)

              // 异步扣除配额
              MembershipService.getInstance()
                .deductQuota({
                  userId: strategy.userId,
                  type: QuotaType.EASYKOL_DATA_TRACK_URL,
                  count: totalItemsToTrack,
                  taskId: task.id,
                })
                .catch((error) => {
                  logger.error(`扣除配额失败:`, error)
                  Sentry.captureException(error, {
                    extra: { strategyId: strategy.id, userId: strategy.userId, taskId: task.id },
                  })
                })

              // 记录成功运行情况
              await strategyService.recordRunSituation(strategy.id)
            } catch (error) {
              logger.error(`处理策略 ${strategy.id} 时出错:`, error)
              Sentry.captureException(error, {
                extra: { strategyId: strategy.id, userId: strategy.userId },
              })
              await TaskService.getInstance().createTrackTask(
                `EasyKOL_Track:${strategy.id}`,
                {},
                strategy.userId,
                TaskReason.EASYKOL_TRACK,
                TaskType.EASYKOL_TRACK,
                strategy.id,
                SimilarChannelTaskStatus.FAILED,
                {
                  error: error instanceof Error ? error.message : '未知错误',
                },
              )

              await strategyService.recordRunSituation(strategy.id)
            }
          },
          { concurrency: 5 },
        )
      }

      await sleep(30_000)
    } catch (error) {
      logger.error('策略处理器发生错误:', error)
      Sentry.captureException(error)
      await sleep(30_000)
    }
  }
}
