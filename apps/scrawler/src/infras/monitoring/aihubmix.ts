// apps/scrawler/src/infras/monitoring/aihubmix.ts
import { AIHUBMIX_MONITORING_API_KEY, EASYKOL_WARNING_CHANNEL } from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'

async function getAihubmixBalance() {
  try {
    const response = await fetch('https://aihubmix.com/api/user/self', {
      headers: {
        Authorization: `Bearer ${AIHUBMIX_MONITORING_API_KEY}`,
      },
    })
    const data = await response.json()

    if (data.success && data.data && data.data.quota) {
      // 将配额除以500000计算余额
      const balance = data.data.quota / 500000
      return {
        balance,
        rawQuota: data.data.quota,
        usedQuota: data.data.used_quota,
      }
    }

    throw new Error('Failed to get balance data')
  } catch (error) {
    console.error('[AihubmixService] Failed to get balance:', error)
    throw error
  }
}

/**
 * 检查Aihubmix余额并在余额过低时发送警告
 * 每小时最多发送一次警告
 * @param threshold 余额警告阈值，默认为100
 * @param channel Slack通知频道
 */
async function checkAihubmixBalance() {
  try {
    const balance = await getAihubmixBalance()
    console.log(`[AihubmixService] Current balance: ${balance.balance}, threshold: 100`)
    await SlackClient.getInstance().sendQuotaWarning(
      EASYKOL_WARNING_CHANNEL,
      'Aihubmix API($)',
      balance.balance,
      100,
    )
  } catch (error) {
    console.error('[AihubmixService] Failed to check balance:', error)
  }
}

const AihubmixService = {
  getAihubmixBalance,
  checkAihubmixBalance,
}

export default AihubmixService
