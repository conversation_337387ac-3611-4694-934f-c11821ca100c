import { SlackClient } from '@/infras/monitoring/slackClient'
import { describe, it } from 'vitest'

// 设置测试频道
const TEST_CHANNEL = 'ek-test-warning-alert'

describe('SlackClient', () => {
  const slackClient = SlackClient.getInstance()

  it('should send a simple message', async () => {
    try {
      const result = await slackClient.sendMessage(
        TEST_CHANNEL,
        '测试消息: ' + new Date().toISOString(),
      )
      console.log('发送消息结果:', JSON.stringify(result, null, 2))
    } catch (err: unknown) {
      console.error('发送消息失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should send a message with blocks', async () => {
    try {
      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '测试: Block Kit 消息',
            emoji: true,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '@channel 这是一条使用 *Block Kit* 格式发送的测试消息',
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: '*测试时间:*\n' + new Date().toLocaleString(),
            },
            {
              type: 'mrkdwn',
              text: '*测试环境:*\nVitest',
            },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: 'SlackClient 测试 - ' + new Date().toISOString(),
            },
          ],
        },
      ]

      const result = await slackClient.sendBlocks(TEST_CHANNEL, blocks, { notify_channel: true })
      console.log('发送 Block 消息结果:', JSON.stringify(result, null, 2))
    } catch (err: unknown) {
      console.error('发送 Block 消息失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should test message with @频道 notification', async () => {
    try {
      const result = await slackClient.sendMessage(TEST_CHANNEL, '测试 @频道 提醒功能', {
        notify_channel: true,
      })
      console.log('@频道 提醒测试完成')
    } catch (err: unknown) {
      console.error('@频道 提醒测试失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should test different channel mention formats', async () => {
    try {
      // 直接使用 slackClient 的 sendMessage 方法
      const result = await slackClient.sendMessage(
        TEST_CHANNEL,
        '测试各种频道通知格式: <!channel> @频道 @channel',
      )

      console.log('各种频道通知格式测试完成')
    } catch (err: unknown) {
      console.error('频道通知格式测试失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should test quota warning functionality', async () => {
    try {
      // 模拟响应头数据 - 修改为正确的格式
      const mockHeaders = {
        'x-ratelimit-remaining': '9500', // 低于默认阈值
        'x-ratelimit-limit': '100000',
        'x-ratelimit-reset': '86400',
      }

      // 发送配额预警
      const result = await slackClient.checkApiQuota(mockHeaders, '测试服务', TEST_CHANNEL)

      console.log('配额预警测试完成')
    } catch (err: unknown) {
      console.error('配额预警测试失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should send payment notification successfully', async () => {
    try {
      const paymentData = {
        email: '<EMAIL>',
        plan: '企业版 Pro',
        amount: '¥19,999.00',
        subscriptionType: '年付',
        paymentTime: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }),
      }

      const result = await slackClient.sendPaymentNotification(
        TEST_CHANNEL,
        paymentData,
        '🎉 新用户付费成功',
        { notify_users: true },
      )

      console.log('付费通知发送成功:', JSON.stringify(result, null, 2))
    } catch (err: unknown) {
      console.error('付费通知发送失败:', String(err))
      throw err
    }
  }, 30_000)

  it('should send enterprise notification successfully', async () => {
    try {
      // 创建测试数据
      const enterpriseData = {
        公司名称: '测试企业科技有限公司',
        购买套餐: '信息卡企业版',
        购买数量: '50张',
        支付金额: '¥9,999.00',
        购买时间: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }),
        联系人: '张经理',
        联系电话: '13800138000',
      }

      // 发送企业通知，测试不同的通知选项
      const result = await slackClient.sendEnterpriseNotification(
        TEST_CHANNEL,
        enterpriseData,
        '🎉 企业客户购买信息卡成功',
        { notify_users: true },
      )

      console.log('企业通知发送成功:', JSON.stringify(result, null, 2))
    } catch (err: unknown) {
      console.error('企业通知发送失败:', String(err))
      throw err
    }
  }, 30_000)
})
