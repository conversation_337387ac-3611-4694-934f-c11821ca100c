import {
  API_QUOTA_WARNING_THRESHOLD,
  EASYKOL_WARNING_CHANNEL,
  REDIS_KEY_PREFIX,
  SLACK_BOT_TOKEN,
} from '@/config/env'
import { SLACK_NOTIFY_USERS, SlackUser } from '@/config/whiteList'
import { redis } from '@/infras/redis'
import { WebClient } from '@slack/web-api'

export class SlackClient {
  private client: WebClient
  private static instance: SlackClient

  // Redis 键前缀和过期时间
  private readonly REDIS_PREFIX = `${REDIS_KEY_PREFIX}slack:quota_warning:`
  private readonly WARNING_EXPIRY = 1 * 60 * 60 // 1 小时过期

  private readonly DEFAULT_API_QUOTA_CHANNEL = EASYKOL_WARNING_CHANNEL
  private readonly DEFAULT_API_QUOTA_THRESHOLD = API_QUOTA_WARNING_THRESHOLD

  private constructor() {
    const token = SLACK_BOT_TOKEN
    if (!token) {
      console.warn('[SlackClient] SLACK_BOT_TOKEN 环境变量未设置，Slack 通知功能将不可用')
    }

    // 创建 WebClient 实例
    this.client = new WebClient(token)
  }

  public static getInstance(): SlackClient {
    if (!SlackClient.instance) {
      SlackClient.instance = new SlackClient()
    }
    return SlackClient.instance
  }

  /**
   * 发送普通 Slack 消息
   * @param channel Slack 频道名
   * @param message 消息内容
   * @param options 额外选项
   * @returns 响应结果
   */
  public async sendMessage(
    channel: string,
    message: string,
    options?: { notify_channel?: boolean },
  ): Promise<any> {
    try {
      // 如果需要通知频道，添加 @channel 标签
      let messageText = message
      if (options?.notify_channel) {
        messageText = `@channel ${message}`
      }
      const result = await this.client.chat.postMessage({
        channel: channel,
        text: messageText,
        link_names: true,
        parse: 'full',
      })
      return result
    } catch (error) {
      console.error(`[SlackClient] Failed to send message to ${channel}:`, error)
      throw error
    }
  }

  /**
   * 发送结构化的 Slack 消息
   * @param channel Slack 频道名
   * @param blocks Slack blocks 格式消息
   * @param options 额外选项
   * @returns 响应结果
   */
  public async sendBlocks(
    channel: string,
    blocks: any[],
    options?: { notify_channel?: boolean },
  ): Promise<any> {
    try {
      // 准备默认文本内容（当客户端不支持 blocks 时显示）
      let defaultText = '消息通知'
      if (options?.notify_channel) {
        defaultText = '@channel 紧急通知'
      }
      // 如果需要通知频道，在第一个block添加通知
      if (options?.notify_channel && blocks.length > 0) {
        // 找到第一个文本块
        for (let i = 0; i < blocks.length; i++) {
          if (blocks[i].type === 'section' && blocks[i].text && blocks[i].text.type === 'mrkdwn') {
            // 添加频道通知到文本开头
            if (!blocks[i].text.text.includes('@channel')) {
              blocks[i].text.text = `@channel ${blocks[i].text.text}`
            }
            break
          }
        }
      }
      const result = await this.client.chat.postMessage({
        channel: channel,
        blocks: blocks,
        text: defaultText,
        link_names: true,
        parse: 'full',
      })
      return result
    } catch (error) {
      console.error(`[SlackClient] Failed to send blocks to ${channel}:`, error)
      throw error
    }
  }

  /**
   * 根据用户ID或用户名查找用户
   * @param idOrName 用户ID或用户名
   * @returns 找到的用户或undefined
   */
  private findUserByIdOrName(idOrName: string): SlackUser | undefined {
    if (!idOrName) return undefined

    // 先尝试按ID查找
    const userById = SLACK_NOTIFY_USERS.find((user) => user.id === idOrName)
    if (userById) return userById

    // 再尝试按名称查找
    return SLACK_NOTIFY_USERS.find((user) => user.name.toLowerCase() === idOrName.toLowerCase())
  }

  /**
   * 生成服务预警状态的 Redis 键
   * @param serviceName 服务名称
   * @returns Redis 键名
   */
  private getServiceRedisKey(serviceName: string): string {
    return `${this.REDIS_PREFIX}${serviceName.toLowerCase().replace(/\s+/g, '_')}`
  }

  /**
   * 检查服务是否已发送过预警
   * @param serviceName 服务名称
   * @returns 是否已发送过预警
   */
  private async hasWarnedService(serviceName: string): Promise<boolean> {
    const key = this.getServiceRedisKey(serviceName)
    const result = await redis.get(key)
    return result === '1'
  }

  /**
   * 标记服务已发送预警
   * @param serviceName 服务名称
   */
  private async markServiceWarned(serviceName: string): Promise<void> {
    const key = this.getServiceRedisKey(serviceName)
    await redis.set(key, '1', 'EX', this.WARNING_EXPIRY)
  }

  /**
   * 清除服务预警状态
   * @param serviceName 服务名称
   */
  private async clearServiceWarning(serviceName: string): Promise<void> {
    const key = this.getServiceRedisKey(serviceName)
    await redis.del(key)
  }

  /**
   * 发送配额不足预警
   * 只在配额从正常变为低于阈值时发送一次，直到配额恢复正常
   * 支持多服务、分布式环境
   *
   * @param channel Slack 频道
   * @param serviceName 服务名称
   * @param currentQuota 当前剩余配额
   * @param threshold 预警阈值
   * @returns Promise 结果或 null（如已发送过）
   */
  public async sendQuotaWarning(
    channel: string,
    serviceName: string,
    currentQuota: number,
    threshold: number = this.DEFAULT_API_QUOTA_THRESHOLD,
  ): Promise<any | null> {
    try {
      // 配额正常，重置预警状态
      if (currentQuota >= threshold) {
        const hasWarned = await this.hasWarnedService(serviceName)
        if (hasWarned) {
          await this.clearServiceWarning(serviceName)
          console.log(`[SlackClient] ${serviceName} 服务配额已恢复正常，发送恢复通知`)
          // 发送恢复正常的通知
          return this.sendMessage(
            channel,
            `✅ *${serviceName} 服务已恢复正常*\n当前剩余：${currentQuota}`,
          )
        }
        return null
      }

      // 配额低于阈值，检查是否已发送过预警
      const hasWarned = await this.hasWarnedService(serviceName)

      if (!hasWarned) {
        // 标记已预警
        await this.markServiceWarned(serviceName)
        console.log(
          `[SlackClient] ${serviceName} 服务配额不足(${currentQuota} < ${threshold})，发送预警`,
        )

        // 构建预警消息块
        const blocks = [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: `⚠️ ${serviceName} 服务配额不足警报`,
              emoji: true,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '@channel EasyKOL 服务预警 ⚠️ - 请及时处理',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*当前剩余:*\n${currentQuota} `,
              },
              {
                type: 'mrkdwn',
                text: `*预警阈值:*\n${threshold}`,
              },
            ],
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '请尽快处理，以免影响业务运行。',
            },
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} | EasyKOL 服务预警`,
              },
            ],
          },
        ]
        // 发送预警消息，启用 @channel 提醒
        return this.sendBlocks(channel, blocks, { notify_channel: true })
      }
      return null
    } catch (error) {
      console.error(`[SlackClient] ${serviceName} 配额预警处理失败: ${error}`)
      return null
    }
  }

  /**
   * 检查 API 响应头中的剩余配额，低于阈值时发送 Slack 预警
   * 通用方法，可供各 API 客户端调用
   *
   * @param headers 响应头
   * @param serviceName 服务名称（例如 'Instagram RapidAPI', 'YouTube RapidAPI'）
   * @param channel Slack 频道名
   * @param threshold 预警阈值
   */
  public async checkApiQuota(
    headers: Headers | Record<string, any> | any,
    serviceName: string,
    channel: string = this.DEFAULT_API_QUOTA_CHANNEL,
    threshold: number = this.DEFAULT_API_QUOTA_THRESHOLD,
  ): Promise<void> {
    try {
      let remaining = 0
      let limit = 0
      let reset = 0

      if (serviceName.includes('TikTok')) {
        // TikTok RapidAPI 使用不同的响应头字段
        remaining = this.getHeaderValue(headers, 'x-ratelimit-remaining')
        limit = this.getHeaderValue(headers, 'x-ratelimit-limit')
        reset = this.getHeaderValue(headers, 'x-ratelimit-reset')
      } else if (serviceName.includes('YouTube')) {
        // YouTube RapidAPI 使用标准字段
        remaining = this.getHeaderValue(headers, 'x-ratelimit-requests-remaining')
        limit = this.getHeaderValue(headers, 'x-ratelimit-requests-limit')
        reset = this.getHeaderValue(headers, 'x-ratelimit-requests-reset')
      } else if (serviceName.includes('Instagram')) {
        // ins 的处理
        remaining = headers.total - headers.used
        limit = headers.total
      } else {
        // 其他服务使用标准字段
        remaining = this.getHeaderValue(headers, 'x-ratelimit-remaining')
        limit = this.getHeaderValue(headers, 'x-ratelimit-limit')
        reset = this.getHeaderValue(headers, 'x-ratelimit-reset')
      }

      if (!isNaN(remaining) && remaining > 0) {
        await this.sendQuotaWarning(channel, serviceName, remaining, threshold)
      } else {
        console.log(`[${serviceName}] 未找到配额信息或配额信息格式不正确`)
      }
    } catch (error) {
      console.error(`[${serviceName}] 检查 API 剩余配额失败:`, error)
    }
  }

  /**
   * 获取响应头的值
   * 支持 Headers 对象和普通对象
   * 处理大小写不敏感的响应头
   */
  private getHeaderValue(headers: Headers | Record<string, any> | any, name: string): number {
    let value
    const lowerName = name.toLowerCase()

    if (headers instanceof Headers) {
      value = headers.get(name)
    } else if (headers && typeof headers === 'object') {
      const headerKeys = Object.keys(headers)

      if (headers[name] !== undefined) {
        value = headers[name]
      } else {
        const matchingKey = headerKeys.find((key) => key.toLowerCase() === lowerName)
        if (matchingKey) {
          value = headers[matchingKey]
        }
      }
    }

    if (typeof value === 'string') {
      const numValue = Number(value)
      if (!isNaN(numValue)) {
        return numValue
      }
    }

    if (typeof value === 'number' && !isNaN(value)) {
      return value
    }

    return 0
  }

  /**
   * 发送付费通知到Slack
   * @param channel Slack频道名
   * @param paymentData 付费数据
   * @param title 通知标题
   * @param options 额外选项，包括是否通知频道
   * @returns Promise<any>
   */
  public async sendPaymentNotification(
    channel: string,
    paymentData: {
      email: string
      plan: string
      amount: string
      subscriptionType: string // 订阅类型
      paymentTime: string
    },
    title: string = '🎉 用户付费通知',
    options?: {
      notify_channel?: boolean
      notify_users?: boolean
      specific_user?: string // 新增：指定特定用户ID或用户名
    },
  ): Promise<any> {
    try {
      if (!channel) {
        throw new Error('Channel is required')
      }

      if (!paymentData.email || !paymentData.plan || !paymentData.amount) {
        throw new Error('Required payment data fields are missing')
      }

      const blocks = this.createPaymentBlocks(paymentData, title)

      let defaultText = 'EasyKOL项目 用户付费通知'

      // 如果指定了特定用户
      if (options?.specific_user) {
        const specificUser = this.findUserByIdOrName(options.specific_user)
        if (specificUser) {
          defaultText = `@${specificUser.name} EasyKOL项目 用户付费通知`

          // 在标题后添加@特定用户通知
          blocks.splice(1, 0, {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `<@${specificUser.id}>`,
            },
          })
        }
      }
      // 如果需要通知所有配置的用户
      else if (options?.notify_users && SLACK_NOTIFY_USERS.length > 0) {
        // 为桌面通知准备更友好的格式
        const userNames = SLACK_NOTIFY_USERS.map((user) => user.name).join(', ')
        defaultText = `@${userNames} EasyKOL项目 用户付费通知`

        // 在标题后添加@所有用户通知
        const userMentions = SLACK_NOTIFY_USERS.map((user) => `<@${user.id}>`).join(' ')
        blocks.splice(1, 0, {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: userMentions,
          },
        })
      }
      // 如果需要通知频道
      else if (options?.notify_channel) {
        defaultText = '<!channel> EasyKOL项目 用户付费通知'

        // 在标题后添加@channel通知
        blocks.splice(1, 0, {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '<!channel>',
          },
        })
      }

      const result = await this.client.chat.postMessage({
        channel: channel,
        blocks: blocks,
        text: defaultText,
        link_names: true,
        parse: 'full',
        unfurl_links: false,
        as_user: true,
      })

      return result
    } catch (error) {
      console.error(`[SlackClient] Failed to send payment notification to ${channel}:`, error)
      throw error
    }
  }

  /**
   * 创建付费通知的Slack blocks
   * @param paymentData 付费数据
   * @param title 通知标题
   * @returns Slack blocks数组
   */
  private createPaymentBlocks(
    paymentData: {
      email: string
      plan: string
      amount: string
      subscriptionType: string
      paymentTime: string
    },
    title: string,
  ): any[] {
    const blocks: any[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: title,
          emoji: true,
        },
      },
    ]

    // 定义字段显示顺序和格式
    const fieldLabels = {
      email: '📧 邮箱',
      plan: '🎫 套餐类型',
      amount: '💰 金额',
      subscriptionType: '💳 订阅类型',
      paymentTime: '⏰ 支付时间',
    }

    // 创建紧凑的文本内容
    let messageText = ''
    Object.entries(paymentData).forEach(([key, value]) => {
      if (value) {
        const label = fieldLabels[key as keyof typeof fieldLabels] || key
        messageText += `*${label}*：${value}\n`
      }
    })

    // 添加消息文本块
    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: messageText,
      },
    })

    // 添加时间戳上下文
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} | EasyKOL Payment Notification`,
        },
      ],
    })

    return blocks
  }

  /**
   * 发送企业相关自定义通知到Slack
   * @param channel Slack频道名
   * @param data 通知数据，字段不限
   * @param title 通知标题
   * @param options 额外选项
   */
  public async sendEnterpriseNotification(
    channel: string,
    data: Record<string, any>,
    title: string = '🎉 信息卡会员购买成功通知',
    options?: {
      notify_channel?: boolean
      notify_users?: boolean
      specific_user?: string
    },
  ): Promise<any> {
    try {
      if (!channel) throw new Error('Channel is required')
      if (!data) throw new Error('Data is required')

      const blocks = this.createEnterpriseBlocks(data, title)
      let defaultText = '信息卡会员购买成功通知'

      if (options?.specific_user) {
        const specificUser = this.findUserByIdOrName(options.specific_user)
        if (specificUser) {
          defaultText = `@${specificUser.name} 信息卡会员购买成功通知`
          blocks.splice(1, 0, {
            type: 'section',
            text: { type: 'mrkdwn', text: `<@${specificUser.id}>` },
          })
        }
      } else if (options?.notify_users && SLACK_NOTIFY_USERS.length > 0) {
        const userNames = SLACK_NOTIFY_USERS.map((user) => user.name).join(', ')
        defaultText = `@${userNames} 信息卡会员购买成功通知`
        const userMentions = SLACK_NOTIFY_USERS.map((user) => `<@${user.id}>`).join(' ')
        blocks.splice(1, 0, {
          type: 'section',
          text: { type: 'mrkdwn', text: userMentions },
        })
      } else if (options?.notify_channel) {
        defaultText = '<!channel> 信息卡会员购买成功通知'
        blocks.splice(1, 0, {
          type: 'section',
          text: { type: 'mrkdwn', text: '<!channel>' },
        })
      }

      const result = await this.client.chat.postMessage({
        channel,
        blocks,
        text: defaultText,
        link_names: true,
        parse: 'full',
        unfurl_links: false,
        as_user: true,
      })
      return result
    } catch (error) {
      console.error(`[SlackClient] Failed to send enterprise notification to ${channel}:`, error)
      throw error
    }
  }

  /**
   * 创建企业自定义通知的Slack blocks
   * @param data 通知数据
   * @param title 通知标题
   */
  private createEnterpriseBlocks(data: Record<string, any>, title: string): any[] {
    const blocks: any[] = [
      {
        type: 'header',
        text: { type: 'plain_text', text: title, emoji: true },
      },
    ]
    let messageText = ''
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        messageText += `*${key}*：${value}\n`
      }
    })
    blocks.push({
      type: 'section',
      text: { type: 'mrkdwn', text: messageText },
    })
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })} | EasyKOL Enterprise Notification`,
        },
      ],
    })
    return blocks
  }
}
