import log4js from 'log4js'

// 配置 log4js，worker服务使用的日志
log4js.configure({
  appenders: {
    // 控制台日志输出
    consoleAppender: {
      type: 'console', // 输出到控制台
      layout: {
        type: 'pattern',
        pattern: '%d{yyyy-MM-dd hh:mm:ss} [%p] %c - %m', // 自定义格式：时间 日志级别 分类 - 日志内容
      },
    },
  },
  categories: {
    default: { appenders: ['consoleAppender'], level: 'info' }, // 默认类别，输出到文件和控制台
    error: { appenders: ['consoleAppender'], level: 'error' }, // 错误日志输出到文件和控制台
    // 你可以根据需要为不同的日志级别配置不同的输出
  },
})

console.log('log4js configured')
// 导出日志实例
export const logger = log4js.getLogger()
