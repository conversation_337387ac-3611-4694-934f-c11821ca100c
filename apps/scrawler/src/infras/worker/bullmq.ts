import {
  EASYKOL_TRACK_QUEUE_TASK_DURATION,
  EASYKOL_TRACK_QUEUE_TASK_LIMIT,
  EMBEDDING_RETRY_ATTEMPTS,
  EMBEDDING_TASK_TIMEOUT,
  INSTAGRAM_QUEUE_TASK_DURATION,
  INSTAGRAM_QUEUE_TASK_LIMIT,
  LONG_CRAWLER_TASK_QUEUE_TASK_DURATION,
  LONG_CRAWLER_TASK_QUEUE_TASK_LIMIT,
  REDIS_KEY_PREFIX,
  TIKTOK_QUEUE_TASK_DURATION,
  TIKTOK_QUEUE_TASK_LIMIT,
  TWITTER_QUEUE_TASK_DURATION,
  TWITTER_QUEUE_TASK_LIMIT,
  US_REDIS_URL,
  YOUTUBE_QUEUE_TASK_DURATION,
  YOUTUBE_QUEUE_TASK_LIMIT,
} from '@/config/env.ts'
import { SupportedPlatform, TaskParams } from '@/types/taskParams'
import { TaskMetricsCollector } from '@/utils/TaskMetricsCollector'
import { TaskReason, TaskType } from '@repo/database'
import Bull from 'bull'
import _ from 'lodash'

export type IEmbeddingTask = {
  id: string
  params: TaskParams
  result: any
  type?: TaskType
  platform: SupportedPlatform | 'unknown'
  reason?: TaskReason
  createdAt: Date
  createdBy: string
  metrics: TaskMetricsCollector
}

const DEFAULT_OPTIONS: Bull.QueueOptions = {
  prefix: `${REDIS_KEY_PREFIX}bull`,
  defaultJobOptions: {
    removeOnComplete: true,
    removeOnFail: 100,
    timeout: +EMBEDDING_TASK_TIMEOUT, // 任务超时时间
    attempts: +EMBEDDING_RETRY_ATTEMPTS, // 任务重试次数
    backoff: {
      type: 'exponential', // 指数退避
      delay: 3000, // 延迟3秒 3*（2^n-1）
    },
  },
}

export const youtubeTaskQueue = new Bull<IEmbeddingTask>(
  'youtube-task-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    // 任务状态监控，特别是任务是否卡住
    settings: {
      stalledInterval: 30_000, // 每隔60秒，Bull会检查一次是否有任务卡住
      maxStalledCount: 3, // 当任务被检测到 stalled 时，会尝试重新执行，最多重试3次
      lockDuration: 240_000, // 当一个 worker 开始处理任务时，会获得一个锁，防止其他 worker 同时处理同一个任务
      lockRenewTime: 60_000, // 每60秒，worker 会尝试续期它正在处理的任务的锁
    },
    // 重试策略

    limiter: {
      max: +YOUTUBE_QUEUE_TASK_LIMIT, // 最大并发数
      duration: +YOUTUBE_QUEUE_TASK_DURATION, // 限制时间
      bounceBack: false, // 如果达到最大并发数，是否返回错误
    },
  }),
)

export const tiktokTaskQueue = new Bull<IEmbeddingTask>(
  'tiktok-task-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 30_000,
      maxStalledCount: 3,
      lockDuration: 240_000,
      lockRenewTime: 60_000,
    },
    // bullmq 限流器
    limiter: {
      max: +TIKTOK_QUEUE_TASK_LIMIT,
      duration: +TIKTOK_QUEUE_TASK_DURATION,
      bounceBack: false,
    },
  }),
)

export const instagramTaskQueue = new Bull<IEmbeddingTask>(
  'instagram-task-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 30_000,
      maxStalledCount: 3,
      lockDuration: 240_000,
      lockRenewTime: 60_000,
    },
    limiter: {
      max: +INSTAGRAM_QUEUE_TASK_LIMIT,
      duration: +INSTAGRAM_QUEUE_TASK_DURATION,
      bounceBack: false,
    },
  }),
)

// 创建专门处理EasyKOL Track类型任务的队列
export const easykolTrackQueue = new Bull<IEmbeddingTask>(
  'easykol-track-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 30_000,
      maxStalledCount: 3,
      lockDuration: 240_000,
      lockRenewTime: 60_000,
    },
    limiter: {
      max: +EASYKOL_TRACK_QUEUE_TASK_LIMIT,
      duration: +EASYKOL_TRACK_QUEUE_TASK_DURATION,
      bounceBack: false,
    },
  }),
)

// 创建专门处理长任务的队列，如Instagram旅游博主爬取
export const longCrawlerTaskQueue = new Bull<IEmbeddingTask>(
  'long-crawler-task-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 90_000, // 每90秒检查一次任务是否卡住
      maxStalledCount: 10, // 最多重试10次卡住的任务
      lockDuration: 180_000, // 锁定时间3分钟
      lockRenewTime: 90_000, // 每1.5分钟续期一次锁
    },
    defaultJobOptions: {
      ...DEFAULT_OPTIONS.defaultJobOptions,
      timeout: 8_640_000, // 任务超时时间24小时
      attempts: 3, // 任务失败后重试3次
      removeOnComplete: true, // 完成后删除任务
      removeOnFail: false, // 失败后不删除，便于调试
    },
    limiter: {
      max: +(LONG_CRAWLER_TASK_QUEUE_TASK_LIMIT || 20), // 最大并发数
      duration: +(LONG_CRAWLER_TASK_QUEUE_TASK_DURATION || 180_000), // 3分钟限制时间
      bounceBack: false,
    },
  }),
)

export const twitterTaskQueue = new Bull<IEmbeddingTask>(
  'twitter-task-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 30_000,
      maxStalledCount: 3,
      lockDuration: 240_000,
      lockRenewTime: 60_000,
    },
    limiter: {
      max: +TWITTER_QUEUE_TASK_LIMIT,
      duration: +TWITTER_QUEUE_TASK_DURATION,
      bounceBack: false,
    },
  }),
)

// 邮件跟进任务的数据类型
export interface IEmailPlanTask {
  id: string
  emailPlanId: string
  userId: string
  createdAt: Date
  createdBy: string
}

// 创建邮件跟进队列
export const emailPlanQueue = new Bull<IEmailPlanTask>(
  'email-plan-queue',
  US_REDIS_URL,
  _.merge(DEFAULT_OPTIONS, {
    settings: {
      stalledInterval: 30_000,
      maxStalledCount: 3,
      lockDuration: 60_000,
      lockRenewTime: 30_000,
    },
    defaultJobOptions: {
      ...DEFAULT_OPTIONS.defaultJobOptions,
      timeout: 60_000, // 邮件发送超时时间 1 分钟
      attempts: 3,
      removeOnComplete: true,
      removeOnFail: 100,
      backoff: {
        type: 'exponential',
        delay: 5000,
      },
    },
    limiter: {
      max: 10, // 最大并发数10个邮件发送
      duration: 60_000, // 每分钟最多发送10封邮件
      bounceBack: false,
    },
  }),
)
