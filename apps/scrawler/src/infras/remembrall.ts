import { ofetch } from 'ofetch'
import { redis } from './redis'
import Sentry from './sentry'

const appId = 'TBFQL3SqAVRW' // EasyKol appId: https://123.midway.run/#/app-config/TBFQL3SqAVRW

export async function dynamicGet<T>(key: string, cacheSeconds?: number): Promise<T | null> {
  try {
    if (cacheSeconds && cacheSeconds > 0) {
      let resp = await getFromCache(key)
      if (resp) {
        try {
          resp = JSON.parse(resp)
        } catch (err) {
          console.log(err)
        }
        return resp as T
      }
    }
    const results = await ofetch('http://remembrall.midway.run/api/configs/dynamicGet', {
      method: 'POST',
      body: {
        appId: appId,
        keys: [key],
      },
    })
    const data = results!.data[key]
    if (cacheSeconds && cacheSeconds > 0) {
      await setCache(key, JSON.stringify(data), cacheSeconds)
    }
    console.log(`get config ${key} from remote`)
    return data
  } catch (err) {
    console.log(err)
    Sentry.captureException(err)
    return null
  }
}

export async function batchDynamicGet<T extends string>(keys: T[]): Promise<Record<T, any> | null> {
  try {
    const results = await ofetch('http://remembrall.midway.run/api/configs/dynamicGet', {
      method: 'POST',
      body: {
        appId: appId,
        keys: keys,
      },
    })
    return results!.data
  } catch (err) {
    Sentry.captureException(err)
    return null
  }
}

const PREFIX = 'easykol:remembrall-cache:'

async function getFromCache(key: string): Promise<string | null> {
  try {
    return redis.get(PREFIX + key)
  } catch (err) {
    return null
  }
}

async function setCache(
  key: string,
  value: string,
  timeoutSeconds: number,
): Promise<string | null> {
  try {
    return redis.set(PREFIX + key, value, 'EX', timeoutSeconds)
  } catch (err) {
    return null
  }
}

async function clearCache(key: string): Promise<number | null> {
  try {
    return redis.del(PREFIX + key)
  } catch (err) {
    return null
  }
}
