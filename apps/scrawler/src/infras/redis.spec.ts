import { redis } from '@/infras/redis.ts'
import { describe, expect, it } from 'vitest'

describe('should operate redis', () => {
  it('should set and get redis data', async () => {
    const testKey = 'test'
    const kv = new Map<string, object>()
    kv.set(testKey, { a: 1, b: 2 })
    await redis.set(testKey, JSON.stringify(kv.get(testKey)))
    const value = await redis.get(testKey)
    expect(value).toBeTruthy()
    const struct = JSON.parse(value!) as { a: number; b: number }
    expect(struct.a).eq(1)
  })
})
