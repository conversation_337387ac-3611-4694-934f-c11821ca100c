import * as Sentry from '@sentry/node'
import { nodeProfilingIntegration } from '@sentry/profiling-node'

Sentry.init({
  dsn: 'https://<EMAIL>/688',
  integrations: [
    // Add our Profiling integration
    nodeProfilingIntegration(),
    Sentry.prismaIntegration(),
  ],
  tracesSampleRate: 1.0,
  profilesSampleRate: 1.0,
  environment: process.env.NODE_ENV,
  beforeSend: (event, hint) => {
    if ((process.env.NODE_ENV === 'development' || process.env.LOG_ERROR) && hint) {
      console.error(hint.originalException || hint.syntheticException)
      return null
    }
    return event
  },
})

export default Sentry
