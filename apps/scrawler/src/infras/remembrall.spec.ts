import { ofetch } from 'ofetch'
import { describe, expect, it } from 'vitest'
import * as dconfig from './remembrall'

const appId = 'TBFQL3SqAVRW'
describe('get config from remembrall', () => {
  it('should return test_url', async () => {
    // const results = await mclients.remembrallService.dynamicGet({
    //   appId: appId,
    //   keys: ['test_url'],
    // })
    const results = await ofetch('http://remembrall.midway.run/api/configs/dynamicGet', {
      method: 'POST',
      body: {
        appId: appId,
        keys: ['test_url'],
      },
    })
    console.log(results)
  })

  it('should get result', async () => {
    const result = await dconfig.dynamicGet('test_url')
    expect(result).eq('http://google.com/')
  })

  it('should get result batch', async () => {
    const result = await dconfig.batchDynamicGet(['test_url', 'test_json'])
    if (result) {
      expect(result.test_url).eq('http://google.com/')
      expect(result.test_json.msg).eq('123')
    }
  })
})
