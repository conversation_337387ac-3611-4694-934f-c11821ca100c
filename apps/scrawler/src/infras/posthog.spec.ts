import { postHogClient } from '@/infras/posthog.ts'
import { describe, it } from 'vitest'

describe('should update tracking info', () => {
  it('should update search result', () => {
    for (let i = 0; i < 10; i++) {
      postHogClient.capture({
        event: 'test-search',
        properties: {
          size: 80,
          projectId: '1',
          taskType: 'SEARCH',
          userId: '123',
        },
        distinctId: 'test',
        timestamp: new Date(),
      })
    }
  })
})
