import { TikTokVideo } from '@/api/@types/rapidapi/Tiktok'
import TiktokApi from '@/api/tiktok'
import fs from 'fs'
import path from 'path'
import { describe, expect, it } from 'vitest'

const testUniqueId = 'thedailyaus'
const testHashTag = '33380'
const testHashTagName = 'iphone16'
const badKeyword = 'sexy'
const testVideoId = '7353637759162141959'

describe('Tiktok APIs', () => {
  describe('get tiktok user info', async () => {
    it('Should return Tiktok user info', async () => {
      const userInfo = await TiktokApi.getInstance().getUserDetail({ unique_id: testUniqueId })
      console.log('userInfo:', userInfo)
      expect(userInfo?.user).toBeDefined()
      expect(userInfo?.stats).toBeDefined()
    })
  })
  describe('get tiktok videos', () => {
    it('should return a video detail', async () => {
      const videos = await TiktokApi.getInstance().getHashtagVideos(testHashTag, 1)
      console.log('videos:', videos)
      expect(videos).not.toBeNull()
      expect(videos!.length).eq(1)
      const targetVideo = await TiktokApi.getInstance().getVideoDetail(videos![0].video_id)
      expect(targetVideo?.id).eq(videos![0].video_id)
    })

    it("should return a user's videos", async () => {
      const videos = await TiktokApi.getInstance().getUserVideos({
        unique_id: testUniqueId,
        count: 10,
        cursor: 0,
      })
      console.log('videos:', videos)
      expect(videos).not.toBeNull()
      expect(videos!.length).eq(10)
    })

    it('should return video list', async () => {
      const videos = await TiktokApi.getInstance().getHashtagVideos(testHashTag)
      expect(videos).not.toBeNull()
      if (videos) {
        expect(videos).toBeDefined()
        expect(videos.length).eq(10)
      } else {
        throw new Error('Videos should not be null')
      }
    })

    it('should return only 1 video', async () => {
      const videos = await TiktokApi.getInstance().getHashtagVideos(testHashTag, 1)
      expect(videos).not.toBeNull()
      if (videos) {
        expect(videos).toBeDefined()
        expect(videos.length).eq(1)
      } else {
        throw new Error('Videos should not be null')
      }
    })

    it('should have a video in common', async () => {
      const firstPage = await TiktokApi.getInstance().getHashtagVideos(testHashTag, 2, 0)
      const secondPage = await TiktokApi.getInstance().getHashtagVideos(testHashTag, 2, 1) // offset 1
      expect(firstPage).not.toBeNull()
      expect(secondPage).not.toBeNull()
      expect(firstPage!.length).eq(2)
      expect(secondPage!.length).eq(2)
      expect(firstPage![1].video_id).eq(secondPage![0].video_id)
    })
  })

  describe('get hash tag info', () => {
    it('should extract video tags', async () => {
      const video = await TiktokApi.getInstance().getVideoDetail(testVideoId)
      const title = video?.title ?? ''
      const regex = /#\S+/g
      const matches = title.match(regex)
      if (!matches) {
        console.log('video has no tag: ' + title)
      }
      const tags = matches?.map((i) => i.slice(1)) as string[]
      console.log('tags:', tags)
      expect(tags.length).gt(0)
    })

    it('should return a hashtag detail', async () => {
      const tag = await TiktokApi.getInstance().getHashTag(testHashTagName)
      console.log('tag:', tag)
      expect(tag).not.toBeNull()
      expect(tag?.cha_name).eq(testHashTagName)
    })

    it('should return tag videos', async () => {
      const searchResults = await TiktokApi.getInstance().searchVideos({
        keywords: badKeyword,
        region: 'jp',
        count: 30,
      })
      expect(searchResults?.videos.length).eq(0)
    })
  })

  describe("suite test get user's related users", () => {
    const fromUniqueId = 'therapyjessaofficial'
    let videos: TikTokVideo[] = []
    it(
      'should run over',
      async () => {
        // find user's videos
        const userVideos = await TiktokApi.getInstance().getUserVideos({
          unique_id: fromUniqueId,
          count: 5,
          cursor: 0,
        })
        expect(userVideos).not.toBeNull()
        if (userVideos) {
          videos = userVideos
          expect(videos.length).gte(5)
        } else {
          throw new Error('User videos should not be null')
        }

        // match videos' tags
        const allTags = []
        for (let i = 0; i < videos.length; i++) {
          const title = videos[i].title
          const regex = /#\S+/g
          const matches = title.match(regex)
          if (!matches) {
            console.log('video has no tag: ' + title)
            continue
          }
          const tags = matches?.map((i) => i.slice(1).toLowerCase()) as string[]
          allTags.push(...tags)
        }
        const countMap = allTags.reduce((map, item) => {
          map.set(item, (map.get(item) || 0) + 1)
          return map
        }, new Map())
        const bestTagMap = Array.from(countMap)
          .filter((_, count) => count >= 2)
          .sort((a, b) => b[1] - a[1])
        console.log(`tags in common: ${JSON.stringify(bestTagMap)}`)
        // TODO 隐含的加权机会，这里先不用加权。后续再考虑
        const bestTags = bestTagMap.map((i) => i[0].toLowerCase())
        console.log(`tags in common: ${JSON.stringify(bestTags)}`)
        expect(bestTags.length).gt(0)

        // find tags' videos
        const relatedVideos: TikTokVideo[] = []
        for (let i = 0; i < bestTags.length; i++) {
          console.log(`finding videos for ${bestTags[i]}(${i}/${bestTags.length})`)
          const tagInfo = await TiktokApi.getInstance().getHashTag(bestTags[i])
          expect(tagInfo).not.toBeNull()
          expect(tagInfo!.cha_name.toLowerCase()).eq(bestTags[i])
          expect(tagInfo!.id).toBeDefined()
          const tagVideos = await TiktokApi.getInstance().getHashtagVideos(tagInfo!.id)
          if (tagVideos) {
            relatedVideos.push(...tagVideos)
          }
        }
        expect(relatedVideos.length).gt(0)

        // find videos users
        const users = relatedVideos.map((video) => video.author)
        expect(users.length).gt(0)
        // all done!

        const userLinks = users.map((user) => 'https://www.tiktok.com/@' + user.unique_id)
        console.log(`here are related users from ${fromUniqueId}:`)
        console.log(userLinks)
      },
      { timeout: 60000 },
    )
  })

  describe('TikTok Search Videos', () => {
    it('should search for videos with specific sort type and print the result', async () => {
      const result = await TiktokApi.getInstance().searchVideos({
        keywords: 'funny cats',
        sortType: 0, // 只指定 keywords 和 sortType
      })

      console.log('Search Results:', JSON.stringify(result, null, 2))

      expect(result).not.toBeNull()
      if (result) {
        expect(result.videos).toBeDefined()
        expect(Array.isArray(result.videos)).toBe(true)
        expect(result.videos.length).toBeGreaterThan(0)
      }
    }, 30000) // 设置超时时间为30秒
  })

  describe('get video comments', () => {
    it('should get video comments', async () => {
      const comments = await TiktokApi.getInstance().getVideoComments({
        url: 'https://www.tiktok.com/@thedailyaus/video/7353637759162141959',
        count: 50,
        cursor: 0,
      })
      console.log('comments:', comments)
      expect(comments).not.toBeNull()
    })
  })
})

describe('tiktok get hashtag all videos', () => {
  const tagId = '61724070'
  const userCount = 2047
  it('should get all videos and the count.', async () => {
    let hasMore = true
    let cursor = 0
    const videos: TikTokVideo[] = []
    while (hasMore) {
      const result = await TiktokApi.getInstance().getHashtagResult(tagId, 20, cursor)
      if (result) {
        console.log(
          `get ${result.videos.length} videos, hasMore: ${result.hasMore}, cursor: ${result.cursor}`,
        )
        videos.push(...result.videos)
        hasMore = result.hasMore
        cursor = result.cursor
      }
    }
    console.log(`get ${videos.length} videos, target is ${userCount}`)
    if (!hasMore) {
      console.log('try to get more videos')
      const result = await TiktokApi.getInstance().getHashtagResult(tagId, 20, cursor)
      if (result) {
        if (result.videos.length > 0) {
          console.log('we did get videos for cursor: ', result.cursor)
          videos.push(...result.videos)
        }
        hasMore = result.hasMore
        cursor = result.cursor
      } else {
        console.log('no more videos')
      }
    }
    console.log(`total videos: ${videos.length}`)

    expect(videos.length).toBeGreaterThan(0)
    const outputDir = path.join(__dirname, '../../data')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    const outputPath = path.join(outputDir, `hashtag_${tagId}_videos.json`)
    fs.writeFileSync(outputPath, JSON.stringify(videos, null, 2))
    console.log(`视频数据已保存到: ${outputPath}`)
  }, 600_000)
})
