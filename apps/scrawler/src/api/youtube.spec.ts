import { describe, expect, it } from 'vitest'
import YoutubeApi from './youtube'

describe('it should test youtube rapid api v1', () => {
  it(
    'should get channel with videos',
    async () => {
      const channel = await YoutubeApi.getInstance().getChannelWithVideos(
        'UCTs8tCOY2h31UOKT-BhAoYw',
      )
      expect(channel).toBeDefined()
      expect(channel?.videos.length).gt(0)
      console.log(channel?.videos.map((video) => video.title).join('\n'))
    },
    60 * 1000,
  )
})
