import InstagramApi from '@/api/instagram.ts'
import Blue<PERSON> from 'bluebird'
import { describe, expect, it } from 'vitest'

describe('should test about instagram call rapid api', () => {
  describe('get instagram info', () => {
    const api = InstagramApi.getInstance()
    const testUsername = 'foodbabyny'
    const testUserId = '1385320598'
    const zuckUsername = 'zuck'

    it(
      "should return user's info",
      async () => {
        const user = await api.getUserWithPosts(testUsername)
        expect(user).not.toBeNull()
        expect(user?.username).eq(testUsername)
      },
      10 * 1000,
    )
    it("should return user's posts", async () => {
      const posts = await api.getPosts(testUsername)
      expect(posts).not.toBeNull()
      expect(posts).instanceOf(Array)
      expect(posts!.length).lte(12)
    })

    it(
      'should return user with its posts',
      async () => {
        const user = await api.getUserWithPosts(testUsername)
        expect(user).toBeDefined()
        expect(user.posts).toBeDefined()
        expect(user.posts?.length).gt(0)
      },
      10 * 1000,
    )

    it(
      'should return users related users',
      async () => {
        const relatedUsers = await api.getUserRelatedUsers(testUserId)
        expect(relatedUsers).toBeDefined()
        expect(relatedUsers?.length).gt(0)
      },
      5 * 1000,
    )

    it(
      'should return users related users',
      async () => {
        const user = await api.getUser('asos')
        expect(user?.id).toBeDefined()
        const relatedUsers = await api.getUserRelatedUsers(user!.id)
        expect(relatedUsers).toBeDefined()
        expect(relatedUsers?.length).gt(0)
      },
      5 * 1000,
    )

    it(
      "should return user's related users detail",
      async () => {
        const users = await api.getUserRelatedUsers('12526211975')
        expect(users).not.toBeNull()
        expect(users).instanceOf(Array)
        // expect(users?.length).eq(80)
        const someUsers = users
        const fullUsers = await Bluebird.map(
          someUsers!,
          async (user) => {
            console.log(`fetching ${user.username}`)
            return InstagramApi.getInstance().getUser(user.username)
          },
          { concurrency: 5 },
        )
        const extactInfo = fullUsers.map((u) => {
          return {
            username: u?.username,
            title: u?.fullName,
            biography: u?.biography,
            country: u?.region,
          }
        })
        console.log(JSON.stringify(extactInfo))
      },
      60 * 60 * 1000,
    )

    it('should return post detail', async () => {
      const postId = 'DExggZvxSkk'
      const post = await InstagramApi.getInstance().getPost(postId)
      expect(post?.caption.text.length).gt(0)
      expect(post?.username?.length).gt(0)
      expect(post?.comment_count).gt(0)
      expect(post?.like_count).gt(0)
      expect(post?.id.length).gt(0)
      expect(post?.created_at).gt(0)
    })

    it('should get reel detail', async () => {
      const reelId = 'DEP16mMByKx'
      const reel = await InstagramApi.getInstance().getPost(reelId)
      expect(reel?.caption.text.length).gt(0)
      expect(reel?.username?.length).gt(0)
      expect(reel?.comment_count).gt(0)
      expect(reel?.like_count).gt(0)
      expect(reel?.play_count).gt(0)
      expect(reel?.id.length).gt(0)
      expect(reel?.created_at).gt(0)
    })

    it('should get userInfo', async () => {
      const username = 'eatingnyc'
      const user = await api.getUser(username)
      console.log(user)
      expect(user).toBeDefined()
    })
  })
})
