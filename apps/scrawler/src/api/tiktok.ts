import {
  RapidApiStatus,
  TTRapidApiResponse,
  TTRapidApiUserPaginateRequest,
  TTRapidApiUserRequest,
} from '@/api/@types/rapidapi/RapidApi'
import {
  GetVideoCommentsParams,
  TikTokSearchResponse,
  TikTokVideo,
  TiktokChallenge,
  TiktokCommentData,
  TiktokFollowersResponse,
  TiktokFollowingResponse,
  TiktokHashtagVideosData,
  TiktokMusicData,
  TiktokMusicPostsData,
  TiktokUser,
  TiktokVideoDetail,
} from '@/api/@types/rapidapi/Tiktok'
import { TIKTOK_API_BASE_URL, TT_RAPID_API_KEY } from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { KolPlatform } from '@repo/database'
import assert from 'assert'
import axios from 'axios'
import axiosRetry from 'axios-retry'
import RapidApiStatsCollector from './ApiCallStat'

// 配置 axios-retry
const axiosInstance = axios.create({
  method: 'GET',
  headers: {
    'api-key': TT_RAPID_API_KEY,
  },
})

axiosRetry(axiosInstance, {
  retries: 3,
  retryDelay: (retryCount) => {
    return 500 // 重试延迟0.5s
  },
  retryCondition: (error) => {
    // 这里可以自定义重试条件
    return (
      axiosRetry.isNetworkOrIdempotentRequestError(error) ||
      error.response?.status === 429 ||
      error.response?.status === 500
    )
  },
})

class TiktokApi {
  private static instance: TiktokApi

  private host = TIKTOK_API_BASE_URL
  private apiKey = TT_RAPID_API_KEY

  public static getInstance(): TiktokApi {
    if (!TiktokApi.instance) {
      TiktokApi.instance = new TiktokApi()
    }
    return TiktokApi.instance
  }

  public async getUserDetail(
    request: TTRapidApiUserRequest,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokUser | null> {
    let user = undefined
    if (request.unique_id) {
      user = await this.handleRequest(() =>
        this.get<TiktokUser>(
          `${this.host}/user/info?unique_id=${request.unique_id?.toLowerCase()}`,
          collector,
        ).then((res) => res),
      )
    } else if (request.user_id) {
      user = await this.handleRequest(() =>
        this.get<TiktokUser>(`${this.host}/user/info?user_id=${request.user_id}`).then(
          (res) => res,
        ),
      )
    } else {
      throw new Error('user_id or unique_id is required')
    }
    if (user?.user.bio_email) {
      user.user.email = user.user.bio_email
      user.user.emailSource = EmailSourceType.RAPID_EMAIL_BUTTON
    } else if (user?.user && extractEmail(user?.user?.signature ?? '')) {
      user.user.email = extractEmail(user?.user?.signature ?? '') || undefined
      user.user.emailSource = EmailSourceType.BIO_EMAIL
    }
    return user
  }

  public async getUserVideos(
    request: TTRapidApiUserPaginateRequest,
    collector?: RapidApiStatsCollector,
  ): Promise<TikTokVideo[] | null> {
    if (request.user_id) {
      return this.handleRequest(() =>
        this.get<{ videos: TikTokVideo[] }>(
          `${this.host}/user/posts?user_id=${request.user_id}&count=${request.count}&cursor=${request.cursor}`,
          collector,
        ).then((res) => res.videos),
      )
    }
    if (request.unique_id) {
      return this.handleRequest(() =>
        this.get<{ videos: TikTokVideo[] }>(
          `${this.host}/user/posts?unique_id=${request.unique_id?.toLowerCase()}&count=${request.count}&cursor=${request.cursor}`,
          collector,
        ).then((res) => res.videos),
      )
    }
    throw new Error('user_id or unique_id is required')
  }

  public async getHashTag(
    name: string,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokChallenge | null> {
    return this.handleRequest(() =>
      this.get(`${this.host}/challenge/info?challenge_name=${name}`, collector),
    )
  }

  public async getHashtagVideos(
    challengeId: string,
    count: number = 30,
    offset: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<TikTokVideo[] | null> {
    return this.handleRequest(() =>
      this.get<{
        videos: TikTokVideo[]
      }>(
        `${this.host}/challenge/posts?challenge_id=${challengeId}&count=${count}&cursor=${offset}`,
        collector,
      ).then((res) => res.videos),
    )
  }

  public async getHashtagResult(
    challengeId: string,
    count: number = 30,
    offset: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<{ videos: TikTokVideo[]; hasMore: boolean; cursor: number } | null> {
    return this.handleRequest(() =>
      this.get<{
        videos: TikTokVideo[]
        hasMore: boolean
        cursor: number
      }>(
        `${this.host}/challenge/posts?challenge_id=${challengeId}&count=${count}&cursor=${offset}`,
        collector,
      ).then((res) => ({
        videos: res.videos,
        hasMore: res.hasMore,
        cursor: res.cursor,
      })),
    )
  }

  public async getHashtagVideosWithResponse(
    challengeId: string,
    count: number = 30,
    cursor: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<TTRapidApiResponse<TiktokHashtagVideosData> | null> {
    return this.handleRequest(() =>
      this.getWithResponse<TiktokHashtagVideosData>(
        `${this.host}/challenge/posts?challenge_id=${challengeId}&count=${count}&cursor=${cursor}`,
        collector,
      ),
    )
  }

  public async getVideoDetail(
    url: string,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokVideoDetail | null> {
    return this.handleRequest(() =>
      this.get<TiktokVideoDetail>(`${this.host}/?url=${url}&hd=1`, collector),
    )
  }

  public async getUserFollowing(
    userId: string,
    count: number = 200,
    time: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokFollowingResponse | null> {
    return this.handleRequest(() =>
      this.get<TiktokFollowingResponse>(
        `${this.host}/user/following?user_id=${userId}&count=${count}&time=${time}`,
        collector,
      ),
    )
  }
  // 获取用户粉丝
  public async getUserFollowers(
    userId: string,
    count: number = 200,
    time: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokFollowersResponse | null> {
    return this.handleRequest(() =>
      this.get<TiktokFollowersResponse>(
        `${this.host}/user/followers?user_id=${userId}&count=${count}&time=${time}`,
        collector,
      ),
    )
  }

  public async searchVideos(
    {
      keywords,
      region,
      count,
      cursor,
      publishTime,
      sortType,
    }: {
      keywords: string
      region?: string
      count?: number
      cursor?: number
      publishTime?: number
      sortType?: number
    },
    collector?: RapidApiStatsCollector,
  ): Promise<TikTokSearchResponse | null> {
    return this.handleRequest(() =>
      this.get<TikTokSearchResponse>(
        `${this.host}/feed/search?keywords=${encodeURIComponent(keywords)}${region ? `&region=${region}` : ''}${count ? `&count=${count}` : ''}${cursor ? `&cursor=${cursor}` : ''}${publishTime ? `&publish_time=${publishTime}` : ''}${sortType ? `&sort_type=${sortType}` : ''}`,
        collector,
      ),
    )
  }
  // 获取视频下的评论
  public async getVideoComments(
    params: GetVideoCommentsParams,
    collector?: RapidApiStatsCollector,
  ): Promise<TiktokCommentData | null> {
    const { url, count = 50, cursor = 0 } = params
    return this.handleRequest(() =>
      this.get<TiktokCommentData>(
        `${this.host}/comment/list?url=${encodeURIComponent(url)}&count=${count}&cursor=${cursor}`,
        collector,
      ),
    )
  }

  public async getMusicInfo(
    url: string,
    collector?: RapidApiStatsCollector,
  ): Promise<TTRapidApiResponse<TiktokMusicData> | null> {
    return this.handleRequest(() =>
      this.getWithResponse<TiktokMusicData>(
        `${this.host}/music/info?url=${encodeURIComponent(url)}`,
        collector,
      ),
    )
  }

  public async getMusicPosts(
    musicId: string,
    count: number = 30,
    cursor: number = 0,
    collector?: RapidApiStatsCollector,
  ): Promise<TTRapidApiResponse<TiktokMusicPostsData> | null> {
    return this.handleRequest(() =>
      this.getWithResponse<TiktokMusicPostsData>(
        `${this.host}/music/posts?music_id=${musicId}&count=${count}&cursor=${cursor}`,
        collector,
      ),
    )
  }

  private async handleRequest<T>(requestFunction: () => Promise<T>): Promise<T | null> {
    try {
      return await requestFunction()
    } catch (error) {
      console.error('请求失败,已重试3次:', error)
      return null
    }
  }

  private async get<T>(url: string, collector?: RapidApiStatsCollector): Promise<T> {
    assert(
      this.apiKey != undefined && this.apiKey != '',
      'TT_RAPID_API_KEY is required and not configured, check the environment config',
    )
    console.log('calling rapid api...' + url)

    const endpoint = url.split('?')[0]
    let statusCode = 0
    let result: TTRapidApiResponse<T> | null = null

    try {
      const response = collector
        ? await collector.collect(axiosInstance.get(url), url)
        : await axiosInstance.get(url)
      statusCode = response.status

      // 检查 API 配额
      if (response.headers) {
        SlackClient.getInstance().checkApiQuota(response.headers, 'TikTok RapidAPI')
      }

      result = response.data as TTRapidApiResponse<T>

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }

      if (!result || result.code !== 0) {
        if (result && result.code) {
          statusCode = result.code
        }
        throw new Error(result?.msg || 'Unknown API Error')
      }

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.TIKTOK, RapidApiStatus.SUCCESS, endpoint, statusCode)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })

      return result.data
    } catch (err: any) {
      console.error(`TikTok API错误 (${statusCode}): ${err.message}`)
      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.TIKTOK, RapidApiStatus.ERROR, endpoint, statusCode)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }

  private async getWithResponse<T>(
    url: string,
    collector?: RapidApiStatsCollector,
  ): Promise<TTRapidApiResponse<T>> {
    assert(
      this.apiKey != undefined && this.apiKey != '',
      'TT_RAPID_API_KEY is required and not configured, check the environment config',
    )
    console.log('calling rapid api...' + url)

    const endpoint = url.split('?')[0]
    let statusCode = 0
    let result: TTRapidApiResponse<T> | null = null

    try {
      const response = collector
        ? await collector.collect(axiosInstance.get(url), url)
        : await axiosInstance.get(url)
      statusCode = response.status

      if (response.headers) {
        SlackClient.getInstance().checkApiQuota(response.headers, 'TikTok RapidAPI')
      }

      result = response.data as TTRapidApiResponse<T>

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }

      if (!result || result.code !== 0) {
        if (result && result.code) {
          statusCode = result.code
        }
        throw new Error(result?.msg || 'Unknown API Error')
      }

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.TIKTOK, RapidApiStatus.SUCCESS, endpoint, statusCode)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })

      return result
    } catch (err: any) {
      console.error(`TikTok API错误 (${statusCode}): ${err.message}`)

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.TIKTOK, RapidApiStatus.ERROR, endpoint, statusCode)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }
}

export default TiktokApi
