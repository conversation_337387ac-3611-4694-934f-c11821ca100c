import { downloadJSON } from '@/api/aliyun'
import { describe, it } from 'vitest'

describe('aliyun', () => {
  it(
    'should download and parse JSON file',
    async () => {
      try {
        const jsonData = await downloadJSON('json/countries.geojson')
        console.log(typeof jsonData)
        console.log('JSON data:', jsonData)
      } catch (error) {
        console.error('Error:', error)
        throw error
      }
    },
    { timeout: 100_000 },
  )
})
