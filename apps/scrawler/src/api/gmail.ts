import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET } from '@/config/env'
import { OAuth2Client } from 'google-auth-library'
import { google } from 'googleapis'

/**
 * Token is from here
 * https://github.com/supabase/auth-js/issues/131#issuecomment-1566224009
 * @param param0
 * @returns
 */
export const newGoogleOAuth2Client = ({
  accessToken,
  refreshToken,
}: {
  accessToken: string
  refreshToken: string
}): OAuth2Client => {
  const client = new google.auth.OAuth2(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET)
  client.setCredentials({
    access_token: accessToken,
    refresh_token: refreshToken,
  })

  return client
}

export const verifyGoogleTokens = async (client: OAuth2Client) => {
  try {
    await client.refreshAccessToken()
    const accessToken = await client.getAccessToken()
    const tokenInfo = await client.getTokenInfo(accessToken.token!)
    return tokenInfo
  } catch (error) {
    console.error('Error validating token:', error)
  }
}

function createMessage(
  sender: string,
  to: string,
  subject: string,
  message: string,
  cc?: string[],
  bcc?: string[],
) {
  const headers = [`From: ${sender}`, `To: ${to}`]

  // 添加 CC 头部
  if (cc && cc.length > 0) {
    headers.push(`Cc: ${cc.join(', ')}`)
  }

  // 添加 BCC 头部
  if (bcc && bcc.length > 0) {
    headers.push(`Bcc: ${bcc.join(', ')}`)
  }

  headers.push(
    'Content-Type: text/html; charset=utf-8',
    'MIME-Version: 1.0',
    `Subject: =?UTF-8?B?${Buffer.from(subject).toString('base64')}?=`,
    '',
    message,
  )

  const str = headers.join('\n')

  return Buffer.from(str, 'utf-8')
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '')
    .replace('\\n', '<br />')
}

export async function sendEmailByGmail({
  client,
  subject,
  text,
  from,
  to,
  cc,
  bcc,
}: {
  client: OAuth2Client
  subject: string
  text: string
  from: string
  to: string
  cc?: string[]
  bcc?: string[]
}): Promise<any> {
  try {
    const gmail = google.gmail({ version: 'v1', auth: client })

    const raw = createMessage(from, to, subject, text, cc, bcc)

    const result = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: raw,
      },
    })

    return result
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

export async function getAllSenders(client: OAuth2Client) {
  const gmail = google.gmail({ version: 'v1', auth: client })
  const res = await gmail.users.settings.sendAs.list({
    userId: 'me',
  })
  const sendAsEmails = res.data.sendAs
  return sendAsEmails ?? []
}
