import { OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, OSS_BUCKET, OSS_REGION } from '@/config/env'
import OSS from 'ali-oss'
import { Readable, Writable } from 'stream'

if (!OSS_ACCESS_KEY_ID || !OSS_ACCESS_KEY_SECRET || !OSS_BUCKET) {
  console.error('OSS env not set')
  process.exit(1)
}

export const client = new OSS({
  region: OSS_REGION,
  accessKeyId: OSS_ACCESS_KEY_ID,
  accessKeySecret: OSS_ACCESS_KEY_SECRET,
  bucket: OSS_BUCKET,
})

// 上传文件到OSS
export async function uploadFileByStream(fileName: string, stream: Readable) {
  try {
    // 上传文件
    const result = await client.putStream(fileName, stream)
    console.log('Upload success:', result)
  } catch (err) {
    console.error('Upload failed:', err)
  }
}

export async function downloadFileByStream(fileName: string): Promise<Writable> {
  try {
    // 下载文件
    const result = await client.getStream(fileName)
    return result.stream
  } catch (err) {
    console.error('Download failed:', err)
    throw err
  }
}

export async function downloadFileParseJSON(fileName: string): Promise<any> {
  try {
    const { stream } = await client.getStream(fileName)
    return new Promise((resolve, reject) => {
      let jsonString = ''

      stream.on('data', (chunk: Buffer) => {
        jsonString += chunk.toString()
      })

      stream.on('end', () => {
        try {
          const jsonData = JSON.parse(jsonString)
          resolve(jsonData)
        } catch (err) {
          reject(new Error('Failed to parse JSON data'))
        }
      })

      stream.on('error', reject)
    })
  } catch (err) {
    console.error('Download failed:', err)
    throw err
  }
}

//  Buffer上传
export async function uploadFileByBuffer(fileName: string, buffer: Buffer) {
  try {
    const result = await client.put(fileName, buffer)
    console.log('Upload success:', result)
    return result
  } catch (err) {
    console.error('Upload failed:', err)
    throw err
  }
}

export async function downloadJSON(fileName: string): Promise<any> {
  try {
    const result = await client.get(fileName)
    return JSON.parse(result.content.toString())
  } catch (err) {
    console.error('Download failed:', err)
    throw err
  }
}
