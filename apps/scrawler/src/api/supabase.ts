import { SUPABASE_SERVER_ROLE_KEY, S<PERSON><PERSON><PERSON>E_URL } from '@/config/env'
import { createClient } from '@supabase/supabase-js'

if (!SUPABASE_URL) {
  throw new Error('Missing env.SUPABASE_URL')
}

if (!SUPABASE_SERVER_ROLE_KEY) {
  throw new Error('Missing env.SUPABASE_SERVER_ROLE_KEY')
}
console.log('SUPABASE_URL', SUPABASE_URL)
console.log('SUPABASE_SERVER_ROLE_KEY', SUPABASE_SERVER_ROLE_KEY)

export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVER_ROLE_KEY)
