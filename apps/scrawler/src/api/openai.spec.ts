import { describe, expect, it } from 'vitest'
import { chatCompletion, openRouterApi } from './openai'

describe('openai', () => {
  it('should test open router api', async () => {
    const embedding = await openRouterApi.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: 'who are you?' }],
    })
    console.log(embedding.choices[0].message.content)
    expect(embedding.choices[0].message.content).toBeDefined()
  }, 600_000)

  it('test about open router search apis', async () => {
    const embedding = await openRouterApi.chat.completions.create({
      model: 'openai/gpt-4.1:online',
      messages: [
        { role: 'user', content: 'What is the date today? And how is the weather in beijing?' },
      ],
    })
    console.log(embedding.choices[0].message.content)
  }, 600_000)

  it('should test embedding model', async () => {
    const embedding = await openRouterApi.embeddings.create({
      model: 'openai/gpt-4o-mini',
      input: 'What is the date today? And how is the weather in beijing?',
    })
    console.log(embedding.data[0].embedding)
  }, 600_000)

  it('should test chat completion', async () => {
    const result = await chatCompletion({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: 'who are you?' }],
    })
    console.log(result)
  }, 600_000)

  it('should test chat completion with custom order', async () => {
    const result = await chatCompletion({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: 'who are you?' }],
      order: ['openRouterApi', 'aihubmix'],
    })
    console.log(result)
  }, 600_000)

  it('should test chat completion with custom order 1', async () => {
    const result = await chatCompletion({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: 'How is the weather in beijing now?' }],
    })
    console.log(result)
  }, 600_000)
})
