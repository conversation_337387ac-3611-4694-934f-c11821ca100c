import { OPENAI_API_KEY, OPENROUTER_API_KEY } from '@/config/env'
import { AIModelCodes, AIModelMap } from '@/config/models'
import Sentry from '@/infras/sentry'
import _ from 'lodash'
import OpenAI, { AzureOpenAI } from 'openai'
import {
  ChatCompletionCreateParamsBase,
  ChatCompletionCreateParamsNonStreaming,
} from 'openai/resources/chat/completions.mjs'

type NonStreamParams = ChatCompletionCreateParamsNonStreaming & { order?: AIProvider[] }

export type AIProvider = 'aihubmix' | 'azureOpenai' | 'openRouterApi'

export const openai = new OpenAI({
  baseURL: 'https://aihubmix.com/v1',
  apiKey: OPENAI_API_KEY,
  timeout: 120 * 1000,
})

export const aihubmix = new OpenAI({
  baseURL: 'https://aihubmix.com/v1',
  apiKey: OPENAI_API_KEY,
  timeout: 120 * 1000,
})

export const azureOpenai = new AzureOpenAI({
  apiKey: String(process.env.AZURE_OPENAI_API_KEY),
  endpoint: 'https://easykol.openai.azure.com/',
  deployment: 'text-embedding-3-large',
  timeout: 120 * 1000,
  apiVersion: '2024-06-01',
})

export const openRouterApi = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: OPENROUTER_API_KEY,
  timeout: 120 * 1000,
})

export type AIModelNames = {
  aihubmix?: string
  azureOpenai?: string
  openRouterApi?: string
}

const aiMap = {
  aihubmix: aihubmix,
  openRouterApi: openRouterApi,
  azureOpenai: azureOpenai,
}
export async function chatCompletion(
  params: ChatCompletionCreateParamsBase & { order?: AIProvider[] },
) {
  const { model, order = defaultOrder } = params
  if (!AIModelMap[model as AIModelCodes]) {
    throw new Error(`模型 ${model} 定义不存在`)
  }
  const modelConfig = AIModelMap[model as AIModelCodes] as AIModelNames

  const triedProviders: string[] = []
  for (const ai of order) {
    if (modelConfig[ai as keyof AIModelNames]) {
      const currentModel = modelConfig[ai as keyof AIModelNames]
      triedProviders.push(`${ai}:${currentModel}`)
      try {
        const result = await aiMap[ai as AIProvider].chat.completions.create({
          ..._.omit(params, 'order'),
          model: currentModel,
        } as NonStreamParams)
        if (result.choices[0].message.content) {
          console.log(
            `模型 ${ai}:${currentModel} 调用成功, 消耗 token: ${result.usage?.total_tokens}`,
          )
          return result
        }
      } catch (error) {
        console.error(`模型 ${ai}:${currentModel} 调用失败: ${error}`)
        Sentry.captureException(error, {
          tags: {
            model,
            ai,
            currentModel,
          },
        })
      }
    }
  }
  const errorMsg = `模型 ${model} 调用失败，已尝试: ${triedProviders.join(', ')}`
  Sentry.captureException(new Error(errorMsg), {
    tags: {
      model,
      triedProviders: triedProviders.join(','),
    },
  })
  throw new Error(errorMsg)
}

export const defaultOrder = ['aihubmix', 'openRouterApi', 'azureOpenai']

// export const callModel = async()
