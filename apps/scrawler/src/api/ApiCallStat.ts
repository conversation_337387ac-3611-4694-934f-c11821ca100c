import { EXTERNAL_LINK_MAX_RAPID_API_REQUEST } from '@/config/env'
import { Prisma } from '@repo/database'
import InputJsonValue = Prisma.InputJsonValue

export type RapidApiStat = {
  url: string
  status: number
  duration: number
  success: boolean
  count?: number
}

export type RapidApiStatReport = InputJsonValue & {
  totalDuration: number
  uriCount: Object
  totalCount: number
  successCount: number
  failedCount: number
  avgDuration: number
  avgSuccessDuration: number
}

class RapidApiStatsCollector {
  private stats: RapidApiStat[] = []
  private startTime: number

  constructor() {
    this.startTime = new Date().getTime()
  }

  async collect<T>(apiCall: Promise<T>, url: string): Promise<T> {
    const startTime = performance.now()
    try {
      const response = await apiCall
      this.stats.push({
        url,
        status: 200,
        duration: performance.now() - startTime,
        success: true,
      })
      return response
    } catch (error: any) {
      this.stats.push({
        url,
        status: error.status ?? 500,
        duration: performance.now() - startTime,
        success: false,
      })
      throw error
    }
  }

  getStats(): RapidApiStatReport {
    return {
      totalDuration: new Date().getTime() - this.startTime,
      uriCount: Object.fromEntries(
        this.stats
          .map((stat) => stat.url)
          .reduce((map, uri) => map.set(uri, map.has(uri) ? map.get(uri) + 1 : 1), new Map()),
      ),
      totalCount: this.stats.length,
      successCount: this.stats.filter((stat) => stat.success).length,
      failedCount: this.stats.filter((stat) => !stat.success).length,
      avgDuration:
        this.stats.map((stat) => stat.duration).reduce((sum, i) => sum + i, 0) / this.stats.length,
      avgSuccessDuration:
        this.stats
          .filter((stat) => stat.success)
          .map((stat) => stat.duration)
          .reduce((sum, i) => sum + i, 0) / this.stats.filter((stat) => stat.success).length,
    }
  }

  isExternalLinkExceeded(): boolean {
    return this.stats.length > Number(EXTERNAL_LINK_MAX_RAPID_API_REQUEST ?? 10)
  }
}

export default RapidApiStatsCollector
