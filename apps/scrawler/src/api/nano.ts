import { NAINF_KEY } from '@/config/env'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { KolPlatform } from '@repo/database'
import dayjs from 'dayjs'
import { ofetch } from 'ofetch'
import { v4 as uuidv4 } from 'uuid'

const toNanoPlatformName = (platform: KolPlatform) => {
  switch (platform) {
    case KolPlatform.YOUTUBE:
      return 'ytb'
    case KolPlatform.INSTAGRAM:
      return 'ins'
    case KolPlatform.TIKTOK:
      return 'tik'
    default:
      throw new Error('Invalid platform')
  }
}

/** @deprecated */
export const getChannelEmailFromNano = async (
  platform: KolPlatform,
  channelId: string,
): Promise<{ email: string | undefined; source: EmailSourceType | undefined }> => {
  const nanoPlatform = toNanoPlatformName(platform)
  let emails = undefined
  let email = undefined
  let source = undefined
  try {
    const offcialRes = await ofetch(
      `https://public-api.similartube.co/${nanoPlatform}/${channelId}/contact?y2b_first=1`,
      {
        headers: {
          'X-NAINF-UUID': NAINF_KEY!,
          'X-REQ-ID': uuidv4(),
        },
        retry: 3,
        retryDelay: 500,
      },
    )
    source =
      platform == KolPlatform.YOUTUBE
        ? EmailSourceType.NANO_EMAIL_BUTTON
        : EmailSourceType.NANO_UNCLEAR
    emails = offcialRes.data.email
    // 只有 youtube 会再去找一下不干净的 nano email
    if (platform == KolPlatform.YOUTUBE && !emails?.length) {
      const allRes = await ofetch(
        `https://public-api.similartube.co/${nanoPlatform}/${channelId}/contact`,
        {
          headers: {
            'X-NAINF-UUID': NAINF_KEY!,
            'X-REQ-ID': uuidv4(),
          },
          retry: 3,
          retryDelay: 500,
        },
      )
      emails = allRes.data.email
      source = EmailSourceType.NANO_UNCLEAR
    }
    if (emails.length > 1) {
      Sentry.captureEvent({
        message: 'nano-milti-email',
        level: 'info',
        timestamp: dayjs().unix(),
        tags: {
          platform: platform,
          id: channelId,
          emails: JSON.stringify(emails),
          y2b_first: true,
        },
      })
    }
    email = offcialRes.data.email?.[0]
    return { email: email, source: source }
  } catch (error) {
    return { email: undefined, source: undefined }
  } finally {
    Sentry.captureEvent({
      message: 'nano-email',
      level: 'info',
      timestamp: dayjs().unix(),
      tags: {
        hasEmail: !!email,
        platform: platform,
        id: channelId,
        emails: emails,
        email: email,
      },
    })
  }
}
