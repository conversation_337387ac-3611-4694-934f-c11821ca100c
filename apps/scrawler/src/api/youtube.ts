import {
  Channel,
  ChannelVideo,
  CommentResponse,
  RelatedVideo,
  SearchVideoResult,
  VideoDetail,
  YoutubeHashTagBreakRes,
} from '@/api/@types/rapidapi/Youtube.ts'
import RapidApiStatsCollector from '@/api/ApiCallStat.ts'
import Sentry from '@/infras/sentry'
import { VideoSearchOptions } from '@/lib/youtube'
import YoutubeRapidApi from '@/lib/youtubeRapidApi'
import YoutubeRapidApiV2 from '@/lib/youtubeRapidApi.v2'
import YoutubeiApi from '@/lib/youtubeiApi'
import { getUnixTimestamp, parsePublishedTime } from '@/utils/date'

async function downgradable(fn: string, ...args: any[]) {
  try {
    const result = await (YoutubeRapidApi.getInstance()[fn as keyof YoutubeRapidApi] as Function)(
      ...args,
    )
    if (fn === 'getChannelVideos' && result.length === 0) {
      throw new Error('no videos by rapid api V1' + fn + JSON.stringify(args[0]))
    }
    return result
  } catch (error) {
    Sentry.captureException(error, {
      tags: {
        transaction: 'Youtube API service',
        api_version: 'v1',
        args: JSON.stringify(args),
      },
    })
    try {
      if (fn === 'searchVideo') {
        // Rapid V2 dont have searchVideo
        return await (YoutubeRapidApi.getInstance()[fn as keyof YoutubeRapidApi] as Function)(
          ...args,
        )
      }
      return await (YoutubeRapidApiV2.getInstance()[fn as keyof YoutubeRapidApiV2] as Function)(
        ...args,
      )
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          transaction: 'Youtube API service',
          api_version: 'v2',
          args: JSON.stringify(args),
        },
      })
      if (fn == 'getChannelId') {
        return await (YoutubeiApi.getInstance()[fn as keyof YoutubeiApi] as Function)(...args)
      }
      throw error
    }
  }
}

class YoutubeApi {
  private static instance: YoutubeApi

  public static getInstance(): YoutubeApi {
    if (!YoutubeApi.instance) {
      YoutubeApi.instance = new YoutubeApi()
    }
    return YoutubeApi.instance
  }

  public async getChannel(channelId: string, collector?: RapidApiStatsCollector): Promise<Channel> {
    return downgradable('getChannel', channelId, collector)
  }

  public async getChannelWithVideos(
    channelIdOrName: string,
    collector?: RapidApiStatsCollector,
  ): Promise<Channel | null> {
    let channelId = channelIdOrName
    if (!channelIdOrName.startsWith('UC')) {
      channelId = await downgradable('getChannelId', channelIdOrName, collector)
    }
    const channel = (await downgradable('getChannel', channelId, collector)) as Channel
    if (!channel?.title) {
      return null
    }
    const videos = await downgradable('getChannelVideos', channelId, collector)
    channel.videos = videos.length ? videos : {}
    return this.processChannel(channel)
  }

  public async getVideoRelatedVideo(
    videoId: string,
    count: number,
    collector?: RapidApiStatsCollector,
  ): Promise<RelatedVideo[]> {
    return downgradable('getVideoRelatedVideos', videoId, count, collector)
  }

  public async getYoutubeChannelId(
    idOrName: string,
    collector?: RapidApiStatsCollector,
  ): Promise<string | undefined> {
    return downgradable('getChannelId', idOrName, collector)
  }

  public async getChannelVideos(
    channelId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]> {
    return downgradable('getChannelVideos', channelId, collector)
  }

  public async searchVideo(
    keyword: string,
    options: VideoSearchOptions,
    collector?: RapidApiStatsCollector,
  ): Promise<SearchVideoResult> {
    return downgradable('searchVideo', keyword, options, collector)
  }

  public async getVideoComments(
    videoId: string,
    options?: {
      token?: string
      sort_by?: 'newest' | 'top'
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<CommentResponse> {
    return downgradable('getVideoComments', videoId, options, collector)
  }

  public async getVideo(
    videoId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<VideoDetail | undefined> {
    return downgradable('getVideo', videoId, collector)
  }

  public async getHashtag(
    hashtag: string,
    options?: {
      token?: string
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakRes> {
    return downgradable('getHashtag', hashtag, options, collector)
  }

  private processChannel(channel: Channel): Channel {
    const videos = channel.videos || []
    if (videos.length === 0 || !('filter' in videos)) {
      return channel
    }
    // 过滤掉播放数为0的视频 取最近 20 个的 top10 并求均播
    const recentVideos = videos
      .filter((video) => +video.viewCount > 0)
      .slice(0, 20)
      .sort((a, b) => +b.viewCount - +a.viewCount)
      .slice(0, 10)

    // 计算平均观看次数
    const videosAverageViewCount =
      recentVideos.length > 0
        ? Math.round(
            recentVideos.reduce((sum, video) => sum + +video.viewCount, 0) / recentVideos.length,
          )
        : 0

    const lastUpdatedAt = recentVideos?.length
      ? Math.max(
          ...recentVideos.map((v) => getUnixTimestamp(parsePublishedTime(v.publishedTimeText))),
        )
      : 0

    // 取最近20个视频
    channel.videos = videos.slice(0, 20) ?? {}
    channel.lastUpdatedAt = lastUpdatedAt
    channel.videosAverageViewCount = videosAverageViewCount
    if (!videosAverageViewCount || !lastUpdatedAt || !channel.subscriberCount) {
      console.log(channel.channelId)
      console.log(videosAverageViewCount)
      console.log(lastUpdatedAt)
      console.log(channel.subscriberCount)
      console.log(`badcase`)
    }
    return channel
  }
}

export default YoutubeApi
