// TikTok RapidAPI 响应的通用类型
export type TTRapidApiResponse<T> = {
  code: number // 响应状态码
  msg: string // 响应消息
  processed_time: number // 处理时间（可能是毫秒）
  data: T // 响应数据，类型由泛型 T 决定
}

// TikTok RapidAPI 分页请求参数
export type TTRapidApiPaginate = {
  count: number // 每页数量
  cursor: number // 分页游标，用于指示从哪里开始获取下一页数据
}

// TikTok RapidAPI 用户请求参数
export type TTRapidApiUserRequest = {
  unique_id?: string // 用户的唯一标识符（通常是用户名）
  user_id?: string // 用户的数字ID
}

// TikTok RapidAPI 带分页的用户请求参数
// 结合了分页参数和用户请求参数
export type TTRapidApiUserPaginateRequest = TTRapidApiPaginate & TTRapidApiUserRequest

// Instagram RapidAPI 响应的通用类型
export type IGRapidApiResponse<T> = {
  data: T // 响应数据，类型由泛型 T 决定
  status?: string // 响应状态
  message?: string | null // 响应消息，可能为 null
  pagination_token?: string
}

export enum RapidApiStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
}
