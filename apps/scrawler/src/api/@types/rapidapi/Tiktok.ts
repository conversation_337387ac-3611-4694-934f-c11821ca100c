import { EmailSourceType } from '@/types/email'

// 所谓 challenge 就是 hashtag
export type TiktokChallenge = {
  id: string // 挑战的唯一标识符
  cha_name: string // 挑战名称
  desc: string // 挑战描述
  user_count: number // 参与挑战的用户数量
  view_count: number // 挑战相关视频的总观看次数
  is_pgcshow: boolean // 是否为PGC（专业生成内容）展示
  is_commerce: boolean // 是否为商业挑战
  is_challenge: boolean // 是否为正式挑战
  is_strong_music: boolean // 是否有强烈的音乐元素
  type: number // 挑战类型
  cover: string // 挑战封面图片URL
}

export type VideoBasic = {
  aweme_id: string // 视频的唯一标识符
  region: string // 视频所属地区
  title: string // 视频标题
  cover: string // 视频封面图片 URL
  ai_dynamic_cover: string // AI 生成的动态封面 URL
  origin_cover: string // 原始封面图片 URL
  duration: number // 视频时长（单位可能是秒）
  play: string // 视频播放 URL
  wmplay: string // 带水印的视频播放 URL
  size: number // 视频文件大小
  wm_size: any // 带水印视频的文件大小（类型不确定）
  music: string // 背景音乐 URL
  music_info?: {
    // 背景音乐详细信息
    id: string // 音乐 ID
    title: string // 音乐标题
    play: string // 音乐播放 URL
    cover: string // 音乐封面图片 URL
    author: string // 音乐作者
    original: boolean // 是否为原创音乐
    duration: number // 音乐时长
    album: string // 所属专辑
  }
  play_count: number // 播放次数
  digg_count: number // 点赞数
  comment_count: number // 评论数
  share_count: number // 分享次数
  download_count: number // 下载次数
  collect_count: number // 收藏次数
  create_time: number // 创建时间（可能是时间戳）
  anchors: any // 主播信息（具体内容需要进一步确认）
  anchors_extras: string // 额外的主播信息
  is_ad: boolean // 是否为广告
  commerce_info: {
    // 商业信息
    adv_promotable: boolean // 是否可推广
    auction_ad_invited: boolean // 是否被邀请参与竞价广告
    branded_content_type: number // 品牌内容类型
    with_comment_filter_words: boolean // 是否有评论过滤词
  }
  commercial_video_info: string // 商业视频信息
  item_comment_settings: number // 评论设置（具体含义需要进一步确认）
  mentioned_users: string // 提及的用户
  author: {
    // 视频作者信息
    id: string // 作者 ID
    unique_id: string // 作者唯一标识符
    nickname: string // 作者昵称
    avatar: string // 作者头像 URL
  }
}

// TikTokVideo 类型扩展了 VideoBasic，添加了一些额外属性
export type TikTokVideo = VideoBasic & {
  video_id: string // 视频 ID（可能与 aweme_id 相同）
  is_top: number // 是否为置顶视频（0 或 1）
}

// TiktokVideoDetail 类型也扩展了 VideoBasic，添加了一些详细信息
export type TiktokVideoDetail = VideoBasic & {
  id: string
  hdplay: string
  hd_size: string
}

// TikTok用户基本信息
export type TiktokUserBasic = {
  id: string // 用户ID
  uniqueId: string // 用户唯一标识符（通常是用户名）
  nickname: string // 用户昵称
  avatarThumb: string // 用户头像缩略图URL
  avatarMedium: string // 用户中等尺寸头像URL
  avatarLarger: string // 用户大尺寸头像URL
  signature: string // 用户个人签名
  verified: boolean // 是否为认证用户
  secUid: string // 安全用户ID
  secret: boolean // 是否为私密账号
  ftc: boolean // 是否遵循FTC规定（美国联邦贸易委员会）
  relation: number // 与当前用户的关系（如：是否关注）
  openFavorite: boolean // 是否公开收藏列表
  // commentSetting: any | null;  // 评论设置
  // duetSetting: any | null;     // 二重唱设置
  // stitchSetting: any | null;   // 剪辑设置
  privateAccount: boolean // 是否为私密账号
  isADVirtual: boolean // 是否为广告虚拟账号
  isUnderAge18: boolean // 是否未满18岁
  ins_id: string // Instagram账号ID
  twitter_id: string // Twitter账号ID
  youtube_channel_title: string // YouTube频道标题
  youtube_channel_id: string // YouTube频道ID
  bio_email?: string // 用户个人简介中的邮箱
  email?: string
  emailSource?: EmailSourceType
}

// TikTok用户统计信息
export type TiktokUserStats = {
  followingCount: number // 关注数
  followerCount: number // 粉丝数
  heartCount: number // 获赞总数
  videoCount: number // 发布视频数
  diggCount: number // 点赞其他视频的数量
  heart: number // 可能是另一种形式的获赞数（具体含义需确认）
}

// 完整的TikTok用户信息，包括基本信息和统计数据
export type TiktokUser = {
  user: TiktokUserBasic // 用户基本信息
  stats: TiktokUserStats // 用户统计信息
}

// 新增类型定义
export interface TikTokSearchVideo extends TikTokVideo {
  // 可能需要添加一些搜索特有的字段
}

export interface TikTokSearchResponse {
  videos: TikTokSearchVideo[]
  cursor: number
  hasMore: boolean
}

export interface TiktokFollowing {
  id: string
  region: string
  sec_uid: string
  unique_id: string
  nickname: string
  signature: string
  avatar: string
  verified: boolean
  secret: boolean
  aweme_count: number
  following_count: number
  follower_count: number
  favoriting_count: number
  total_favorited: number
  ins_id: string
  youtube_channel_title: string
  youtube_channel_id: string
  twitter_name: string
  twitter_id: string
}

export interface TiktokFollowingResponse {
  followings: TiktokFollowing[]
  total: number
  time: number
  hasMore: boolean
}

// 评论用户信息
export interface TiktokCommentUser {
  id: string
  region: string
  sec_uid: string
  unique_id: string
  nickname: string
  signature: string
  avatar: string
  verified: boolean
  secret: boolean
  aweme_count: number
  following_count: number
  follower_count: number
  favoriting_count: number
  total_favorited: number
  ins_id: string
  youtube_channel_title: string
  youtube_channel_id: string
  twitter_name: string
  twitter_id: string
}

// TiktokFollower 与 TiktokFollowing 结构相同
export interface TiktokFollower {
  id: string // 用户ID
  region: string // 地区
  sec_uid: string // 安全用户ID
  unique_id: string // 唯一标识符（用户名）
  nickname: string // 昵称
  signature: string // 个人签名
  avatar: string // 头像URL
  verified: boolean // 是否认证
  secret: boolean // 是否为私密账号
  aweme_count: number // 发布视频数量
  following_count: number // 关注数量
  follower_count: number // 粉丝数量
  favoriting_count: number // 收藏数量
  total_favorited: number // 获赞总数
  ins_id: string // Instagram ID
  youtube_channel_title: string // YouTube 频道标题
  youtube_channel_id: string // YouTube 频道ID
  twitter_name: string // Twitter 用户名
  twitter_id: string // Twitter ID
}

// tiktok followers response
export interface TiktokFollowersResponse {
  followers: TiktokFollower[]
  total: number
  time: number
  hasMore: boolean
}

// TikTok评论信息
export interface TiktokComment {
  id: string
  video_id: string
  text: string
  create_time: number
  digg_count: number
  reply_total: number
  user: TiktokCommentUser
  status: number
}

// TikTok评论列表数据结构
export interface TiktokCommentData {
  comments: TiktokComment[]
  total: number
  cursor: number
  hasMore: boolean
}

export interface GetVideoCommentsParams {
  url: string // 完整的TikTok视频URL
  count?: number // 每页评论数量
  cursor?: number // 分页游标
}

export interface TiktokHashtagVideosData {
  cursor: number
  hasMore: boolean
  videos: TikTokVideo[]
}

export interface TiktokMusicData {
  id: string
  title: string
  play: string
  cover: string
  author: string
  original: boolean
  duration: number
  album: string
  video_count: number
}

export interface TiktokMusicPostsData {
  cursor: number
  hasMore: boolean
  videos: TikTokVideo[]
}
