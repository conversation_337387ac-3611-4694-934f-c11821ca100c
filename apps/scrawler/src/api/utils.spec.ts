import { parsePathParams } from '@/api/util.ts'
import countries from 'i18n-iso-countries'
import { describe, expect, it } from 'vitest'

describe('test utils', () => {
  it('should return params', () => {
    const params = {
      a: 1,
      b: ['2', '3'],
    }
    const result = parsePathParams(params)
    expect('a=1&b=2%2C3').equals(result)
  })

  it('should return country code', async () => {
    const name = 'China'
    const code = countries.getAlpha2Code(name, 'en')
    expect(code).eq('CN')
  })
})
