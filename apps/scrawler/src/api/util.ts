export function parsePathParams(params: object): string {
  return Object.entries(params)
    .reduce((acc, [key, value]) => {
      if (value) {
        return (acc += key + '=' + encodeURIComponent(value) + '&')
      }
      return acc
    }, '')
    .slice(0, -1)
}

export async function logTime<T>(promise: Promise<T>, name: string) {
  const start = Date.now()
  const result = await promise
  const end = Date.now()
  console.log(`${name}: ${end - start}ms`)
  return result as T
}

export function logFilter<T>(
  array: T[],
  predicate: (value: T) => boolean,
  logPrefix?: string,
): T[] {
  const originalLength = array.length
  const filtered = array.filter(predicate)
  const newLength = filtered.length
  console.log(
    `${logPrefix || 'Filter'}: 数组长度从 ${originalLength} 变更为 ${newLength}, 过滤掉 ${originalLength - newLength} 个元素`,
  )
  return filtered
}

// 检查 BigInt 字段
export const checkBigInt = (obj: any, path: string = '') => {
  if (obj === null || obj === undefined) return
  if (typeof obj === 'bigint') {
    console.log(`发现 BigInt 字段: ${path} = ${obj}`)
  } else if (typeof obj === 'object') {
    Object.entries(obj).forEach(([key, value]) => {
      checkBigInt(value, path ? `${path}.${key}` : key)
    })
  }
}
