pnpm install --filter=worker...
pnpm run build --filter=worker...
cd apps/scrawler && pnpm run build:worker && cd ../..
pnpm --filter=worker --prod deploy worker
cp -r apps/scrawler/dist worker/
docker build -t tm-scrawler-worker-prod -f apps/scrawler/Dockerfile-worker worker 
docker rm -f tm-scrawler-worker-prod
# docker run -d --env-file .env --name tm-scrawler-worker tm-scrawler-worker
docker run -d --env-file .env --name tm-scrawler-worker-prod --restart unless-stopped tm-scrawler-worker-prod
