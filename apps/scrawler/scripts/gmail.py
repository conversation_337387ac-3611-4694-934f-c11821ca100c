from google.oauth2 import service_account
from googleapiclient.discovery import build
import base64
from email.mime.text import MIMEText

# 加载服务账号的密钥文件
KEYFILE_PATH = 'talent-marketing-a5a2462423c8.json'
SCOPES = ['https://www.googleapis.com/auth/gmail.send']

# 使用服务账号的凭证
credentials = service_account.Credentials.from_service_account_file(KEYFILE_PATH, scopes=SCOPES)
delegated_credentials = credentials.with_subject('<EMAIL>')

# 构建Gmail服务
service = build('gmail', 'v1', credentials=delegated_credentials)

def create_message(sender, to, subject, message_text):
    message = MIMEText(message_text)
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject
    return {'raw': base64.urlsafe_b64encode(message.as_bytes()).decode()}

def send_message(service, user_id, message):
    try:
        message = (service.users().messages().send(userId=user_id, body=message).execute())
        print(f'Message Id: {message["id"]}')
        return message
    except Exception as error:
        print(f'An error occurred: {error}')
        return None

# 创建邮件
sender = '<EMAIL>'
to = '<EMAIL>'
subject = 'Test Email'
message_text = 'This is a test email sent using Gmail API and Python!'

message = create_message(sender, to, subject, message_text)

# 发送邮件
send_message(service, 'me', message)
