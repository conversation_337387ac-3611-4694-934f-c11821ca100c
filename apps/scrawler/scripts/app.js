const request = require('request');

async function getVideoInfo(videoId) {
    return new Promise((resolve, reject) => {
        let url = `https://www.youtube.com/watch?v=${videoId}`;

        // 访问YouTube视频页面
        request(url, (error, response, html) => {
            if (!error && response.statusCode === 200) {
                let videoInfo = {};

                // 从HTML中提取视频信息
                try {
                    let playerMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+meta|<\/script>)/);
                    let playerData = JSON.parse(playerMatch[1]);

                    let videoDetails = playerData.videoDetails;
                    videoInfo.title = videoDetails.title;
                    videoInfo.lengthSeconds = videoDetails.lengthSeconds;
                    videoInfo.channelId = videoDetails.channelId;
                    videoInfo.videoId = videoDetails.videoId;
                    videoInfo.viewCount = videoDetails.viewCount;
                    videoInfo.author = videoDetails.author;

                    let playerMicroformat = playerData.microformat.playerMicroformatRenderer;
                    videoInfo.description = playerMicroformat.description.simpleText;
                    videoInfo.category = playerMicroformat.category;
                    videoInfo.publishDate = playerMicroformat.publishDate;
                    videoInfo.uploadDate = playerMicroformat.uploadDate;

                    // 提取相关视频推荐
                    let relatedVideos = [];
                    let relatedMatch = html.match(/ytInitialData\s*=\s*({.+?})\s*;\s*(?:var\s+meta|<\/script>)/);
                    if (relatedMatch) {
                        let relatedData = JSON.parse(relatedMatch[1]);
                        if (relatedData.contents && relatedData.contents.twoColumnWatchNextResults &&
                            relatedData.contents.twoColumnWatchNextResults.secondaryResults &&
                            relatedData.contents.twoColumnWatchNextResults.secondaryResults.secondaryResults &&
                            relatedData.contents.twoColumnWatchNextResults.secondaryResults.secondaryResults.results) {
                            let secondaryResults = relatedData.contents.twoColumnWatchNextResults.secondaryResults.secondaryResults.results;
                            secondaryResults.forEach(result => {
                                if (result.compactVideoRenderer) {
                                    let video = {};
                                    let renderer = result.compactVideoRenderer;
                                    video.videoId = renderer.videoId;
                                    video.title = renderer.title.simpleText;
                                    video.thumbnail = renderer.thumbnail && renderer.thumbnail.thumbnails ? renderer.thumbnail.thumbnails[0].url : '';
                                    video.duration = renderer.lengthText ? renderer.lengthText.simpleText : '';
                                    video.viewCount = renderer.viewCountText ? renderer.viewCountText.simpleText : '';
                                    relatedVideos.push(video);
                                }
                            });
                        }
                    }
                    videoInfo.relatedVideos = relatedVideos;

                    resolve(videoInfo);
                } catch (error) {
                    reject(error);
                }
            } else {
                reject(error);
            }
        });
    });
}

// 示例用法
getVideoInfo('F-WG3EkxEzg')
    .then(videoInfo => {
        console.log(videoInfo);
    })
    .catch(error => {
        console.error('Error:', error);
    });
