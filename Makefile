ifndef APP
    $(error VA<PERSON>ABLE APP is not set. Please set it when calling make)
endif
SUFFIX := $(shell git branch --show-current | tr '/' '-')-$(shell git rev-parse --short HEAD)
TAG_HZ := registry.jellow.site/iftech/talent-marketing-server:$(SUFFIX)
TAG_HK := registry-intl.cn-hongkong.aliyuncs.com/iftech-hk/talent-marketing-server:$(SUFFIX)

.PHONY: install
install:
	@echo "Installing dependencies for: $(APP)"
	pnpm install --frozen-lockfile --filter=$(APP)...

.PHONY: compile
compile: clean install
	@echo "Compiling: app=$(APP)"
	pnpm run build --filter=$(APP)...
	@echo "Deploying: app=$(APP)"
	pnpm --filter=$(APP) --prod deploy ./dist/$(APP)

.PHONY: build
build: 
	@echo "Building: suffix=$(SUFFIX)"
	docker build -f ./Dockerfile.$(APP) --platform linux/amd64 --build-arg NPM_AUTH_TOKEN=$(NPM_AUTH_TOKEN) -t $(TAG_HZ) .

.PHONY: push-hz
push-hz:
	@echo "Pushing: suffix=$(SUFFIX)"
	docker push $(TAG_HZ)

.PHONY: push-hk
push-hk:
	@echo "Pushing: suffix=$(SUFFIX)"
	docker tag $(TAG_HZ) $(TAG_HK)
	docker push $(TAG_HK)

.PHONY: clean
clean:
	@echo "Cleaning: app=$(APP)"
	rm -rf ./dist/$(APP)