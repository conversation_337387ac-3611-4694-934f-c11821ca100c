FROM node:20-bookworm AS builder

ARG NODE_ENV
ARG NPM_AUTH_TOKEN
ENV NODE_ENV=$NODE_ENV
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV APP=worker

RUN corepack enable && corepack prepare pnpm@latest --activate

WORKDIR /tmp

COPY . .
RUN echo "//npm.midway.run/:_authToken=$NPM_AUTH_TOKEN" >> .npmrc
RUN make compile

FROM node:20-alpine3.20

WORKDIR /app

COPY --from=builder /tmp/dist/worker /app/

CMD ["npm", "run", "start"]